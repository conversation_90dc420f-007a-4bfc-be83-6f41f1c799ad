#! /bin/bash

set -e -o pipefail

script_dir=$(dirname "${BASH_SOURCE[0]}")
root=$(readlink -f $script_dir/../..)
package_dir=$root/package
external_package=ext-pkg

# Initialize a temporary file as an empty YAML document
temp_file="temp.yaml"
touch $temp_file

# Get packages that have dependencies from the multi-package.yaml file
packages=$(yq '.packages[]' $(find $package_dir/$1 -name multi-package.yaml))

# Loop through the list of files
for package in $packages; do

  package=$(eval echo $package)

  file=$(find $package_dir/$1/$package -name "daml.yaml")

  # Extract the "name" field using yq
  name_field=$(yq '.name' "$file")

  # Check if the "name" already exists in the temp file
  existing_file=$(yq ".[] | select(.name == \"$name_field\").file" $temp_file)

  if [[ -n "$existing_file" ]]; then
      # Compare timestamps and update if the current file is newer
      if [[ "$file" -nt "$existing_file" ]]; then
          # Update the entry with the newer file
          yq -i "(.[] | select(.name == \"$name_field\")).file = \"$file\"" $temp_file
      fi
  else
      # Add a new entry for this name
      yq -i ". += [{\"name\": \"$name_field\", \"file\": \"$file\"}]" $temp_file
  fi
done

for file in $(yq '.[].file' $temp_file); do
  echo "Getting depencies for $file"

  for dependency in $(yq '.data-dependencies[]' $file); do

    dir=$(dirname $dependency)
    dir_structure=$(eval echo ${dir##*../}) # Remove leading relative path
    dirname=${dir_structure%%/.*} # Remove trailing file structure

    if [[ $dirname == "$external_package"* ]]; then
      mkdir -p $dir_structure
      module_path=${dir_structure#*/}


      echo ""
      echo $dir_structure
      echo ""



      if [[ $module_path == $external_package ]]; then
        module_path=""
      fi

      filename=$(basename $dependency) # Get file name
      filename=$(eval echo $filename)
      filename_prefix=${filename%%-*} # Remove all trailing dash

      repository=$(yq 'to_entries | map(select(.value."dependencies-prefix"[] | contains("'$filename_prefix'")) | .value)[0]' $root/$external_package/dependencies.json)

      base_url=$(echo $repository | yq '.base-url')
      url=$base_url/${module_path:+"$module_path/"}$filename

      username=$(printenv $(echo $repository | yq '.auth.username'))
      password=$(printenv $(echo $repository | yq '.auth.password'))

      echo "Fetching $filename dependency for $(yq '.name' $file)"

      wget -N -nv -P $root/$dir_structure \
        --user ${username:?"$username_var environment variable is not set"} \
        --password ${password:?"$password_var environment variable is not set"} \
        $url
    fi
  done
done

rm -f $temp_file