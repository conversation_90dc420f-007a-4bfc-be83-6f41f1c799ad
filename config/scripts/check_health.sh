#! /bin/bash

set -e -o pipefail

attempts=1
while :
do
    http_status=$(curl -s -o /dev/null -w "%{http_code}" $1)
    if [ $(echo "$http_status") = 200 ]
    then
        echo "Node ready (status="$http_status")!"
        break
    elif [ $attempts -gt $2 ]
    then
        echo "Timed out..."
        exit 1
    else
        echo "Node not ready (status="$http_status", attempts=$attempts), sleeping..."
        let "attempts++"
        sleep 5
    fi
done