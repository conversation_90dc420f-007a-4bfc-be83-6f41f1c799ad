#! /bin/bash

set -e -o pipefail

script_dir=$(dirname "${BASH_SOURCE[0]}")
root=$(readlink -f $script_dir/../..)
migration=$root/package/migration

for file in $(find $migration/$1 -name "multi-package.yaml"); do
  for package in $(yq '.packages[]' $file); do

    cd $migration
    eval_package=$(eval echo $package)

    if [ ! -d "$eval_package" ]; then
      migration_package=${eval_package%%/*} # Remove trailing file structure
      versioned_migration=${eval_package##*/} # Remove leading file structure
      legacy_package=${versioned_migration%%-*} # Remove trailing version

      cd $migration_package
      latest_migration_dir=$(find . -type d -name "*-$legacy_package")

      echo "Creating copy directory for new migration code for $eval_package from existing ${latest_migration_dir##./}"
      cp -r $latest_migration_dir $versioned_migration
    fi
  done
done