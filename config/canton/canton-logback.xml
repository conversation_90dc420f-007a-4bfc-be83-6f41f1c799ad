<configuration scan="true" scanPeriod="1 minute"> <!-- to pick up changes every minute -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!-- drops every event below WARN, that is INFO, TRACE and DEBUG -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg %replace(, context: %marker){', context: $', ''} %n</pattern>
        </encoder>
    </appender>
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>../../log/canton.log</file>
        <append>true</append>
        <immediateFlush>false</immediateFlush>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
          <!-- daily rollover -->
          <fileNamePattern>../../log/canton.%d{yyyy-MM-dd}.log.gz</fileNamePattern>
        </rollingPolicy>
        <encoder>
          <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg %replace(, context: %marker){', context: $', ''} %n</pattern>
        </encoder>
      </appender>

    <logger name="io.netty" level="WARN">
        <appender-ref ref="FILE"/>
    </logger>
    <logger name="io.grpc.netty" level="WARN">
        <appender-ref ref="FILE"/>
    </logger>
    <logger name="c.d.c.p.p.TransactionProcessor" level="DEBUG">
        <appender-ref ref="FILE"/>
    </logger>
    <logger name="c.d.c.n.g.ApiRequestLogger" level="DEBUG">
        <appender-ref ref="FILE"/>
    </logger>
    <logger name="c.d.c.p.a.s.c.CommandSubmissionServiceImpl" level="DEBUG">
        <appender-ref ref="FILE"/>
    </logger>
    <root level="INFO">
        <appender-ref ref="STDOUT" />
        <appender-ref ref="FILE" />
    </root>
</configuration>