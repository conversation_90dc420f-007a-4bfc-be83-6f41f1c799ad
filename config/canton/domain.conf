canton {
  monitoring{
    logging {
      event-details = true
      api {
          message-payloads = true
          max-method-length = 1000
          max-message-lines = 10000
          max-string-length = 10000
          max-metadata-size = 10000
      }
    }
  }
  domains {
    domain {
      storage.type = memory
      public-api.port = 5011
      admin-api.port = 5012
      init.domain-parameters.unique-contract-keys = false
      init.domain-parameters.protocol-version = 5
    }
  }
  // enable ledger_api commands for our getting started guide
  features.enable-testing-commands = yes
}