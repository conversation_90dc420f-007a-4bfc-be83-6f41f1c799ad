canton {
  monitoring {
    health {
      server {
          port = 7001
      }
      check {
        type = ping
        participant = participant
        interval = 30s
      }
    }
    logging {
      event-details = true
      api {
          message-payloads = true
          max-method-length = 1000
          max-message-lines = 10000
          max-string-length = 10000
          max-metadata-size = 10000
      }
    }
  }
  participants {
    participant {
      storage.type = memory
      ledger-api.port = 5001
      admin-api.port = 5002
      init.parameters.unique-contract-keys = false
    }
  }
  features{
    // enable ledger_api commands for our getting started guide
    enable-testing-commands = yes
    // enable multi-domain support
    enable-preview-commands = yes
  }
}