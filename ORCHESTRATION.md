# Orchestration

## Orchestrate local execution

- The chosen standard technology to manage building, running and testing Daml application is **make**.
- Each makefile must contain the following targets:
  - `stop`: Should stop all running nodes and services.
  - `clean`: Should clean all artifacts and temporary files generated on a previous execution.
  - `setup-env`: Should initiate local setup with needed env variables, configurations, etc (**Optional**).
  - `get-dependencies`: Should retrieve all dependencies that are external to current project (**Optional**).
  - `build`: Should build all artifacts for the current Daml project, leveraging the multi package approach.
  - `test`: Should run all application tests
  - `run`: Should start up the applications ledger, as well as any other step necessary to have a fully functioning
  local environment. For example, if ledger startup is independent from dar file upload and bootstrap scritps, all these
  steps can be their own targets called as dependency on `run`.
  - `all`: Should run same as build

### Starting local ledger

- If the recomended direnv extension is installed in VS Code, upon starting the UDE, direnv should automatically pickup
the variables contained in the [.envrc](.envrc) file. If it does not, run `direnv allow .` in the project's root and
reload VS Code.
- A local ledger can be initiated via one of two methods:
  - Via Daml Cli, with `daml start` or `daml sandbox`.
  - Via the canton binary with `canton daemon -c <file.conf>`

  > **NOTE:** For unclear reasons, `JAVA_OPTS` env variable is not applied unless canton binary is used. This is
  relevant, as an example, to change logging configuration for the node in question. For more details on how to accomplish this, check this [daml discuss](https://discuss.daml.com/t/using-a-custom-logback-xml-for-the-sandbox/154/7) page.

- Within the `run` target of the project's makefile one of the above methods must be chosen to start the local ledger.
  - For projects with multiple artifacts, they will still need to be deployed to the ledger, since the `daml sandbox`
  and the canton binary commands don't include this step and the `daml start` command will only deploy the dar file
  compiled for the project where "current" daml.yaml file points to.
