# IntellectEU Styleguide Daml

## I. Project description

This project aims to define standards for Daml development and local project setup.

- Navigate to the [styleguide](STYLEGUIDE.md) document for details on starting and developing in a Daml project.
- Navigate to the [orchestration](ORCHESTRATION.md) guide for more information on building and running local
applications.

**NOTE:** The `.gitkeep` files are a workaround to track wanted directories.

## II. Project dependencies

The technologies used to develop this system were:

The tech requisites for running the this service are:

- [**DAML SDK**](https://docs.daml.com/getting-started/installation.html)
- [**Canton**](https://docs.daml.com/canton/usermanual/downloading.html)
- [**Make**](https://www.gnu.org/software/make/manual/make.html)
- [**Direnv**](https://direnv.net/)

**NOTE:** The Daml version of the current project is 2.9.3 and can be changed [here](.envrc)

## III. Running application

### III.1. Installing canton

To install canton on the local machine, retrieve the tarball from the download page in
[Project Dependencies](#ii-project-dependencies), choose open-source or enterprise version according to needs and
extract it to a convenient folder. The binary's path should look something like so (for open-souce version)
`<chosen-path>/canton-open-source-<canton-version>/bin`.

Set canton's binary to the `$PATH` environment variable in the machine's selected configuration file (`.bashrc`,
`.zshrc`, etc...), like in the example below:

```shell
CANTON_VERSION="2.8.5"
CANTON_HOME="$HOME/.canton/canton-open-source-$CANTON_VERSION"
export PATH="$CANTON_HOME/bin:$PATH"
```

### III.2. Starting application

The application can be started locally, leveraging the existing makefile targets and for a complete startup, execute
`make all`.
