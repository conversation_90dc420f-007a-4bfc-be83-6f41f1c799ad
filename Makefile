ROOT_DIR := $(shell git rev-parse --show-toplevel)

LOG_DIR := $(ROOT_DIR)/log
PACKAGE_DIR := $(ROOT_DIR)/package
LIB_DIR := $(PACKAGE_DIR)/lib
TEST_DIR := $(PACKAGE_DIR)/test
CONFIG_DIR := $(ROOT_DIR)/config
SCRIPT_DIR := $(CONFIG_DIR)/scripts
CANTON_DIR := $(CONFIG_DIR)/canton
EXTERNAL_DIR := $(ROOT_DIR)/ext-pkg
BUILD_DIR := $(ROOT_DIR)/build
MIGRATION_DIR := $(PACKAGE_DIR)/migration

.PHONY: all
all: build

.PHONY: setup-env
setup-env:

.PHONY: build
build:
	@echo "Fetching dependencies for source code"
	@$(SCRIPT_DIR)/get_dependencies.sh "implementation"
	@echo "Building artifacts..."
	@daml build --all

.PHONY: clean
clean:
	@echo "Cleaning artifacts and temporary files..."
	@echo "Clean all artifacts"
	@daml clean --all
	@cd $(MIGRATION_DIR) && daml clean --all
	@find $(BUILD_DIR) -name "*.dar" -type f -delete
	@find $(EXTERNAL_DIR) -name "*.dar" -type f -delete
	@find $(LOG_DIR) -name "*.log" -type f -delete

build-test: build
	@cd $(TEST_DIR) && daml build --all

.PHONY: test
test: build-test
	@echo "Running tests..."
	@find $(TEST_DIR) -iname 'daml.yaml' -execdir daml test --show-coverage --color \;

.PHONY: run
run: stop build start-canton upload-artifacts

.PHONY: stop
stop:
	@echo "Stopping application ledger and services..."
	@kill $$(lsof -i -P -n | grep 5001 | awk '{print $$2}')
	@kill $$(lsof -i -P -n | grep 5011 | awk '{print $$2}')

.PHONY: start-canton
start-canton:
	@echo "Starting Canton..."

	@echo "Starting Domain node..."
	@cd $(SCRIPT_DIR) && \
		./start_domain_node.sh $(CANTON_DIR)/canton-logback.xml $(CANTON_DIR)/domain.conf

	@echo "Starting Participant node..."
	@cd $(SCRIPT_DIR) && \
		./start_participant_node.sh $(CANTON_DIR)/canton-logback.xml $(CANTON_DIR)/participant.conf \
		$(CANTON_DIR)/bootstrap_participant.canton 7001 "domain@http://localhost:5011"

.PHONY: upload-artifacts
upload-artifacts:
	@find $(BUILD_DIR) -iname '*.dar' -exec daml ledger upload-dar () --host localhost --port 5001 \;

start-navigator:
	@echo "Starting Navigator UI..."
	@daml navigator server --port 4011 localhost 5001 --feature-user-management false >> $(LOG_DIR)/navigator.log &

get-migration-dependencies:
	@$(SCRIPT_DIR)/create_migration_folder.sh
	@echo "Fetching dependencies for migration"
	@$(SCRIPT_DIR)/get_dependencies.sh "migration"
	@echo "Building local dependencies for migration"
	$(MAKE) build

build-migration: get-migration-dependencies
	@echo "Compiling migration code"
	@cd $(MIGRATION_DIR) && \
		daml build --all

test-migration: build-migration
	@echo "Running migration tests..."
	@find $(MIGRATION_DIR) -iname 'daml.yaml' -execdir daml test --show-coverage --color \;