{
  "daml.multiPackageIdeSupport": true,
  "files.associations": {
    "*.daml": "daml",
  },
  "editor.rulers": [
    120
  ],
  "[daml]": {
    "editor.rulers": [
      100
    ],
    "editor.tabSize": 2,
  },
  "extensions.ignoreRecommendations": false,
  "files.readonlyInclude": {
    "**/.daml/unpacked-dars/**": true
  },
  "files.autoSave": "afterDelay",
    "files.autoSaveDelay": 100,
    "todo-tree.regex.regex": "(--|//|#|<!--|;|/\\*|^|^[ \\t]*(-|\\d+.))\\s*($TAGS)",
    "todo-tree.filtering.excludeGlobs": [
        "**/da-marketplace", 
        "**/*.hie"
    ]
}