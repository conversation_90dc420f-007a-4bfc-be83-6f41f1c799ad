{
	"recommendations": [
    "DigitalAssetHoldingsLLC.daml", /* Daml editing and analysis tools */
    "eamodio.gitlens", /* For useful features like in IDE git blame */
		"Gruntfuggly.todo-tree", /* Scan for all TODO, FIXME, etc annotations in code */
    "mohsen1.prettify-json", /* Prettify json files */
    "Tyriar.sort-lines", /* Sort code lines (Useful for sorting imports alphabetically) */
    "dandric.vscode-jq", /* Test jq statements directly in local json files */
    "redhat.vscode-yaml", /* Yaml language support */
    "PKief.material-icon-theme", /* Change file icons on Explorer */
    "mkhl.direnv" /* Automatically bring env variables into IDE context */
	]
}