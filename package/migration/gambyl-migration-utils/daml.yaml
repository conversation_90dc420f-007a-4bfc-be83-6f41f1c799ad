# for config file options, refer to
# https://docs.daml.com/tools/assistant.html#project-config-file-daml-yaml

sdk-version: 2.9.4
name: gambyl-migration-utils
source: daml
init-script:
parties:
  - Operator
version: ${GAMBYL_MIGRATION_UTILS_VERSION}
dependencies:
  - daml-prim
  - daml-stdlib
  - daml-script
data-dependencies:
  # External Dependencies
  # - ../../../ext-pkg/da-marketplace-${DA_MARKETPLACE_CURRENT_VERSION}.dar
  - ../../../ext-pkg/da-marketplace/${DA_MARKETPLACE_CURRENT_VERSION}/da-marketplace-${DA_MARKETPLACE_CURRENT_VERSION}.dar
sandbox-options:
  - --wall-clock-time
build-options:
  - --ghc-option=-Wall
  - --ghc-option=-Wno-name-shadowing
  - --ghc-option=-fno-warn-orphans
  - --ghc-option=-Wno-unused-do-bind
  - --ghc-option=-Werror
  - --output=../../../build/migration/gambyl-migration-utils-${GAMBYL_MIGRATION_UTILS_VERSION}.dar

