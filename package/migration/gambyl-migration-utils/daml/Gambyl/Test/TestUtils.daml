module Gambyl.Test.TestUtils where

import Daml.Script
import Gambyl.Migration.Common

import DA.Action                                                (when)
import DA.Either                                                (partitionEithers)
import DA.Finance.Asset
import DA.Finance.Types
import DA.Set qualified as Set

import ContingentClaims.Claim.Serializable qualified as Serialized

import Marketplace.Issuance.AssetDescription qualified as AssetDescription
import Marketplace.Issuance.CFI qualified as CFI

data Report = Report with {
  contractType : Text,
  errorCount : Int,
  successCount : Int,
  warnsCount : Int
} deriving Show

data ReportSummary = ReportSummary with {
  contracts : Int,
  errorCount : Int,
  successCount : Int,
  warnsCount : Int,
  reports : [Report]
} deriving Show

checkReport : ReportSummary -> Script ()
checkReport r@ReportSummary{errorCount, warnsCount} = do
  when (errorCount /= 0) $ do
    error "Errors found"
    debug r
  when (warnsCount /= 0) $ do
    error "Warnings found"
    debug r

combineReport : ReportSummary -> Report -> ReportSummary
combineReport
  ReportSummary{contracts = c1, errorCount = e1, successCount = s1, warnsCount = w1, reports = r1}
  r@Report{errorCount = e2, successCount = s2, warnsCount = w2} =
    ReportSummary{contracts = c1 + 1, errorCount = e1 + e2, successCount = s1 + s2, warnsCount = w1 + w2, reports = r :: r1}

collectReport : Text -> Script [MigrationResult (ContractId i)] -> Script Report
collectReport contractType actions = do
  results <- actions
  let (errors, successes) = partitionEithers results
      warnsCount = if null successes then 1 else 0
  pure (Report contractType (length errors) (length successes) warnsCount)

{-- HELPERS --}
data Target = Bets | Orders | Assets deriving (Show, Eq)

template IDGenerator with
    testDriver: Party
    bets: Text
    betsNbr: Int
    orders: Text
    ordersNbr: Int
    assets: Text
    assetsNbr: Int
  where
    signatory testDriver
    key testDriver: Party
    maintainer key

    choice Generate : Text
      with
        target: Target
      controller testDriver
      do
        case target of
          Bets -> do
            let nextNbr = betsNbr + 1
                next = "bet-" <> show nextNbr
            create this with
              bets = next
              betsNbr = nextNbr
              orders = orders
              ordersNbr = ordersNbr
              assets = assets
              assetsNbr = assetsNbr
            pure next
          Orders -> do
            let nextNbr = ordersNbr + 1
                next = "order-" <> show nextNbr
            create this with
              bets = bets
              betsNbr = betsNbr
              orders = next
              ordersNbr = nextNbr
              assets = assets
              assetsNbr = assetsNbr
            pure next
          Assets -> do
            let nextNbr = assetsNbr + 1
                next = "asset-" <> show nextNbr
            create this with
              bets = bets
              betsNbr = betsNbr
              orders = orders
              ordersNbr = ordersNbr
              assets = next
              assetsNbr = nextNbr
            pure next
            pure next

data GeneratorHandle = GeneratorHandle with
  generate : Target -> Script Text

initGenerator : Party -> Script GeneratorHandle
initGenerator testDriver = do
    submit testDriver $
      createCmd IDGenerator with
        testDriver
        bets = "0"
        betsNbr = 0
        orders = "0"
        ordersNbr = 0
        assets = "0"
        assetsNbr = 0
    pure GeneratorHandle with
      generate = \target ->
        submit testDriver $ exerciseByKeyCmd @IDGenerator testDriver $ Generate with target

prepareAssetDeposit : [Party] -> Script (ContractId AssetDeposit)
prepareAssetDeposit parties = script do
  let
    [operator, provider, customer, other] = parties
    observers = Set.empty

    accountId = Id with
        signatories = Set.fromList [other]
        label = "AccountLabel"
        version = 1
    account = Account with
      id = accountId
      provider = operator
      owner = customer
    assetId = Id with
      signatories = Set.fromList [provider]
      label = "AccountLabel"
      version = 1
    asset = Asset with
      id = assetId
      quantity = 10.0
    [_, _, customer, _] = parties
    lockers = Set.fromList [customer]
    signatories = Set.toList account.id.signatories <> Set.toList lockers

  submitMulti signatories [] $ do
    createCmd AssetDeposit with ..

prepareAssetDescription : [Party] -> Int -> Script (ContractId AssetDescription.AssetDescription)
prepareAssetDescription parties id = script do
  let
    [operator, provider, customer, _] = parties
    observers = Set.empty

    assetId = Id with
      signatories = Set.fromList [provider]
      label = "AssetLabel"
      version = id
    description = "description"
    cfi = CFI.CFI with code = "cfi code"
    issuer = operator
    -- TODO: prepare all types for Claim.Serialized enum
    claims = Serialized.Zero
    registrar = provider

  submitMulti [operator, provider, customer] [] $ do
    createCmd AssetDescription.AssetDescription with ..
