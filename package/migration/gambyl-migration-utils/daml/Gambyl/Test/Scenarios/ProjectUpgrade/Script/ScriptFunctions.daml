module Gambyl.Test.Scenarios.ProjectUpgrade.Script.ScriptFunctions where

import Daml.Script
import Gambyl.Migration.Common

import Gambyl.Test.Scenarios.ProjectUpgrade.Interface.Dummy qualified as DummyModel

upgradeIUpgradeDummy : Party -> [ContractId DummyModel.IUpgradeDummy] -> Script [MigrationResult (ContractId DummyModel.IUpgradeDummy)]
upgradeIUpgradeDummy migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid DummyModel.UpgradeDummy with ..
