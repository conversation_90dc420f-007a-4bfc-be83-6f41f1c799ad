module Gambyl.Test.Scenarios.ProjectUpgrade.Script.UpgradeScript where

import Daml.Script

import Gambyl.Migration.Common
import Gambyl.Test.TestUtils

import Gambyl.Test.Scenarios.ProjectUpgrade.Script.ScriptFunctions

upgradeScript : Input -> Script ReportSummary
upgradeScript Input{gambyl} = script do

  debug "START Upgrading project"

  r <- collectReport "Dummy" $ migrateDirectly "1-IUpgradeDummy" gambyl upgradeIUpgradeDummy

  debug "FINISH Upgrading project"

  let report = foldl combineReport (ReportSummary 0 0 0 0 []) [r]

  pure report