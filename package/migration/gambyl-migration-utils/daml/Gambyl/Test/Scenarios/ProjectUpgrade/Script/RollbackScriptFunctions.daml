module Gambyl.Test.Scenarios.ProjectUpgrade.Script.RollbackScriptFunctions where

import Daml.Script
import Gambyl.Migration.Common

import Gambyl.Test.Scenarios.ProjectUpgrade.Interface.DummyRollback qualified as DummyModel

upgradeIUpgradeDummyRollback : Party -> [ContractId DummyModel.IUpgradeDummyRollback] -> Script [MigrationResult (ContractId DummyModel.IUpgradeDummyRollback)]
upgradeIUpgradeDummyRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid DummyModel.UpgradeDummyRollback with ..
