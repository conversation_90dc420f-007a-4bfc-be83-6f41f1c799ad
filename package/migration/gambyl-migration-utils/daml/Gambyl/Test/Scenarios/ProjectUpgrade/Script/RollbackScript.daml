module Gambyl.Test.Scenarios.ProjectUpgrade.Script.RollbackScript where

import Daml.Script

import Gambyl.Migration.Common
import Gambyl.Test.TestUtils

import Gambyl.Test.Scenarios.ProjectUpgrade.Script.RollbackScriptFunctions

rollbackScript : Input -> Script ReportSummary
rollbackScript Input{gambyl} = script do

  debug "START RollingBack project"

  r <- collectReport "DummyRollback" $ migrateDirectly "1-IUpgradeDummyRollback" gambyl upgradeIUpgradeDummyRollback

  debug "FINISH RollingBack project"

  let report = foldl combineReport (ReportSummary 0 0 0 0 []) [r]

  pure report