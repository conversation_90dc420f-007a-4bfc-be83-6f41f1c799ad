module Gambyl.Test.Scenarios.ProjectUpgrade.Test.TestFunctionsRollback where

import Daml.Script

import Gambyl.Test.Scenarios.ProjectTo.Dummy qualified as ToProject

prepareIUpgradeDummy : [Party] -> <PERSON>ript (ContractId ToProject.Dummy)
prepareIUpgradeDummy parties = script do
  let
    [operator, _, _, _] = parties

    dummy = operator
    attr2 = "dummy"

  submitMulti [operator] [] $ do
    createCmd ToProject.Dummy  with ..

prepareLedger : Party -> Party -> Script ()
prepareLedger operator provider = script do
  [customer, other] <- mapA allocateParty ["alice", "bob"]

  let parties = [operator, provider, customer, other]

  prepareIUpgradeDummy parties

  pure ()