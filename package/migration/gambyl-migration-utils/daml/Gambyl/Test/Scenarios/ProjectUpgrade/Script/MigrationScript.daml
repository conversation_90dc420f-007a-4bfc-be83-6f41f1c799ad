module Gambyl.Test.Scenarios.ProjectUpgrade.Script.MigrationScript where

import Daml.Script

import Gambyl.Test.Scenarios.ProjectUpgrade.Test.TestFunctions qualified as Fixture
import Gambyl.Test.Scenarios.ProjectUpgrade.Test.TestFunctionsRollback qualified as RollbackFixture

import Gambyl.Test.Scenarios.ProjectUpgrade.Script.UpgradeScript qualified as Upgrade
import Gambyl.Test.Scenarios.ProjectUpgrade.Script.RollbackScript qualified as Rollback

import Gambyl.Migration.Common
import Gambyl.Test.TestUtils

migrationScript : Input -> Script ReportSummary
migrationScript Input{..} = logMigrationTimes do
  if rollback
  then Rollback.rollbackScript (Input{..})
  else Upgrade.upgradeScript (Input{..})

{--TEST--}
testMigration : Script ReportSummary
testMigration = script do
  gambyl <- allocateParty "gambyl"
  enetpulse <- allocateParty "enetpulse"
  moneymatrix <- allocateParty "moneymatrix"
  quickbooks <- allocateParty "quickbooks"
  exberry <- allocateParty "exberry"
  jumio <- allocateParty "jumio"

  let rollback = False

  Fixture.prepareLedger gambyl gambyl

  migrationScript (Input{..})

{--TEST--}
testRollback : Script ReportSummary
testRollback = script do
  gambyl <- allocateParty "gambyl"
  enetpulse <- allocateParty "enetpulse"
  moneymatrix <- allocateParty "moneymatrix"
  quickbooks <- allocateParty "quickbooks"
  exberry <- allocateParty "exberry"
  jumio <- allocateParty "jumio"

  let rollback = True

  RollbackFixture.prepareLedger gambyl gambyl

  migrationScript (Input{..})
