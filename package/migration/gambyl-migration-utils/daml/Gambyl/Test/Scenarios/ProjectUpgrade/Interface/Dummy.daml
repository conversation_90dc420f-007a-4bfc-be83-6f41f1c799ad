module Gambyl.Test.Scenarios.ProjectUpgrade.Interface.Dummy where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Test.Scenarios.ProjectUpgrade.Model.Dummy  ()

import Gambyl.Test.Scenarios.ProjectFrom.Dummy qualified as FromProject
import Gambyl.Test.Scenarios.ProjectTo.Dummy qualified as ToProject

interface IUpgradeDummy where
  viewtype ToProject.Dummy

  upgradeDummy : ContractId IUpgradeDummy -> Update (ContractId IUpgradeDummy)

  nonconsuming choice UpgradeDummy : MigrationResult (ContractId IUpgradeDummy)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeDummy contract") $
          migratingParty == (view this).dummy
        upgradeDummy this self

  interface instance IUpgradeDummy for FromProject.Dummy where
    view = convert this
    upgradeDummy self = do
      archive self
      toInterfaceContractId @IUpgradeDummy <$> create (convert this : ToProject.Dummy)

  interface instance IUpgradeDummy for ToProject.Dummy where
    view = this
    upgradeDummy = pure

