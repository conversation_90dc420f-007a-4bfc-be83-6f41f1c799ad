module Gambyl.Test.Scenarios.ProjectUpgrade.Test.TestFunctions where

import Daml.Script

import Gambyl.Test.Scenarios.ProjectFrom.Dummy qualified as FromProject

prepareIUpgradeDummy : [Party] -> <PERSON>ript (ContractId FromProject.Dummy)
prepareIUpgradeDummy parties = script do
  let
    [operator, _, _, _] = parties

    dummy = operator
    attr1 = "dummy"

  submitMulti [operator] [] $ do
    createCmd FromProject.Dummy  with ..

prepareLedger : Party -> Party -> Script ()
prepareLedger operator provider = script do
  [customer, other] <- mapA allocateParty ["alice", "bob"]

  let parties = [operator, provider, customer, other]

  prepareIUpgradeDummy parties

  pure ()