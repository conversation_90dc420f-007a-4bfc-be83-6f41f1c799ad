module Gambyl.Test.Scenarios.ProjectUpgrade.Interface.DummyRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Test.Scenarios.ProjectUpgrade.Model.DummyRollback  ()

import Gambyl.Test.Scenarios.ProjectFrom.Dummy qualified as FromProject
import Gambyl.Test.Scenarios.ProjectTo.Dummy qualified as ToProject

interface IUpgradeDummyRollback where
  viewtype FromProject.Dummy

  upgradeDummyRollback : ContractId IUpgradeDummyRollback -> Update (ContractId IUpgradeDummyRollback)

  nonconsuming choice UpgradeDummyRollback : MigrationResult (ContractId IUpgradeDummyRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeDummyRollback contract") $
          migratingParty == (view this).dummy
        upgradeDummyRollback this self

  interface instance IUpgradeDummyRollback for ToProject.Dummy where
    view = convert this
    upgradeDummyRollback self = do
      archive self
      toInterfaceContractId @IUpgradeDummyRollback <$> create (convert this : FromProject.Dummy)

  interface instance IUpgradeDummyRollback for FromProject.Dummy where
    view = this
    upgradeDummyRollback = pure

