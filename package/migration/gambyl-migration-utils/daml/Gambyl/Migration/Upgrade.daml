module Gambyl.Migration.Upgrade where

import DA.Map                                                (Map)
import DA.Map qualified as Map
import DA.Set                                                (Set)
import DA.Set qualified as Set

-- import FromEnetPulse.EnetPulseIntegration.Events qualified as IntegrationEvents

class DAMLUpgrade previous current where
  convert : previous -> current

instance DAMLUpgrade previous current => DAMLUpgrade [previous] [current] where
  convert = fmap convert

instance (Ord previous, Ord current, DAMLUpgrade previous current) => DAMLUpgrade (Set previous) (Set current) where
    convert xs = (Set.fromList . map convert . Set.toList) xs

instance DAMLUpgrade previous current => DAMLUpgrade (Optional previous) (Optional current) where
  convert = fmap convert

instance (Ord k, Ord k', DAMLUpgrade k k', DAMLUpgrade v v')
  => DAMLUpgrade (Map k v) (Map k' v') where
  convert oldMap = (Map.fromList . convert . Map.toList) oldMap

instance (DAMLUpgrade a a', DAMLUpgrade b b')
  => DAMLUpgrade (a, b) (a', b') where
  convert (a, b) = (convert a, convert b)

instance (DAMLUpgrade a a', DAMLUpgrade b b', DAMLUpgrade c c')
  => DAMLUpgrade (a, b, c) (a', b', c') where
  convert (a, b, c) = (convert a, convert b, convert c)

instance (DAMLUpgrade a a', DAMLUpgrade b b', DAMLUpgrade c c', DAMLUpgrade d d')
  => DAMLUpgrade (a, b, c, d) (a', b', c', d') where
  convert (a, b, c, d) = (convert a, convert b, convert c, convert d)

instance (DAMLUpgrade a a', DAMLUpgrade b b', DAMLUpgrade c c', DAMLUpgrade d d', DAMLUpgrade e e')
  => DAMLUpgrade (a, b, c, d, e) (a', b', c', d', e') where
  convert (a, b, c, d, e) = (convert a, convert b, convert c, convert d, convert e)

instance DAMLUpgrade Party Party where
  convert = identity

instance DAMLUpgrade Text Text where
  convert = identity

instance DAMLUpgrade Int Int where
  convert = identity

instance NumericScale n => DAMLUpgrade (Numeric n) (Numeric n) where
  convert = identity

instance DAMLUpgrade Bool Bool where
  convert = identity

instance DAMLUpgrade Date Date where
  convert = identity

instance DAMLUpgrade Time Time where
  convert = identity

instance (DAMLUpgrade previous current, HasArchive previous, HasCreate current, HasFetch previous) =>
  DAMLUpgrade (ContractId previous) (Update (ContractId current)) where
    convert cid = do
      previous <- fetch cid
      archive cid
      create (convert previous)

instance DAMLUpgrade previous (Update current) => DAMLUpgrade [previous] (Update [current]) where
  convert = mapA convert

-- -- FIXME: to be removed if enetpulse integration also gets migrated
-- -- | Necessary because Geography is a key in the map in the marketmap contract and map conversion lead to list conversion,
-- -- followed by tuple conversions, where a convert instance is needed
-- instance DAMLUpgrade IntegrationEvents.Geography IntegrationEvents.Geography where
--   convert = identity