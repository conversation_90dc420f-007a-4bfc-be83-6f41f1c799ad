module Gambyl.Migration.Common where

import Daml.Script

import DA.Action (unless, when)
import DA.Either qualified as Either
import DA.Exception qualified as Exception
import DA.List qualified as List
import DA.Text (unwords)
import DA.Time (subTime, convertRelTimeToMicroseconds)

data Input = Input with
  gambyl : Party
  enetpulse : Party
  moneymatrix : Party
  quickbooks : Party
  exberry : Party
  jumio : Party
  rollback : Bool

newtype MigrationError = MigrationError { reason : Text }

type MigrationResult a = Either MigrationError a

withErrorHandling : (Exception.ActionCatch m) => m i -> m (MigrationResult i)
withErrorHandling action =
  try do
    Right <$> action
  catch (ex : AnyException) -> do
        let errorMessage = Exception.message ex
        pure $ Left (MigrationError errorMessage)

migrateDirectly : forall i i' v. (Template i, HasInterfaceView i v) =>
  Text ->
  Party ->
  (Party -> [ContractId i] -> Script [MigrationResult (ContractId i')]) ->
  Script [MigrationResult (ContractId i')]
migrateDirectly contractType party f = script do
  cidListResult <- withErrorHandling $ do
          cidList <- map fst <$> queryInterface @i party
          pure cidList

  (migratedList, cidList) <- case cidListResult of
      Left err -> pure ([Left err], [])
      Right cidList -> (\r -> (r, cidList)) <$> (f party cidList)

  let (errors, success) = Either.partitionEithers migratedList

  unless (null errors) $ do
    debug ("[ERROR] errors reason: " <> show (reason $ List.head errors))
    debug ("[ERROR] errors count: " <> show (length errors))

  unless (null success) $ do
    let originCount = length cidList
        migratedCount = length success
    info contractType originCount migratedCount

  when (null success) $ warn contractType

  pure migratedList

  where
    info contractType count count' =
      debug $ unwords ["[INFO] Migrated", contractType, "contracts: from", show count, "to", show count']

    warn contractType =
      debug $ unwords ["[WARN] No contracts migrated for", contractType]

preMigrateDirectly : forall a b. (Template a, HasAgreement a) =>
  Party ->
  ([(ContractId a, a)] -> Script [MigrationResult b]) ->
  Script [MigrationResult b]
preMigrateDirectly party f = script do
  templates <- withErrorHandling $ query @a party
  (results, _) <- case templates of
      Left err -> pure ([Left err], [])
      Right ts -> (\r -> (r, ts)) <$> (f ts)
  pure results

logMigrationTimes : Script a -> Script a
logMigrationTimes migrationRunner = do
  startTime <- getTime
  debug $ "Upgrade START time: " <> show startTime

  result <- migrationRunner
  endTime <- getTime

  let
    timeDiff = subTime endTime startTime
    microsecondsDiff = convertRelTimeToMicroseconds timeDiff
    secondsDiff = microsecondsDiff / 1_000_000 -- Microseconds per second
    secsRan = show $ secondsDiff % 60 -- seconds per minute
    minutesDiff = secondsDiff / 60 -- seconds per minute
    minsRan = show $ minutesDiff % 60 -- Hours per minute
    hoursRan = show $ minutesDiff / 60 -- Hours per minute

  debug $ "Upgrade FINISH time: " <> show endTime <> ".\
    \ Ran for " <> hoursRan <> ":" <> minsRan <> ":" <> secsRan

  pure result

test_ : Script ()
test_ = do
  logMigrationTimes do
    pure ()
  pure ()