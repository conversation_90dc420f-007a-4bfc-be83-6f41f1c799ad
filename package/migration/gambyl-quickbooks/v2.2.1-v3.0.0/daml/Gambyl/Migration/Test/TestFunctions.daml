module Gambyl.Migration.Test.TestFunctions where

import Daml.Script

import Legacy.QuickbooksIntegration.Integration qualified as QuickbooksIntegrationTemplates
import Legacy.QuickbooksIntegration.Account.Model qualified as QuickbooksAccount
import Legacy.QuickbooksIntegration.Deposit.Model qualified as QuickbooksDeposit
import Legacy.QuickbooksIntegration.Transfer.Model qualified as QuickbooksTransfer
import Legacy.QuickbooksIntegration.Common qualified as QuickbooksCommon

{-- CONTRACTS --}
-- | 0-IUpgradeOAuthRequest
prepareIUpgradeOauthRequest : [Party] -> Script (ContractId QuickbooksIntegrationTemplates.OauthRequest)
prepareIUpgradeOauthRequest parties = script do
  let
    [operator, quickbooks, _, _] = parties
    integrationParty = quickbooks
    ledgerOperator = operator
    oauthUrl = "oauth url"

  submitMulti [quickbooks] [] $ do
    createCmd QuickbooksIntegrationTemplates.OauthRequest  with ..

-- | 1-IUpgradeConfiguration
prepareIUpgradeConfiguration : [Party] -> Script (ContractId QuickbooksIntegrationTemplates.Configuration)
prepareIUpgradeConfiguration parties = script do
  let
    [_, quickbooks, _, _] = parties
    integrationParty = quickbooks
    observers = []

  submitMulti [quickbooks] [] $ do
    createCmd QuickbooksIntegrationTemplates.Configuration  with ..

-- | 2-IUpgradeAccount
prepareIUpgradeAccount : [Party] -> Script (ContractId QuickbooksAccount.Account)
prepareIUpgradeAccount parties = script do
  let
    [operator, quickbooks, customer, _] = parties
    integrationParty = quickbooks
    accountHolder = customer
    observers = [operator]
    currencyRef = QuickbooksAccount.CurrencyRef with
      name = "name"
      type_ = "type_"
      value = "value"
    account = QuickbooksAccount.AccountObject with
      fullyQualifiedName = "fullyQualifiedName"
      domain = "domain"
      name = "name"
      classification = None
      accountSubType = "accountSubType"
      currentBalanceWithSubAccounts = 5.0
      sparse = True
      metadata = None
      accountType = "accountType"
      currentBalance = 2.0
      active = True
      syncToken = "syncToken"
      id = "accountId"
      subAccount = False
      currencyRef

  submitMulti [quickbooks] [] $ do
    createCmd QuickbooksAccount.Account  with ..

-- | 3-IUpgradeAccountRequest
prepareIUpgradeAccountRequest : [Party] -> Script (ContractId QuickbooksAccount.CreateAccountRequest)
prepareIUpgradeAccountRequest parties = script do
  let
    [operator, quickbooks, customer, _] = parties
    integrationParty = quickbooks
    requestingParty = operator
    requestingFor = customer
    observers = []
    request = QuickbooksAccount.AccountRequestObject with
      name = "name"
      accountSubType = "accountSubType"
      accountType = "accountType"

  submitMulti [operator] [] $ do
    createCmd QuickbooksAccount.CreateAccountRequest  with ..

-- | 4-IUpgradeDepositRequest
prepareIUpgradeDepositRequest : [Party] -> Script (ContractId QuickbooksDeposit.DepositRequest)
prepareIUpgradeDepositRequest parties = script do
  let
    [operator, quickbooks, customer, _] = parties
    integrationParty = quickbooks
    requestingParty = operator
    requestingFor = customer
    observers = [operator]
    accountRef = QuickbooksCommon.AccountRef with
      name = "name"
      value = "value"
    depositLineDetail = QuickbooksDeposit.DepositLineDetail with
      accountRef
    line = QuickbooksDeposit.RequestLine with
      detailType = "DepositLineDetail"
      amount = 100.0
      depositLineDetail
    request = QuickbooksDeposit.RequestDepositObject with
      line = [line]
      depositToAccountRef = accountRef
    transactionId = "transaction-id"

  submitMulti [operator] [] $ do
    createCmd QuickbooksDeposit.DepositRequest with ..

-- | 5-IUpgradeTransferRequest
prepareIUpgradeTransferRequest : [Party] -> Script (ContractId QuickbooksTransfer.TransferRequest)
prepareIUpgradeTransferRequest parties = script do
  let
    [operator, quickbooks, customer, _] = parties
    integrationParty = quickbooks
    requestingParty = operator
    requestingFor = customer
    observers = [operator]
    accountRef = QuickbooksCommon.AccountRef with
      name = "name"
      value = "value"
    accountRef2 = QuickbooksCommon.AccountRef with
      name = "name2"
      value = "value2"
    request = QuickbooksTransfer.TransferRequestObject with
      amount = 20.0
      fromAccountRef = accountRef
      toAccountRef = accountRef2
    transactionId = "transaction-id"

  submitMulti [operator] [] $ do
    createCmd QuickbooksTransfer.TransferRequest with ..

-- | 6-IUpgradeAuthorization
prepareIUpgradeAuthorization : [Party] -> Script (ContractId QuickbooksIntegrationTemplates.Authorization)
prepareIUpgradeAuthorization parties = script do
  nowTime <- getTime

  let
    [_, quickbooks, _, _] = parties
    integrationParty = quickbooks
    authorizationCode = "authorizationCode"
    accessToken = "accessToken"
    accessTokenExpiresAt = nowTime
    refreshToken = "refreshToken"
    refreshTokenExpiresAt = nowTime

  submitMulti [quickbooks] [] $ do
    createCmd QuickbooksIntegrationTemplates.Authorization  with ..


prepareLedger : Party -> Party -> Script ()
prepareLedger operator quickbooks = script do
  [customer, other] <- mapA allocateParty ["alice", "bob"]

  let parties = [operator, quickbooks, customer, other]

  prepareIUpgradeOauthRequest parties
  prepareIUpgradeConfiguration parties
  prepareIUpgradeAccount parties
  prepareIUpgradeAccountRequest parties
  prepareIUpgradeDepositRequest parties
  prepareIUpgradeTransferRequest parties
  prepareIUpgradeAuthorization parties

  pure ()
