module Gambyl.Migration.Interface.QuickbooksIntegration.Account.ModelRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.QuickbooksIntegration.Account.ModelRollback ()

import Legacy.QuickbooksIntegration.Account.Model qualified as FromAccountModel
import Current.QuickbooksIntegration.Account.Model qualified as ToAccountModel

interface IUpgradeCreateAccountRequestRollback where
  viewtype FromAccountModel.CreateAccountRequest

  upgradeCreateAccountRequestRollback : ContractId IUpgradeCreateAccountRequestRollback -> Update (ContractId IUpgradeCreateAccountRequestRollback)

  nonconsuming choice UpgradeCreateAccountRequestRollback : MigrationResult (ContractId IUpgradeCreateAccountRequestRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeCreateAccountRequestRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeCreateAccountRequestRollback this self

  interface instance IUpgradeCreateAccountRequestRollback for ToAccountModel.CreateAccountRequest where
    view = convert this
    upgradeCreateAccountRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeCreateAccountRequestRollback <$> create (convert this : FromAccountModel.CreateAccountRequest)

  interface instance IUpgradeCreateAccountRequestRollback for FromAccountModel.CreateAccountRequest where
    view = this
    upgradeCreateAccountRequestRollback = pure

interface IUpgradeAccountRollback where
  viewtype FromAccountModel.Account

  upgradeAccountRollback : ContractId IUpgradeAccountRollback -> Update (ContractId IUpgradeAccountRollback)

  nonconsuming choice UpgradeAccountRollback : MigrationResult (ContractId IUpgradeAccountRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeAccountRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeAccountRollback this self

  interface instance IUpgradeAccountRollback for ToAccountModel.Account where
    view = convert this
    upgradeAccountRollback self = do
      archive self
      toInterfaceContractId @IUpgradeAccountRollback <$> create (convert this : FromAccountModel.Account)

  interface instance IUpgradeAccountRollback for FromAccountModel.Account where
    view = this
    upgradeAccountRollback = pure