module Gambyl.Migration.Interface.QuickbooksIntegration.Deposit.ModelRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.QuickbooksIntegration.Deposit.ModelRollback ()

import Legacy.QuickbooksIntegration.Deposit.Model qualified as FromDepositModel
import Current.QuickbooksIntegration.Deposit.Model qualified as ToDepositModel

interface IUpgradeDepositRequestRollback where
  viewtype FromDepositModel.DepositRequest

  upgradeDepositRequestRollback : ContractId IUpgradeDepositRequestRollback -> Update (ContractId IUpgradeDepositRequestRollback)

  nonconsuming choice UpgradeDepositRequestRollback : MigrationResult (ContractId IUpgradeDepositRequestRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeDepositRequestRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeDepositRequestRollback this self

  interface instance IUpgradeDepositRequestRollback for ToDepositModel.DepositRequest where
    view = convert this
    upgradeDepositRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeDepositRequestRollback <$> create (convert this : FromDepositModel.DepositRequest)

  interface instance IUpgradeDepositRequestRollback for FromDepositModel.DepositRequest where
    view = this
    upgradeDepositRequestRollback = pure