module Gambyl.Migration.Interface.QuickbooksIntegration.Transfer.Model where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.QuickbooksIntegration.Transfer.Model ()

import Legacy.QuickbooksIntegration.Transfer.Model qualified as FromTransferModel
import Current.QuickbooksIntegration.Transfer.Model qualified as ToTransferModel

interface IUpgradeTransferRequest where
  viewtype ToTransferModel.TransferRequest

  upgradeTransferRequest : ContractId IUpgradeTransferRequest -> Update (ContractId IUpgradeTransferRequest)

  nonconsuming choice UpgradeTransferRequest : MigrationResult (ContractId IUpgradeTransferRequest)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeTransferRequest contract") $
          migratingParty == (view this).integrationParty
        upgradeTransferRequest this self

  interface instance IUpgradeTransferRequest for FromTransferModel.TransferRequest where
    view = convert this
    upgradeTransferRequest self = do
      archive self
      toInterfaceContractId @IUpgradeTransferRequest <$> create (convert this : ToTransferModel.TransferRequest)

  interface instance IUpgradeTransferRequest for ToTransferModel.TransferRequest where
    view = this
    upgradeTransferRequest = pure