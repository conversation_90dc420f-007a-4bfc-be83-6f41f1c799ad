module Gambyl.Migration.Interface.QuickbooksIntegration.Account.Model where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.QuickbooksIntegration.Account.Model ()

import Legacy.QuickbooksIntegration.Account.Model qualified as FromAccountModel
import Current.QuickbooksIntegration.Account.Model qualified as ToAccountModel

interface IUpgradeCreateAccountRequest where
  viewtype ToAccountModel.CreateAccountRequest

  upgradeCreateAccountRequest : ContractId IUpgradeCreateAccountRequest -> Update (ContractId IUpgradeCreateAccountRequest)

  nonconsuming choice UpgradeCreateAccountRequest : MigrationResult (ContractId IUpgradeCreateAccountRequest)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeCreateAccountRequest contract") $
          migratingParty == (view this).integrationParty
        upgradeCreateAccountRequest this self

  interface instance IUpgradeCreateAccountRequest for FromAccountModel.CreateAccountRequest where
    view = convert this
    upgradeCreateAccountRequest self = do
      archive self
      toInterfaceContractId @IUpgradeCreateAccountRequest <$> create (convert this : ToAccountModel.CreateAccountRequest)

  interface instance IUpgradeCreateAccountRequest for ToAccountModel.CreateAccountRequest where
    view = this
    upgradeCreateAccountRequest = pure

interface IUpgradeAccount where
  viewtype ToAccountModel.Account

  upgradeAccount : ContractId IUpgradeAccount -> Update (ContractId IUpgradeAccount)

  nonconsuming choice UpgradeAccount : MigrationResult (ContractId IUpgradeAccount)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeAccount contract") $
          migratingParty == (view this).integrationParty
        upgradeAccount this self

  interface instance IUpgradeAccount for FromAccountModel.Account where
    view = convert this
    upgradeAccount self = do
      archive self
      toInterfaceContractId @IUpgradeAccount <$> create (convert this : ToAccountModel.Account)

  interface instance IUpgradeAccount for ToAccountModel.Account where
    view = this
    upgradeAccount = pure