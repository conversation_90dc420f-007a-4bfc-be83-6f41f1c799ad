module Gambyl.Migration.Interface.QuickbooksIntegration.Deposit.Model where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.QuickbooksIntegration.Deposit.Model ()

import Legacy.QuickbooksIntegration.Deposit.Model qualified as FromDepositModel
import Current.QuickbooksIntegration.Deposit.Model qualified as ToDepositModel

interface IUpgradeDepositRequest where
  viewtype ToDepositModel.DepositRequest

  upgradeDepositRequest : ContractId IUpgradeDepositRequest -> Update (ContractId IUpgradeDepositRequest)

  nonconsuming choice UpgradeDepositRequest : MigrationResult (ContractId IUpgradeDepositRequest)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeDepositRequest contract") $
          migratingParty == (view this).integrationParty
        upgradeDepositRequest this self

  interface instance IUpgradeDepositRequest for FromDepositModel.DepositRequest where
    view = convert this
    upgradeDepositRequest self = do
      archive self
      toInterfaceContractId @IUpgradeDepositRequest <$> create (convert this : ToDepositModel.DepositRequest)

  interface instance IUpgradeDepositRequest for ToDepositModel.DepositRequest where
    view = this
    upgradeDepositRequest = pure