module Gambyl.Migration.Interface.QuickbooksIntegration.IntegrationRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.QuickbooksIntegration.IntegrationRollback ()

import Legacy.QuickbooksIntegration.Integration qualified as FromQuickbooksIntegrationIntegration
import Current.QuickbooksIntegration.Integration qualified as ToQuickbooksIntegrationIntegration

interface IUpgradeOauthRequestRollback where
  viewtype FromQuickbooksIntegrationIntegration.OauthRequest

  upgradeOauthRequestRollback : ContractId IUpgradeOauthRequestRollback -> Update (ContractId IUpgradeOauthRequestRollback)

  nonconsuming choice UpgradeOauthRequestRollback : MigrationResult (ContractId IUpgradeOauthRequestRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeOauthRequestRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeOauthRequestRollback this self

  interface instance IUpgradeOauthRequestRollback for ToQuickbooksIntegrationIntegration.OauthRequest where
    view = convert this
    upgradeOauthRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeOauthRequestRollback <$> create (convert this : FromQuickbooksIntegrationIntegration.OauthRequest)

  interface instance IUpgradeOauthRequestRollback for FromQuickbooksIntegrationIntegration.OauthRequest where
    view = this
    upgradeOauthRequestRollback = pure

interface IUpgradeConfigurationRollback where
  viewtype FromQuickbooksIntegrationIntegration.Configuration

  upgradeConfigurationRollback : ContractId IUpgradeConfigurationRollback -> Update (ContractId IUpgradeConfigurationRollback)

  nonconsuming choice UpgradeConfigurationRollback : MigrationResult (ContractId IUpgradeConfigurationRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeConfigurationRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeConfigurationRollback this self

  interface instance IUpgradeConfigurationRollback for ToQuickbooksIntegrationIntegration.Configuration where
    view = convert this
    upgradeConfigurationRollback self = do
      archive self
      toInterfaceContractId @IUpgradeConfigurationRollback <$> create (convert this : FromQuickbooksIntegrationIntegration.Configuration)

  interface instance IUpgradeConfigurationRollback for FromQuickbooksIntegrationIntegration.Configuration where
    view = this
    upgradeConfigurationRollback = pure

interface IUpgradeAuthorizationRollback where
  viewtype FromQuickbooksIntegrationIntegration.Authorization

  upgradeAuthorizationRollback : ContractId IUpgradeAuthorizationRollback -> Update (ContractId IUpgradeAuthorizationRollback)

  nonconsuming choice UpgradeAuthorizationRollback : MigrationResult (ContractId IUpgradeAuthorizationRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeAuthorizationRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeAuthorizationRollback this self

  interface instance IUpgradeAuthorizationRollback for ToQuickbooksIntegrationIntegration.Authorization where
    view = convert this
    upgradeAuthorizationRollback self = do
      archive self
      toInterfaceContractId @IUpgradeAuthorizationRollback <$> create (convert this : FromQuickbooksIntegrationIntegration.Authorization)

  interface instance IUpgradeAuthorizationRollback for FromQuickbooksIntegrationIntegration.Authorization where
    view = this
    upgradeAuthorizationRollback = pure