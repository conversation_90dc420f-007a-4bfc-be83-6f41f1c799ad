module Gambyl.Migration.Interface.QuickbooksIntegration.Integration where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.QuickbooksIntegration.Integration ()

import Legacy.QuickbooksIntegration.Integration qualified as FromQuickbooksIntegrationIntegration
import Current.QuickbooksIntegration.Integration qualified as ToQuickbooksIntegrationIntegration

interface IUpgradeOAuthRequest where
  viewtype ToQuickbooksIntegrationIntegration.OauthRequest

  upgradeOauthRequest : ContractId IUpgradeOAuthRequest -> Update (ContractId IUpgradeOAuthRequest)

  nonconsuming choice UpgradeOauthRequest : MigrationResult (ContractId IUpgradeOAuthRequest)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeOAuthRequest contract") $
          migratingParty == (view this).integrationParty
        upgradeOauthRequest this self

  interface instance IUpgradeOAuthRequest for FromQuickbooksIntegrationIntegration.OauthRequest where
    view = convert this
    upgradeOauthRequest self = do
      archive self
      toInterfaceContractId @IUpgradeOAuthRequest <$> create (convert this : ToQuickbooksIntegrationIntegration.OauthRequest)

  interface instance IUpgradeOAuthRequest for ToQuickbooksIntegrationIntegration.OauthRequest where
    view = this
    upgradeOauthRequest = pure

interface IUpgradeConfiguration where
  viewtype ToQuickbooksIntegrationIntegration.Configuration

  upgradeConfiguration : ContractId IUpgradeConfiguration -> Update (ContractId IUpgradeConfiguration)

  nonconsuming choice UpgradeConfiguration : MigrationResult (ContractId IUpgradeConfiguration)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeConfiguration contract") $
          migratingParty == (view this).integrationParty
        upgradeConfiguration this self

  interface instance IUpgradeConfiguration for FromQuickbooksIntegrationIntegration.Configuration where
    view = convert this
    upgradeConfiguration self = do
      archive self
      toInterfaceContractId @IUpgradeConfiguration <$> create (convert this : ToQuickbooksIntegrationIntegration.Configuration)

  interface instance IUpgradeConfiguration for ToQuickbooksIntegrationIntegration.Configuration where
    view = this
    upgradeConfiguration = pure

interface IUpgradeAuthorization where
  viewtype ToQuickbooksIntegrationIntegration.Authorization

  upgradeAuthorization : ContractId IUpgradeAuthorization -> Update (ContractId IUpgradeAuthorization)

  nonconsuming choice UpgradeAuthorization : MigrationResult (ContractId IUpgradeAuthorization)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeAuthorization contract") $
          migratingParty == (view this).integrationParty
        upgradeAuthorization this self

  interface instance IUpgradeAuthorization for FromQuickbooksIntegrationIntegration.Authorization where
    view = convert this
    upgradeAuthorization self = do
      archive self
      toInterfaceContractId @IUpgradeAuthorization <$> create (convert this : ToQuickbooksIntegrationIntegration.Authorization)

  interface instance IUpgradeAuthorization for ToQuickbooksIntegrationIntegration.Authorization where
    view = this
    upgradeAuthorization = pure
