module Gambyl.Migration.Interface.QuickbooksIntegration.Transfer.ModelRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.QuickbooksIntegration.Transfer.ModelRollback ()

import Legacy.QuickbooksIntegration.Transfer.Model qualified as FromTransferModel
import Current.QuickbooksIntegration.Transfer.Model qualified as ToTransferModel

interface IUpgradeTransferRequestRollback where
  viewtype FromTransferModel.TransferRequest

  upgradeTransferRequestRollback : ContractId IUpgradeTransferRequestRollback -> Update (ContractId IUpgradeTransferRequestRollback)

  nonconsuming choice UpgradeTransferRequestRollback : MigrationResult (ContractId IUpgradeTransferRequestRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeTransferRequestRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeTransferRequestRollback this self

  interface instance IUpgradeTransferRequestRollback for ToTransferModel.TransferRequest where
    view = convert this
    upgradeTransferRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeTransferRequestRollback <$> create (convert this : FromTransferModel.TransferRequest)

  interface instance IUpgradeTransferRequestRollback for FromTransferModel.TransferRequest where
    view = this
    upgradeTransferRequestRollback = pure