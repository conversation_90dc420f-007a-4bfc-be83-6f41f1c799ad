module Gambyl.Migration.QuickbooksIntegration.Transfer.ModelRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.QuickbooksIntegration.CommonRollback ()

import Legacy.QuickbooksIntegration.Transfer.Model qualified as FromTransferModel
import Current.QuickbooksIntegration.Transfer.Model qualified as ToTransferModel

instance DAMLUpgrade ToTransferModel.TransferRequestObject FromTransferModel.TransferRequestObject where
  convert ToTransferModel.TransferRequestObject{..} = FromTransferModel.TransferRequestObject with
    fromAccountRef = convert fromAccountRef
    toAccountRef = convert toAccountRef
    ..
instance DAMLUpgrade ToTransferModel.Transfer FromTransferModel.Transfer where
  convert ToTransferModel.Transfer{..} = FromTransferModel.Transfer with
    toAccountRef = convert toAccountRef
    fromAccountRef = convert fromAccountRef
    metadata = convert metadata
    ..
instance DAMLUpgrade ToTransferModel.TransferRequest FromTransferModel.TransferRequest where
  convert ToTransferModel.TransferRequest{..} = FromTransferModel.TransferRequest with
    observers = convert observers
    request = convert request
    ..