module Gambyl.Migration.QuickbooksIntegration.Deposit.ModelRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.QuickbooksIntegration.CommonRollback ()

import Legacy.QuickbooksIntegration.Deposit.Model qualified as FromDepositModel
import Current.QuickbooksIntegration.Deposit.Model qualified as ToDepositModel

instance DAMLUpgrade ToDepositModel.RequestDepositObject FromDepositModel.RequestDepositObject where
  convert ToDepositModel.RequestDepositObject{..} = FromDepositModel.RequestDepositObject with
    depositToAccountRef = convert depositToAccountRef
    line = convert line
    ..
instance DAMLUpgrade ToDepositModel.RequestLine FromDepositModel.RequestLine where
  convert ToDepositModel.RequestLine{..} = FromDepositModel.RequestLine with
    depositLineDetail = convert depositLineDetail
    ..
instance DAMLUpgrade ToDepositModel.DepositLineDetail FromDepositModel.DepositLineDetail where
  convert ToDepositModel.DepositLineDetail{..} = FromDepositModel.DepositLineDetail with
    accountRef = convert accountRef
    ..
instance DAMLUpgrade ToDepositModel.DepositObject FromDepositModel.DepositObject where
  convert ToDepositModel.DepositObject{..} = FromDepositModel.DepositObject with
    depositToAccountRef = convert depositToAccountRef
    line = convert line
    metadata = convert metadata
    ..
instance DAMLUpgrade ToDepositModel.Line FromDepositModel.Line where
  convert ToDepositModel.Line{..} = FromDepositModel.Line with
    depositLineDetail = convert depositLineDetail
    linkedTxn = convert linkedTxn
    ..
instance DAMLUpgrade ToDepositModel.Txn FromDepositModel.Txn where
  convert ToDepositModel.Txn{..} = FromDepositModel.Txn with
    ..
instance DAMLUpgrade ToDepositModel.DepositRequest FromDepositModel.DepositRequest where
  convert ToDepositModel.DepositRequest{..} = FromDepositModel.DepositRequest with
    observers = convert observers
    request = convert request
    ..