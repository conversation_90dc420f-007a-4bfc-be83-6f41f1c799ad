module Gambyl.Migration.QuickbooksIntegration.Integration where

import Gambyl.Migration.Upgrade

import Legacy.QuickbooksIntegration.Integration qualified as FromQuickbooksIntegrationIntegration
import Current.QuickbooksIntegration.Integration qualified as ToQuickbooksIntegrationIntegration

instance DAMLUpgrade FromQuickbooksIntegrationIntegration.OauthRequest ToQuickbooksIntegrationIntegration.OauthRequest where
  convert FromQuickbooksIntegrationIntegration.OauthRequest{..} = ToQuickbooksIntegrationIntegration.OauthRequest with
    ..
instance DAMLUpgrade FromQuickbooksIntegrationIntegration.Configuration ToQuickbooksIntegrationIntegration.Configuration where
  convert FromQuickbooksIntegrationIntegration.Configuration{..} = ToQuickbooksIntegrationIntegration.Configuration with
    observers = convert observers
    ..
instance DAMLUpgrade FromQuickbooksIntegrationIntegration.Authorization ToQuickbooksIntegrationIntegration.Authorization where
  convert FromQuickbooksIntegrationIntegration.Authorization{..} = ToQuickbooksIntegrationIntegration.Authorization with
    ..