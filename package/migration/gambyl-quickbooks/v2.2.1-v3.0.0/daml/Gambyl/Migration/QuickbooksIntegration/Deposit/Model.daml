module Gambyl.Migration.QuickbooksIntegration.Deposit.Model where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.QuickbooksIntegration.Common ()

import Legacy.QuickbooksIntegration.Deposit.Model qualified as FromDepositModel
import Current.QuickbooksIntegration.Deposit.Model qualified as ToDepositModel

instance DAMLUpgrade FromDepositModel.RequestDepositObject ToDepositModel.RequestDepositObject where
  convert FromDepositModel.RequestDepositObject{..} = ToDepositModel.RequestDepositObject with
    depositToAccountRef = convert depositToAccountRef
    line = convert line
    ..
instance DAMLUpgrade FromDepositModel.RequestLine ToDepositModel.RequestLine where
  convert FromDepositModel.RequestLine{..} = ToDepositModel.RequestLine with
    depositLineDetail = convert depositLineDetail
    ..
instance DAMLUpgrade FromDepositModel.DepositLineDetail ToDepositModel.DepositLineDetail where
  convert FromDepositModel.DepositLineDetail{..} = ToDepositModel.DepositLineDetail with
    accountRef = convert accountRef
    ..
instance DAMLUpgrade FromDepositModel.DepositObject ToDepositModel.DepositObject where
  convert FromDepositModel.DepositObject{..} = ToDepositModel.DepositObject with
    depositToAccountRef = convert depositToAccountRef
    line = convert line
    metadata = convert metadata
    ..
instance DAMLUpgrade FromDepositModel.Line ToDepositModel.Line where
  convert FromDepositModel.Line{..} = ToDepositModel.Line with
    depositLineDetail = convert depositLineDetail
    linkedTxn = convert linkedTxn
    ..
instance DAMLUpgrade FromDepositModel.Txn ToDepositModel.Txn where
  convert FromDepositModel.Txn{..} = ToDepositModel.Txn with
    ..
instance DAMLUpgrade FromDepositModel.DepositRequest ToDepositModel.DepositRequest where
  convert FromDepositModel.DepositRequest{..} = ToDepositModel.DepositRequest with
    observers = convert observers
    request = convert request
    ..