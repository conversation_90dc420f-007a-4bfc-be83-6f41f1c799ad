module Gambyl.Migration.QuickbooksIntegration.IntegrationRollback where

import Gambyl.Migration.Upgrade

import Legacy.QuickbooksIntegration.Integration qualified as FromQuickbooksIntegrationIntegration
import Current.QuickbooksIntegration.Integration qualified as ToQuickbooksIntegrationIntegration

instance DAMLUpgrade ToQuickbooksIntegrationIntegration.OauthRequest FromQuickbooksIntegrationIntegration.OauthRequest where
  convert ToQuickbooksIntegrationIntegration.OauthRequest{..} = FromQuickbooksIntegrationIntegration.OauthRequest with
    ..
instance DAMLUpgrade ToQuickbooksIntegrationIntegration.Configuration FromQuickbooksIntegrationIntegration.Configuration where
  convert ToQuickbooksIntegrationIntegration.Configuration{..} = FromQuickbooksIntegrationIntegration.Configuration with
    observers = convert observers
    ..
instance DAMLUpgrade ToQuickbooksIntegrationIntegration.Authorization FromQuickbooksIntegrationIntegration.Authorization where
  convert ToQuickbooksIntegrationIntegration.Authorization{..} = FromQuickbooksIntegrationIntegration.Authorization with
    ..