module Gambyl.Migration.QuickbooksIntegration.Transfer.Model where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.QuickbooksIntegration.Common ()

import Legacy.QuickbooksIntegration.Transfer.Model qualified as FromTransferModel
import Current.QuickbooksIntegration.Transfer.Model qualified as ToTransferModel

instance DAMLUpgrade FromTransferModel.TransferRequestObject ToTransferModel.TransferRequestObject where
  convert FromTransferModel.TransferRequestObject{..} = ToTransferModel.TransferRequestObject with
    fromAccountRef = convert fromAccountRef
    toAccountRef = convert toAccountRef
    ..
instance DAMLUpgrade FromTransferModel.Transfer ToTransferModel.Transfer where
  convert FromTransferModel.Transfer{..} = ToTransferModel.Transfer with
    toAccountRef = convert toAccountRef
    fromAccountRef = convert fromAccountRef
    metadata = convert metadata
    ..
instance DAMLUpgrade FromTransferModel.TransferRequest ToTransferModel.TransferRequest where
  convert FromTransferModel.TransferRequest{..} = ToTransferModel.TransferRequest with
    observers = convert observers
    request = convert request
    ..