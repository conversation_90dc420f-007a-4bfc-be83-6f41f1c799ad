module Gambyl.Migration.QuickbooksIntegration.Payment where

import Gambyl.Migration.Upgrade

import Gambyl.Migration.QuickbooksIntegration.Common ()

import Legacy.QuickbooksIntegration.Payment qualified as FromQuickbooksIntegrationPayment
import Current.QuickbooksIntegration.Payment qualified as ToQuickbooksIntegrationPayment

instance DAMLUpgrade FromQuickbooksIntegrationPayment.PaymentObject ToQuickbooksIntegrationPayment.PaymentObject where
  convert FromQuickbooksIntegrationPayment.PaymentObject{..} = ToQuickbooksIntegrationPayment.PaymentObject with
    payment = convert payment
    time = convert time
    ..
instance DAMLUpgrade FromQuickbooksIntegrationPayment.Payment ToQuickbooksIntegrationPayment.Payment where
  convert FromQuickbooksIntegrationPayment.Payment{..} = ToQuickbooksIntegrationPayment.Payment with
    depositToAccountRef = convert depositToAccountRef
    line = convert line
    metadata = convert metadata
    ..
instance DAMLUpgrade FromQuickbooksIntegrationPayment.Line ToQuickbooksIntegrationPayment.Line where
  convert FromQuickbooksIntegrationPayment.Line{..} = ToQuickbooksIntegrationPayment.Line with
    lineEx = convert lineEx
    linkedTxn = convert linkedTxn
    ..
instance DAMLUpgrade FromQuickbooksIntegrationPayment.LineEx ToQuickbooksIntegrationPayment.LineEx where
  convert FromQuickbooksIntegrationPayment.LineEx{..} = ToQuickbooksIntegrationPayment.LineEx with
    any = convert any
    ..
instance DAMLUpgrade FromQuickbooksIntegrationPayment.AnyObject ToQuickbooksIntegrationPayment.AnyObject where
  convert FromQuickbooksIntegrationPayment.AnyObject{..} = ToQuickbooksIntegrationPayment.AnyObject with
    value = convert value
    typeSubstituted = convert typeSubstituted
    ..
instance DAMLUpgrade FromQuickbooksIntegrationPayment.ValueObject ToQuickbooksIntegrationPayment.ValueObject where
  convert FromQuickbooksIntegrationPayment.ValueObject{..} = ToQuickbooksIntegrationPayment.ValueObject with
    value = convert value
    ..
instance DAMLUpgrade FromQuickbooksIntegrationPayment.Txn ToQuickbooksIntegrationPayment.Txn where
  convert FromQuickbooksIntegrationPayment.Txn{..} = ToQuickbooksIntegrationPayment.Txn with
    ..
instance DAMLUpgrade FromQuickbooksIntegrationPayment.PaymentResponse ToQuickbooksIntegrationPayment.PaymentResponse where
  convert FromQuickbooksIntegrationPayment.PaymentResponse{..} = ToQuickbooksIntegrationPayment.PaymentResponse with
    payment = convert payment
    time = convert time
    ..