module Gambyl.Migration.QuickbooksIntegration.Common where

import Gambyl.Migration.Upgrade

import Legacy.QuickbooksIntegration.Common qualified as FromQuickbooksIntegrationCommon
import Current.QuickbooksIntegration.Common qualified as ToQuickbooksIntegrationCommon

instance DAMLUpgrade FromQuickbooksIntegrationCommon.Metadata ToQuickbooksIntegrationCommon.Metadata where
  convert FromQuickbooksIntegrationCommon.Metadata{..} = ToQuickbooksIntegrationCommon.Metadata with
    ..
instance DAMLUpgrade FromQuickbooksIntegrationCommon.AccountRef ToQuickbooksIntegrationCommon.AccountRef where
  convert FromQuickbooksIntegrationCommon.AccountRef{..} = ToQuickbooksIntegrationCommon.AccountRef with
    ..