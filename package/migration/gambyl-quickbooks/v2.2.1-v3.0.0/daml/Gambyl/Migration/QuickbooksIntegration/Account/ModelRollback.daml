module Gambyl.Migration.QuickbooksIntegration.Account.ModelRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.QuickbooksIntegration.CommonRollback ()

import Legacy.QuickbooksIntegration.Account.Model qualified as FromAccountModel
import Current.QuickbooksIntegration.Account.Model qualified as ToAccountModel

instance DAMLUpgrade ToAccountModel.AccountRequestObject FromAccountModel.AccountRequestObject where
  convert ToAccountModel.AccountRequestObject{..} = FromAccountModel.AccountRequestObject with
    ..
instance DAMLUpgrade ToAccountModel.AccountObject FromAccountModel.AccountObject where
  convert ToAccountModel.AccountObject{..} = FromAccountModel.AccountObject with
    classification = convert classification
    currencyRef = convert currencyRef
    metadata = convert metadata
    ..
instance DAMLUpgrade ToAccountModel.CurrencyRef FromAccountModel.CurrencyRef where
  convert ToAccountModel.CurrencyRef{..} = FromAccountModel.CurrencyRef with
    value = convert value
    ..
instance DAMLUpgrade ToAccountModel.CreateAccountRequest FromAccountModel.CreateAccountRequest where
  convert ToAccountModel.CreateAccountRequest{..} = FromAccountModel.CreateAccountRequest with
    observers = convert observers
    request = convert request
    ..
instance DAMLUpgrade ToAccountModel.Account FromAccountModel.Account where
  convert ToAccountModel.Account{..} = FromAccountModel.Account with
    observers = convert observers
    account = convert account
    ..