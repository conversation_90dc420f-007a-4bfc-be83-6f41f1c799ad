module Gambyl.Migration.QuickbooksIntegration.Account.Model where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.QuickbooksIntegration.Common ()

import Legacy.QuickbooksIntegration.Account.Model qualified as FromAccountModel
import Current.QuickbooksIntegration.Account.Model qualified as ToAccountModel

instance DAMLUpgrade FromAccountModel.AccountRequestObject ToAccountModel.AccountRequestObject where
  convert FromAccountModel.AccountRequestObject{..} = ToAccountModel.AccountRequestObject with
    ..
instance DAMLUpgrade FromAccountModel.AccountObject ToAccountModel.AccountObject where
  convert FromAccountModel.AccountObject{..} = ToAccountModel.AccountObject with
    classification = convert classification
    currencyRef = convert currencyRef
    metadata = convert metadata
    ..
instance DAMLUpgrade FromAccountModel.CurrencyRef ToAccountModel.CurrencyRef where
  convert FromAccountModel.CurrencyRef{..} = ToAccountModel.CurrencyRef with
    value = convert value
    ..
instance DAMLUpgrade FromAccountModel.CreateAccountRequest ToAccountModel.CreateAccountRequest where
  convert FromAccountModel.CreateAccountRequest{..} = ToAccountModel.CreateAccountRequest with
    observers = convert observers
    request = convert request
    ..
instance DAMLUpgrade FromAccountModel.Account ToAccountModel.Account where
  convert FromAccountModel.Account{..} = ToAccountModel.Account with
    observers = convert observers
    account = convert account
    ..