module Gambyl.Migration.QuickbooksIntegration.PaymentRollback where

import Gambyl.Migration.Upgrade

import Gambyl.Migration.QuickbooksIntegration.CommonRollback ()

import Legacy.QuickbooksIntegration.Payment qualified as FromQuickbooksIntegrationPayment
import Current.QuickbooksIntegration.Payment qualified as ToQuickbooksIntegrationPayment

instance DAMLUpgrade ToQuickbooksIntegrationPayment.PaymentObject FromQuickbooksIntegrationPayment.PaymentObject where
  convert ToQuickbooksIntegrationPayment.PaymentObject{..} = FromQuickbooksIntegrationPayment.PaymentObject with
    payment = convert payment
    time = convert time
    ..
instance DAMLUpgrade ToQuickbooksIntegrationPayment.Payment FromQuickbooksIntegrationPayment.Payment where
  convert ToQuickbooksIntegrationPayment.Payment{..} = FromQuickbooksIntegrationPayment.Payment with
    depositToAccountRef = convert depositToAccountRef
    line = convert line
    metadata = convert metadata
    ..
instance DAMLUpgrade ToQuickbooksIntegrationPayment.Line FromQuickbooksIntegrationPayment.Line where
  convert ToQuickbooksIntegrationPayment.Line{..} = FromQuickbooksIntegrationPayment.Line with
    lineEx = convert lineEx
    linkedTxn = convert linkedTxn
    ..
instance DAMLUpgrade ToQuickbooksIntegrationPayment.LineEx FromQuickbooksIntegrationPayment.LineEx where
  convert ToQuickbooksIntegrationPayment.LineEx{..} = FromQuickbooksIntegrationPayment.LineEx with
    any = convert any
    ..
instance DAMLUpgrade ToQuickbooksIntegrationPayment.AnyObject FromQuickbooksIntegrationPayment.AnyObject where
  convert ToQuickbooksIntegrationPayment.AnyObject{..} = FromQuickbooksIntegrationPayment.AnyObject with
    value = convert value
    typeSubstituted = convert typeSubstituted
    ..
instance DAMLUpgrade ToQuickbooksIntegrationPayment.ValueObject FromQuickbooksIntegrationPayment.ValueObject where
  convert ToQuickbooksIntegrationPayment.ValueObject{..} = FromQuickbooksIntegrationPayment.ValueObject with
    value = convert value
    ..
instance DAMLUpgrade ToQuickbooksIntegrationPayment.Txn FromQuickbooksIntegrationPayment.Txn where
  convert ToQuickbooksIntegrationPayment.Txn{..} = FromQuickbooksIntegrationPayment.Txn with
    ..
instance DAMLUpgrade ToQuickbooksIntegrationPayment.PaymentResponse FromQuickbooksIntegrationPayment.PaymentResponse where
  convert ToQuickbooksIntegrationPayment.PaymentResponse{..} = FromQuickbooksIntegrationPayment.PaymentResponse with
    payment = convert payment
    time = convert time
    ..