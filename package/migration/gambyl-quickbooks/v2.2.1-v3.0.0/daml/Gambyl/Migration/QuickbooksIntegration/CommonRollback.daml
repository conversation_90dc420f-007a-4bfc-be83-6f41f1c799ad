module Gambyl.Migration.QuickbooksIntegration.CommonRollback where

import Gambyl.Migration.Upgrade

import Legacy.QuickbooksIntegration.Common qualified as FromQuickbooksIntegrationCommon
import Current.QuickbooksIntegration.Common qualified as ToQuickbooksIntegrationCommon

instance DAMLUpgrade ToQuickbooksIntegrationCommon.Metadata FromQuickbooksIntegrationCommon.Metadata where
  convert ToQuickbooksIntegrationCommon.Metadata{..} = FromQuickbooksIntegrationCommon.Metadata with
    ..
instance DAMLUpgrade ToQuickbooksIntegrationCommon.AccountRef FromQuickbooksIntegrationCommon.AccountRef where
  convert ToQuickbooksIntegrationCommon.AccountRef{..} = FromQuickbooksIntegrationCommon.AccountRef with
    ..