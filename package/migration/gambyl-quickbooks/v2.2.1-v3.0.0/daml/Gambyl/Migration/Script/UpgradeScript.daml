module Gambyl.Migration.Script.UpgradeScript where

import Daml.Script

import Gambyl.Migration.Script.ScriptFunctions

import Gambyl.Migration.Common
import Gambyl.Test.TestUtils

upgradeScript : Input -> Script ReportSummary
upgradeScript Input{quickbooks} = script do

  debug "START Upgrading quickbooks"
  r1 <- collectReport "OAuthRequest" $ migrateDirectly "0-IUpgradeOAuthRequest" quickbooks upgradeListOfIUpgradeOauthRequest
  r2 <- collectReport "Configuration" $ migrateDirectly "1-IUpgradeConfiguration" quickbooks upgradeListOfIUpgradeConfiguration
  r3 <- collectReport "Account" $ migrateDirectly "2-IUpgradeAccount" quickbooks upgradeListOfIUpgradeAccount
  r4 <- collectReport "AccountRequest" $ migrateDirectly "3-IUpgradeAccountRequest" quickbooks upgradeListOfIUpgradeCreateAccountRequest
  r5 <- collectReport "DepositRequest" $ migrateDirectly "4-IUpgradeDepositRequest" quickbooks upgradeListOfIUpgradeDepositRequest
  r6 <- collectReport "TransferRequest" $ migrateDirectly "5-IUpgradeTransferRequest" quickbooks upgradeListOfIUpgradeTransferRequest
  r7 <- collectReport "Authorization" $ migrateDirectly "6-IUpgradeAuthorization" quickbooks upgradeListOfIUpgradeAuthorization
  debug "FINISH Upgrading quickbooks"

  let report = foldl combineReport (ReportSummary 0 0 0 0 []) [
                                      r1, r2, r3, r4
                                      , r5, r6, r7]

  pure report

