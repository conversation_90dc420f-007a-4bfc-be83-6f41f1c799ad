module Gambyl.Migration.Script.RollbackScriptFunctions where

import Dam<PERSON>.Script
import Gambyl.Migration.Common

import Gambyl.Migration.Interface.QuickbooksIntegration.Transfer.ModelRollback
import Gambyl.Migration.Interface.QuickbooksIntegration.Account.ModelRollback
import Gambyl.Migration.Interface.QuickbooksIntegration.IntegrationRollback
import Gambyl.Migration.Interface.QuickbooksIntegration.Deposit.ModelRollback

upgradeListOfIUpgradeOauthRequestRollback : Party -> [ContractId IUpgradeOauthRequestRollback] -> Script [MigrationResult (ContractId IUpgradeOauthRequestRollback)]
upgradeListOfIUpgradeOauthRequestRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeOauthRequestRollback with ..

upgradeListOfIUpgradeConfigurationRollback : Party -> [ContractId IUpgradeConfigurationRollback] -> Script [MigrationResult (ContractId IUpgradeConfigurationRollback)]
upgradeListOfIUpgradeConfigurationRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeConfigurationRollback with ..

upgradeListOfIUpgradeDepositRequestRollback : Party -> [ContractId IUpgradeDepositRequestRollback] -> Script [MigrationResult (ContractId IUpgradeDepositRequestRollback)]
upgradeListOfIUpgradeDepositRequestRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeDepositRequestRollback with ..

upgradeListOfIUpgradeTransferRequestRollback : Party -> [ContractId IUpgradeTransferRequestRollback] -> Script [MigrationResult (ContractId IUpgradeTransferRequestRollback)]
upgradeListOfIUpgradeTransferRequestRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeTransferRequestRollback with ..

upgradeListOfIUpgradeCreateAccountRequestRollback : Party -> [ContractId IUpgradeCreateAccountRequestRollback] -> Script [MigrationResult (ContractId IUpgradeCreateAccountRequestRollback)]
upgradeListOfIUpgradeCreateAccountRequestRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeCreateAccountRequestRollback with ..

upgradeListOfIUpgradeAccountRollback : Party -> [ContractId IUpgradeAccountRollback] -> Script [MigrationResult (ContractId IUpgradeAccountRollback)]
upgradeListOfIUpgradeAccountRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeAccountRollback with ..

upgradeListOfIUpgradeAuthorizationRollback : Party -> [ContractId IUpgradeAuthorizationRollback] -> Script [MigrationResult (ContractId IUpgradeAuthorizationRollback)]
upgradeListOfIUpgradeAuthorizationRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeAuthorizationRollback with ..