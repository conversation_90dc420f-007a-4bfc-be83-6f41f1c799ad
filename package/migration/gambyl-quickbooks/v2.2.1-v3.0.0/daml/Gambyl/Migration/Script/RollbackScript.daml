module Gambyl.Migration.Script.RollbackScript where

import Daml.Script

import Gambyl.Migration.Script.RollbackScriptFunctions

import Gambyl.Migration.Common
import Gambyl.Test.TestUtils

rollbackScript : Input -> Script ReportSummary
rollbackScript Input{quickbooks} = script do

  debug "START RollingBack quickbooks"
  r1 <- collectReport "OAuthRequestRollback" $ migrateDirectly "0-IUpgradeOAuthRequestRollback" quickbooks upgradeListOfIUpgradeOauthRequestRollback
  r2 <- collectReport "ConfigurationRollback" $ migrateDirectly "1-IUpgradeConfigurationRollback" quickbooks upgradeListOfIUpgradeConfigurationRollback
  r3 <- collectReport "AccountRollback" $ migrateDirectly "2-IUpgradeAccountRollback" quickbooks upgradeListOfIUpgradeAccountRollback
  r4 <- collectReport "AccountRequestRollback" $ migrateDirectly "3-IUpgradeAccountRequestRollback" quickbooks upgradeListOfIUpgradeCreateAccountRequestRollback
  r5 <- collectReport "DepositRequestRollback" $ migrateDirectly "4-IUpgradeDepositRequestRollback" quickbooks upgradeListOfIUpgradeDepositRequestRollback
  r6 <- collectReport "TransferRequestRollback" $ migrateDirectly "5-IUpgradeTransferRequestRollback" quickbooks upgradeListOfIUpgradeTransferRequestRollback
  r7 <- collectReport "AuthorizationRollback" $ migrateDirectly "6-IUpgradeAuthorizationRollback" quickbooks upgradeListOfIUpgradeAuthorizationRollback
  debug "FINISH RollingBack quickbooks"

  let report = foldl combineReport (ReportSummary 0 0 0 0 []) [r1, r2, r3, r4, r5, r6, r7]

  pure report

