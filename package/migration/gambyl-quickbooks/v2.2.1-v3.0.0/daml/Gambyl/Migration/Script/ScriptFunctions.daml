module Gambyl.Migration.Script.ScriptFunctions where

import Daml.Script
import Gambyl.Migration.Common

import Gambyl.Migration.Interface.QuickbooksIntegration.Transfer.Model
import Gambyl.Migration.Interface.QuickbooksIntegration.Account.Model
import Gambyl.Migration.Interface.QuickbooksIntegration.Integration
import Gambyl.Migration.Interface.QuickbooksIntegration.Deposit.Model

upgradeListOfIUpgradeOauthRequest : Party -> [ContractId IUpgradeOAuthRequest] -> Script [MigrationResult (ContractId IUpgradeOAuthRequest)]
upgradeListOfIUpgradeOauthRequest migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeOauthRequest with ..

upgradeListOfIUpgradeConfiguration : Party -> [ContractId IUpgradeConfiguration] -> Script [MigrationResult (ContractId IUpgradeConfiguration)]
upgradeListOfIUpgradeConfiguration migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeConfiguration with ..

upgradeListOfIUpgradeDepositRequest : Party -> [ContractId IUpgradeDepositRequest] -> Script [MigrationResult (ContractId IUpgradeDepositRequest)]
upgradeListOfIUpgradeDepositRequest migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeDepositRequest with ..

upgradeListOfIUpgradeTransferRequest : Party -> [ContractId IUpgradeTransferRequest] -> Script [MigrationResult (ContractId IUpgradeTransferRequest)]
upgradeListOfIUpgradeTransferRequest migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeTransferRequest with ..

upgradeListOfIUpgradeCreateAccountRequest : Party -> [ContractId IUpgradeCreateAccountRequest] -> Script [MigrationResult (ContractId IUpgradeCreateAccountRequest)]
upgradeListOfIUpgradeCreateAccountRequest migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeCreateAccountRequest with ..

upgradeListOfIUpgradeAccount : Party -> [ContractId IUpgradeAccount] -> Script [MigrationResult (ContractId IUpgradeAccount)]
upgradeListOfIUpgradeAccount migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeAccount with ..

upgradeListOfIUpgradeAuthorization : Party -> [ContractId IUpgradeAuthorization] -> Script [MigrationResult (ContractId IUpgradeAuthorization)]
upgradeListOfIUpgradeAuthorization migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeAuthorization with ..