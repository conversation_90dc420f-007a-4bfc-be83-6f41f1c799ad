module Gambyl.Migration.Interface.Jumio.Configuration where

import Gambyl.Migration.Common
import Gambyl.Migration.Upgrade
import Gambyl.Migration.Jumio.Configuration ()

import Legacy.JumioIntegration.Configuration qualified as LegacyConfiguration
import Current.JumioIntegration.Configuration qualified as CurrentConfiguration

interface IUpgradeConfiguration where
  viewtype CurrentConfiguration.Configuration

  upgradeConfiguration : ContractId IUpgradeConfiguration -> Update (ContractId IUpgradeConfiguration)

  nonconsuming choice UpgradeConfiguration : MigrationResult (ContractId IUpgradeConfiguration)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeConfiguration contract") $
          migratingParty == (view this).integrationParty
        upgradeConfiguration this self

  interface instance IUpgradeConfiguration for LegacyConfiguration.Configuration where
    view = convert this
    upgradeConfiguration self = do
      archive self
      toInterfaceContractId @IUpgradeConfiguration <$> create (convert this : CurrentConfiguration.Configuration)

  interface instance IUpgradeConfiguration for CurrentConfiguration.Configuration where
    view = this
    upgradeConfiguration = pure
