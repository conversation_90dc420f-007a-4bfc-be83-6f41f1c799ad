module Gambyl.Migration.Interface.Jumio.IdentityRollback where

import Gambyl.Migration.Common
import Gambyl.Migration.Upgrade
import Gambyl.Migration.Jumio.IdentityRollback ()

import Legacy.JumioIntegration.Identity qualified as LegacyIdentity
import Current.JumioIntegration.Identity qualified as CurrentIdentity

interface IUpgradeInitialIdentityVerificationRequestRollback where
  viewtype LegacyIdentity.InitialIdentityVerificationRequest

  upgradeInitialIdentityVerificationRequest : ContractId IUpgradeInitialIdentityVerificationRequestRollback -> Update (ContractId IUpgradeInitialIdentityVerificationRequestRollback)

  nonconsuming choice UpgradeInitialIdentityVerificationRequest : MigrationResult (ContractId IUpgradeInitialIdentityVerificationRequestRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeInitialIdentityVerificationRequestRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeInitialIdentityVerificationRequest this self

  interface instance IUpgradeInitialIdentityVerificationRequestRollback for CurrentIdentity.InitialIdentityVerificationRequest where
    view = convert this
    upgradeInitialIdentityVerificationRequest self = do
      archive self
      toInterfaceContractId @IUpgradeInitialIdentityVerificationRequestRollback <$> create (convert this : LegacyIdentity.InitialIdentityVerificationRequest)

  interface instance IUpgradeInitialIdentityVerificationRequestRollback for LegacyIdentity.InitialIdentityVerificationRequest where
    view = this
    upgradeInitialIdentityVerificationRequest = pure


interface IUpgradeIdentityVerificationRequestRollback where
  viewtype LegacyIdentity.IdentityVerificationRequest

  upgradeIdentityVerificationRequest : ContractId IUpgradeIdentityVerificationRequestRollback -> Update (ContractId IUpgradeIdentityVerificationRequestRollback)

  nonconsuming choice UpgradeIdentityVerificationRequest : MigrationResult (ContractId IUpgradeIdentityVerificationRequestRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeIdentityVerificationRequestRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeIdentityVerificationRequest this self

  interface instance IUpgradeIdentityVerificationRequestRollback for CurrentIdentity.IdentityVerificationRequest where
    view = convert this
    upgradeIdentityVerificationRequest self = do
      archive self
      toInterfaceContractId @IUpgradeIdentityVerificationRequestRollback <$> create (convert this : LegacyIdentity.IdentityVerificationRequest)

  interface instance IUpgradeIdentityVerificationRequestRollback for LegacyIdentity.IdentityVerificationRequest where
    view = this
    upgradeIdentityVerificationRequest = pure


interface IUpgradePendingIdentityRequestRollback where
  viewtype LegacyIdentity.PendingIdentityRequest

  upgradePendingIdentityRequest : ContractId IUpgradePendingIdentityRequestRollback -> Update (ContractId IUpgradePendingIdentityRequestRollback)

  nonconsuming choice UpgradePendingIdentityRequest : MigrationResult (ContractId IUpgradePendingIdentityRequestRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradePendingIdentityRequestRollback contract") $
          migratingParty == (view this).integrationParty
        upgradePendingIdentityRequest this self

  interface instance IUpgradePendingIdentityRequestRollback for CurrentIdentity.PendingIdentityRequest where
    view = convert this
    upgradePendingIdentityRequest self = do
      archive self
      toInterfaceContractId @IUpgradePendingIdentityRequestRollback <$> create (convert this : LegacyIdentity.PendingIdentityRequest)

  interface instance IUpgradePendingIdentityRequestRollback for LegacyIdentity.PendingIdentityRequest where
    view = this
    upgradePendingIdentityRequest = pure


interface IUpgradeVerifiedIdentityRollback where
  viewtype LegacyIdentity.VerifiedIdentity

  upgradeVerifiedIdentity : ContractId IUpgradeVerifiedIdentityRollback -> Update (ContractId IUpgradeVerifiedIdentityRollback)

  nonconsuming choice UpgradeVerifiedIdentity : MigrationResult (ContractId IUpgradeVerifiedIdentityRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeVerifiedIdentityRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeVerifiedIdentity this self

  interface instance IUpgradeVerifiedIdentityRollback for CurrentIdentity.VerifiedIdentity where
    view = convert this
    upgradeVerifiedIdentity self = do
      archive self
      toInterfaceContractId @IUpgradeVerifiedIdentityRollback <$> create (convert this : LegacyIdentity.VerifiedIdentity)

  interface instance IUpgradeVerifiedIdentityRollback for LegacyIdentity.VerifiedIdentity where
    view = this
    upgradeVerifiedIdentity = pure


interface IUpgradeRejectedIdentityRollback where
  viewtype LegacyIdentity.RejectedIdentity

  upgradeRejectedIdentity : ContractId IUpgradeRejectedIdentityRollback -> Update (ContractId IUpgradeRejectedIdentityRollback)

  nonconsuming choice UpgradeRejectedIdentity : MigrationResult (ContractId IUpgradeRejectedIdentityRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeRejectedIdentityRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeRejectedIdentity this self

  interface instance IUpgradeRejectedIdentityRollback for CurrentIdentity.RejectedIdentity where
    view = convert this
    upgradeRejectedIdentity self = do
      archive self
      toInterfaceContractId @IUpgradeRejectedIdentityRollback <$> create (convert this : LegacyIdentity.RejectedIdentity)

  interface instance IUpgradeRejectedIdentityRollback for LegacyIdentity.RejectedIdentity where
    view = this
    upgradeRejectedIdentity = pure
