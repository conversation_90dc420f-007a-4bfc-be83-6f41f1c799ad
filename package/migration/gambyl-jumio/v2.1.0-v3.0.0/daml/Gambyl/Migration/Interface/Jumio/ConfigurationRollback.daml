module Gambyl.Migration.Interface.Jumio.ConfigurationRollback where

import Gambyl.Migration.Common
import Gambyl.Migration.Upgrade
import Gambyl.Migration.Jumio.ConfigurationRollback ()

import Legacy.JumioIntegration.Configuration qualified as LegacyConfiguration
import Current.JumioIntegration.Configuration qualified as CurrentConfiguration

interface IUpgradeConfigurationRollback where
  viewtype LegacyConfiguration.Configuration

  upgradeConfiguration : ContractId IUpgradeConfigurationRollback -> Update (ContractId IUpgradeConfigurationRollback)

  nonconsuming choice UpgradeConfiguration : MigrationResult (ContractId IUpgradeConfigurationRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeConfigurationRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeConfiguration this self

  interface instance IUpgradeConfigurationRollback for CurrentConfiguration.Configuration where
    view = convert this
    upgradeConfiguration self = do
      archive self
      toInterfaceContractId @IUpgradeConfigurationRollback <$> create (convert this : LegacyConfiguration.Configuration)

  interface instance IUpgradeConfigurationRollback for LegacyConfiguration.Configuration where
    view = this
    upgradeConfiguration = pure
