module Gambyl.Migration.Interface.Jumio.Identity where

import Gambyl.Migration.Common
import Gambyl.Migration.Upgrade
import Gambyl.Migration.Jumio.Identity ()

import Legacy.JumioIntegration.Identity qualified as LegacyIdentity
import Current.JumioIntegration.Identity qualified as CurrentIdentity

interface IUpgradeInitialIdentityVerificationRequest where
  viewtype CurrentIdentity.InitialIdentityVerificationRequest

  upgradeInitialIdentityVerificationRequest : ContractId IUpgradeInitialIdentityVerificationRequest -> Update (ContractId IUpgradeInitialIdentityVerificationRequest)

  nonconsuming choice UpgradeInitialIdentityVerificationRequest : MigrationResult (ContractId IUpgradeInitialIdentityVerificationRequest)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeInitialIdentityVerificationRequest contract") $
          migratingParty == (view this).integrationParty
        upgradeInitialIdentityVerificationRequest this self

  interface instance IUpgradeInitialIdentityVerificationRequest for LegacyIdentity.InitialIdentityVerificationRequest where
    view = convert this
    upgradeInitialIdentityVerificationRequest self = do
      archive self
      toInterfaceContractId @IUpgradeInitialIdentityVerificationRequest <$> create (convert this : CurrentIdentity.InitialIdentityVerificationRequest)

  interface instance IUpgradeInitialIdentityVerificationRequest for CurrentIdentity.InitialIdentityVerificationRequest where
    view = this
    upgradeInitialIdentityVerificationRequest = pure


interface IUpgradeIdentityVerificationRequest where
  viewtype CurrentIdentity.IdentityVerificationRequest

  upgradeIdentityVerificationRequest : ContractId IUpgradeIdentityVerificationRequest -> Update (ContractId IUpgradeIdentityVerificationRequest)

  nonconsuming choice UpgradeIdentityVerificationRequest : MigrationResult (ContractId IUpgradeIdentityVerificationRequest)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeIdentityVerificationRequest contract") $
          migratingParty == (view this).integrationParty
        upgradeIdentityVerificationRequest this self

  interface instance IUpgradeIdentityVerificationRequest for LegacyIdentity.IdentityVerificationRequest where
    view = convert this
    upgradeIdentityVerificationRequest self = do
      archive self
      toInterfaceContractId @IUpgradeIdentityVerificationRequest <$> create (convert this : CurrentIdentity.IdentityVerificationRequest)

  interface instance IUpgradeIdentityVerificationRequest for CurrentIdentity.IdentityVerificationRequest where
    view = this
    upgradeIdentityVerificationRequest = pure


interface IUpgradePendingIdentityRequest where
  viewtype CurrentIdentity.PendingIdentityRequest

  upgradePendingIdentityRequest : ContractId IUpgradePendingIdentityRequest -> Update (ContractId IUpgradePendingIdentityRequest)

  nonconsuming choice UpgradePendingIdentityRequest : MigrationResult (ContractId IUpgradePendingIdentityRequest)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradePendingIdentityRequest contract") $
          migratingParty == (view this).integrationParty
        upgradePendingIdentityRequest this self

  interface instance IUpgradePendingIdentityRequest for LegacyIdentity.PendingIdentityRequest where
    view = convert this
    upgradePendingIdentityRequest self = do
      archive self
      toInterfaceContractId @IUpgradePendingIdentityRequest <$> create (convert this : CurrentIdentity.PendingIdentityRequest)

  interface instance IUpgradePendingIdentityRequest for CurrentIdentity.PendingIdentityRequest where
    view = this
    upgradePendingIdentityRequest = pure


interface IUpgradeVerifiedIdentity where
  viewtype CurrentIdentity.VerifiedIdentity

  upgradeVerifiedIdentity : ContractId IUpgradeVerifiedIdentity -> Update (ContractId IUpgradeVerifiedIdentity)

  nonconsuming choice UpgradeVerifiedIdentity : MigrationResult (ContractId IUpgradeVerifiedIdentity)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeVerifiedIdentity contract") $
          migratingParty == (view this).integrationParty
        upgradeVerifiedIdentity this self

  interface instance IUpgradeVerifiedIdentity for LegacyIdentity.VerifiedIdentity where
    view = convert this
    upgradeVerifiedIdentity self = do
      archive self
      toInterfaceContractId @IUpgradeVerifiedIdentity <$> create (convert this : CurrentIdentity.VerifiedIdentity)

  interface instance IUpgradeVerifiedIdentity for CurrentIdentity.VerifiedIdentity where
    view = this
    upgradeVerifiedIdentity = pure


interface IUpgradeRejectedIdentity where
  viewtype CurrentIdentity.RejectedIdentity

  upgradeRejectedIdentity : ContractId IUpgradeRejectedIdentity -> Update (ContractId IUpgradeRejectedIdentity)

  nonconsuming choice UpgradeRejectedIdentity : MigrationResult (ContractId IUpgradeRejectedIdentity)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeRejectedIdentity contract") $
          migratingParty == (view this).integrationParty
        upgradeRejectedIdentity this self

  interface instance IUpgradeRejectedIdentity for LegacyIdentity.RejectedIdentity where
    view = convert this
    upgradeRejectedIdentity self = do
      archive self
      toInterfaceContractId @IUpgradeRejectedIdentity <$> create (convert this : CurrentIdentity.RejectedIdentity)

  interface instance IUpgradeRejectedIdentity for CurrentIdentity.RejectedIdentity where
    view = this
    upgradeRejectedIdentity = pure
