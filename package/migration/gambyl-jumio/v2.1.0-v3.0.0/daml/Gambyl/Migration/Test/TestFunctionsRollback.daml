module Gambyl.Migration.Test.TestFunctionsRollback where

import Daml.Script

import DA.Date(date, Month(Jan))

import Current.JumioIntegration.Configuration qualified as JumioConfiguration
import Current.JumioIntegration.Identity qualified as JumioIdentity

{-- CONTRACTS --}

-- | 0-IUpgradeConfiguration
prepareIUpgradeConfiguration : [Party] -> Script (ContractId JumioConfiguration.Configuration)
prepareIUpgradeConfiguration parties = script do

  let [operator, _, jumio, _] = parties

  submitMulti [jumio] [] $ do
    createCmd JumioConfiguration.Configuration with
      integrationParty = jumio
      observers = [operator]
      token = "token"
      secretToken = "secret-token"

-- | 1-IUpgradeInitialIdentityVerificationRequest
prepareIUpgradeInitialIdentityVerificationRequest : [Party] -> Script (ContractId JumioIdentity.InitialIdentityVerificationRequest)
prepareIUpgradeInitialIdentityVerificationRequest parties = script do

  let [operator, provider, jumio, customer] = parties

  submitMulti [jumio, customer] [] $ do
    createCmd JumioIdentity.InitialIdentityVerificationRequest with
      operator
      provider
      requestFrom = customer
      integrationParty = jumio
      locale = "en_US"
      observers = [operator, provider]

-- | 2-IUpgradeIdentityVerificationRequest
prepareIUpgradeIUpgradeIdentityVerificationRequest : [Party] -> Script (ContractId JumioIdentity.IdentityVerificationRequest)
prepareIUpgradeIUpgradeIdentityVerificationRequest parties = script do

  let [operator, provider, jumio, customer] = parties

  createdAt <- getTime
  submitMulti [jumio, customer] [] $ do
    createCmd JumioIdentity.IdentityVerificationRequest with
      operator
      provider
      requestFrom = customer
      integrationParty = jumio
      observers = [operator, provider]
      redirectUrl = "redirect-url"
      requestFromHashed = "requestor-hash"
      createdAt

-- | 3-IUpgradePendingIdentityRequest
prepareIUpgradeIUpgradePendingIdentityRequest : [Party] -> Script (ContractId JumioIdentity.PendingIdentityRequest)
prepareIUpgradeIUpgradePendingIdentityRequest parties = script do

  let [operator, provider, jumio, customer] = parties

  createdAt <- getTime
  submitMulti [jumio, customer] [] $ do
    createCmd JumioIdentity.PendingIdentityRequest with
      operator
      provider
      requestFrom = customer
      integrationParty = jumio
      observers = [operator, provider]
      redirectUrl = "redirect-url"
      requestFromHashed = "requestor-hash"
      createdAt

-- | 4-IUpgradeVerifiedIdentity
prepareIUpgradeIUpgradeVerifiedIdentity : [Party] -> Script (ContractId JumioIdentity.VerifiedIdentity)
prepareIUpgradeIUpgradeVerifiedIdentity parties = script do

  let
    [operator, provider, jumio, customer] = parties
    userData = JumioIdentity.User with
      firstName = "firstName"
      lastName = "lastName"
      birthday = date 1970 Jan 1
      city = "city = "
      country = "country"
      postalCode = "postalCode"
      subDivision = "subDivision"
      addressLine1 = "addressLine1"
      addressLine2 = "addressLine2"

  submitMulti [jumio, customer] [] $ do
    createCmd JumioIdentity.VerifiedIdentity with
      requestFrom = customer
      integrationParty = jumio
      observers = [operator, provider]
      userData
      dataUrl = "data-url"

-- | 5-IUpgradeRejectedIdentity
prepareIUpgradeIUpgradeRejectedIdentity : [Party] -> Script (ContractId JumioIdentity.RejectedIdentity)
prepareIUpgradeIUpgradeRejectedIdentity parties = script do

  let [operator, provider, jumio, customer] = parties

  submitMulti [jumio, customer] [] $ do
    createCmd JumioIdentity.RejectedIdentity with
      requestFrom = customer
      integrationParty = jumio
      observers = [operator, provider]
      dataUrl = "data-url"
      rejectReason = "reason"


prepareLedger : Party -> Party -> Script ()
prepareLedger operator jumio = script do
  [customer, other] <- mapA allocateParty ["alice", "bob"]

  let parties = [operator, other, jumio, customer]

  prepareIUpgradeConfiguration parties
  prepareIUpgradeInitialIdentityVerificationRequest parties
  prepareIUpgradeIUpgradeIdentityVerificationRequest parties
  prepareIUpgradeIUpgradePendingIdentityRequest parties
  prepareIUpgradeIUpgradeVerifiedIdentity parties
  prepareIUpgradeIUpgradeRejectedIdentity parties

  pure ()