module Gambyl.Migration.Script.UpgradeScript where

import Daml.Script

import Gambyl.Migration.Common
import Gambyl.Test.TestUtils

import Gambyl.Migration.Script.ScriptFunctions

upgradeScript : Input -> Script ReportSummary
upgradeScript Input{jumio} = script do

  debug "START RollingBack Jumio"

  r01 <- collectReport "Configuration" $ migrateDirectly "01-IUpgradeConfiguration" jumio upgradeListOfIUpgradeConfiguration
  r02 <- collectReport "InitialIdentityVerificationRequest" $ migrateDirectly "02-IUpgradeInitialIdentityVerificationRequest" jumio upgradeListOfIUpgradeInitialIdentityVerificationRequest
  r03 <- collectReport "IdentityVerificationRequest" $ migrateDirectly "03-IUpgradeIdentityVerificationRequest" jumio upgradeListOfIUpgradeIdentityVerificationRequest
  r04 <- collectReport "PendingIdentityRequest" $ migrateDirectly "04-IUpgradePendingIdentityRequest" jumio upgradeListOfIUpgradePendingIdentityRequest
  r05 <- collectReport "VerifiedIdentity" $ migrateDirectly "05-IUpgradeVerifiedIdentity" jumio upgradeListOfIUpgradeVerifiedIdentity
  r06 <- collectReport "RejectedIdentity" $ migrateDirectly "06-IUpgradeRejectedIdentity" jumio upgradeListOfIUpgradeRejectedIdentity

  let report = foldl combineReport (ReportSummary 0 0 0 0 []) [r01, r02, r03, r04, r05, r06]

  debug "FINISH RollingBack Jumio"

  pure report