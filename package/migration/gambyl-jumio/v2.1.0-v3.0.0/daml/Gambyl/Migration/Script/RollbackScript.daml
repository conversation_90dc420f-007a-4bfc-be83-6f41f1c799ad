module Gambyl.Migration.Script.RollbackScript where

import Daml.Script

import Gambyl.Migration.Common
import Gambyl.Test.TestUtils

import Gambyl.Migration.Script.RollbackScriptFunctions

rollbackScript : Input -> Script ReportSummary
rollbackScript Input{jumio} = script do

  debug "START Upgrading Jumio"

  r01 <- collectReport "ConfigurationRollback" $ migrateDirectly "01-IUpgradeConfigurationRollback" jumio upgradeListOfIUpgradeConfigurationRollback
  r02 <- collectReport "InitialIdentityVerificationRequestRollback" $ migrateDirectly "02-IUpgradeInitialIdentityVerificationRequestRollback" jumio upgradeListOfIUpgradeInitialIdentityVerificationRequestRollback
  r03 <- collectReport "IdentityVerificationRequestRollback" $ migrateDirectly "03-IUpgradeIdentityVerificationRequestRollback" jumio upgradeListOfIUpgradeIdentityVerificationRequestRollback
  r04 <- collectReport "PendingIdentityRequestRollback" $ migrateDirectly "04-IUpgradePendingIdentityRequestRollback" jumio upgradeListOfIUpgradePendingIdentityRequestRollback
  r05 <- collectReport "VerifiedIdentityRollback" $ migrateDirectly "05-IUpgradeVerifiedIdentityRollback" jumio upgradeListOfIUpgradeVerifiedIdentityRollback
  r06 <- collectReport "RejectedIdentityRollback" $ migrateDirectly "06-IUpgradeRejectedIdentityRollback" jumio upgradeListOfIUpgradeRejectedIdentityRollback

  let report = foldl combineReport (ReportSummary 0 0 0 0 []) [r01, r02, r03, r04, r05, r06]

  debug "FINISH Upgrading Jumio"

  pure report
