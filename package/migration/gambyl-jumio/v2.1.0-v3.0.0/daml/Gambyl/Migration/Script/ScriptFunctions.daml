module Gambyl.Migration.Script.ScriptFunctions where

import Daml.Script
import Gambyl.Migration.Common

import Gambyl.Migration.Interface.Jumio.Configuration qualified as Configuration
import Gambyl.Migration.Interface.Jumio.Identity qualified as Identity

upgradeListOfIUpgradeConfiguration : Party -> [ContractId Configuration.IUpgradeConfiguration] -> Script [MigrationResult (ContractId Configuration.IUpgradeConfiguration)]
upgradeListOfIUpgradeConfiguration migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Configuration.UpgradeConfiguration with ..

upgradeListOfIUpgradeInitialIdentityVerificationRequest : Party -> [ContractId Identity.IUpgradeIdentityVerificationRequest] -> Script [MigrationResult (ContractId Identity.IUpgradeIdentityVerificationRequest)]
upgradeListOfIUpgradeInitialIdentityVerificationRequest migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Identity.UpgradeIdentityVerificationRequest with ..

upgradeListOfIUpgradeIdentityVerificationRequest : Party -> [ContractId Identity.IUpgradeIdentityVerificationRequest] -> Script [MigrationResult (ContractId Identity.IUpgradeIdentityVerificationRequest)]
upgradeListOfIUpgradeIdentityVerificationRequest migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Identity.UpgradeIdentityVerificationRequest with ..

upgradeListOfIUpgradePendingIdentityRequest : Party -> [ContractId Identity.IUpgradePendingIdentityRequest] -> Script [MigrationResult (ContractId Identity.IUpgradePendingIdentityRequest)]
upgradeListOfIUpgradePendingIdentityRequest migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Identity.UpgradePendingIdentityRequest with ..

upgradeListOfIUpgradeVerifiedIdentity : Party -> [ContractId Identity.IUpgradeVerifiedIdentity] -> Script [MigrationResult (ContractId Identity.IUpgradeVerifiedIdentity)]
upgradeListOfIUpgradeVerifiedIdentity migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Identity.UpgradeVerifiedIdentity with ..

upgradeListOfIUpgradeRejectedIdentity : Party -> [ContractId Identity.IUpgradeRejectedIdentity] -> Script [MigrationResult (ContractId Identity.IUpgradeRejectedIdentity)]
upgradeListOfIUpgradeRejectedIdentity migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Identity.UpgradeRejectedIdentity with ..