module Gambyl.Migration.Script.RollbackScriptFunctions where

import Daml.Script
import Gambyl.Migration.Common

import Gambyl.Migration.Interface.Jumio.ConfigurationRollback qualified as Configuration
import Gambyl.Migration.Interface.Jumio.IdentityRollback qualified as Identity

upgradeListOfIUpgradeConfigurationRollback : Party -> [ContractId Configuration.IUpgradeConfigurationRollback] -> Script [MigrationResult (ContractId Configuration.IUpgradeConfigurationRollback)]
upgradeListOfIUpgradeConfigurationRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Configuration.UpgradeConfiguration with ..

upgradeListOfIUpgradeInitialIdentityVerificationRequestRollback : Party -> [ContractId Identity.IUpgradeIdentityVerificationRequestRollback] -> Script [MigrationResult (ContractId Identity.IUpgradeIdentityVerificationRequestRollback)]
upgradeListOfIUpgradeInitialIdentityVerificationRequestRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Identity.UpgradeIdentityVerificationRequest with ..

upgradeListOfIUpgradeIdentityVerificationRequestRollback : Party -> [ContractId Identity.IUpgradeIdentityVerificationRequestRollback] -> Script [MigrationResult (ContractId Identity.IUpgradeIdentityVerificationRequestRollback)]
upgradeListOfIUpgradeIdentityVerificationRequestRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Identity.UpgradeIdentityVerificationRequest with ..

upgradeListOfIUpgradePendingIdentityRequestRollback : Party -> [ContractId Identity.IUpgradePendingIdentityRequestRollback] -> Script [MigrationResult (ContractId Identity.IUpgradePendingIdentityRequestRollback)]
upgradeListOfIUpgradePendingIdentityRequestRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Identity.UpgradePendingIdentityRequest with ..

upgradeListOfIUpgradeVerifiedIdentityRollback : Party -> [ContractId Identity.IUpgradeVerifiedIdentityRollback] -> Script [MigrationResult (ContractId Identity.IUpgradeVerifiedIdentityRollback)]
upgradeListOfIUpgradeVerifiedIdentityRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Identity.UpgradeVerifiedIdentity with ..

upgradeListOfIUpgradeRejectedIdentityRollback : Party -> [ContractId Identity.IUpgradeRejectedIdentityRollback] -> Script [MigrationResult (ContractId Identity.IUpgradeRejectedIdentityRollback)]
upgradeListOfIUpgradeRejectedIdentityRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Identity.UpgradeRejectedIdentity with ..
