module Gambyl.Migration.Jumio.Identity where

import Gambyl.Migration.Upgrade

import Legacy.JumioIntegration.Identity qualified as LegacyIdentity
import Current.JumioIntegration.Identity qualified as CurrentIdentity


instance DAMLUpgrade LegacyIdentity.InitialIdentityVerificationRequest CurrentIdentity.InitialIdentityVerificationRequest where
  convert LegacyIdentity.InitialIdentityVerificationRequest{..} = CurrentIdentity.InitialIdentityVerificationRequest with
        ..

instance DAMLUpgrade LegacyIdentity.IdentityVerificationRequest CurrentIdentity.IdentityVerificationRequest where
  convert LegacyIdentity.IdentityVerificationRequest{..} = CurrentIdentity.IdentityVerificationRequest with
        ..

instance DAMLUpgrade LegacyIdentity.PendingIdentityRequest CurrentIdentity.PendingIdentityRequest where
  convert LegacyIdentity.PendingIdentityRequest{..} = CurrentIdentity.PendingIdentityRequest with
        ..

instance DAMLUpgrade LegacyIdentity.VerifiedIdentity CurrentIdentity.VerifiedIdentity where
  convert LegacyIdentity.VerifiedIdentity{..} = CurrentIdentity.VerifiedIdentity with
    userData = convert userData
    ..

instance DAMLUpgrade LegacyIdentity.RejectedIdentity CurrentIdentity.RejectedIdentity where
  convert LegacyIdentity.RejectedIdentity{..} = CurrentIdentity.RejectedIdentity with
        ..

instance DAMLUpgrade LegacyIdentity.User CurrentIdentity.User where
  convert LegacyIdentity.User{..} = CurrentIdentity.User with
        ..