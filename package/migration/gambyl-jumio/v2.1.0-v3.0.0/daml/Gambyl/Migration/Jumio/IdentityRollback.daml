module Gambyl.Migration.Jumio.IdentityRollback where

import Gambyl.Migration.Upgrade

import Legacy.JumioIntegration.Identity qualified as LegacyIdentity
import Current.JumioIntegration.Identity qualified as CurrentIdentity


instance DAMLUpgrade CurrentIdentity.InitialIdentityVerificationRequest LegacyIdentity.InitialIdentityVerificationRequest where
  convert CurrentIdentity.InitialIdentityVerificationRequest{..} = LegacyIdentity.InitialIdentityVerificationRequest with
        ..

instance DAMLUpgrade CurrentIdentity.IdentityVerificationRequest LegacyIdentity.IdentityVerificationRequest where
  convert CurrentIdentity.IdentityVerificationRequest{..} = LegacyIdentity.IdentityVerificationRequest with
        ..

instance DAMLUpgrade CurrentIdentity.PendingIdentityRequest LegacyIdentity.PendingIdentityRequest where
  convert CurrentIdentity.PendingIdentityRequest{..} = LegacyIdentity.PendingIdentityRequest with
        ..

instance DAMLUpgrade CurrentIdentity.VerifiedIdentity LegacyIdentity.VerifiedIdentity where
  convert CurrentIdentity.VerifiedIdentity{..} = LegacyIdentity.VerifiedIdentity with
    userData = convert userData
    ..

instance DAMLUpgrade CurrentIdentity.RejectedIdentity LegacyIdentity.RejectedIdentity where
  convert CurrentIdentity.RejectedIdentity{..} = LegacyIdentity.RejectedIdentity with
        ..

instance DAMLUpgrade CurrentIdentity.User LegacyIdentity.User where
  convert CurrentIdentity.User{..} = LegacyIdentity.User with
        ..