module Gambyl.Migration.SendgridIntegration.Message where

import Gambyl.Migration.Upgrade

import Legacy.SendgridIntegration.Message qualified as FromSendgridIntegrationMessage
import Current.SendgridIntegration.Message qualified as ToSendgridIntegrationMessage

instance DAMLUpgrade FromSendgridIntegrationMessage.BetDetails ToSendgridIntegrationMessage.BetDetails where
  convert FromSendgridIntegrationMessage.BetDetails{..} = ToSendgridIntegrationMessage.BetDetails with
    stake = convert stake
    ..

instance DAMLUpgrade FromSendgridIntegrationMessage.Subject ToSendgridIntegrationMessage.Subject where
  convert FromSendgridIntegrationMessage.KYCApproved = ToSendgridIntegrationMessage.KYCApproved
  convert FromSendgridIntegrationMessage.KYCRejected = ToSendgridIntegrationMessage.KYCRejected
  convert FromSendgridIntegrationMessage.KYCPending = ToSendgridIntegrationMessage.KYCPending
  convert FromSendgridIntegrationMessage.Deposit{..} = ToSendgridIntegrationMessage.Deposit with
    transactionCode = convert transactionCode
    requestedAmount = convert requestedAmount
    feeCharged = convert feeCharged
    ..
  convert FromSendgridIntegrationMessage.Withdraw{..} = ToSendgridIntegrationMessage.Withdraw with
    transactionCode = convert transactionCode
    requestedAmount = convert requestedAmount
    feeCharged = convert feeCharged
    ..
  convert FromSendgridIntegrationMessage.BetPlacement{..} = ToSendgridIntegrationMessage.BetPlacement with
    betDetails = convert betDetails
    ..
  convert FromSendgridIntegrationMessage.BetCancellation{..} = ToSendgridIntegrationMessage.BetCancellation with
    eventTitle = convert eventTitle
    dateTime = convert dateTime
    side = convert side
    stake = convert stake
    oddValue = convert oddValue
    actionType = convert actionType
    ..
  convert FromSendgridIntegrationMessage.Promotion{..} = ToSendgridIntegrationMessage.Promotion with
    ..
  convert FromSendgridIntegrationMessage.BetSettled{..} = ToSendgridIntegrationMessage.BetSettled with
    details = convert details
    ..

instance DAMLUpgrade FromSendgridIntegrationMessage.Message ToSendgridIntegrationMessage.Message where
  convert FromSendgridIntegrationMessage.Message{..} = ToSendgridIntegrationMessage.Message with
    subject = convert subject
    ..

instance DAMLUpgrade FromSendgridIntegrationMessage.ActionType ToSendgridIntegrationMessage.ActionType where
  convert FromSendgridIntegrationMessage.ManualCancellation = ToSendgridIntegrationMessage.ManualCancellation
  convert FromSendgridIntegrationMessage.EventInProgress = ToSendgridIntegrationMessage.EventInProgress
  convert FromSendgridIntegrationMessage.EventCancelled = ToSendgridIntegrationMessage.EventCancelled
  convert FromSendgridIntegrationMessage.EventPostponed = ToSendgridIntegrationMessage.EventPostponed
  convert FromSendgridIntegrationMessage.EventFinished = ToSendgridIntegrationMessage.EventFinished
  convert FromSendgridIntegrationMessage.EventArchived = ToSendgridIntegrationMessage.EventArchived
  convert FromSendgridIntegrationMessage.EventOutcomesRevised = ToSendgridIntegrationMessage.EventOutcomesRevised