module Gambyl.Migration.Script.RollbackScript where

import Daml.Script

import Gambyl.Migration.Script.RollbackScriptFunctions

import Gambyl.Migration.Common
import Gambyl.Test.TestUtils

rollbackScript : Input -> Script ReportSummary
rollbackScript Input{gambyl} = script do

  debug "START RollingBack sendgrid"
  r1 <- collectReport "MessageRollback" $ migrateDirectly "0-IUpgradeMessageRollback" gambyl upgradeListOfIUpgradeMessageRollback
  debug "FINISH RollingBack sendgrid"

  let report = foldl combineReport (ReportSummary 0 0 0 0 []) [r1]

  pure report