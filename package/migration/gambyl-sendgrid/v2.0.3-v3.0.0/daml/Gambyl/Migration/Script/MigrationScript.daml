module Gambyl.Migration.Script.MigrationScript where

import Daml.Script

import Gambyl.Migration.Test.TestFunctions qualified as Fixture
import Gambyl.Migration.Script.UpgradeScript qualified as Upgrade
import Gambyl.Migration.Script.RollbackScript qualified as Rollback
import Gambyl.Migration.Test.TestFunctionsRollback qualified as RollbackFixture

import Gambyl.Migration.Common
import Gambyl.Test.TestUtils

migrationScript : Input -> Script ReportSummary
migrationScript Input{..} = logMigrationTimes do
  if rollback
  then Rollback.rollbackScript (Input{..})
  else Upgrade.upgradeScript (Input{..})

{--TEST--}
testMigration : Script ()
testMigration = script do
  gambyl <- allocateParty "gambyl"
  enetpulse <- allocateParty "enetpulse"
  moneymatrix <- allocateParty "moneymatrix"
  quickbooks <- allocateParty "quickbooks"
  exberry <- allocateParty "exberry"
  jumio <- allocateParty "jumio"

  Fixture.prepareLedger gambyl gambyl

  let rollback = False
  let input = Input {..}
  checkReport =<< migrationScript input

{--TEST--}
testRollback : Script ()
testRollback = script do
  gambyl <- allocateParty "gambyl"
  enetpulse <- allocateParty "enetpulse"
  moneymatrix <- allocateParty "moneymatrix"
  quickbooks <- allocateParty "quickbooks"
  exberry <- allocateParty "exberry"
  jumio <- allocateParty "jumio"

  RollbackFixture.prepareLedger gambyl gambyl

  let rollback = True
  let input = Input {..}
  checkReport =<< migrationScript input
