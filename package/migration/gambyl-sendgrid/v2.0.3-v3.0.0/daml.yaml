# for config file options, refer to
# https://docs.daml.com/tools/assistant.html#project-config-file-daml-yaml

sdk-version: 2.9.4
name: gambyl-sendgrid-migration
source: daml
init-script:
parties:
  - Operator
version: 1.0.0
dependencies:
  - daml-prim
  - daml-stdlib
  - daml-script
data-dependencies:
  # External Dependencies
  - ../../../../ext-pkg/integrations/sendgrid/${GAMBYL_SENDGRID_IMPL_LEGACY_VERSION}/daml/sendgrid-integration-model-${GAMBYL_SENDGRID_IMPL_LEGACY_VERSION}.dar
  # Local Dependencies
  - ../../../../build/migration/gambyl-migration-utils-${GAMBYL_MIGRATION_UTILS_VERSION}.dar
  - ../../../../build/implementation/gambyl-sendgrid-impl-${GAMBYL_SENDGRID_IMPL_CURRENT_VERSION}.dar
build-options:
  - --ghc-option=-Wall
  - --ghc-option=-Wno-name-shadowing
  - --ghc-option=-fno-warn-orphans
  - --ghc-option=-Wno-unused-do-bind
  - --ghc-option=-Werror
  - --output=../../../../build/migration/gambyl-sendgrid-v${GAMBYL_SENDGRID_IMPL_LEGACY_VERSION}-v${GAMBYL_SENDGRID_IMPL_CURRENT_VERSION}-migration-1.0.0.dar
module-prefixes:
  sendgrid-integration-model-${GAMBYL_SENDGRID_IMPL_LEGACY_VERSION}: Legacy
  gambyl-sendgrid-impl-${GAMBYL_SENDGRID_IMPL_CURRENT_VERSION}: Current