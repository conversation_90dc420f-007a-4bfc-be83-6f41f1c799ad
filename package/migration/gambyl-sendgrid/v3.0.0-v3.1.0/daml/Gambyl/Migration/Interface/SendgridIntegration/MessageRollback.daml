module Gambyl.Migration.Interface.SendgridIntegration.MessageRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.SendgridIntegration.MessageRollback ()

import Legacy.SendgridIntegration.Message qualified as FromSendgridIntegrationMessage
import Current.SendgridIntegration.Message qualified as ToSendgridIntegrationMessage

interface IUpgradeMessageRollback where
  viewtype FromSendgridIntegrationMessage.Message

  upgradeMessageRollback : ContractId IUpgradeMessageRollback -> Update (ContractId IUpgradeMessageRollback)

  nonconsuming choice UpgradeMessageRollback : MigrationResult (ContractId IUpgradeMessageRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeMessageRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeMessageRollback this self

  interface instance IUpgradeMessageRollback for ToSendgridIntegrationMessage.Message where
    view = convert this
    upgradeMessageRollback self = do
      archive self
      toInterfaceContractId @IUpgradeMessageRollback <$> create (convert this : FromSendgridIntegrationMessage.Message)

  interface instance IUpgradeMessageRollback for FromSendgridIntegrationMessage.Message where
    view = this
    upgradeMessageRollback = pure