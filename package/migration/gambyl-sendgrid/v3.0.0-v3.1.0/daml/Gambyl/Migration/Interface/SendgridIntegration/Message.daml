module Gambyl.Migration.Interface.SendgridIntegration.Message where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.SendgridIntegration.Message ()

import Legacy.SendgridIntegration.Message qualified as FromSendgridIntegrationMessage
import Current.SendgridIntegration.Message qualified as ToSendgridIntegrationMessage

interface IUpgradeMessage where
  viewtype ToSendgridIntegrationMessage.Message

  upgradeMessage : ContractId IUpgradeMessage -> Update (ContractId IUpgradeMessage)

  nonconsuming choice UpgradeMessage : MigrationResult (ContractId IUpgradeMessage)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeMessage contract") $
          migratingParty == (view this).integrationParty
        upgradeMessage this self

  interface instance IUpgradeMessage for FromSendgridIntegrationMessage.Message where
    view = convert this
    upgradeMessage self = do
      archive self
      toInterfaceContractId @IUpgradeMessage <$> create (convert this : ToSendgridIntegrationMessage.Message)

  interface instance IUpgradeMessage for ToSendgridIntegrationMessage.Message where
    view = this
    upgradeMessage = pure