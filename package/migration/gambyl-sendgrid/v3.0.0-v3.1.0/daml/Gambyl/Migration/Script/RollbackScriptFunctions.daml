module Gambyl.Migration.Script.RollbackScriptFunctions where

import Daml.Script
import Gambyl.Migration.Common

import Gambyl.Migration.Interface.SendgridIntegration.MessageRollback

upgradeListOfIUpgradeMessageRollback : Party -> [ContractId IUpgradeMessageRollback] -> Script [MigrationResult (ContractId IUpgradeMessageRollback)]
upgradeListOfIUpgradeMessageRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeMessageRollback with ..