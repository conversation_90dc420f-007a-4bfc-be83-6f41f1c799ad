module Gambyl.Migration.Script.UpgradeScript where

import Daml.Script

import Gambyl.Migration.Script.ScriptFunctions

import Gambyl.Migration.Common
import Gambyl.Test.TestUtils

upgradeScript : Input -> Script ReportSummary
upgradeScript Input{gambyl} = script do

  debug "START Upgrading sendgrid"
  r1 <- collectReport "Message" $ migrateDirectly "0-IUpgradeMessage" gambyl upgradeListOfIUpgradeMessage
  debug "FINISH Upgrading sendgrid"

  let report = foldl combineReport (ReportSummary 0 0 0 0 []) [r1]

  pure report