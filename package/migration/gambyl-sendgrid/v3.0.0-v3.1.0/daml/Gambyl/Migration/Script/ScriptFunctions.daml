module Gambyl.Migration.Script.ScriptFunctions where

import Daml.Script
import Gambyl.Migration.Common

import Gambyl.Migration.Interface.SendgridIntegration.Message

upgradeListOfIUpgradeMessage : Party -> [ContractId IUpgradeMessage] -> Script [MigrationResult (ContractId IUpgradeMessage)]
upgradeListOfIUpgradeMessage migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeMessage with ..