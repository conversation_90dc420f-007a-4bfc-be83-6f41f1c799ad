module Gambyl.Migration.Test.TestFunctionsRollback where

import Daml.Script

import Current.SendgridIntegration.Message qualified as SendGrid

{-- CONTRACTS --}
prepareIUpgradeMessage : [Party] -> <PERSON>ript (ContractId SendGrid.Message)
prepareIUpgradeMessage parties = script do
  let
    [operator, _, _, _] = parties
    integrationParty = operator
    toEmail = "customer@email"
    -- TODO: prepare all types for SendGrid.Subject enum
    subject = SendGrid.KYCApproved

  submitMulti [operator] [] $ do
    createCmd SendGrid.Message  with ..


prepareLedger : Party -> Party -> Script ()
prepareLedger operator gambyl = script do
  [customer, other] <- mapA allocateParty ["alice", "bob"]

  let parties = [operator, gambyl, customer, other]

  prepareIUpgradeMessage parties

  pure ()
