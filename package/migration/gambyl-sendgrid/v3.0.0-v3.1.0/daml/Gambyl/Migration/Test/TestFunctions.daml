module Gambyl.Migration.Test.TestFunctions where

import Daml.Script

import Legacy.SendgridIntegration.Message qualified as SendGrid

{-- CONTRACTS --}
prepareIUpgradeMessage : [Party] -> Script (ContractId SendGrid.Message)
prepareIUpgradeMessage parties = script do
  let
    [operator, _, _, _] = parties
    integrationParty = operator
    toEmail = "customer@email"
    -- TODO: prepare all types for SendGrid.Subject enum
    subject = SendGrid.KYCApproved

  submitMulti [operator] [] $ do
    createCmd SendGrid.Message  with ..


prepareLedger : Party -> Party -> Script ()
prepareLedger operator gambyl = script do
  [customer, other] <- mapA allocateParty ["alice", "bob"]

  let parties = [operator, gambyl, customer, other]

  prepareIUpgradeMessage parties

  pure ()
