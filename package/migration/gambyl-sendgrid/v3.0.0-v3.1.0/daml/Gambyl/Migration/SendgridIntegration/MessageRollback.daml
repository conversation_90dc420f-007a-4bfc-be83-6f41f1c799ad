module Gambyl.Migration.SendgridIntegration.MessageRollback where

import Gambyl.Migration.Upgrade

import Legacy.SendgridIntegration.Message qualified as FromSendgridIntegrationMessage
import Current.SendgridIntegration.Message qualified as ToSendgridIntegrationMessage

instance DAMLUpgrade ToSendgridIntegrationMessage.BetDetails FromSendgridIntegrationMessage.BetDetails where
  convert ToSendgridIntegrationMessage.BetDetails{..} = FromSendgridIntegrationMessage.BetDetails with
    stake = convert stake
    ..

instance DAMLUpgrade ToSendgridIntegrationMessage.Subject FromSendgridIntegrationMessage.Subject where
  convert ToSendgridIntegrationMessage.KYCApproved = FromSendgridIntegrationMessage.KYCApproved
  convert ToSendgridIntegrationMessage.KYCRejected = FromSendgridIntegrationMessage.KYCRejected
  convert ToSendgridIntegrationMessage.KYCPending = FromSendgridIntegrationMessage.KYCPending
  convert ToSendgridIntegrationMessage.Deposit{..} = FromSendgridIntegrationMessage.Deposit with
    transactionCode = convert transactionCode
    requestedAmount = convert requestedAmount
    feeCharged = convert feeCharged
    ..
  convert ToSendgridIntegrationMessage.Withdraw{..} = FromSendgridIntegrationMessage.Withdraw with
    transactionCode = convert transactionCode
    requestedAmount = convert requestedAmount
    feeCharged = convert feeCharged
    ..
  convert ToSendgridIntegrationMessage.BetPlacement{..} = FromSendgridIntegrationMessage.BetPlacement with
    betDetails = convert betDetails
    ..
  convert ToSendgridIntegrationMessage.BetCancellation{..} = FromSendgridIntegrationMessage.BetCancellation with
    eventTitle = convert eventTitle
    dateTime = convert dateTime
    side = convert side
    stake = convert stake
    oddValue = convert oddValue
    actionType = convert actionType
    ..
  convert ToSendgridIntegrationMessage.Promotion{..} = FromSendgridIntegrationMessage.Promotion with
    ..
  convert ToSendgridIntegrationMessage.BetSettled{..} = FromSendgridIntegrationMessage.BetSettled with
    details = convert details
    ..

instance DAMLUpgrade ToSendgridIntegrationMessage.Message FromSendgridIntegrationMessage.Message where
  convert ToSendgridIntegrationMessage.Message{..} = FromSendgridIntegrationMessage.Message with
    subject = convert subject
    ..

instance DAMLUpgrade ToSendgridIntegrationMessage.ActionType FromSendgridIntegrationMessage.ActionType where
  convert ToSendgridIntegrationMessage.ManualCancellation = FromSendgridIntegrationMessage.ManualCancellation
  convert ToSendgridIntegrationMessage.EventInProgress = FromSendgridIntegrationMessage.EventInProgress
  convert ToSendgridIntegrationMessage.EventCancelled = FromSendgridIntegrationMessage.EventCancelled
  convert ToSendgridIntegrationMessage.EventPostponed = FromSendgridIntegrationMessage.EventPostponed
  convert ToSendgridIntegrationMessage.EventFinished = FromSendgridIntegrationMessage.EventFinished
  convert ToSendgridIntegrationMessage.EventArchived = FromSendgridIntegrationMessage.EventArchived
  convert ToSendgridIntegrationMessage.EventRevised = FromSendgridIntegrationMessage.EventOutcomesRevised