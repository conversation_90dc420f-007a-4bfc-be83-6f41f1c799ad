module Gambyl.Migration.Gambyl.Marketing.Model where

import Gambyl.Migration.Upgrade

import Legacy.Gambyl.Marketing.Model qualified as LegacyMarketingModel
import Current.Gambyl.Marketing.Model qualified as CurrentMarketingModel

instance DAMLUpgrade LegacyMarketingModel.Status CurrentMarketingModel.Status where
  convert LegacyMarketingModel.Active = CurrentMarketingModel.Active
  convert LegacyMarketingModel.Expired = CurrentMarketingModel.Expired

instance DAMLUpgrade LegacyMarketingModel.PromotionConfig CurrentMarketingModel.PromotionConfig where
  convert LegacyMarketingModel.PromotionConfig{..} = CurrentMarketingModel.PromotionConfig with
    promoType = convert promoType
    action = convert action
    ..

instance DAMLUpgrade LegacyMarketingModel.PromotionType CurrentMarketingModel.PromotionType where
  convert (LegacyMarketingModel.FirstTime val) = CurrentMarketingModel.FirstTime $ convert val
  convert LegacyMarketingModel.Reload = CurrentMarketingModel.Reload
  convert LegacyMarketingModel.Other = CurrentMarketingModel.Other

instance DAMLUpgrade LegacyMarketingModel.PromotionAction CurrentMarketingModel.PromotionAction where
  convert (LegacyMarketingModel.Deposit val) = CurrentMarketingModel.Deposit $ convert val
  convert (LegacyMarketingModel.Withdrawal val) = CurrentMarketingModel.Withdrawal $ convert val
  convert (LegacyMarketingModel.Bet val) = CurrentMarketingModel.Bet $ convert val

instance DAMLUpgrade LegacyMarketingModel.PromotionDetails CurrentMarketingModel.PromotionDetails where
  convert (LegacyMarketingModel.Bonus val) = CurrentMarketingModel.Bonus $ convert val
  convert (LegacyMarketingModel.Discount val) = CurrentMarketingModel.Discount $ convert val

instance DAMLUpgrade LegacyMarketingModel.Redemption CurrentMarketingModel.Redemption where
  convert (LegacyMarketingModel.Percentage val) = CurrentMarketingModel.Percentage $ convert val
  convert (LegacyMarketingModel.Cash val) = CurrentMarketingModel.Cash $ convert val

instance DAMLUpgrade LegacyMarketingModel.PromotionUsage CurrentMarketingModel.PromotionUsage where
  convert LegacyMarketingModel.PromotionUsage{..} = CurrentMarketingModel.PromotionUsage with
        ..

instance DAMLUpgrade LegacyMarketingModel.GlobalPromotions CurrentMarketingModel.GlobalPromotions where
  convert LegacyMarketingModel.GlobalPromotions{..} = CurrentMarketingModel.GlobalPromotions with
        promotions = convert promotions
        public = convert observers
        ..

instance DAMLUpgrade LegacyMarketingModel.Promotion CurrentMarketingModel.Promotion where
  convert LegacyMarketingModel.Promotion{..} = CurrentMarketingModel.Promotion with
        config = convert config
        status = convert status
        public = convert observers
        ..

instance DAMLUpgrade LegacyMarketingModel.PromotionRequest CurrentMarketingModel.PromotionRequest where
  convert LegacyMarketingModel.PromotionRequest{..} = CurrentMarketingModel.PromotionRequest with
        config = convert config
        status = convert status
        ..

instance DAMLUpgrade LegacyMarketingModel.PromotionUpdateRequest CurrentMarketingModel.PromotionUpdateRequest where
  convert LegacyMarketingModel.PromotionUpdateRequest{..} = CurrentMarketingModel.PromotionUpdateRequest with
        promotionKey = convert promotionKey
        newConfig = convert newConfig
        newStartDate = convert newStartDate
        ..

instance DAMLUpgrade LegacyMarketingModel.PromotionWallet CurrentMarketingModel.PromotionWallet where
  convert LegacyMarketingModel.PromotionWallet{..} = CurrentMarketingModel.PromotionWallet with
        promotionMap = convert promotionMap
        ..

instance DAMLUpgrade LegacyMarketingModel.PromotionUsageHistory CurrentMarketingModel.PromotionUsageHistory where
  convert LegacyMarketingModel.PromotionUsageHistory{..} = CurrentMarketingModel.PromotionUsageHistory with
        action = convert action
        usageDate = convert usageDate
        ..
