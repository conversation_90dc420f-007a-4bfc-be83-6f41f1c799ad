module Gambyl.Migration.Gambyl.Marketing.ModelRollback where

import DA.Set qualified as Set (empty)

import Gambyl.Migration.Upgrade

import Legacy.Gambyl.Marketing.Model qualified as LegacyMarketingModel
import Current.Gambyl.Marketing.Model qualified as CurrentMarketingModel

instance DAMLUpgrade CurrentMarketingModel.Status LegacyMarketingModel.Status where
  convert CurrentMarketingModel.Active = LegacyMarketingModel.Active
  convert CurrentMarketingModel.Expired = LegacyMarketingModel.Expired

instance DAMLUpgrade CurrentMarketingModel.PromotionConfig LegacyMarketingModel.PromotionConfig where
  convert CurrentMarketingModel.PromotionConfig{..} = LegacyMarketingModel.PromotionConfig with
    promoType = convert promoType
    action = convert action
    ..

instance DAMLUpgrade CurrentMarketingModel.PromotionType LegacyMarketingModel.PromotionType where
  convert (CurrentMarketingModel.FirstTime val) = LegacyMarketingModel.FirstTime $ convert val
  convert CurrentMarketingModel.Reload = LegacyMarketingModel.Reload
  convert CurrentMarketingModel.Other = LegacyMarketingModel.Other

instance DAMLUpgrade CurrentMarketingModel.PromotionAction LegacyMarketingModel.PromotionAction where
    convert (CurrentMarketingModel.Deposit val) = LegacyMarketingModel.Deposit $ convert val
    convert (CurrentMarketingModel.Withdrawal val) = LegacyMarketingModel.Withdrawal $ convert val
    convert (CurrentMarketingModel.Bet val) = LegacyMarketingModel.Bet $ convert val

instance DAMLUpgrade CurrentMarketingModel.PromotionDetails LegacyMarketingModel.PromotionDetails where
    convert (CurrentMarketingModel.Bonus val) = LegacyMarketingModel.Bonus $ convert val
    convert (CurrentMarketingModel.Discount val) = LegacyMarketingModel.Discount $ convert val

instance DAMLUpgrade CurrentMarketingModel.Redemption LegacyMarketingModel.Redemption where
    convert (CurrentMarketingModel.Percentage val) = LegacyMarketingModel.Percentage $ convert val
    convert (CurrentMarketingModel.Cash val) = LegacyMarketingModel.Cash $ convert val

instance DAMLUpgrade CurrentMarketingModel.PromotionUsage LegacyMarketingModel.PromotionUsage where
  convert CurrentMarketingModel.PromotionUsage{..} = LegacyMarketingModel.PromotionUsage with
    ..

instance DAMLUpgrade CurrentMarketingModel.GlobalPromotions LegacyMarketingModel.GlobalPromotions where
  convert CurrentMarketingModel.GlobalPromotions{..} = LegacyMarketingModel.GlobalPromotions with
    promotions = convert promotions
    observers = Set.empty
    ..

instance DAMLUpgrade CurrentMarketingModel.Promotion LegacyMarketingModel.Promotion where
  convert CurrentMarketingModel.Promotion{..} = LegacyMarketingModel.Promotion with
    config = convert config
    status = convert status
    observers = Set.empty
    ..

instance DAMLUpgrade CurrentMarketingModel.PromotionRequest LegacyMarketingModel.PromotionRequest where
  convert CurrentMarketingModel.PromotionRequest{..} = LegacyMarketingModel.PromotionRequest with
    config = convert config
    status = convert status
    observers = Set.empty
    ..

instance DAMLUpgrade CurrentMarketingModel.PromotionUpdateRequest LegacyMarketingModel.PromotionUpdateRequest where
  convert CurrentMarketingModel.PromotionUpdateRequest{..} = LegacyMarketingModel.PromotionUpdateRequest with
    promotionKey = convert promotionKey
    newConfig = convert newConfig
    newStartDate = convert newStartDate
    observers = Set.empty
    ..

instance DAMLUpgrade CurrentMarketingModel.PromotionWallet LegacyMarketingModel.PromotionWallet where
  convert CurrentMarketingModel.PromotionWallet{..} = LegacyMarketingModel.PromotionWallet with
    observers = Set.empty
    promotionMap = convert promotionMap
    ..

instance DAMLUpgrade CurrentMarketingModel.PromotionUsageHistory LegacyMarketingModel.PromotionUsageHistory where
  convert CurrentMarketingModel.PromotionUsageHistory{..} = LegacyMarketingModel.PromotionUsageHistory with
    action = convert action
    usageDate = convert usageDate
    observers = Set.empty
    ..
