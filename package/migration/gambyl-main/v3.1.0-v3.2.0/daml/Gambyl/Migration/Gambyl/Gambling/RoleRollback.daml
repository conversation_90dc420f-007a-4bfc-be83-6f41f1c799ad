module Gambyl.Migration.Gambyl.Gambling.RoleRollback where

import DA.Set qualified as Set (empty)

import Gambyl.Migration.Upgrade

import Gambyl.Migration.Gambyl.Gambling.ServiceRollback ()

import Legacy.Gambyl.Gambling.Role qualified as FromGamblingRole
import Current.Gambyl.Gambling.Role qualified as ToGamblingRole

instance DAMLUpgrade ToGamblingRole.Role FromGamblingRole.Role where
  convert ToGamblingRole.Role{..} = FromGamblingRole.Role with
    observers = Set.empty
    ..

instance DAMLUpgrade ToGamblingRole.Offer FromGamblingRole.Offer where
  convert ToGamblingRole.Offer{..} = FromGamblingRole.Offer with
    ..

instance DAMLUpgrade ToGamblingRole.Request FromGamblingRole.Request where
  convert ToGamblingRole.Request{..} = FromGamblingRole.Request with
    ..
