module Gambyl.Migration.Gambyl.Operator.RoleRollback where

import DA.Set qualified as Set (empty)

import Gambyl.Migration.Upgrade

import Legacy.Gambyl.Operator.Role qualified as LegacyOperatorRole
import Current.Gambyl.Operator.Role qualified as CurrentOperatorRole

instance DAMLUpgrade CurrentOperatorRole.Role LegacyOperatorRole.Role where
  convert CurrentOperatorRole.Role{..} = LegacyOperatorRole.Role with
    observers = Set.empty
    ..
