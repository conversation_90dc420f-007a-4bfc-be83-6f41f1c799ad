module Gambyl.Migration.Gambyl.Gambling.Settlement.Model where

import DA.Text (intercalate)

import Gambyl.Migration.Upgrade

import Gambyl.Migration.Gambyl.Gambling.Event.Model ()

import Legacy.Gambyl.Gambling.Settlement.Model qualified as FromSettlementModel
import Current.Gambyl.Gambling.Settlement.Model qualified as ToSettlementModel

instance DAMLUpgrade FromSettlementModel.ExecutedBets ToSettlementModel.ExecutedBets where
  convert FromSettlementModel.ExecutedBets{..} = ToSettlementModel.ExecutedBets with
    executedOdd = convert executedOdd
    executionId = intercalate "_" [backBetPlacementId, layBetPlacementId]
    ..
