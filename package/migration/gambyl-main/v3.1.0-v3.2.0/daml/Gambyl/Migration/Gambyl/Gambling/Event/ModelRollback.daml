module Gambyl.Migration.Gambyl.Gambling.Event.ModelRollback where

import DA.Set qualified as Set (empty)

import Marketplace.Issuance.Service qualified as Issuance (RequestOrigination(..))

import Gambyl.Migration.Upgrade

import Gambyl.Migration.Gambyl.Gambling.Bet.Odds.ModelRollback ()
import Gambyl.Migration.EnetPulse.EventsRollback ()

import Legacy.Gambyl.Gambling.Event.Model qualified as LegacyEventModel
import Current.Gambyl.Gambling.Event.Model qualified as CurrentEventModel
import Marketplace.Issuance.CFI (other)
import qualified ContingentClaims.Claim.Serializable as Serialized

instance DAMLUpgrade CurrentEventModel.Market LegacyEventModel.Market where
  convert (CurrentEventModel.Sport val) = LegacyEventModel.Sport $ convert val
  convert CurrentEventModel.Politics = LegacyEventModel.Politics
  convert CurrentEventModel.Entertainment = LegacyEventModel.Entertainment
  convert (CurrentEventModel.Other_Market val) = LegacyEventModel.Other_Market $ convert val

instance DAMLUpgrade CurrentEventModel.Submarket LegacyEventModel.Submarket where

  convert (CurrentEventModel.Tournament text) = LegacyEventModel.Tournament text
  convert (CurrentEventModel.Other_Submarket text) = LegacyEventModel.Other_Submarket text

instance DAMLUpgrade CurrentEventModel.Status LegacyEventModel.Status where
  convert CurrentEventModel.Expired = LegacyEventModel.Expired
  convert CurrentEventModel.Active = LegacyEventModel.Active

instance DAMLUpgrade CurrentEventModel.OutcomeOdds LegacyEventModel.OutcomeOdds where
  convert CurrentEventModel.OutcomeOdds{..} = LegacyEventModel.OutcomeOdds with
    odds = convert odds
    -- outcome = convert outcome
    -- TODO: Uncomment when there's changes between an legacy and current versions of Jumio models
    ..

instance DAMLUpgrade CurrentEventModel.Details LegacyEventModel.Details where
  convert CurrentEventModel.Details{..} = LegacyEventModel.Details with
    market = convert market
    submarkets = convert submarkets
    outcomes = convert outcomes
    origin = convert origin
    geography = convert geography
    -- eventParticipants = convert eventParticipants
    -- eventStatus = convert eventStatus
    -- eventResults = convert eventResults
    -- TODO: Uncomment when there's changes between an legacy and current versions of Jumio models
    ..

instance DAMLUpgrade CurrentEventModel.OriginType LegacyEventModel.OriginType where
  convert CurrentEventModel.Customer = LegacyEventModel.Customer
  convert CurrentEventModel.Integration = LegacyEventModel.Integration

instance DAMLUpgrade CurrentEventModel.InputOutcomeOdd LegacyEventModel.InputOutcomeOdd where
  convert CurrentEventModel.InputOutcomeOdd{..} = LegacyEventModel.InputOutcomeOdd with
    odd = convert odd
    -- outcome = convert outcome
    -- TODO: Uncomment when there's changes between an legacy and current versions of Jumio models
    ..

instance DAMLUpgrade CurrentEventModel.EventInstrumentRequest LegacyEventModel.EventInstrumentRequest where
  -- TODO: Delete origination request construciton in next version (> v3.2.0), since it won't be either in legacy and new versions
  convert CurrentEventModel.EventInstrumentRequest{..} =
    let originationRequest = Issuance.RequestOrigination with
          assetLabel = eventId.label, cfi = other, claims = Serialized.Zero
          description = details.description, observers = []
    in LegacyEventModel.EventInstrumentRequest with
        details = convert details
        observers = Set.empty
        originationRequest
        ..

instance DAMLUpgrade CurrentEventModel.EventInstrumentStatusUpdateRequest LegacyEventModel.EventInstrumentStatusUpdateRequest where
  convert CurrentEventModel.EventInstrumentStatusUpdateRequest{..} = LegacyEventModel.EventInstrumentStatusUpdateRequest with
    -- newEventStatus = convert newEventStatus
    -- eventResults = convert eventResults
    -- TODO: Uncomment when there's changes between an legacy and current versions of Jumio models
    observers = Set.empty
    ..

instance DAMLUpgrade CurrentEventModel.EventInstrumentUpdateRequest (LegacyEventModel.EventInstrumentUpdateRequest, Text) where
  -- TODO: Delete tuple construciton in next version (> v3.2.0), since this field will exist in both legacy and current
  convert CurrentEventModel.EventInstrumentUpdateRequest{..} = (, details.description) LegacyEventModel.EventInstrumentUpdateRequest with
    details = convert details
    observers = Set.empty
    ..

instance DAMLUpgrade CurrentEventModel.EventInstrument (LegacyEventModel.EventInstrument, Text) where
  -- TODO: Delete tuple construciton in next version (> v3.2.0), since this field will exist in both legacy and current
  convert CurrentEventModel.EventInstrument{..} = (, details.description) LegacyEventModel.EventInstrument with
    details = convert details
    status = convert status
    observers = Set.empty
    ..

instance DAMLUpgrade CurrentEventModel.EventInstrumentToggleFeaturedRequest LegacyEventModel.EventInstrumentToggleFeaturedRequest where
  convert CurrentEventModel.EventInstrumentToggleFeaturedRequest{..} = LegacyEventModel.EventInstrumentToggleFeaturedRequest with
      observers = Set.empty
      ..

instance DAMLUpgrade CurrentEventModel.EventInstrumentCancelRequest LegacyEventModel.EventInstrumentCancelRequest where
  convert CurrentEventModel.EventInstrumentCancelRequest{..} = LegacyEventModel.EventInstrumentCancelRequest with
      observers = Set.empty
      ..

instance DAMLUpgrade CurrentEventModel.EventInstrumentReinstateRequest LegacyEventModel.EventInstrumentReinstateRequest where
  convert CurrentEventModel.EventInstrumentReinstateRequest{..} = LegacyEventModel.EventInstrumentReinstateRequest with
        observers = Set.empty
        ..

instance DAMLUpgrade CurrentEventModel.EventInstrumentUpdateOutcomesOddsRequest (LegacyEventModel.EventInstrumentUpdateOutcomesOddsRequest, Text) where
  -- TODO: Delete tuple construciton in next version (> v3.2.0), since this field will exist in both legacy and current
  convert CurrentEventModel.EventInstrumentUpdateOutcomesOddsRequest{..} = (, details.description) LegacyEventModel.EventInstrumentUpdateOutcomesOddsRequest with
        details = convert details
        status = convert status
        newOutcomes = convert newOutcomes
        observers = Set.empty
        ..

instance DAMLUpgrade CurrentEventModel.EventInstrumentNoOutcomes (LegacyEventModel.EventInstrumentNoOutcomes, Text) where
  -- TODO: Delete tuple construciton in next version (> v3.2.0), since this field will exist in both legacy and current
  convert CurrentEventModel.EventInstrumentNoOutcomes{..} = (, event.details.description) LegacyEventModel.EventInstrumentNoOutcomes with
        event = fst (convert event : (LegacyEventModel.EventInstrument, Text))
        ..

instance DAMLUpgrade CurrentEventModel.MarketMap LegacyEventModel.MarketMap where
  convert CurrentEventModel.MarketMap{..} = LegacyEventModel.MarketMap with
        map = convert map
        observers = Set.empty
        ..

