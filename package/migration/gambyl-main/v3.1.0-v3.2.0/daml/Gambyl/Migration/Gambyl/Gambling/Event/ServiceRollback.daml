module Gambyl.Migration.Gambyl.Gambling.Event.ServiceRollback where

import DA.Set qualified as Set (empty)

import Gambyl.Migration.Upgrade

import Legacy.Gambyl.Gambling.Event.Service qualified as LegacyEventService
import Current.Gambyl.Gambling.Event.Service qualified as CurrentEventService

instance DAMLUpgrade CurrentEventService.Service LegacyEventService.Service where
  convert CurrentEventService.Service{..} = LegacyEventService.Service with
    observers = Set.empty
    ..

instance DAMLUpgrade CurrentEventService.Offer LegacyEventService.Offer where
  convert CurrentEventService.Offer{..} = LegacyEventService.Offer with
    observers = Set.empty
    ..

instance DAMLUpgrade CurrentEventService.Request LegacyEventService.Request where
  convert CurrentEventService.Request{..} = LegacyEventService.Request with
    observers = Set.empty
    ..
