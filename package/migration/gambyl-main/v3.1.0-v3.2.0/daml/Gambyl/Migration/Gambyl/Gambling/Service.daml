module Gambyl.Migration.Gambyl.Gambling.Service where

import Gambyl.Migration.Upgrade

import Gambyl.Migration.Gambyl.Gambling.Model ()
import Gambyl.Migration.Gambyl.Marketing.Model ()

import Legacy.Gambyl.Gambling.Service qualified as FromGamblingService
import Current.Gambyl.Gambling.Service qualified as ToGamblingService

import DA.Date (datetime, Month(..))

instance DAMLUpgrade FromGamblingService.Service ToGamblingService.Service where
  convert FromGamblingService.Service{..} = ToGamblingService.Service with
    permissions = convert permissions
    ..

instance DAMLUpgrade FromGamblingService.Offer ToGamblingService.Offer where
  convert FromGamblingService.Offer{..} = ToGamblingService.Offer with
    ..

instance DAMLUpgrade FromGamblingService.Request ToGamblingService.Request where
  convert FromGamblingService.Request{..} = ToGamblingService.Request with
    ..

instance DAMLUpgrade FromGamblingService.BlockedService ToGamblingService.BlockedService where
  convert FromGamblingService.BlockedService{..} = ToGamblingService.BlockedService with
    service = convert service
    blockedAt = datetime 1970 Jan 1 0 0 0
    ..

instance DAMLUpgrade FromGamblingService.PendingPromotionApplication ToGamblingService.PendingPromotionApplication where
  convert FromGamblingService.PendingPromotionApplication{..} = ToGamblingService.PendingPromotionApplication with
    promotionKey = convert promotionKey
    offeredAmount = convert offeredAmount
    ..
