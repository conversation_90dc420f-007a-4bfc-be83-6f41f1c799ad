module Gambyl.Migration.Gambyl.Gambling.Event.Model where

import DA.Finance.Types (Id(..))
import DA.Set qualified as Set (fromList)

import Gambyl.Migration.Upgrade
import Gambyl.Migration.EnetPulse.Events ()
import Gambyl.Migration.Gambyl.Gambling.Bet.Odds.Model ()

import Legacy.Gambyl.Gambling.Event.Model qualified as LegacyEventModel
import Current.Gambyl.Gambling.Event.Model qualified as CurrentEventModel

instance DAMLUpgrade LegacyEventModel.Market CurrentEventModel.Market where
  convert (LegacyEventModel.Sport val) = CurrentEventModel.Sport $ convert val
  convert LegacyEventModel.Politics = CurrentEventModel.Politics
  convert LegacyEventModel.Entertainment = CurrentEventModel.Entertainment
  convert (LegacyEventModel.Other_Market val) = CurrentEventModel.Other_Market $ convert val

instance DAMLUpgrade LegacyEventModel.Submarket CurrentEventModel.Submarket where

  convert (LegacyEventModel.Tournament text) = CurrentEventModel.Tournament text
  convert (LegacyEventModel.Other_Submarket text) = CurrentEventModel.Other_Submarket text

instance DAMLUpgrade LegacyEventModel.Status CurrentEventModel.Status where
  convert LegacyEventModel.Expired = CurrentEventModel.Expired
  convert LegacyEventModel.Active = CurrentEventModel.Active

instance DAMLUpgrade LegacyEventModel.OutcomeOdds CurrentEventModel.OutcomeOdds where
  convert LegacyEventModel.OutcomeOdds{..} = CurrentEventModel.OutcomeOdds with
    odds = convert odds
    -- outcome = convert outcome
    -- TODO: Uncomment when there's changes between an legacy and current versions of Jumio models
    ..

instance DAMLUpgrade LegacyEventModel.Details (Text -> CurrentEventModel.Details) where
  -- TODO: Delete in next version (> v3.2.0), since this field will exist in both legacy and current
  convert LegacyEventModel.Details{..} = \ description -> CurrentEventModel.Details with
    market = convert market
    submarkets = convert submarkets
    outcomes = convert outcomes
    origin = convert origin
    -- eventParticipants = convert eventParticipants
    -- eventStatus = convert eventStatus
    -- eventResults = convert eventResults
    -- TODO: Uncomment when there's changes between an legacy and current versions of Jumio models
    ..

instance DAMLUpgrade LegacyEventModel.OriginType CurrentEventModel.OriginType where
  convert LegacyEventModel.Customer = CurrentEventModel.Customer
  convert LegacyEventModel.Integration = CurrentEventModel.Integration

instance DAMLUpgrade LegacyEventModel.InputOutcomeOdd CurrentEventModel.InputOutcomeOdd where
  convert LegacyEventModel.InputOutcomeOdd{..} = CurrentEventModel.InputOutcomeOdd with
    -- outcome = convert outcome
    -- TODO: Uncomment when there's changes between an legacy and current versions of Jumio models
    odd = convert odd
    ..

instance DAMLUpgrade LegacyEventModel.EventInstrumentRequest (Text -> Text -> CurrentEventModel.EventInstrumentRequest) where
  -- TODO: Delete in next version (> v3.2.0), since this field will exist in both legacy and current
  convert LegacyEventModel.EventInstrumentRequest{..} = \ eventDescription eventLabel -> CurrentEventModel.EventInstrumentRequest with
    details = (convert details) eventDescription
    eventId = Id with signatories = Set.fromList [provider, customer]; label = eventLabel; version = 0
    ..

instance DAMLUpgrade LegacyEventModel.EventInstrumentStatusUpdateRequest CurrentEventModel.EventInstrumentStatusUpdateRequest where
  convert LegacyEventModel.EventInstrumentStatusUpdateRequest{..} = CurrentEventModel.EventInstrumentStatusUpdateRequest with
    -- newEventStatus = convert newEventStatus
    -- eventResults = convert eventResults
    -- TODO: Uncomment when there's changes between an legacy and current versions of Jumio models
    ..

instance DAMLUpgrade LegacyEventModel.EventInstrumentUpdateRequest (Text -> CurrentEventModel.EventInstrumentUpdateRequest) where
  -- TODO: Delete in next version (> v3.2.0), since this field will exist in both legacy and current
  convert LegacyEventModel.EventInstrumentUpdateRequest{..} = \ eventDescription -> CurrentEventModel.EventInstrumentUpdateRequest with
    details = (convert details) eventDescription
    ..

instance DAMLUpgrade LegacyEventModel.EventInstrument (Text -> CurrentEventModel.EventInstrument) where
  -- TODO: Delete in next version (> v3.2.0), since this field will exist in both legacy and current
  convert LegacyEventModel.EventInstrument{..} = \ eventDescription -> CurrentEventModel.EventInstrument with
    details = (convert details) eventDescription
    status = convert status
    public = convert observers
    ..

instance DAMLUpgrade LegacyEventModel.EventInstrumentToggleFeaturedRequest CurrentEventModel.EventInstrumentToggleFeaturedRequest where
  convert LegacyEventModel.EventInstrumentToggleFeaturedRequest{..} = CurrentEventModel.EventInstrumentToggleFeaturedRequest with
      ..

instance DAMLUpgrade LegacyEventModel.EventInstrumentCancelRequest CurrentEventModel.EventInstrumentCancelRequest where
  convert LegacyEventModel.EventInstrumentCancelRequest{..} = CurrentEventModel.EventInstrumentCancelRequest with
      ..

instance DAMLUpgrade LegacyEventModel.EventInstrumentReinstateRequest CurrentEventModel.EventInstrumentReinstateRequest where
  convert LegacyEventModel.EventInstrumentReinstateRequest{..} = CurrentEventModel.EventInstrumentReinstateRequest with
    ..

instance DAMLUpgrade LegacyEventModel.EventInstrumentUpdateOutcomesOddsRequest (Text -> CurrentEventModel.EventInstrumentUpdateOutcomesOddsRequest) where
  -- TODO: Delete in next version (> v3.2.0), since this field will exist in both legacy and current
  convert LegacyEventModel.EventInstrumentUpdateOutcomesOddsRequest{..} = \ eventDescription -> CurrentEventModel.EventInstrumentUpdateOutcomesOddsRequest with
    details = (convert details) eventDescription
    status = convert status
    newOutcomes = convert newOutcomes
    ..

instance DAMLUpgrade LegacyEventModel.EventInstrumentNoOutcomes (Text -> CurrentEventModel.EventInstrumentNoOutcomes) where
  -- TODO: Delete in next version (> v3.2.0), since this field will exist in both legacy and current
  convert LegacyEventModel.EventInstrumentNoOutcomes{..} = \ eventDescription -> CurrentEventModel.EventInstrumentNoOutcomes with
    event = (convert event) eventDescription
    ..

instance DAMLUpgrade LegacyEventModel.MarketMap CurrentEventModel.MarketMap where
  convert LegacyEventModel.MarketMap{..} = CurrentEventModel.MarketMap with
    map = convert map
    public = convert observers
    ..