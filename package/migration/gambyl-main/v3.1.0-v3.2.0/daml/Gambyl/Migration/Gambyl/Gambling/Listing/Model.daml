module Gambyl.Migration.Gambyl.Gambling.Listing.Model where

import Gambyl.Migration.Upgrade

import Gambyl.Migration.Gambyl.Gambling.Bet.Odds.Model ()
import Gambyl.Migration.EnetPulse.Events ()

import Legacy.Gambyl.Gambling.Listing.Model qualified as LegacyListingModel
import Current.Gambyl.Gambling.Listing.Model qualified as CurrentListingModel

instance DAMLUpgrade LegacyListingModel.Status CurrentListingModel.Status where
  convert LegacyListingModel.Active = CurrentListingModel.Active
  convert LegacyListingModel.Disabled = CurrentListingModel.Disabled
  convert LegacyListingModel.Fail = CurrentListingModel.Fail

instance DAMLUpgrade LegacyListingModel.GamblingListing CurrentListingModel.GamblingListing where
  convert LegacyListingModel.GamblingListing{..} = CurrentListingModel.GamblingListing with
    public = convert observers
    layOdds = convert layOdds
    backOdds = convert backOdds
    status = convert status
    -- outcome = convert outcome
    -- TODO: Uncomment when there's changes between an legacy and current versions of Enetpulse models
    ..

instance DAMLUpgrade LegacyListingModel.GamblingUpdateListingRequest CurrentListingModel.GamblingUpdateListingRequest where
  convert LegacyListingModel.GamblingUpdateListingRequest{..} = CurrentListingModel.GamblingUpdateListingRequest with
    status = convert status
    ..

instance DAMLUpgrade LegacyListingModel.FailedGamblingUpdateListing CurrentListingModel.FailedGamblingUpdateListing where
  convert LegacyListingModel.FailedGamblingUpdateListing{..} = CurrentListingModel.FailedGamblingUpdateListing with
    ..
