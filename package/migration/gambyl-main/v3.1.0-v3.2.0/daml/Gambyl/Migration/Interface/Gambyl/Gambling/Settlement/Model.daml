module Gambyl.Migration.Interface.Gambyl.Gambling.Settlement.Model where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Gambling.Settlement.Model()

import Legacy.Gambyl.Gambling.Settlement.Model qualified as FromSettlementModel
import Current.Gambyl.Gambling.Settlement.Model qualified as ToSettlementModel

interface IUpgradeExecutedBets where
  viewtype ToSettlementModel.ExecutedBets

  upgradeExecutedBets : ContractId IUpgradeExecutedBets -> Update (ContractId IUpgradeExecutedBets)

  nonconsuming choice UpgradeExecutedBets : MigrationResult (ContractId IUpgradeExecutedBets)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeExecutedBets contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeExecutedBets this self

  interface instance IUpgradeExecutedBets for FromSettlementModel.ExecutedBets where
    view = convert this
    upgradeExecutedBets self = do
      archive self
      toInterfaceContractId @IUpgradeExecutedBets <$> create (convert this : ToSettlementModel.ExecutedBets)

  interface instance IUpgradeExecutedBets for ToSettlementModel.ExecutedBets where
    view = this
    upgradeExecutedBets = pure
