module Gambyl.Migration.Interface.Gambyl.Operator.Role where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Operator.Role()

import Legacy.Gambyl.Operator.Role qualified as LegacyOperatorRole
import Current.Gambyl.Operator.Role qualified as CurrentOperatorRole

interface IUpgradeRole where
  viewtype CurrentOperatorRole.Role

  upgradeRole : ContractId IUpgradeRole -> Update (ContractId IUpgradeRole)

  nonconsuming choice UpgradeRole : MigrationResult (ContractId IUpgradeRole)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeRole contract") $
          migratingParty == (view this).operator
        upgradeRole this self

  interface instance IUpgradeRole for LegacyOperatorRole.Role where
    view = convert this
    upgradeRole self = do
      archive self
      toInterfaceContractId @IUpgradeRole <$> create (convert this : CurrentOperatorRole.Role)

  interface instance IUpgradeRole for CurrentOperatorRole.Role where
    view = this
    upgradeRole = pure
