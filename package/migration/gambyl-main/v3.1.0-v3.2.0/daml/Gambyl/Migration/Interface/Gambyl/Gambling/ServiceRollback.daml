module Gambyl.Migration.Interface.Gambyl.Gambling.ServiceRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Gambling.ServiceRollback ()

import Legacy.Gambyl.Gambling.Service qualified as FromGamblingService
import Current.Gambyl.Gambling.Service qualified as ToGamblingService

interface IUpgradeServiceRollback where
  viewtype FromGamblingService.Service

  upgradeServiceRollback : ContractId IUpgradeServiceRollback -> Update (ContractId IUpgradeServiceRollback)

  nonconsuming choice UpgradeServiceRollback : MigrationResult (ContractId IUpgradeServiceRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeServiceRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeServiceRollback this self

  interface instance IUpgradeServiceRollback for ToGamblingService.Service where
    view = convert this
    upgradeServiceRollback self = do
      archive self
      toInterfaceContractId @IUpgradeServiceRollback <$> create (convert this : FromGamblingService.Service)

  interface instance IUpgradeServiceRollback for FromGamblingService.Service where
    view = this
    upgradeServiceRollback = pure

interface IUpgradeOfferRollback where
  viewtype FromGamblingService.Offer

  upgradeOfferRollback : ContractId IUpgradeOfferRollback -> Update (ContractId IUpgradeOfferRollback)

  nonconsuming choice UpgradeOfferRollback : MigrationResult (ContractId IUpgradeOfferRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeOfferRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeOfferRollback this self

  interface instance IUpgradeOfferRollback for ToGamblingService.Offer where
    view = convert this
    upgradeOfferRollback self = do
      archive self
      toInterfaceContractId @IUpgradeOfferRollback <$> create (convert this : FromGamblingService.Offer)

  interface instance IUpgradeOfferRollback for FromGamblingService.Offer where
    view = this
    upgradeOfferRollback = pure

interface IUpgradeRequestRollback where
  viewtype FromGamblingService.Request

  upgradeRequestRollback : ContractId IUpgradeRequestRollback -> Update (ContractId IUpgradeRequestRollback)

  nonconsuming choice UpgradeRequestRollback : MigrationResult (ContractId IUpgradeRequestRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeRequestRollback contract") $
          migratingParty == (view this).provider
        upgradeRequestRollback this self

  interface instance IUpgradeRequestRollback for ToGamblingService.Request where
    view = convert this
    upgradeRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeRequestRollback <$> create (convert this : FromGamblingService.Request)

  interface instance IUpgradeRequestRollback for FromGamblingService.Request where
    view = this
    upgradeRequestRollback = pure

interface IUpgradeBlockedServiceRollback where
  viewtype FromGamblingService.BlockedService

  upgradeBlockedServiceRollback : ContractId IUpgradeBlockedServiceRollback -> Update (ContractId IUpgradeBlockedServiceRollback)

  nonconsuming choice UpgradeBlockedServiceRollback : MigrationResult (ContractId IUpgradeBlockedServiceRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeBlockedServiceRollback contract") $
          migratingParty == (view this).service.operator
          || migratingParty == (view this).service.provider
        upgradeBlockedServiceRollback this self

  interface instance IUpgradeBlockedServiceRollback for ToGamblingService.BlockedService where
    view = convert this
    upgradeBlockedServiceRollback self = do
      archive self
      toInterfaceContractId @IUpgradeBlockedServiceRollback <$> create (convert this : FromGamblingService.BlockedService)

  interface instance IUpgradeBlockedServiceRollback for FromGamblingService.BlockedService where
    view = this
    upgradeBlockedServiceRollback = pure

interface IUpgradePendingPromotionApplicationRollback where
  viewtype FromGamblingService.PendingPromotionApplication

  upgradePendingPromotionApplicationRollback : ContractId IUpgradePendingPromotionApplicationRollback -> Update (ContractId IUpgradePendingPromotionApplicationRollback)

  nonconsuming choice UpgradePendingPromotionApplicationRollback : MigrationResult (ContractId IUpgradePendingPromotionApplicationRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradePendingPromotionApplicationRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradePendingPromotionApplicationRollback this self

  interface instance IUpgradePendingPromotionApplicationRollback for ToGamblingService.PendingPromotionApplication where
    view = convert this
    upgradePendingPromotionApplicationRollback self = do
      archive self
      toInterfaceContractId @IUpgradePendingPromotionApplicationRollback <$> create (convert this : FromGamblingService.PendingPromotionApplication)

  interface instance IUpgradePendingPromotionApplicationRollback for FromGamblingService.PendingPromotionApplication where
    view = this
    upgradePendingPromotionApplicationRollback = pure
