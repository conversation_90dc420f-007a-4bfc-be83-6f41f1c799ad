module Gambyl.Migration.Interface.Gambyl.Gambling.Bet.ModelRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Gambling.Bet.ModelRollback()

import Legacy.Gambyl.Gambling.Bet.Model qualified as LegacyBetModel
import Current.Gambyl.Gambling.Bet.Model qualified as CurrentBetModel

import Gambyl.Migration.Gambyl.Gambling.Bet.Odds.ModelRollback()
import Gambyl.Migration.Gambyl.Marketing.ModelRollback()

interface IUpgradeBetPlacementRequestRollback where
  viewtype LegacyBetModel.BetPlacementRequest

  upgradeBetPlacementRequestRollback : ContractId IUpgradeBetPlacementRequestRollback -> Update (ContractId IUpgradeBetPlacementRequestRollback)

  nonconsuming choice UpgradeBetPlacementRequestRollback : MigrationResult (ContractId IUpgradeBetPlacementRequestRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeBetPlacementRequestRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeBetPlacementRequestRollback this self

  interface instance IUpgradeBetPlacementRequestRollback for CurrentBetModel.BetPlacementRequest where
    view = convert this
    upgradeBetPlacementRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeBetPlacementRequestRollback <$> create (convert this : LegacyBetModel.BetPlacementRequest)

  interface instance IUpgradeBetPlacementRequestRollback for LegacyBetModel.BetPlacementRequest where
    view = this
    upgradeBetPlacementRequestRollback = pure

interface IUpgradeBetPlacementRequestFlagRollback where
  viewtype LegacyBetModel.BetPlacementRequestFlag

  upgradeBetPlacementRequestFlagRollback : ContractId IUpgradeBetPlacementRequestFlagRollback -> Update (ContractId IUpgradeBetPlacementRequestFlagRollback)

  nonconsuming choice UpgradeBetPlacementRequestFlagRollback : MigrationResult (ContractId IUpgradeBetPlacementRequestFlagRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeBetPlacementRequestFlagRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeBetPlacementRequestFlagRollback this self

  interface instance IUpgradeBetPlacementRequestFlagRollback for CurrentBetModel.BetPlacementRequestFlag where
    view = convert this
    upgradeBetPlacementRequestFlagRollback self = do
      archive self
      toInterfaceContractId @IUpgradeBetPlacementRequestFlagRollback <$> create (convert this : LegacyBetModel.BetPlacementRequestFlag)

  interface instance IUpgradeBetPlacementRequestFlagRollback for LegacyBetModel.BetPlacementRequestFlag where
    view = this
    upgradeBetPlacementRequestFlagRollback = pure

interface IUpgradeBulkBetPlacementRequestFlagRollback where
  viewtype LegacyBetModel.BulkBetPlacementRequestFlag

  upgradeBulkBetPlacementRequestFlagRollback : ContractId IUpgradeBulkBetPlacementRequestFlagRollback -> Update (ContractId IUpgradeBulkBetPlacementRequestFlagRollback)

  nonconsuming choice UpgradeBulkBetPlacementRequestFlagRollback : MigrationResult (ContractId IUpgradeBulkBetPlacementRequestFlagRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeBulkBetPlacementRequestFlagRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeBulkBetPlacementRequestFlagRollback this self

  interface instance IUpgradeBulkBetPlacementRequestFlagRollback for CurrentBetModel.BulkBetPlacementRequestFlag where
    view = convert this
    upgradeBulkBetPlacementRequestFlagRollback self = do
      archive self
      toInterfaceContractId @IUpgradeBulkBetPlacementRequestFlagRollback <$> create (convert this : LegacyBetModel.BulkBetPlacementRequestFlag)

  interface instance IUpgradeBulkBetPlacementRequestFlagRollback for LegacyBetModel.BulkBetPlacementRequestFlag where
    view = this
    upgradeBulkBetPlacementRequestFlagRollback = pure

interface IUpgradeBetPlacementFinalizeRequestRollback where
  viewtype LegacyBetModel.BetPlacementFinalizeRequest

  upgradeBetPlacementFinalizeRequestRollback : ContractId IUpgradeBetPlacementFinalizeRequestRollback -> Update (ContractId IUpgradeBetPlacementFinalizeRequestRollback)

  nonconsuming choice UpgradeBetPlacementFinalizeRequestRollback : MigrationResult (ContractId IUpgradeBetPlacementFinalizeRequestRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeBetPlacementFinalizeRequestRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeBetPlacementFinalizeRequestRollback this self

  interface instance IUpgradeBetPlacementFinalizeRequestRollback for CurrentBetModel.BetPlacementFinalizeRequest where
    view = convert this
    upgradeBetPlacementFinalizeRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeBetPlacementFinalizeRequestRollback <$> create (convert this : LegacyBetModel.BetPlacementFinalizeRequest)

  interface instance IUpgradeBetPlacementFinalizeRequestRollback for LegacyBetModel.BetPlacementFinalizeRequest where
    view = this
    upgradeBetPlacementFinalizeRequestRollback = pure

interface IUpgradeBetPlacementRollback where
  viewtype LegacyBetModel.BetPlacement

  upgradeBetPlacementRollback : ContractId IUpgradeBetPlacementRollback -> Update (ContractId IUpgradeBetPlacementRollback)

  nonconsuming choice UpgradeBetPlacementRollback : MigrationResult (ContractId IUpgradeBetPlacementRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeBetPlacementRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeBetPlacementRollback this self

  interface instance IUpgradeBetPlacementRollback for CurrentBetModel.BetPlacement where
    view = convert this
    upgradeBetPlacementRollback self = do
      archive self
      toInterfaceContractId @IUpgradeBetPlacementRollback <$> create (convert this : LegacyBetModel.BetPlacement)

  interface instance IUpgradeBetPlacementRollback for LegacyBetModel.BetPlacement where
    view = this
    upgradeBetPlacementRollback = pure

interface IUpgradeBetPlacementSplitRequestRollback where
  viewtype LegacyBetModel.BetPlacementSplitRequest

  upgradeBetPlacementSplitRequestRollback : ContractId IUpgradeBetPlacementSplitRequestRollback -> Update (ContractId IUpgradeBetPlacementSplitRequestRollback)

  nonconsuming choice UpgradeBetPlacementSplitRequestRollback : MigrationResult (ContractId IUpgradeBetPlacementSplitRequestRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeBetPlacementSplitRequestRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeBetPlacementSplitRequestRollback this self

  interface instance IUpgradeBetPlacementSplitRequestRollback for CurrentBetModel.BetPlacementSplitRequest where
    view = convert this
    upgradeBetPlacementSplitRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeBetPlacementSplitRequestRollback <$> create (convert this : LegacyBetModel.BetPlacementSplitRequest)

  interface instance IUpgradeBetPlacementSplitRequestRollback for LegacyBetModel.BetPlacementSplitRequest where
    view = this
    upgradeBetPlacementSplitRequestRollback = pure

interface IUpgradeBetPlacementCancelRequestRollback where
  viewtype LegacyBetModel.BetPlacementCancelRequest

  upgradeBetPlacementCancelRequestRollback : ContractId IUpgradeBetPlacementCancelRequestRollback -> Update (ContractId IUpgradeBetPlacementCancelRequestRollback)

  nonconsuming choice UpgradeBetPlacementCancelRequestRollback : MigrationResult (ContractId IUpgradeBetPlacementCancelRequestRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeBetPlacementCancelRequestRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeBetPlacementCancelRequestRollback this self

  interface instance IUpgradeBetPlacementCancelRequestRollback for CurrentBetModel.BetPlacementCancelRequest where
   view = convert this
   upgradeBetPlacementCancelRequestRollback self = do
     archive self
     toInterfaceContractId @IUpgradeBetPlacementCancelRequestRollback <$> create (convert this : LegacyBetModel.BetPlacementCancelRequest)

  interface instance IUpgradeBetPlacementCancelRequestRollback for LegacyBetModel.BetPlacementCancelRequest where
    view = this
    upgradeBetPlacementCancelRequestRollback = pure

interface IUpgradeFinalizeBetPlacementCancelRequestRollback where
  viewtype LegacyBetModel.FinalizeBetPlacementCancelRequest

  upgradeFinalizeBetPlacementCancelRequestRollback : ContractId IUpgradeFinalizeBetPlacementCancelRequestRollback -> Update (ContractId IUpgradeFinalizeBetPlacementCancelRequestRollback)

  nonconsuming choice UpgradeFinalizeBetPlacementCancelRequestRollback : MigrationResult (ContractId IUpgradeFinalizeBetPlacementCancelRequestRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeFinalizeBetPlacementCancelRequestRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeFinalizeBetPlacementCancelRequestRollback this self

  interface instance IUpgradeFinalizeBetPlacementCancelRequestRollback for CurrentBetModel.FinalizeBetPlacementCancelRequest where
    view = convert this
    upgradeFinalizeBetPlacementCancelRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeFinalizeBetPlacementCancelRequestRollback <$> create (convert this : LegacyBetModel.FinalizeBetPlacementCancelRequest)

  interface instance IUpgradeFinalizeBetPlacementCancelRequestRollback for LegacyBetModel.FinalizeBetPlacementCancelRequest where
    view = this
    upgradeFinalizeBetPlacementCancelRequestRollback = pure


interface IUpgradeCancelledBetPlacementRollback where
  viewtype LegacyBetModel.CancelledBetPlacement

  upgradeCancelledBetPlacementRollback : ContractId IUpgradeCancelledBetPlacementRollback -> Update (ContractId IUpgradeCancelledBetPlacementRollback)

  nonconsuming choice UpgradeCancelledBetPlacementRollback : MigrationResult (ContractId IUpgradeCancelledBetPlacementRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeCancelledBetPlacementRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeCancelledBetPlacementRollback this self

  interface instance IUpgradeCancelledBetPlacementRollback for CurrentBetModel.CancelledBetPlacement where
    view = convert this
    upgradeCancelledBetPlacementRollback self = do
      archive self
      toInterfaceContractId @IUpgradeCancelledBetPlacementRollback <$> create (convert this : LegacyBetModel.CancelledBetPlacement)

  interface instance IUpgradeCancelledBetPlacementRollback for LegacyBetModel.CancelledBetPlacement where
    view = this
    upgradeCancelledBetPlacementRollback = pure


interface IUpgradeBetHistoryRollback where
  viewtype LegacyBetModel.BetHistory

  upgradeBetHistoryRollback : ContractId IUpgradeBetHistoryRollback -> Update (ContractId IUpgradeBetHistoryRollback)

  nonconsuming choice UpgradeBetHistoryRollback : MigrationResult (ContractId IUpgradeBetHistoryRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeBetHistoryRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeBetHistoryRollback this self

  interface instance IUpgradeBetHistoryRollback for CurrentBetModel.BetHistory where
    view = convert this
    upgradeBetHistoryRollback self = do
      archive self
      toInterfaceContractId @IUpgradeBetHistoryRollback <$> create (convert this : LegacyBetModel.BetHistory)

  interface instance IUpgradeBetHistoryRollback for LegacyBetModel.BetHistory where
    view = this
    upgradeBetHistoryRollback = pure


interface IUpgradeBulkBetPlacementRequestRollback where
  viewtype LegacyBetModel.BulkBetPlacementRequest

  upgradeBulkBetPlacementRequestRollback : ContractId IUpgradeBulkBetPlacementRequestRollback -> Update (ContractId IUpgradeBulkBetPlacementRequestRollback)

  nonconsuming choice UpgradeBulkBetPlacementRequestRollback : MigrationResult (ContractId IUpgradeBulkBetPlacementRequestRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeBulkBetPlacementRequestRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeBulkBetPlacementRequestRollback this self

  interface instance IUpgradeBulkBetPlacementRequestRollback for CurrentBetModel.BulkBetPlacementRequest where
    view = convert this
    upgradeBulkBetPlacementRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeBulkBetPlacementRequestRollback <$> create (convert this : LegacyBetModel.BulkBetPlacementRequest)

  interface instance IUpgradeBulkBetPlacementRequestRollback for LegacyBetModel.BulkBetPlacementRequest where
    view = this
    upgradeBulkBetPlacementRequestRollback = pure

interface IUpgradeBulkBetPlacementFinalizeRequestRollback where
  viewtype LegacyBetModel.BulkBetPlacementFinalizeRequest

  stakeholders : [Party]
  upgradeBulkBetPlacementFinalizeRequestRollback : ContractId IUpgradeBulkBetPlacementFinalizeRequestRollback -> Update (ContractId IUpgradeBulkBetPlacementFinalizeRequestRollback)

  nonconsuming choice UpgradeBulkBetPlacementFinalizeRequestRollback : MigrationResult (ContractId IUpgradeBulkBetPlacementFinalizeRequestRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeBulkBetPlacementFinalizeRequest contract") $
          migratingParty `elem` stakeholders this
        upgradeBulkBetPlacementFinalizeRequestRollback this self

  interface instance IUpgradeBulkBetPlacementFinalizeRequestRollback for CurrentBetModel.BulkBetPlacementFinalizeRequest where
    view = convert this
    stakeholders = [this.operator, this.provider]
    upgradeBulkBetPlacementFinalizeRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeBulkBetPlacementFinalizeRequestRollback <$> create (convert this : LegacyBetModel.BulkBetPlacementFinalizeRequest)

  interface instance IUpgradeBulkBetPlacementFinalizeRequestRollback for LegacyBetModel.BulkBetPlacementFinalizeRequest where
    view = this
    stakeholders = [this.operator, this.provider]
    upgradeBulkBetPlacementFinalizeRequestRollback = pure

interface IUpgradeBulkBetPlacementRollback where
  viewtype LegacyBetModel.BulkBetPlacement

  upgradeBulkBetPlacementRollback : ContractId IUpgradeBulkBetPlacementRollback -> Update (ContractId IUpgradeBulkBetPlacementRollback)

  nonconsuming choice UpgradeBulkBetPlacementRollback : MigrationResult (ContractId IUpgradeBulkBetPlacementRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeBulkBetPlacementRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeBulkBetPlacementRollback this self

  interface instance IUpgradeBulkBetPlacementRollback for CurrentBetModel.BulkBetPlacement where
    view = convert this
    upgradeBulkBetPlacementRollback self = do
      archive self
      toInterfaceContractId @IUpgradeBulkBetPlacementRollback <$> create (convert this : LegacyBetModel.BulkBetPlacement)

  interface instance IUpgradeBulkBetPlacementRollback for LegacyBetModel.BulkBetPlacement where
    view = this
    upgradeBulkBetPlacementRollback = pure
