module Gambyl.Migration.Interface.Gambyl.Gambling.Event.Service where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Gambling.Event.Service ()

import Legacy.Gambyl.Gambling.Event.Service qualified as LegacyEventService
import Current.Gambyl.Gambling.Event.Service qualified as CurrentEventService

interface IUpgradeService where
  viewtype CurrentEventService.Service

  upgradeService : ContractId IUpgradeService -> Update (ContractId IUpgradeService)

  nonconsuming choice UpgradeService : MigrationResult (ContractId IUpgradeService)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeService contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeService this self

  interface instance IUpgradeService for LegacyEventService.Service where
    view = convert this
    upgradeService self = do
      archive self
      toInterfaceContractId @IUpgradeService <$> create (convert this : CurrentEventService.Service)

  interface instance IUpgradeService for CurrentEventService.Service where
    view = this
    upgradeService = pure

interface IUpgradeOffer where
  viewtype CurrentEventService.Offer

  upgradeOffer : ContractId IUpgradeOffer -> Update (ContractId IUpgradeOffer)

  nonconsuming choice UpgradeOffer : MigrationResult (ContractId IUpgradeOffer)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeOffer contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeOffer this self

  interface instance IUpgradeOffer for LegacyEventService.Offer where
    view = convert this
    upgradeOffer self = do
      archive self
      toInterfaceContractId @IUpgradeOffer <$> create (convert this : CurrentEventService.Offer)

  interface instance IUpgradeOffer for CurrentEventService.Offer where
    view = this
    upgradeOffer = pure

interface IUpgradeRequest where
  viewtype CurrentEventService.Request

  upgradeRequest : ContractId IUpgradeRequest -> Update (ContractId IUpgradeRequest)

  nonconsuming choice UpgradeRequest : MigrationResult (ContractId IUpgradeRequest)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeRequest contract") $
          migratingParty == (view this).provider
        upgradeRequest this self

  interface instance IUpgradeRequest for LegacyEventService.Request where
    view = convert this
    upgradeRequest self = do
      archive self
      toInterfaceContractId @IUpgradeRequest <$> create (convert this : CurrentEventService.Request)

  interface instance IUpgradeRequest for CurrentEventService.Request where
    view = this
    upgradeRequest = pure
