module Gambyl.Migration.Interface.Gambyl.Gambling.Identity.Model where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Gambling.Identity.Model()

import Legacy.Gambyl.Gambling.Identity.Model qualified as LegacyIdentityModel
import Current.Gambyl.Gambling.Identity.Model qualified as CurrentIdentityModel

interface IUpgradePendingIdentity where
  viewtype CurrentIdentityModel.PendingIdentity

  upgradePendingIdentity : ContractId IUpgradePendingIdentity -> Update (ContractId IUpgradePendingIdentity)

  choice ArchiveFaultyPendingIdentityContract : ()
    with migratingParty: Party
    controller migratingParty
    do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradePendingIdentity contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider

  nonconsuming choice UpgradePendingIdentity : MigrationResult (ContractId IUpgradePendingIdentity)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradePendingIdentity contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradePendingIdentity this self

  interface instance IUpgradePendingIdentity for LegacyIdentityModel.PendingIdentity where
    view = convert this
    upgradePendingIdentity self = do
      archive self
      toInterfaceContractId @IUpgradePendingIdentity <$> create (convert this : CurrentIdentityModel.PendingIdentity)

  interface instance IUpgradePendingIdentity for CurrentIdentityModel.PendingIdentity where
    view = this
    upgradePendingIdentity = pure

interface IUpgradeGamblerUnverifiedIdentityRequest where
  viewtype CurrentIdentityModel.GamblerUnverifiedIdentityRequest

  upgradeGamblerUnverifiedIdentityRequest : ContractId IUpgradeGamblerUnverifiedIdentityRequest -> Update (ContractId IUpgradeGamblerUnverifiedIdentityRequest)

  nonconsuming choice UpgradeGamblerUnverifiedIdentityRequest : MigrationResult (ContractId IUpgradeGamblerUnverifiedIdentityRequest)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeGamblerUnverifiedIdentityRequest contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeGamblerUnverifiedIdentityRequest this self

  interface instance IUpgradeGamblerUnverifiedIdentityRequest for LegacyIdentityModel.GamblerUnverifiedIdentityRequest where
    view = convert this
    upgradeGamblerUnverifiedIdentityRequest self = do
      archive self
      toInterfaceContractId @IUpgradeGamblerUnverifiedIdentityRequest <$> create (convert this : CurrentIdentityModel.GamblerUnverifiedIdentityRequest)

  interface instance IUpgradeGamblerUnverifiedIdentityRequest for CurrentIdentityModel.GamblerUnverifiedIdentityRequest where
    view = this
    upgradeGamblerUnverifiedIdentityRequest = pure

interface IUpgradeGamblerUnverifiedIdentity where
  viewtype CurrentIdentityModel.GamblerUnverifiedIdentity

  upgradeGamblerUnverifiedIdentity : ContractId IUpgradeGamblerUnverifiedIdentity -> Update (ContractId IUpgradeGamblerUnverifiedIdentity)

  nonconsuming choice UpgradeGamblerUnverifiedIdentity : MigrationResult (ContractId IUpgradeGamblerUnverifiedIdentity)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeGamblerUnverifiedIdentity contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeGamblerUnverifiedIdentity this self

  interface instance IUpgradeGamblerUnverifiedIdentity for LegacyIdentityModel.GamblerUnverifiedIdentity where
    view = convert this
    upgradeGamblerUnverifiedIdentity self = do
      archive self
      toInterfaceContractId @IUpgradeGamblerUnverifiedIdentity <$> create (convert this : CurrentIdentityModel.GamblerUnverifiedIdentity)

  interface instance IUpgradeGamblerUnverifiedIdentity for CurrentIdentityModel.GamblerUnverifiedIdentity where
    view = this
    upgradeGamblerUnverifiedIdentity = pure

interface IUpgradeGamblerIdentity where
  viewtype CurrentIdentityModel.GamblerIdentity

  upgradeGamblerIdentity : ContractId IUpgradeGamblerIdentity -> Update (ContractId IUpgradeGamblerIdentity)

  nonconsuming choice UpgradeGamblerIdentity : MigrationResult (ContractId IUpgradeGamblerIdentity)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeGamblerIdentity contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeGamblerIdentity this self

  interface instance IUpgradeGamblerIdentity for LegacyIdentityModel.GamblerIdentity where
    view = convert this
    upgradeGamblerIdentity self = do
      archive self
      toInterfaceContractId @IUpgradeGamblerIdentity <$> create (convert this : CurrentIdentityModel.GamblerIdentity)

  interface instance IUpgradeGamblerIdentity for CurrentIdentityModel.GamblerIdentity where
    view = this
    upgradeGamblerIdentity = pure

interface IUpgradeRejectedIdentity where
  viewtype CurrentIdentityModel.RejectedIdentity

  upgradeRejectedIdentity : ContractId IUpgradeRejectedIdentity -> Update (ContractId IUpgradeRejectedIdentity)

  nonconsuming choice UpgradeRejectedIdentity : MigrationResult (ContractId IUpgradeRejectedIdentity)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeRejectedIdentity contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeRejectedIdentity this self

  interface instance IUpgradeRejectedIdentity for LegacyIdentityModel.RejectedIdentity where
    view = convert this
    upgradeRejectedIdentity self = do
      archive self
      toInterfaceContractId @IUpgradeRejectedIdentity <$> create (convert this : CurrentIdentityModel.RejectedIdentity)

  interface instance IUpgradeRejectedIdentity for CurrentIdentityModel.RejectedIdentity where
    view = this
    upgradeRejectedIdentity = pure
