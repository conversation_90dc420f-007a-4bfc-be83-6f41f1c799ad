# for config file options, refer to
# https://docs.daml.com/tools/assistant.html#project-config-file-daml-yaml

sdk-version: 2.9.4
name: gambyl-main-migration
source: daml
init-script:
parties:
  - Operator
version: ${GAMBYL_MAIN_MIGRATION_VERSION}
dependencies:
  - daml-prim
  - daml-stdlib
  - daml-script
data-dependencies:
  # External Dependencies
  # - ../../../../ext-pkg/da-marketplace-${DA_MARKETPLACE_CURRENT_VERSION}.dar
  - ../../../../ext-pkg/da-marketplace/${DA_MARKETPLACE_CURRENT_VERSION}/da-marketplace-${DA_MARKETPLACE_CURRENT_VERSION}.dar
  # - ../../../../ext-pkg/gambyl-enetpulse-impl-${GAMBYL_ENETPULSE_IMPL_LEGACY_VERSION}.dar
  # - ../../../../ext-pkg/gambyl-jumio-impl-${GAMBYL_JUMIO_IMPL_LEGACY_VERSION}.dar
  # - ../../../../ext-pkg/gambyl-ledger-${GAMBYL_MAIN_IMPL_LEGACY_VERSION}.dar
  # Local Dependencies
  - ../../../../build/implementation/gambyl-main-impl-${GAMBYL_MAIN_IMPL_LEGACY_VERSION}.dar

  - ../../../../build/migration/gambyl-enetpulse-v${GAMBYL_ENETPULSE_IMPL_LEGACY_VERSION}-v${GAMBYL_ENETPULSE_IMPL_CURRENT_VERSION}-migration-1.0.0.dar
  - ../../../../build/migration/gambyl-jumio-v${GAMBYL_JUMIO_IMPL_LEGACY_VERSION}-v${GAMBYL_JUMIO_IMPL_CURRENT_VERSION}-migration-1.0.0.dar
  - ../../../../build/migration/gambyl-migration-utils-${GAMBYL_MIGRATION_UTILS_VERSION}.dar
  - ../../../../build/implementation/gambyl-main-impl-${GAMBYL_MAIN_IMPL_CURRENT_VERSION}.dar
  - ../../../../build/implementation/gambyl-jumio-impl-${GAMBYL_JUMIO_IMPL_CURRENT_VERSION}.dar
  - ../../../../build/implementation/gambyl-enetpulse-impl-${GAMBYL_ENETPULSE_IMPL_CURRENT_VERSION}.dar
  - ../../../../build/implementation/gambyl-moneymatrix-impl-${GAMBYL_MONEYMATRIX_IMPL_CURRENT_VERSION}.dar
  - ../../../../build/implementation/gambyl-quickbooks-impl-${GAMBYL_QUICKBOOKS_IMPL_CURRENT_VERSION}.dar
build-options:
  - --ghc-option=-Wall
  - --ghc-option=-Wno-name-shadowing
  - --ghc-option=-Wno-unused-do-bind
  - --ghc-option=-fno-warn-orphans
  - --ghc-option=-Werror
  - --output=../../../../build/migration/gambyl-main-v${GAMBYL_MAIN_IMPL_LEGACY_VERSION}-v${GAMBYL_MAIN_IMPL_CURRENT_VERSION}-migration-${GAMBYL_MAIN_MIGRATION_VERSION}.dar
module-prefixes:
  gambyl-main-impl-${GAMBYL_MAIN_IMPL_LEGACY_VERSION}: Legacy
  gambyl-enetpulse-impl-${GAMBYL_ENETPULSE_IMPL_LEGACY_VERSION}: Legacy
  # jumio-integration-model-${GAMBYL_JUMIO_IMPL_LEGACY_VERSION}: Legacy
  gambyl-main-impl-${GAMBYL_MAIN_IMPL_CURRENT_VERSION}: Current
  gambyl-enetpulse-impl-${GAMBYL_ENETPULSE_IMPL_CURRENT_VERSION}: Current
  gambyl-jumio-impl-${GAMBYL_JUMIO_IMPL_CURRENT_VERSION}: Current