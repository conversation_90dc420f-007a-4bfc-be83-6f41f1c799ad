module Gambyl.Migration.Gambyl.Marketing.Service where

import Gambyl.Migration.Upgrade

import Legacy.Gambyl.Marketing.Service qualified as LegacyMarketingService
import Current.Gambyl.Marketing.Service qualified as CurrentMarketingService

instance DAMLUpgrade LegacyMarketingService.Service CurrentMarketingService.Service where
  convert LegacyMarketingService.Service{..} = CurrentMarketingService.Service with
    ..

instance DAMLUpgrade LegacyMarketingService.Offer CurrentMarketingService.Offer where
  convert LegacyMarketingService.Offer{..} = CurrentMarketingService.Offer with
    ..

instance DAMLUpgrade LegacyMarketingService.Request CurrentMarketingService.Request where
  convert LegacyMarketingService.Request{..} = CurrentMarketingService.Request with
    ..
