module Gambyl.Migration.Gambyl.Gambling.Role where

import Gambyl.Migration.Upgrade

import Gambyl.Migration.Gambyl.Gambling.Service ()

import Legacy.Gambyl.Gambling.Role qualified as FromGamblingRole
import Current.Gambyl.Gambling.Role qualified as ToGamblingRole

instance DAMLUpgrade FromGamblingRole.Role ToGamblingRole.Role where
  convert FromGamblingRole.Role{..} = ToGamblingRole.Role with
    ..

instance DAMLUpgrade FromGamblingRole.Offer ToGamblingRole.Offer where
  convert FromGamblingRole.Offer{..} = ToGamblingRole.Offer with
    ..

instance DAMLUpgrade FromGamblingRole.Request ToGamblingRole.Request where
  convert FromGamblingRole.Request{..} = ToGamblingRole.Request with
    ..
