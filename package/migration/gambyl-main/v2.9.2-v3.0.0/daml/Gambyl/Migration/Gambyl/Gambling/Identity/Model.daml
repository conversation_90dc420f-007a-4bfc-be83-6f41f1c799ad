module Gambyl.Migration.Gambyl.Gambling.Identity.Model where

import Gambyl.Migration.Upgrade

import Gambyl.Migration.Jumio.Identity ()

import Legacy.Gambyl.Gambling.Identity.Model qualified as LegacyIdentityModel
import Current.Gambyl.Gambling.Identity.Model qualified as CurrentIdentityModel

instance DAMLUpgrade LegacyIdentityModel.PendingIdentity CurrentIdentityModel.PendingIdentity where
    convert LegacyIdentityModel.PendingIdentity{..} = CurrentIdentityModel.PendingIdentity with
        ..

instance DAMLUpgrade LegacyIdentityModel.GamblerUnverifiedIdentityRequest CurrentIdentityModel.GamblerUnverifiedIdentityRequest where
  convert LegacyIdentityModel.GamblerUnverifiedIdentityRequest{..} = CurrentIdentityModel.GamblerUnverifiedIdentityRequest with
        ..

instance DAMLUpgrade LegacyIdentityModel.GamblerUnverifiedIdentity CurrentIdentityModel.GamblerUnverifiedIdentity where
  convert LegacyIdentityModel.GamblerUnverifiedIdentity{..} = CurrentIdentityModel.GamblerUnverifiedIdentity with
        ..

instance DAMLUpgrade LegacyIdentityModel.GamblerIdentity CurrentIdentityModel.GamblerIdentity where
    convert LegacyIdentityModel.GamblerIdentity{..} = CurrentIdentityModel.GamblerIdentity with
      jumioUserData = convert jumioUserData
      ..

instance DAMLUpgrade LegacyIdentityModel.RejectedIdentity CurrentIdentityModel.RejectedIdentity where
  convert LegacyIdentityModel.RejectedIdentity{..} = CurrentIdentityModel.RejectedIdentity with
        ..