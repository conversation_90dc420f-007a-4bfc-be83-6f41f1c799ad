module Gambyl.Migration.Gambyl.Gambling.Settlement.ModelRollback where

import Gambyl.Migration.Upgrade

import Gambyl.Migration.Gambyl.Gambling.Event.ModelRollback ()

import Legacy.Gambyl.Gambling.Settlement.Model qualified as FromSettlementModel
import Current.Gambyl.Gambling.Settlement.Model qualified as ToSettlementModel

instance DAMLUpgrade ToSettlementModel.ExecutedBets FromSettlementModel.ExecutedBets where
  convert ToSettlementModel.ExecutedBets{..} = FromSettlementModel.ExecutedBets with
    executedOdd = convert executedOdd
    ..
