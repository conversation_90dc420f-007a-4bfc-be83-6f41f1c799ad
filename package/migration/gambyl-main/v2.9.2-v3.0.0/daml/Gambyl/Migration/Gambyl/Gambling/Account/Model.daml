module Gambyl.Migration.Gambyl.Gambling.Account.Model where

import Gambyl.Migration.Upgrade

import Gambyl.Migration.Gambyl.Gambling.Bet.Model ()
import Gambyl.Migration.Gambyl.Gambling.Event.Model ()
import Gambyl.Migration.Gambyl.Gambling.Model ()
import Gambyl.Migration.Gambyl.Marketing.Model ()

import Legacy.Gambyl.Gambling.Account.Model qualified as LegacyAccountModel
import Current.Gambyl.Gambling.Account.Model qualified as CurrentAccountModel


instance DAMLUpgrade LegacyAccountModel.TimedLimit CurrentAccountModel.TimedLimit where
  convert LegacyAccountModel.Daily = CurrentAccountModel.Daily
  convert LegacyAccountModel.Weekly = CurrentAccountModel.Weekly
  convert LegacyAccountModel.Monthly = CurrentAccountModel.Monthly
  convert LegacyAccountModel.Yearly = CurrentAccountModel.Yearly

instance DAMLUpgrade LegacyAccountModel.AccruedLimit CurrentAccountModel.AccruedLimit where
  convert LegacyAccountModel.AccruedLimit{..} = CurrentAccountModel.AccruedLimit with
        ..

instance DAMLUpgrade LegacyAccountModel.Account CurrentAccountModel.Account where
  convert LegacyAccountModel.Account{..} = CurrentAccountModel.Account with
        totalMainBalance = convert totalMainBalance
        totalWithdrawBalance = convert totalWithdrawBalance
        totalBetBalance = convert totalBetBalance
        totalBonusBalance = convert totalBonusBalance
        totalBonusBetBalance = convert totalBonusBetBalance
        favouriteMarkets = convert favouriteMarkets
        betHistory = convert betHistory
        totalFees = convert totalFees
        transactionHistory = convert transactionHistory
        depositLimit = convert depositLimit
        ..

instance DAMLUpgrade LegacyAccountModel.WithdrawRequestForApproval CurrentAccountModel.WithdrawRequestForApproval where
  convert LegacyAccountModel.WithdrawRequestForApproval{..} = CurrentAccountModel.WithdrawRequestForApproval with
        promotion = convert promotion
        ..

instance DAMLUpgrade LegacyAccountModel.DepositRequestForApproval CurrentAccountModel.DepositRequestForApproval where
  convert LegacyAccountModel.DepositRequestForApproval{..} = CurrentAccountModel.DepositRequestForApproval with
        promotion = convert promotion
        ..

instance DAMLUpgrade LegacyAccountModel.TransactionHistory CurrentAccountModel.TransactionHistory where
  convert LegacyAccountModel.TransactionHistory{..} = CurrentAccountModel.TransactionHistory with
        confirmedAmount = convert confirmedAmount
        transactionType = convert transactionType
        ..

instance DAMLUpgrade LegacyAccountModel.PendingTransaction CurrentAccountModel.PendingTransaction where
  convert LegacyAccountModel.PendingTransaction{..} = CurrentAccountModel.PendingTransaction with
        transactionType = convert transactionType
        requestedAmount = convert requestedAmount
        ..

instance DAMLUpgrade LegacyAccountModel.PendingDeposits CurrentAccountModel.PendingDeposits where
  convert LegacyAccountModel.PendingDeposits{..} = CurrentAccountModel.PendingDeposits with
        ..

instance DAMLUpgrade LegacyAccountModel.Flag CurrentAccountModel.Flag where
  convert LegacyAccountModel.Flag{..} = CurrentAccountModel.Flag with
        amount = convert amount
        ..
