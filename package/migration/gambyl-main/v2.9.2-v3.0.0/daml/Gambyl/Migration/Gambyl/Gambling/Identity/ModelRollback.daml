module Gambyl.Migration.Gambyl.Gambling.Identity.ModelRollback where

import Gambyl.Migration.Upgrade

import Gambyl.Migration.Jumio.IdentityRollback ()

import Legacy.Gambyl.Gambling.Identity.Model qualified as LegacyIdentityModel
import Current.Gambyl.Gambling.Identity.Model qualified as CurrentIdentityModel

instance DAMLUpgrade CurrentIdentityModel.PendingIdentity LegacyIdentityModel.PendingIdentity where
    convert CurrentIdentityModel.PendingIdentity{..} = LegacyIdentityModel.PendingIdentity with
      ..

instance DAMLUpgrade CurrentIdentityModel.GamblerUnverifiedIdentityRequest LegacyIdentityModel.GamblerUnverifiedIdentityRequest where
  convert CurrentIdentityModel.GamblerUnverifiedIdentityRequest{..} = LegacyIdentityModel.GamblerUnverifiedIdentityRequest with
    ..

instance DAMLUpgrade CurrentIdentityModel.GamblerUnverifiedIdentity LegacyIdentityModel.GamblerUnverifiedIdentity where
  convert CurrentIdentityModel.GamblerUnverifiedIdentity{..} = LegacyIdentityModel.GamblerUnverifiedIdentity with
    ..

instance DAMLUpgrade CurrentIdentityModel.GamblerIdentity LegacyIdentityModel.GamblerIdentity where
    convert CurrentIdentityModel.GamblerIdentity{..} = LegacyIdentityModel.GamblerIdentity with
      jumioUserData = convert jumioUserData
      ..

instance DAMLUpgrade CurrentIdentityModel.RejectedIdentity LegacyIdentityModel.RejectedIdentity where
  convert CurrentIdentityModel.RejectedIdentity{..} = LegacyIdentityModel.RejectedIdentity with
      ..
