module Gambyl.Migration.Interface.Gambyl.Gambling.Event.ModelRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Gambling.Event.ModelRollback ()

import Legacy.Gambyl.Gambling.Event.Model qualified as LegacyEventModel
import Current.Gambyl.Gambling.Event.Model qualified as CurrentEventModel

interface IUpgradeEventInstrumentRequestRollback where
  viewtype LegacyEventModel.EventInstrumentRequest

  upgradeEventInstrumentRequestRollback : ContractId IUpgradeEventInstrumentRequestRollback -> Update (ContractId IUpgradeEventInstrumentRequestRollback)

  nonconsuming choice UpgradeEventInstrumentRequestRollback : MigrationResult (ContractId IUpgradeEventInstrumentRequestRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeEventInstrumentRequestRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeEventInstrumentRequestRollback this self

  interface instance IUpgradeEventInstrumentRequestRollback for CurrentEventModel.EventInstrumentRequest where
    view = convert this
    upgradeEventInstrumentRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeEventInstrumentRequestRollback <$> create (convert this : LegacyEventModel.EventInstrumentRequest)

  interface instance IUpgradeEventInstrumentRequestRollback for LegacyEventModel.EventInstrumentRequest where
    view = this
    upgradeEventInstrumentRequestRollback = pure

interface IUpgradeEventInstrumentStatusUpdateRequestRollback where
  viewtype LegacyEventModel.EventInstrumentStatusUpdateRequest

  upgradeEventInstrumentStatusUpdateRequestRollback : ContractId IUpgradeEventInstrumentStatusUpdateRequestRollback -> Update (ContractId IUpgradeEventInstrumentStatusUpdateRequestRollback)

  nonconsuming choice UpgradeEventInstrumentStatusUpdateRequestRollback : MigrationResult (ContractId IUpgradeEventInstrumentStatusUpdateRequestRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeEventInstrumentStatusUpdateRequestRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeEventInstrumentStatusUpdateRequestRollback this self

  interface instance IUpgradeEventInstrumentStatusUpdateRequestRollback for CurrentEventModel.EventInstrumentStatusUpdateRequest where
    view = convert this
    upgradeEventInstrumentStatusUpdateRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeEventInstrumentStatusUpdateRequestRollback <$> create (convert this : LegacyEventModel.EventInstrumentStatusUpdateRequest)

  interface instance IUpgradeEventInstrumentStatusUpdateRequestRollback for LegacyEventModel.EventInstrumentStatusUpdateRequest where
    view = this
    upgradeEventInstrumentStatusUpdateRequestRollback = pure

interface IUpgradeEventInstrumentUpdateRequestRollback where
  viewtype LegacyEventModel.EventInstrumentUpdateRequest

  upgradeEventInstrumentUpdateRequestRollback : ContractId IUpgradeEventInstrumentUpdateRequestRollback -> Update (ContractId IUpgradeEventInstrumentUpdateRequestRollback)

  nonconsuming choice UpgradeEventInstrumentUpdateRequestRollback : MigrationResult (ContractId IUpgradeEventInstrumentUpdateRequestRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeEventInstrumentUpdateRequestRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeEventInstrumentUpdateRequestRollback this self

  interface instance IUpgradeEventInstrumentUpdateRequestRollback for CurrentEventModel.EventInstrumentUpdateRequest where
    view = convert this
    upgradeEventInstrumentUpdateRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeEventInstrumentUpdateRequestRollback <$> create (convert this : LegacyEventModel.EventInstrumentUpdateRequest)

  interface instance IUpgradeEventInstrumentUpdateRequestRollback for LegacyEventModel.EventInstrumentUpdateRequest where
    view = this
    upgradeEventInstrumentUpdateRequestRollback = pure

interface IUpgradeEventInstrumentRollback where
  viewtype LegacyEventModel.EventInstrument

  upgradeEventInstrumentRollback : ContractId IUpgradeEventInstrumentRollback -> Update (ContractId IUpgradeEventInstrumentRollback)

  nonconsuming choice UpgradeEventInstrumentRollback : MigrationResult (ContractId IUpgradeEventInstrumentRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeEventInstrumentRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeEventInstrumentRollback this self

  interface instance IUpgradeEventInstrumentRollback for CurrentEventModel.EventInstrument where
    view = convert this
    upgradeEventInstrumentRollback self = do
      archive self
      toInterfaceContractId @IUpgradeEventInstrumentRollback <$> create (convert this : LegacyEventModel.EventInstrument)

  interface instance IUpgradeEventInstrumentRollback for LegacyEventModel.EventInstrument where
    view = this
    upgradeEventInstrumentRollback = pure

interface IUpgradeEventInstrumentToggleFeaturedRequestRollback where
  viewtype LegacyEventModel.EventInstrumentToggleFeaturedRequest

  upgradeEventInstrumentToggleFeaturedRequestRollback : ContractId IUpgradeEventInstrumentToggleFeaturedRequestRollback -> Update (ContractId IUpgradeEventInstrumentToggleFeaturedRequestRollback)

  nonconsuming choice UpgradeEventInstrumentToggleFeaturedRequestRollback : MigrationResult (ContractId IUpgradeEventInstrumentToggleFeaturedRequestRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeEventInstrumentToggleFeaturedRequestRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeEventInstrumentToggleFeaturedRequestRollback this self

  interface instance IUpgradeEventInstrumentToggleFeaturedRequestRollback for CurrentEventModel.EventInstrumentToggleFeaturedRequest where
    view = convert this
    upgradeEventInstrumentToggleFeaturedRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeEventInstrumentToggleFeaturedRequestRollback <$> create (convert this : LegacyEventModel.EventInstrumentToggleFeaturedRequest)

  interface instance IUpgradeEventInstrumentToggleFeaturedRequestRollback for LegacyEventModel.EventInstrumentToggleFeaturedRequest where
    view = this
    upgradeEventInstrumentToggleFeaturedRequestRollback = pure

interface IUpgradeEventInstrumentCancelRequestRollback where
  viewtype LegacyEventModel.EventInstrumentCancelRequest

  upgradeEventInstrumentCancelRequestRollback : ContractId IUpgradeEventInstrumentCancelRequestRollback -> Update (ContractId IUpgradeEventInstrumentCancelRequestRollback)

  nonconsuming choice UpgradeEventInstrumentCancelRequestRollback : MigrationResult (ContractId IUpgradeEventInstrumentCancelRequestRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeEventInstrumentCancelRequestRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeEventInstrumentCancelRequestRollback this self

  interface instance IUpgradeEventInstrumentCancelRequestRollback for CurrentEventModel.EventInstrumentCancelRequest where
    view = convert this
    upgradeEventInstrumentCancelRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeEventInstrumentCancelRequestRollback <$> create (convert this : LegacyEventModel.EventInstrumentCancelRequest)

  interface instance IUpgradeEventInstrumentCancelRequestRollback for LegacyEventModel.EventInstrumentCancelRequest where
    view = this
    upgradeEventInstrumentCancelRequestRollback = pure

interface IUpgradeEventInstrumentReinstateRequestRollback where
  viewtype LegacyEventModel.EventInstrumentReinstateRequest

  upgradeEventInstrumentReinstateRequestRollback : ContractId IUpgradeEventInstrumentReinstateRequestRollback -> Update (ContractId IUpgradeEventInstrumentReinstateRequestRollback)

  nonconsuming choice UpgradeEventInstrumentReinstateRequestRollback : MigrationResult (ContractId IUpgradeEventInstrumentReinstateRequestRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeEventInstrumentReinstateRequestRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeEventInstrumentReinstateRequestRollback this self

  interface instance IUpgradeEventInstrumentReinstateRequestRollback for CurrentEventModel.EventInstrumentReinstateRequest where
    view = convert this
    upgradeEventInstrumentReinstateRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeEventInstrumentReinstateRequestRollback <$> create (convert this : LegacyEventModel.EventInstrumentReinstateRequest)

  interface instance IUpgradeEventInstrumentReinstateRequestRollback for LegacyEventModel.EventInstrumentReinstateRequest where
    view = this
    upgradeEventInstrumentReinstateRequestRollback = pure

interface IUpgradeEventInstrumentUpdateOutcomesOddsRequestRollback where
  viewtype LegacyEventModel.EventInstrumentUpdateOutcomesOddsRequest

  upgradeEventInstrumentUpdateOutcomesOddsRequestRollback : ContractId IUpgradeEventInstrumentUpdateOutcomesOddsRequestRollback -> Update (ContractId IUpgradeEventInstrumentUpdateOutcomesOddsRequestRollback)

  nonconsuming choice UpgradeEventInstrumentUpdateOutcomesOddsRequestRollback : MigrationResult (ContractId IUpgradeEventInstrumentUpdateOutcomesOddsRequestRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeEventInstrumentUpdateOutcomesOddsRequestRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeEventInstrumentUpdateOutcomesOddsRequestRollback this self

  interface instance IUpgradeEventInstrumentUpdateOutcomesOddsRequestRollback for CurrentEventModel.EventInstrumentUpdateOutcomesOddsRequest where
    view = convert this
    upgradeEventInstrumentUpdateOutcomesOddsRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeEventInstrumentUpdateOutcomesOddsRequestRollback <$> create (convert this : LegacyEventModel.EventInstrumentUpdateOutcomesOddsRequest)

  interface instance IUpgradeEventInstrumentUpdateOutcomesOddsRequestRollback for LegacyEventModel.EventInstrumentUpdateOutcomesOddsRequest where
    view = this
    upgradeEventInstrumentUpdateOutcomesOddsRequestRollback = pure

interface IUpgradeEventInstrumentNoOutcomesRollback where
  viewtype LegacyEventModel.EventInstrumentNoOutcomes

  upgradeEventInstrumentNoOutcomesRollback : ContractId IUpgradeEventInstrumentNoOutcomesRollback -> Update (ContractId IUpgradeEventInstrumentNoOutcomesRollback)

  nonconsuming choice UpgradeEventInstrumentNoOutcomesRollback : MigrationResult (ContractId IUpgradeEventInstrumentNoOutcomesRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeEventInstrumentNoOutcomesRollback contract") $
          migratingParty == (view this).event.operator
          || migratingParty == (view this).event.provider
        upgradeEventInstrumentNoOutcomesRollback this self

  interface instance IUpgradeEventInstrumentNoOutcomesRollback for CurrentEventModel.EventInstrumentNoOutcomes where
    view = convert this
    upgradeEventInstrumentNoOutcomesRollback self = do
      archive self
      toInterfaceContractId @IUpgradeEventInstrumentNoOutcomesRollback <$> create (convert this : LegacyEventModel.EventInstrumentNoOutcomes)

  interface instance IUpgradeEventInstrumentNoOutcomesRollback for LegacyEventModel.EventInstrumentNoOutcomes where
    view = this
    upgradeEventInstrumentNoOutcomesRollback = pure

interface IUpgradeMarketMapRollback where
  viewtype LegacyEventModel.MarketMap

  upgradeMarketMapRollback : ContractId IUpgradeMarketMapRollback -> Update (ContractId IUpgradeMarketMapRollback)

  nonconsuming choice UpgradeMarketMapRollback : MigrationResult (ContractId IUpgradeMarketMapRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeMarketMapRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeMarketMapRollback this self

  interface instance IUpgradeMarketMapRollback for CurrentEventModel.MarketMap where
    view = convert this
    upgradeMarketMapRollback self = do
      archive self
      toInterfaceContractId @IUpgradeMarketMapRollback <$> create (convert this : LegacyEventModel.MarketMap)

  interface instance IUpgradeMarketMapRollback for LegacyEventModel.MarketMap where
    view = this
    upgradeMarketMapRollback = pure

