module Gambyl.Migration.Interface.Gambyl.Gambling.Settlement.ModelRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Gambling.Settlement.ModelRollback()

import Legacy.Gambyl.Gambling.Settlement.Model qualified as FromSettlementModel
import Current.Gambyl.Gambling.Settlement.Model qualified as ToSettlementModel

interface IUpgradeExecutedBetsRollback where
  viewtype FromSettlementModel.ExecutedBets

  upgradeExecutedBetsRollback : ContractId IUpgradeExecutedBetsRollback -> Update (ContractId IUpgradeExecutedBetsRollback)

  nonconsuming choice UpgradeExecutedBetsRollback : MigrationResult (ContractId IUpgradeExecutedBetsRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeExecutedBetsRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeExecutedBetsRollback this self

  interface instance IUpgradeExecutedBetsRollback for ToSettlementModel.ExecutedBets where
    view = convert this
    upgradeExecutedBetsRollback self = do
      archive self
      toInterfaceContractId @IUpgradeExecutedBetsRollback <$> create (convert this : FromSettlementModel.ExecutedBets)

  interface instance IUpgradeExecutedBetsRollback for FromSettlementModel.ExecutedBets where
    view = this
    upgradeExecutedBetsRollback = pure
