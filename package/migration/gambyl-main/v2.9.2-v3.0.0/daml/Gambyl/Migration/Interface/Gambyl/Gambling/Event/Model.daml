module Gambyl.Migration.Interface.Gambyl.Gambling.Event.Model where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Gambling.Event.Model ()

import Legacy.Gambyl.Gambling.Event.Model qualified as LegacyEventModel
import Current.Gambyl.Gambling.Event.Model qualified as CurrentEventModel

interface IUpgradeEventInstrumentRequest where
  viewtype CurrentEventModel.EventInstrumentRequest

  upgradeEventInstrumentRequest : ContractId IUpgradeEventInstrumentRequest -> Update (ContractId IUpgradeEventInstrumentRequest)

  nonconsuming choice UpgradeEventInstrumentRequest : MigrationResult (ContractId IUpgradeEventInstrumentRequest)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeEventInstrumentRequest contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeEventInstrumentRequest this self

  interface instance IUpgradeEventInstrumentRequest for LegacyEventModel.EventInstrumentRequest where
    view = convert this
    upgradeEventInstrumentRequest self = do
      archive self
      toInterfaceContractId @IUpgradeEventInstrumentRequest <$> create (convert this : CurrentEventModel.EventInstrumentRequest)

  interface instance IUpgradeEventInstrumentRequest for CurrentEventModel.EventInstrumentRequest where
    view = this
    upgradeEventInstrumentRequest = pure

interface IUpgradeEventInstrumentStatusUpdateRequest where
  viewtype CurrentEventModel.EventInstrumentStatusUpdateRequest

  upgradeEventInstrumentStatusUpdateRequest : ContractId IUpgradeEventInstrumentStatusUpdateRequest -> Update (ContractId IUpgradeEventInstrumentStatusUpdateRequest)

  nonconsuming choice UpgradeEventInstrumentStatusUpdateRequest : MigrationResult (ContractId IUpgradeEventInstrumentStatusUpdateRequest)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeEventInstrumentStatusUpdateRequest contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeEventInstrumentStatusUpdateRequest this self

  interface instance IUpgradeEventInstrumentStatusUpdateRequest for LegacyEventModel.EventInstrumentStatusUpdateRequest where
    view = convert this
    upgradeEventInstrumentStatusUpdateRequest self = do
      archive self
      toInterfaceContractId @IUpgradeEventInstrumentStatusUpdateRequest <$> create (convert this : CurrentEventModel.EventInstrumentStatusUpdateRequest)

  interface instance IUpgradeEventInstrumentStatusUpdateRequest for CurrentEventModel.EventInstrumentStatusUpdateRequest where
    view = this
    upgradeEventInstrumentStatusUpdateRequest = pure


interface IUpgradeEventInstrumentUpdateRequest where
  viewtype CurrentEventModel.EventInstrumentUpdateRequest

  upgradeEventInstrumentUpdateRequest : ContractId IUpgradeEventInstrumentUpdateRequest -> Update (ContractId IUpgradeEventInstrumentUpdateRequest)

  nonconsuming choice UpgradeEventInstrumentUpdateRequest : MigrationResult (ContractId IUpgradeEventInstrumentUpdateRequest)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeEventInstrumentUpdateRequest contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeEventInstrumentUpdateRequest this self

  interface instance IUpgradeEventInstrumentUpdateRequest for LegacyEventModel.EventInstrumentUpdateRequest where
    view = convert this
    upgradeEventInstrumentUpdateRequest self = do
      archive self
      toInterfaceContractId @IUpgradeEventInstrumentUpdateRequest <$> create (convert this : CurrentEventModel.EventInstrumentUpdateRequest)

  interface instance IUpgradeEventInstrumentUpdateRequest for CurrentEventModel.EventInstrumentUpdateRequest where
    view = this
    upgradeEventInstrumentUpdateRequest = pure

interface IUpgradeEventInstrument where
  viewtype CurrentEventModel.EventInstrument

  upgradeEventInstrument : ContractId IUpgradeEventInstrument -> Update (ContractId IUpgradeEventInstrument)

  nonconsuming choice UpgradeEventInstrument : MigrationResult (ContractId IUpgradeEventInstrument)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeEventInstrument contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeEventInstrument this self

  interface instance IUpgradeEventInstrument for LegacyEventModel.EventInstrument where
    view = convert this
    upgradeEventInstrument self = do
      archive self
      toInterfaceContractId @IUpgradeEventInstrument <$> create (convert this : CurrentEventModel.EventInstrument)

  interface instance IUpgradeEventInstrument for CurrentEventModel.EventInstrument where
    view = this
    upgradeEventInstrument = pure

interface IUpgradeEventInstrumentToggleFeaturedRequest where
  viewtype CurrentEventModel.EventInstrumentToggleFeaturedRequest

  upgradeEventInstrumentToggleFeaturedRequest : ContractId IUpgradeEventInstrumentToggleFeaturedRequest -> Update (ContractId IUpgradeEventInstrumentToggleFeaturedRequest)

  nonconsuming choice UpgradeEventInstrumentToggleFeaturedRequest : MigrationResult (ContractId IUpgradeEventInstrumentToggleFeaturedRequest)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeEventInstrumentToggleFeaturedRequest contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeEventInstrumentToggleFeaturedRequest this self

  interface instance IUpgradeEventInstrumentToggleFeaturedRequest for LegacyEventModel.EventInstrumentToggleFeaturedRequest where
    view = convert this
    upgradeEventInstrumentToggleFeaturedRequest self = do
      archive self
      toInterfaceContractId @IUpgradeEventInstrumentToggleFeaturedRequest <$> create (convert this : CurrentEventModel.EventInstrumentToggleFeaturedRequest)

  interface instance IUpgradeEventInstrumentToggleFeaturedRequest for CurrentEventModel.EventInstrumentToggleFeaturedRequest where
    view = this
    upgradeEventInstrumentToggleFeaturedRequest = pure

interface IUpgradeEventInstrumentCancelRequest where
  viewtype CurrentEventModel.EventInstrumentCancelRequest

  upgradeEventInstrumentCancelRequest : ContractId IUpgradeEventInstrumentCancelRequest -> Update (ContractId IUpgradeEventInstrumentCancelRequest)

  nonconsuming choice UpgradeEventInstrumentCancelRequest : MigrationResult (ContractId IUpgradeEventInstrumentCancelRequest)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeEventInstrumentCancelRequest contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeEventInstrumentCancelRequest this self

  interface instance IUpgradeEventInstrumentCancelRequest for LegacyEventModel.EventInstrumentCancelRequest where
    view = convert this
    upgradeEventInstrumentCancelRequest self = do
      archive self
      toInterfaceContractId @IUpgradeEventInstrumentCancelRequest <$> create (convert this : CurrentEventModel.EventInstrumentCancelRequest)

  interface instance IUpgradeEventInstrumentCancelRequest for CurrentEventModel.EventInstrumentCancelRequest where
    view = this
    upgradeEventInstrumentCancelRequest = pure

interface IUpgradeEventInstrumentReinstateRequest where
  viewtype CurrentEventModel.EventInstrumentReinstateRequest

  upgradeEventInstrumentReinstateRequest : ContractId IUpgradeEventInstrumentReinstateRequest -> Update (ContractId IUpgradeEventInstrumentReinstateRequest)

  nonconsuming choice UpgradeEventInstrumentReinstateRequest : MigrationResult (ContractId IUpgradeEventInstrumentReinstateRequest)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeEventInstrumentReinstateRequest contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeEventInstrumentReinstateRequest this self

  interface instance IUpgradeEventInstrumentReinstateRequest for LegacyEventModel.EventInstrumentReinstateRequest where
    view = convert this
    upgradeEventInstrumentReinstateRequest self = do
      archive self
      toInterfaceContractId @IUpgradeEventInstrumentReinstateRequest <$> create (convert this : CurrentEventModel.EventInstrumentReinstateRequest)

  interface instance IUpgradeEventInstrumentReinstateRequest for CurrentEventModel.EventInstrumentReinstateRequest where
    view = this
    upgradeEventInstrumentReinstateRequest = pure

interface IUpgradeEventInstrumentUpdateOutcomesOddsRequest where
  viewtype CurrentEventModel.EventInstrumentUpdateOutcomesOddsRequest

  upgradeEventInstrumentUpdateOutcomesOddsRequest : ContractId IUpgradeEventInstrumentUpdateOutcomesOddsRequest -> Update (ContractId IUpgradeEventInstrumentUpdateOutcomesOddsRequest)

  nonconsuming choice UpgradeEventInstrumentUpdateOutcomesOddsRequest : MigrationResult (ContractId IUpgradeEventInstrumentUpdateOutcomesOddsRequest)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeEventInstrumentUpdateOutcomesOddsRequest contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeEventInstrumentUpdateOutcomesOddsRequest this self

  interface instance IUpgradeEventInstrumentUpdateOutcomesOddsRequest for LegacyEventModel.EventInstrumentUpdateOutcomesOddsRequest where
    view = convert this
    upgradeEventInstrumentUpdateOutcomesOddsRequest self = do
      archive self
      toInterfaceContractId @IUpgradeEventInstrumentUpdateOutcomesOddsRequest <$> create (convert this : CurrentEventModel.EventInstrumentUpdateOutcomesOddsRequest)

  interface instance IUpgradeEventInstrumentUpdateOutcomesOddsRequest for CurrentEventModel.EventInstrumentUpdateOutcomesOddsRequest where
    view = this
    upgradeEventInstrumentUpdateOutcomesOddsRequest = pure

interface IUpgradeEventInstrumentNoOutcomes where
  viewtype CurrentEventModel.EventInstrumentNoOutcomes

  upgradeEventInstrumentNoOutcomes : ContractId IUpgradeEventInstrumentNoOutcomes -> Update (ContractId IUpgradeEventInstrumentNoOutcomes)

  nonconsuming choice UpgradeEventInstrumentNoOutcomes : MigrationResult (ContractId IUpgradeEventInstrumentNoOutcomes)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeEventInstrumentNoOutcomes contract") $
          migratingParty == (view this).event.operator
          || migratingParty == (view this).event.provider
        upgradeEventInstrumentNoOutcomes this self

  interface instance IUpgradeEventInstrumentNoOutcomes for LegacyEventModel.EventInstrumentNoOutcomes where
    view = convert this
    upgradeEventInstrumentNoOutcomes self = do
      archive self
      toInterfaceContractId @IUpgradeEventInstrumentNoOutcomes <$> create (convert this : CurrentEventModel.EventInstrumentNoOutcomes)

  interface instance IUpgradeEventInstrumentNoOutcomes for CurrentEventModel.EventInstrumentNoOutcomes where
    view = this
    upgradeEventInstrumentNoOutcomes = pure

interface IUpgradeMarketMap where
  viewtype CurrentEventModel.MarketMap

  upgradeMarketMap : ContractId IUpgradeMarketMap -> Update (ContractId IUpgradeMarketMap)

  nonconsuming choice UpgradeMarketMap : MigrationResult (ContractId IUpgradeMarketMap)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeMarketMap contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeMarketMap this self

  interface instance IUpgradeMarketMap for LegacyEventModel.MarketMap where
    view = convert this
    upgradeMarketMap self = do
      archive self
      toInterfaceContractId @IUpgradeMarketMap <$> create (convert this : CurrentEventModel.MarketMap)

  interface instance IUpgradeMarketMap for CurrentEventModel.MarketMap where
    view = this
    upgradeMarketMap = pure
