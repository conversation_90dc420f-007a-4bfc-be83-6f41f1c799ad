module Gambyl.Migration.Interface.Gambyl.Gambling.Listing.ModelRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Gambling.Listing.ModelRollback ()

import Legacy.Gambyl.Gambling.Listing.Model qualified as LegacyListingModel
import Current.Gambyl.Gambling.Listing.Model qualified as CurrentListingModel

interface IUpgradeGamblingListingRollback where
  viewtype LegacyListingModel.GamblingListing

  upgradeGamblingListingRollback : ContractId IUpgradeGamblingListingRollback -> Update (ContractId IUpgradeGamblingListingRollback)

  nonconsuming choice UpgradeGamblingListingRollback : MigrationResult (ContractId IUpgradeGamblingListingRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeGamblingListingRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeGamblingListingRollback this self

  interface instance IUpgradeGamblingListingRollback for CurrentListingModel.GamblingListing where
    view = convert this
    upgradeGamblingListingRollback self = do
      archive self
      toInterfaceContractId @IUpgradeGamblingListingRollback <$> create (convert this : LegacyListingModel.GamblingListing)

  interface instance IUpgradeGamblingListingRollback for LegacyListingModel.GamblingListing where
    view = this
    upgradeGamblingListingRollback = pure

interface IUpgradeGamblingUpdateListingRequestRollback where
  viewtype LegacyListingModel.GamblingUpdateListingRequest

  upgradeGamblingUpdateListingRequestRollback : ContractId IUpgradeGamblingUpdateListingRequestRollback -> Update (ContractId IUpgradeGamblingUpdateListingRequestRollback)

  nonconsuming choice UpgradeGamblingUpdateListingRequestRollback : MigrationResult (ContractId IUpgradeGamblingUpdateListingRequestRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeGamblingUpdateListingRequestRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeGamblingUpdateListingRequestRollback this self

  interface instance IUpgradeGamblingUpdateListingRequestRollback for CurrentListingModel.GamblingUpdateListingRequest where
    view = convert this
    upgradeGamblingUpdateListingRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeGamblingUpdateListingRequestRollback <$> create (convert this : LegacyListingModel.GamblingUpdateListingRequest)

  interface instance IUpgradeGamblingUpdateListingRequestRollback for LegacyListingModel.GamblingUpdateListingRequest where
    view = this
    upgradeGamblingUpdateListingRequestRollback = pure

interface IUpgradeFailedGamblingUpdateListingRollback where
  viewtype LegacyListingModel.FailedGamblingUpdateListing

  upgradeFailedGamblingUpdateListingRollback : ContractId IUpgradeFailedGamblingUpdateListingRollback -> Update (ContractId IUpgradeFailedGamblingUpdateListingRollback)

  nonconsuming choice UpgradeFailedGamblingUpdateListingRollback : MigrationResult (ContractId IUpgradeFailedGamblingUpdateListingRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeFailedGamblingUpdateListingRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeFailedGamblingUpdateListingRollback this self

  interface instance IUpgradeFailedGamblingUpdateListingRollback for CurrentListingModel.FailedGamblingUpdateListing where
    view = convert this
    upgradeFailedGamblingUpdateListingRollback self = do
      archive self
      toInterfaceContractId @IUpgradeFailedGamblingUpdateListingRollback <$> create (convert this : LegacyListingModel.FailedGamblingUpdateListing)

  interface instance IUpgradeFailedGamblingUpdateListingRollback for LegacyListingModel.FailedGamblingUpdateListing where
    view = this
    upgradeFailedGamblingUpdateListingRollback = pure
