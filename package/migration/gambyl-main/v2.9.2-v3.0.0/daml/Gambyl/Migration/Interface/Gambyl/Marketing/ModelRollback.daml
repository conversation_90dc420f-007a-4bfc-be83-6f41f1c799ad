module Gambyl.Migration.Interface.Gambyl.Marketing.ModelRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Marketing.ModelRollback()

import Legacy.Gambyl.Marketing.Model qualified as LegacyMarketingModel
import Current.Gambyl.Marketing.Model qualified as CurrentMarketingModel

interface IUpgradeGlobalPromotionsRollback where
  viewtype LegacyMarketingModel.GlobalPromotions

  upgradeGlobalPromotionsRollback : ContractId IUpgradeGlobalPromotionsRollback -> Update (ContractId IUpgradeGlobalPromotionsRollback)

  nonconsuming choice UpgradeGlobalPromotionsRollback : MigrationResult (ContractId IUpgradeGlobalPromotionsRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeGlobalPromotionsRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeGlobalPromotionsRollback this self

  interface instance IUpgradeGlobalPromotionsRollback for CurrentMarketingModel.GlobalPromotions where
    view = convert this
    upgradeGlobalPromotionsRollback self = do
      archive self
      toInterfaceContractId @IUpgradeGlobalPromotionsRollback <$> create (convert this : LegacyMarketingModel.GlobalPromotions)

  interface instance IUpgradeGlobalPromotionsRollback for LegacyMarketingModel.GlobalPromotions where
    view = this
    upgradeGlobalPromotionsRollback = pure

interface IUpgradePromotionRollback where
  viewtype LegacyMarketingModel.Promotion

  upgradePromotionRollback : ContractId IUpgradePromotionRollback -> Update (ContractId IUpgradePromotionRollback)

  nonconsuming choice UpgradePromotionRollback : MigrationResult (ContractId IUpgradePromotionRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradePromotionRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradePromotionRollback this self

  interface instance IUpgradePromotionRollback for CurrentMarketingModel.Promotion where
    view = convert this
    upgradePromotionRollback self = do
      archive self
      toInterfaceContractId @IUpgradePromotionRollback <$> create (convert this : LegacyMarketingModel.Promotion)

  interface instance IUpgradePromotionRollback for LegacyMarketingModel.Promotion where
    view = this
    upgradePromotionRollback = pure

interface IUpgradePromotionRequestRollback where
  viewtype LegacyMarketingModel.PromotionRequest

  upgradePromotionRequestRollback : ContractId IUpgradePromotionRequestRollback -> Update (ContractId IUpgradePromotionRequestRollback)

  nonconsuming choice UpgradePromotionRequestRollback : MigrationResult (ContractId IUpgradePromotionRequestRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradePromotionRequestRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradePromotionRequestRollback this self

  interface instance IUpgradePromotionRequestRollback for CurrentMarketingModel.PromotionRequest where
    view = convert this
    upgradePromotionRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradePromotionRequestRollback <$> create (convert this : LegacyMarketingModel.PromotionRequest)

  interface instance IUpgradePromotionRequestRollback for LegacyMarketingModel.PromotionRequest where
    view = this
    upgradePromotionRequestRollback = pure

interface IUpgradePromotionUpdateRequestRollback where
  viewtype LegacyMarketingModel.PromotionUpdateRequest

  upgradePromotionUpdateRequestRollback : ContractId IUpgradePromotionUpdateRequestRollback -> Update (ContractId IUpgradePromotionUpdateRequestRollback)

  nonconsuming choice UpgradePromotionUpdateRequestRollback : MigrationResult (ContractId IUpgradePromotionUpdateRequestRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradePromotionUpdateRequestRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradePromotionUpdateRequestRollback this self

  interface instance IUpgradePromotionUpdateRequestRollback for CurrentMarketingModel.PromotionUpdateRequest where
    view = convert this
    upgradePromotionUpdateRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradePromotionUpdateRequestRollback <$> create (convert this : LegacyMarketingModel.PromotionUpdateRequest)

  interface instance IUpgradePromotionUpdateRequestRollback for LegacyMarketingModel.PromotionUpdateRequest where
    view = this
    upgradePromotionUpdateRequestRollback = pure

interface IUpgradePromotionWalletRollback where
  viewtype LegacyMarketingModel.PromotionWallet

  upgradePromotionWalletRollback : ContractId IUpgradePromotionWalletRollback -> Update (ContractId IUpgradePromotionWalletRollback)

  nonconsuming choice UpgradePromotionWalletRollback : MigrationResult (ContractId IUpgradePromotionWalletRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradePromotionWalletRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradePromotionWalletRollback this self

  interface instance IUpgradePromotionWalletRollback for CurrentMarketingModel.PromotionWallet where
    view = convert this
    upgradePromotionWalletRollback self = do
      archive self
      toInterfaceContractId @IUpgradePromotionWalletRollback <$> create (convert this : LegacyMarketingModel.PromotionWallet)

  interface instance IUpgradePromotionWalletRollback for LegacyMarketingModel.PromotionWallet where
    view = this
    upgradePromotionWalletRollback = pure

interface IUpgradePromotionUsageHistoryRollback where
  viewtype LegacyMarketingModel.PromotionUsageHistory

  upgradePromotionUsageHistoryRollback : ContractId IUpgradePromotionUsageHistoryRollback -> Update (ContractId IUpgradePromotionUsageHistoryRollback)

  nonconsuming choice UpgradePromotionUsageHistoryRollback : MigrationResult (ContractId IUpgradePromotionUsageHistoryRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradePromotionUsageHistoryRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradePromotionUsageHistoryRollback this self

  interface instance IUpgradePromotionUsageHistoryRollback for CurrentMarketingModel.PromotionUsageHistory where
    view = convert this
    upgradePromotionUsageHistoryRollback self = do
      archive self
      toInterfaceContractId @IUpgradePromotionUsageHistoryRollback <$> create (convert this : LegacyMarketingModel.PromotionUsageHistory)

  interface instance IUpgradePromotionUsageHistoryRollback for LegacyMarketingModel.PromotionUsageHistory where
    view = this
    upgradePromotionUsageHistoryRollback = pure
