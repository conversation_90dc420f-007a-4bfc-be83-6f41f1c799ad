module Gambyl.Migration.Interface.Gambyl.Gambling.Account.ModelRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Gambling.Account.ModelRollback ()

import Legacy.Gambyl.Gambling.Account.Model qualified as LegacyAccountModel
import Current.Gambyl.Gambling.Account.Model qualified as CurrentAccountModel

interface IUpgradeAccountRollback where
  viewtype LegacyAccountModel.Account

  upgradeAccountRollback : ContractId IUpgradeAccountRollback -> Update (ContractId IUpgradeAccountRollback)

  nonconsuming choice UpgradeAccountRollback : MigrationResult (ContractId IUpgradeAccountRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeAccountRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeAccountRollback this self

  interface instance IUpgradeAccountRollback for CurrentAccountModel.Account where
    view = convert this
    upgradeAccountRollback self = do
      archive self
      toInterfaceContractId @IUpgradeAccountRollback <$> create (convert this : LegacyAccountModel.Account)

  interface instance IUpgradeAccountRollback for LegacyAccountModel.Account where
    view = this
    upgradeAccountRollback = pure

interface IUpgradeWithdrawRequestForApprovalRollback where
  viewtype LegacyAccountModel.WithdrawRequestForApproval

  upgradeWithdrawRequestForApprovalRollback : ContractId IUpgradeWithdrawRequestForApprovalRollback -> Update (ContractId IUpgradeWithdrawRequestForApprovalRollback)

  nonconsuming choice UpgradeWithdrawRequestForApprovalRollback : MigrationResult (ContractId IUpgradeWithdrawRequestForApprovalRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeWithdrawRequestForApprovalRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeWithdrawRequestForApprovalRollback this self

  -- NOTE: this was commented as may result handy
  interface instance IUpgradeWithdrawRequestForApprovalRollback for CurrentAccountModel.WithdrawRequestForApproval where
   view = convert this
   upgradeWithdrawRequestForApprovalRollback self = do
     archive self
     toInterfaceContractId @IUpgradeWithdrawRequestForApprovalRollback <$> create (convert this : LegacyAccountModel.WithdrawRequestForApproval)

  interface instance IUpgradeWithdrawRequestForApprovalRollback for LegacyAccountModel.WithdrawRequestForApproval where
    view = this
    upgradeWithdrawRequestForApprovalRollback = pure

interface IUpgradeDepositRequestForApprovalRollback where
  viewtype LegacyAccountModel.DepositRequestForApproval

  upgradeDepositRequestForApprovalRollback : ContractId IUpgradeDepositRequestForApprovalRollback -> Update (ContractId IUpgradeDepositRequestForApprovalRollback)

  nonconsuming choice UpgradeDepositRequestForApprovalRollback : MigrationResult (ContractId IUpgradeDepositRequestForApprovalRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeDepositRequestForApprovalRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeDepositRequestForApprovalRollback this self

  -- NOTE: this was commented as may result handy
  interface instance IUpgradeDepositRequestForApprovalRollback for CurrentAccountModel.DepositRequestForApproval where
    view = convert this
    upgradeDepositRequestForApprovalRollback self = do
      archive self
      toInterfaceContractId @IUpgradeDepositRequestForApprovalRollback <$> create (convert this : LegacyAccountModel.DepositRequestForApproval)

  interface instance IUpgradeDepositRequestForApprovalRollback for LegacyAccountModel.DepositRequestForApproval where
    view = this
    upgradeDepositRequestForApprovalRollback = pure

interface IUpgradeTransactionHistoryRollback where
  viewtype LegacyAccountModel.TransactionHistory

  upgradeTransactionHistoryRollback : ContractId IUpgradeTransactionHistoryRollback -> Update (ContractId IUpgradeTransactionHistoryRollback)

  nonconsuming choice UpgradeTransactionHistoryRollback : MigrationResult (ContractId IUpgradeTransactionHistoryRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeTransactionHistoryRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeTransactionHistoryRollback this self

  interface instance IUpgradeTransactionHistoryRollback for CurrentAccountModel.TransactionHistory where
    view = convert this
    upgradeTransactionHistoryRollback self = do
      archive self
      toInterfaceContractId @IUpgradeTransactionHistoryRollback <$> create (convert this : LegacyAccountModel.TransactionHistory)

  interface instance IUpgradeTransactionHistoryRollback for LegacyAccountModel.TransactionHistory where
    view = this
    upgradeTransactionHistoryRollback = pure

interface IUpgradePendingTransactionRollback where
  viewtype LegacyAccountModel.PendingTransaction

  upgradePendingTransactionRollback : ContractId IUpgradePendingTransactionRollback -> Update (ContractId IUpgradePendingTransactionRollback)

  nonconsuming choice UpgradePendingTransactionRollback : MigrationResult (ContractId IUpgradePendingTransactionRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradePendingTransactionRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradePendingTransactionRollback this self

  interface instance IUpgradePendingTransactionRollback for CurrentAccountModel.PendingTransaction where
    view = convert this
    upgradePendingTransactionRollback self = do
      archive self
      toInterfaceContractId @IUpgradePendingTransactionRollback <$> create (convert this : LegacyAccountModel.PendingTransaction)

  interface instance IUpgradePendingTransactionRollback for LegacyAccountModel.PendingTransaction where
    view = this
    upgradePendingTransactionRollback = pure

interface IUpgradePendingDepositsRollback where
  viewtype LegacyAccountModel.PendingDeposits

  upgradePendingDepositsRollback : ContractId IUpgradePendingDepositsRollback -> Update (ContractId IUpgradePendingDepositsRollback)

  nonconsuming choice UpgradePendingDepositsRollback : MigrationResult (ContractId IUpgradePendingDepositsRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradePendingDepositsRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradePendingDepositsRollback this self

  interface instance IUpgradePendingDepositsRollback for CurrentAccountModel.PendingDeposits where
    view = convert this
    upgradePendingDepositsRollback self = do
      archive self
      toInterfaceContractId @IUpgradePendingDepositsRollback <$> create (convert this : LegacyAccountModel.PendingDeposits)

  interface instance IUpgradePendingDepositsRollback for LegacyAccountModel.PendingDeposits where
    view = this
    upgradePendingDepositsRollback = pure

interface IUpgradeFlagRollback where
  viewtype LegacyAccountModel.Flag

  upgradeFlagRollback : ContractId IUpgradeFlagRollback -> Update (ContractId IUpgradeFlagRollback)

  nonconsuming choice UpgradeFlagRollback : MigrationResult (ContractId IUpgradeFlagRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeFlagRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeFlagRollback this self

  interface instance IUpgradeFlagRollback for CurrentAccountModel.Flag where
    view = convert this
    upgradeFlagRollback self = do
      archive self
      toInterfaceContractId @IUpgradeFlagRollback <$> create (convert this : LegacyAccountModel.Flag)

  interface instance IUpgradeFlagRollback for LegacyAccountModel.Flag where
    view = this
    upgradeFlagRollback = pure
