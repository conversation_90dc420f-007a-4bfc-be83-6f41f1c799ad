module Gambyl.Migration.Interface.Gambyl.Marketing.Service where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Marketing.Service()

import Legacy.Gambyl.Marketing.Service qualified as LegacyMarketingService
import Current.Gambyl.Marketing.Service qualified as CurrentMarketingService

interface IUpgradeService where
  viewtype CurrentMarketingService.Service

  upgradeService : ContractId IUpgradeService -> Update (ContractId IUpgradeService)

  nonconsuming choice UpgradeService : MigrationResult (ContractId IUpgradeService)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeService contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeService this self

  interface instance IUpgradeService for LegacyMarketingService.Service where
    view = convert this
    upgradeService self = do
      archive self
      toInterfaceContractId @IUpgradeService <$> create (convert this : CurrentMarketingService.Service)

  interface instance IUpgradeService for CurrentMarketingService.Service where
    view = this
    upgradeService = pure

interface IUpgradeOffer where
  viewtype CurrentMarketingService.Offer

  upgradeOffer : ContractId IUpgradeOffer -> Update (ContractId IUpgradeOffer)

  nonconsuming choice UpgradeOffer : MigrationResult (ContractId IUpgradeOffer)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeOffer contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeOffer this self

  interface instance IUpgradeOffer for LegacyMarketingService.Offer where
    view = convert this
    upgradeOffer self = do
      archive self
      toInterfaceContractId @IUpgradeOffer <$> create (convert this : CurrentMarketingService.Offer)

  interface instance IUpgradeOffer for CurrentMarketingService.Offer where
    view = this
    upgradeOffer = pure

interface IUpgradeRequest where
  viewtype CurrentMarketingService.Request

  upgradeRequest : ContractId IUpgradeRequest -> Update (ContractId IUpgradeRequest)

  nonconsuming choice UpgradeRequest : MigrationResult (ContractId IUpgradeRequest)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeRequest contract") $
          migratingParty == (view this).provider
        upgradeRequest this self

  interface instance IUpgradeRequest for LegacyMarketingService.Request where
    view = convert this
    upgradeRequest self = do
      archive self
      toInterfaceContractId @IUpgradeRequest <$> create (convert this : CurrentMarketingService.Request)

  interface instance IUpgradeRequest for CurrentMarketingService.Request where
    view = this
    upgradeRequest = pure
