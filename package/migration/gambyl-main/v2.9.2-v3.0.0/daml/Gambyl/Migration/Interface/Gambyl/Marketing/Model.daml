module Gambyl.Migration.Interface.Gambyl.Marketing.Model where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Marketing.Model()

import Legacy.Gambyl.Marketing.Model qualified as LegacyMarketingModel
import Current.Gambyl.Marketing.Model qualified as CurrentMarketingModel

interface IUpgradeGlobalPromotions where
  viewtype CurrentMarketingModel.GlobalPromotions

  upgradeGlobalPromotions : ContractId IUpgradeGlobalPromotions -> Update (ContractId IUpgradeGlobalPromotions)

  nonconsuming choice UpgradeGlobalPromotions : MigrationResult (ContractId IUpgradeGlobalPromotions)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeGlobalPromotions contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeGlobalPromotions this self

  interface instance IUpgradeGlobalPromotions for LegacyMarketingModel.GlobalPromotions where
    view = convert this
    upgradeGlobalPromotions self = do
      archive self
      toInterfaceContractId @IUpgradeGlobalPromotions <$> create (convert this : CurrentMarketingModel.GlobalPromotions)

  interface instance IUpgradeGlobalPromotions for CurrentMarketingModel.GlobalPromotions where
    view = this
    upgradeGlobalPromotions = pure

interface IUpgradePromotion where
  viewtype CurrentMarketingModel.Promotion

  upgradePromotion : ContractId IUpgradePromotion -> Update (ContractId IUpgradePromotion)

  nonconsuming choice UpgradePromotion : MigrationResult (ContractId IUpgradePromotion)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradePromotion contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradePromotion this self

  interface instance IUpgradePromotion for LegacyMarketingModel.Promotion where
    view = convert this
    upgradePromotion self = do
      archive self
      toInterfaceContractId @IUpgradePromotion <$> create (convert this : CurrentMarketingModel.Promotion)

  interface instance IUpgradePromotion for CurrentMarketingModel.Promotion where
    view = this
    upgradePromotion = pure

interface IUpgradePromotionRequest where
  viewtype CurrentMarketingModel.PromotionRequest

  upgradePromotionRequest : ContractId IUpgradePromotionRequest -> Update (ContractId IUpgradePromotionRequest)

  nonconsuming choice UpgradePromotionRequest : MigrationResult (ContractId IUpgradePromotionRequest)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradePromotionRequest contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradePromotionRequest this self

  interface instance IUpgradePromotionRequest for LegacyMarketingModel.PromotionRequest where
    view = convert this
    upgradePromotionRequest self = do
      archive self
      toInterfaceContractId @IUpgradePromotionRequest <$> create (convert this : CurrentMarketingModel.PromotionRequest)

  interface instance IUpgradePromotionRequest for CurrentMarketingModel.PromotionRequest where
    view = this
    upgradePromotionRequest = pure

interface IUpgradePromotionUpdateRequest where
  viewtype CurrentMarketingModel.PromotionUpdateRequest

  upgradePromotionUpdateRequest : ContractId IUpgradePromotionUpdateRequest -> Update (ContractId IUpgradePromotionUpdateRequest)

  nonconsuming choice UpgradePromotionUpdateRequest : MigrationResult (ContractId IUpgradePromotionUpdateRequest)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradePromotionUpdateRequest contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradePromotionUpdateRequest this self

  interface instance IUpgradePromotionUpdateRequest for LegacyMarketingModel.PromotionUpdateRequest where
    view = convert this
    upgradePromotionUpdateRequest self = do
      archive self
      toInterfaceContractId @IUpgradePromotionUpdateRequest <$> create (convert this : CurrentMarketingModel.PromotionUpdateRequest)

  interface instance IUpgradePromotionUpdateRequest for CurrentMarketingModel.PromotionUpdateRequest where
    view = this
    upgradePromotionUpdateRequest = pure

interface IUpgradePromotionWallet where
  viewtype CurrentMarketingModel.PromotionWallet

  upgradePromotionWallet : ContractId IUpgradePromotionWallet -> Update (ContractId IUpgradePromotionWallet)

  nonconsuming choice UpgradePromotionWallet : MigrationResult (ContractId IUpgradePromotionWallet)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradePromotionWallet contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradePromotionWallet this self

  interface instance IUpgradePromotionWallet for LegacyMarketingModel.PromotionWallet where
    view = convert this
    upgradePromotionWallet self = do
      archive self
      toInterfaceContractId @IUpgradePromotionWallet <$> create (convert this : CurrentMarketingModel.PromotionWallet)

  interface instance IUpgradePromotionWallet for CurrentMarketingModel.PromotionWallet where
    view = this
    upgradePromotionWallet = pure

interface IUpgradePromotionUsageHistory where
  viewtype CurrentMarketingModel.PromotionUsageHistory

  upgradePromotionUsageHistory : ContractId IUpgradePromotionUsageHistory -> Update (ContractId IUpgradePromotionUsageHistory)

  nonconsuming choice UpgradePromotionUsageHistory : MigrationResult (ContractId IUpgradePromotionUsageHistory)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradePromotionUsageHistory contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradePromotionUsageHistory this self

  interface instance IUpgradePromotionUsageHistory for LegacyMarketingModel.PromotionUsageHistory where
    view = convert this
    upgradePromotionUsageHistory self = do
      archive self
      toInterfaceContractId @IUpgradePromotionUsageHistory <$> create (convert this : CurrentMarketingModel.PromotionUsageHistory)

  interface instance IUpgradePromotionUsageHistory for CurrentMarketingModel.PromotionUsageHistory where
    view = this
    upgradePromotionUsageHistory = pure
