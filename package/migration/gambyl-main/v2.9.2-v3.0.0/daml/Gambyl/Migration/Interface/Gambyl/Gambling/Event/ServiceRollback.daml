module Gambyl.Migration.Interface.Gambyl.Gambling.Event.ServiceRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Gambling.Event.ServiceRollback ()

import Legacy.Gambyl.Gambling.Event.Service qualified as LegacyEventService
import Current.Gambyl.Gambling.Event.Service qualified as CurrentEventService

interface IUpgradeServiceRollback where
  viewtype LegacyEventService.Service

  upgradeServiceRollback : ContractId IUpgradeServiceRollback -> Update (ContractId IUpgradeServiceRollback)

  nonconsuming choice UpgradeServiceRollback : MigrationResult (ContractId IUpgradeServiceRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeServiceRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeServiceRollback this self

  interface instance IUpgradeServiceRollback for CurrentEventService.Service where
    view = convert this
    upgradeServiceRollback self = do
      archive self
      toInterfaceContractId @IUpgradeServiceRollback <$> create (convert this : LegacyEventService.Service)

  interface instance IUpgradeServiceRollback for LegacyEventService.Service where
    view = this
    upgradeServiceRollback = pure

interface IUpgradeOfferRollback where
  viewtype LegacyEventService.Offer

  upgradeOfferRollback : ContractId IUpgradeOfferRollback -> Update (ContractId IUpgradeOfferRollback)

  nonconsuming choice UpgradeOfferRollback : MigrationResult (ContractId IUpgradeOfferRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeOfferRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeOfferRollback this self

  interface instance IUpgradeOfferRollback for CurrentEventService.Offer where
    view = convert this
    upgradeOfferRollback self = do
      archive self
      toInterfaceContractId @IUpgradeOfferRollback <$> create (convert this : LegacyEventService.Offer)

  interface instance IUpgradeOfferRollback for LegacyEventService.Offer where
    view = this
    upgradeOfferRollback = pure

interface IUpgradeRequestRollback where
  viewtype LegacyEventService.Request

  upgradeRequestRollback : ContractId IUpgradeRequestRollback -> Update (ContractId IUpgradeRequestRollback)

  nonconsuming choice UpgradeRequestRollback : MigrationResult (ContractId IUpgradeRequestRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeRequestRollback contract") $
          migratingParty == (view this).provider
        upgradeRequestRollback this self

  interface instance IUpgradeRequestRollback for CurrentEventService.Request where
    view = convert this
    upgradeRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeRequestRollback <$> create (convert this : LegacyEventService.Request)

  interface instance IUpgradeRequestRollback for LegacyEventService.Request where
    view = this
    upgradeRequestRollback = pure
