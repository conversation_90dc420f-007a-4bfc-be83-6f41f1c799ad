module Gambyl.Migration.Script.ScriptFunctions where

import Daml.Script
import Gambyl.Migration.Common

import Gambyl.Migration.Interface.Gambyl.Counter.Model qualified as CounterModel
import Gambyl.Migration.Interface.Gambyl.Gambling.Account.Model qualified as GamblingAccountModel
import Gambyl.Migration.Interface.Gambyl.Gambling.Bet.Model qualified as GamblingBetModel
import Gambyl.Migration.Interface.Gambyl.Gambling.Event.Model qualified as GamblingEventModel
import Gambyl.Migration.Interface.Gambyl.Gambling.Event.Service qualified as GamblingEventService
import Gambyl.Migration.Interface.Gambyl.Gambling.Identity.Model qualified as GamblingIdentityModel
import Gambyl.Migration.Interface.Gambyl.Gambling.Listing.Model qualified as GamblingListingModel
import Gambyl.Migration.Interface.Gambyl.Gambling.Model qualified as GamblingModel
import Gambyl.Migration.Interface.Gambyl.Gambling.Role qualified as GamblingRole
import Gambyl.Migration.Interface.Gambyl.Gambling.Service qualified as GamblingService
import Gambyl.Migration.Interface.Gambyl.Gambling.Settlement.Model qualified as GamblingSettlementModel
import Gambyl.Migration.Interface.Gambyl.Marketing.Model qualified as MarketingModel
import Gambyl.Migration.Interface.Gambyl.Marketing.Service qualified as MarketingService
import Gambyl.Migration.Interface.Gambyl.Operator.Role qualified as OperatorRole

upgradeListOfIUpgradeGlobalGamblingConfiguration : Party -> [ContractId GamblingModel.IUpgradeGlobalGamblingConfiguration] -> Script [MigrationResult (ContractId GamblingModel.IUpgradeGlobalGamblingConfiguration)]
upgradeListOfIUpgradeGlobalGamblingConfiguration migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingModel.UpgradeGlobalGamblingConfiguration with ..

upgradeListOfIUpgradeActionFailure : Party -> [ContractId GamblingModel.IUpgradeActionFailure] -> Script [MigrationResult (ContractId GamblingModel.IUpgradeActionFailure)]
upgradeListOfIUpgradeActionFailure migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingModel.UpgradeActionFailure with ..

upgradeListOfIUpgradeActionSuccess : Party -> [ContractId GamblingModel.IUpgradeActionSuccess] -> Script [MigrationResult (ContractId GamblingModel.IUpgradeActionSuccess)]
upgradeListOfIUpgradeActionSuccess migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingModel.UpgradeActionSuccess with ..

upgradeListOfGamblingRoleIUpgradeRole : Party -> [ContractId GamblingRole.IUpgradeRole] -> Script [MigrationResult (ContractId GamblingRole.IUpgradeRole)]
upgradeListOfGamblingRoleIUpgradeRole migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingRole.UpgradeRole with ..

upgradeListOfGamblingRoleIUpgradeOffer : Party -> [ContractId GamblingRole.IUpgradeOffer] -> Script [MigrationResult (ContractId GamblingRole.IUpgradeOffer)]
upgradeListOfGamblingRoleIUpgradeOffer migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingRole.UpgradeOffer with ..

upgradeListOfGamblingRoleIUpgradeRequest : Party -> [ContractId GamblingRole.IUpgradeRequest] -> Script [MigrationResult (ContractId GamblingRole.IUpgradeRequest)]
upgradeListOfGamblingRoleIUpgradeRequest migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingRole.UpgradeRequest with ..

upgradeListOfGamblingServiceIUpgradeService : Party -> [ContractId GamblingService.IUpgradeService] -> Script [MigrationResult (ContractId GamblingService.IUpgradeService)]
upgradeListOfGamblingServiceIUpgradeService migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingService.UpgradeService with ..

upgradeListOfGamblingServiceIUpgradeOffer : Party -> [ContractId GamblingService.IUpgradeOffer] -> Script [MigrationResult (ContractId GamblingService.IUpgradeOffer)]
upgradeListOfGamblingServiceIUpgradeOffer migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingService.UpgradeOffer with ..

upgradeListOfGamblingServiceIUpgradeRequest : Party -> [ContractId GamblingService.IUpgradeRequest] -> Script [MigrationResult (ContractId GamblingService.IUpgradeRequest)]
upgradeListOfGamblingServiceIUpgradeRequest migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingService.UpgradeRequest with ..

upgradeListOfIUpgradeBlockedService : Party -> [ContractId GamblingService.IUpgradeBlockedService] -> Script [MigrationResult (ContractId GamblingService.IUpgradeBlockedService)]
upgradeListOfIUpgradeBlockedService migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingService.UpgradeBlockedService with ..

upgradeListOfIUpgradePendingPromotionApplication : Party -> [ContractId GamblingService.IUpgradePendingPromotionApplication] -> Script [MigrationResult (ContractId GamblingService.IUpgradePendingPromotionApplication)]
upgradeListOfIUpgradePendingPromotionApplication migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingService.UpgradePendingPromotionApplication with ..

upgradeListOfIUpgradePendingIdentity : Party -> [ContractId GamblingIdentityModel.IUpgradePendingIdentity] -> Script [MigrationResult (ContractId GamblingIdentityModel.IUpgradePendingIdentity)]
upgradeListOfIUpgradePendingIdentity migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingIdentityModel.UpgradePendingIdentity with ..

upgradeListOfIUpgradeGamblerUnverifiedIdentityRequest : Party -> [ContractId GamblingIdentityModel.IUpgradeGamblerUnverifiedIdentityRequest] -> Script [MigrationResult (ContractId GamblingIdentityModel.IUpgradeGamblerUnverifiedIdentityRequest)]
upgradeListOfIUpgradeGamblerUnverifiedIdentityRequest migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingIdentityModel.UpgradeGamblerUnverifiedIdentityRequest with ..

upgradeListOfIUpgradeGamblerUnverifiedIdentity : Party -> [ContractId GamblingIdentityModel.IUpgradeGamblerUnverifiedIdentity] -> Script [MigrationResult (ContractId GamblingIdentityModel.IUpgradeGamblerUnverifiedIdentity)]
upgradeListOfIUpgradeGamblerUnverifiedIdentity migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingIdentityModel.UpgradeGamblerUnverifiedIdentity with ..

upgradeListOfIUpgradeGamblerIdentity : Party -> [ContractId GamblingIdentityModel.IUpgradeGamblerIdentity] -> Script [MigrationResult (ContractId GamblingIdentityModel.IUpgradeGamblerIdentity)]
upgradeListOfIUpgradeGamblerIdentity migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingIdentityModel.UpgradeGamblerIdentity with ..

upgradeListOfIUpgradeRejectedIdentity : Party -> [ContractId GamblingIdentityModel.IUpgradeRejectedIdentity] -> Script [MigrationResult (ContractId GamblingIdentityModel.IUpgradeRejectedIdentity)]
upgradeListOfIUpgradeRejectedIdentity migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingIdentityModel.UpgradeRejectedIdentity with ..

upgradeListOfIUpgradeExecutedBets : Party -> [ContractId GamblingSettlementModel.IUpgradeExecutedBets] -> Script [MigrationResult (ContractId GamblingSettlementModel.IUpgradeExecutedBets)]
upgradeListOfIUpgradeExecutedBets migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingSettlementModel.UpgradeExecutedBets with ..

upgradeListOfIUpgradeBetPlacementRequest : Party -> [ContractId GamblingBetModel.IUpgradeBetPlacementRequest] -> Script [MigrationResult (ContractId GamblingBetModel.IUpgradeBetPlacementRequest)]
upgradeListOfIUpgradeBetPlacementRequest migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingBetModel.UpgradeBetPlacementRequest with ..

upgradeListOfIUpgradeBetPlacementRequestFlag : Party -> [ContractId GamblingBetModel.IUpgradeBetPlacementRequestFlag] -> Script [MigrationResult (ContractId GamblingBetModel.IUpgradeBetPlacementRequestFlag)]
upgradeListOfIUpgradeBetPlacementRequestFlag migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingBetModel.UpgradeBetPlacementRequestFlag with ..

upgradeListOfIUpgradeBulkBetPlacementRequestFlag : Party -> [ContractId GamblingBetModel.IUpgradeBulkBetPlacementRequestFlag] -> Script [MigrationResult (ContractId GamblingBetModel.IUpgradeBulkBetPlacementRequestFlag)]
upgradeListOfIUpgradeBulkBetPlacementRequestFlag migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingBetModel.UpgradeBulkBetPlacementRequestFlag with ..

upgradeListOfIUpgradeBetPlacementFinalizeRequest : Party -> [ContractId GamblingBetModel.IUpgradeBetPlacementFinalizeRequest] -> Script [MigrationResult (ContractId GamblingBetModel.IUpgradeBetPlacementFinalizeRequest)]
upgradeListOfIUpgradeBetPlacementFinalizeRequest migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingBetModel.UpgradeBetPlacementFinalizeRequest with ..

upgradeListOfIUpgradeBetPlacement : Party -> [ContractId GamblingBetModel.IUpgradeBetPlacement] -> Script [MigrationResult (ContractId GamblingBetModel.IUpgradeBetPlacement)]
upgradeListOfIUpgradeBetPlacement migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingBetModel.UpgradeBetPlacement with ..

upgradeListOfIUpgradeBetPlacementSplitRequest : Party -> [ContractId GamblingBetModel.IUpgradeBetPlacementSplitRequest] -> Script [MigrationResult (ContractId GamblingBetModel.IUpgradeBetPlacementSplitRequest)]
upgradeListOfIUpgradeBetPlacementSplitRequest migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingBetModel.UpgradeBetPlacementSplitRequest with ..

upgradeListOfIUpgradeBetPlacementCancelRequest : Party -> [ContractId GamblingBetModel.IUpgradeBetPlacementCancelRequest] -> Script [MigrationResult (ContractId GamblingBetModel.IUpgradeBetPlacementCancelRequest)]
upgradeListOfIUpgradeBetPlacementCancelRequest migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingBetModel.UpgradeBetPlacementCancelRequest with ..

upgradeListOfIUpgradeFinalizeBetPlacementCancelRequest : Party -> [ContractId GamblingBetModel.IUpgradeFinalizeBetPlacementCancelRequest] -> Script [MigrationResult (ContractId GamblingBetModel.IUpgradeFinalizeBetPlacementCancelRequest)]
upgradeListOfIUpgradeFinalizeBetPlacementCancelRequest migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingBetModel.UpgradeFinalizeBetPlacementCancelRequest with ..

upgradeListOfIUpgradeBetHistory : Party -> [ContractId GamblingBetModel.IUpgradeBetHistory] -> Script [MigrationResult (ContractId GamblingBetModel.IUpgradeBetHistory)]
upgradeListOfIUpgradeBetHistory migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingBetModel.UpgradeBetHistory with ..

upgradeListOfIUpgradeBulkBetPlacementRequest : Party -> [ContractId GamblingBetModel.IUpgradeBulkBetPlacementRequest] -> Script [MigrationResult (ContractId GamblingBetModel.IUpgradeBulkBetPlacementRequest)]
upgradeListOfIUpgradeBulkBetPlacementRequest migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingBetModel.UpgradeBulkBetPlacementRequest with ..

upgradeListOfIUpgradeBulkBetPlacementFinalizeRequest : Party -> [ContractId GamblingBetModel.IUpgradeBulkBetPlacementFinalizeRequest] -> Script [MigrationResult (ContractId GamblingBetModel.IUpgradeBulkBetPlacementFinalizeRequest)]
upgradeListOfIUpgradeBulkBetPlacementFinalizeRequest migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingBetModel.UpgradeBulkBetPlacementFinalizeRequest with ..

upgradeListOfIUpgradeBulkBetPlacement : Party -> [ContractId GamblingBetModel.IUpgradeBulkBetPlacement] -> Script [MigrationResult (ContractId GamblingBetModel.IUpgradeBulkBetPlacement)]
upgradeListOfIUpgradeBulkBetPlacement migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingBetModel.UpgradeBulkBetPlacement with ..

upgradeListOfIUpgradeGamblingListing : Party -> [ContractId GamblingListingModel.IUpgradeGamblingListing] -> Script [MigrationResult (ContractId GamblingListingModel.IUpgradeGamblingListing)]
upgradeListOfIUpgradeGamblingListing migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingListingModel.UpgradeGamblingListing with ..

upgradeListOfIUpgradeGamblingUpdateListingRequest : Party -> [ContractId GamblingListingModel.IUpgradeGamblingUpdateListingRequest] -> Script [MigrationResult (ContractId GamblingListingModel.IUpgradeGamblingUpdateListingRequest)]
upgradeListOfIUpgradeGamblingUpdateListingRequest migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingListingModel.UpgradeGamblingUpdateListingRequest with ..

upgradeListOfIUpgradeFailedGamblingUpdateListing : Party -> [ContractId GamblingListingModel.IUpgradeFailedGamblingUpdateListing] -> Script [MigrationResult (ContractId GamblingListingModel.IUpgradeFailedGamblingUpdateListing)]
upgradeListOfIUpgradeFailedGamblingUpdateListing migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingListingModel.UpgradeFailedGamblingUpdateListing with ..

upgradeListOfIUpgradeAccount : Party -> [ContractId GamblingAccountModel.IUpgradeAccount] -> Script [MigrationResult (ContractId GamblingAccountModel.IUpgradeAccount)]
upgradeListOfIUpgradeAccount migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingAccountModel.UpgradeAccount with ..

upgradeListOfIUpgradeWithdrawRequestForApproval : Party -> [ContractId GamblingAccountModel.IUpgradeWithdrawRequestForApproval] -> Script [MigrationResult (ContractId GamblingAccountModel.IUpgradeWithdrawRequestForApproval)]
upgradeListOfIUpgradeWithdrawRequestForApproval migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingAccountModel.UpgradeWithdrawRequestForApproval with ..

upgradeListOfIUpgradeDepositRequestForApproval : Party -> [ContractId GamblingAccountModel.IUpgradeDepositRequestForApproval] -> Script [MigrationResult (ContractId GamblingAccountModel.IUpgradeDepositRequestForApproval)]
upgradeListOfIUpgradeDepositRequestForApproval migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingAccountModel.UpgradeDepositRequestForApproval with ..

upgradeListOfIUpgradeTransactionHistory : Party -> [ContractId GamblingAccountModel.IUpgradeTransactionHistory] -> Script [MigrationResult (ContractId GamblingAccountModel.IUpgradeTransactionHistory)]
upgradeListOfIUpgradeTransactionHistory migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingAccountModel.UpgradeTransactionHistory with ..

upgradeListOfIUpgradePendingTransaction : Party -> [ContractId GamblingAccountModel.IUpgradePendingTransaction] -> Script [MigrationResult (ContractId GamblingAccountModel.IUpgradePendingTransaction)]
upgradeListOfIUpgradePendingTransaction migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingAccountModel.UpgradePendingTransaction with ..

upgradeListOfIUpgradePendingDeposits : Party -> [ContractId GamblingAccountModel.IUpgradePendingDeposits] -> Script [MigrationResult (ContractId GamblingAccountModel.IUpgradePendingDeposits)]
upgradeListOfIUpgradePendingDeposits migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingAccountModel.UpgradePendingDeposits with ..

upgradeListOfIUpgradeFlag : Party -> [ContractId GamblingAccountModel.IUpgradeFlag] -> Script [MigrationResult (ContractId GamblingAccountModel.IUpgradeFlag)]
upgradeListOfIUpgradeFlag migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingAccountModel.UpgradeFlag with ..

upgradeListOfIUpgradeEventInstrumentRequest : Party -> [ContractId GamblingEventModel.IUpgradeEventInstrumentRequest] -> Script [MigrationResult (ContractId GamblingEventModel.IUpgradeEventInstrumentRequest)]
upgradeListOfIUpgradeEventInstrumentRequest migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingEventModel.UpgradeEventInstrumentRequest with ..

upgradeListOfIUpgradeEventInstrumentStatusUpdateRequest : Party -> [ContractId GamblingEventModel.IUpgradeEventInstrumentStatusUpdateRequest] -> Script [MigrationResult (ContractId GamblingEventModel.IUpgradeEventInstrumentStatusUpdateRequest)]
upgradeListOfIUpgradeEventInstrumentStatusUpdateRequest migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingEventModel.UpgradeEventInstrumentStatusUpdateRequest with ..

upgradeListOfIUpgradeEventInstrumentUpdateRequest : Party -> [ContractId GamblingEventModel.IUpgradeEventInstrumentUpdateRequest] -> Script [MigrationResult (ContractId GamblingEventModel.IUpgradeEventInstrumentUpdateRequest)]
upgradeListOfIUpgradeEventInstrumentUpdateRequest migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingEventModel.UpgradeEventInstrumentUpdateRequest with ..

upgradeListOfIUpgradeEventInstrument : Party -> [ContractId GamblingEventModel.IUpgradeEventInstrument] -> Script [MigrationResult (ContractId GamblingEventModel.IUpgradeEventInstrument)]
upgradeListOfIUpgradeEventInstrument migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingEventModel.UpgradeEventInstrument with ..

upgradeListOfIUpgradeEventInstrumentToggleFeaturedRequest : Party -> [ContractId GamblingEventModel.IUpgradeEventInstrumentToggleFeaturedRequest] -> Script [MigrationResult (ContractId GamblingEventModel.IUpgradeEventInstrumentToggleFeaturedRequest)]
upgradeListOfIUpgradeEventInstrumentToggleFeaturedRequest migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingEventModel.UpgradeEventInstrumentToggleFeaturedRequest with ..

upgradeListOfIUpgradeEventInstrumentCancelRequest : Party -> [ContractId GamblingEventModel.IUpgradeEventInstrumentCancelRequest] -> Script [MigrationResult (ContractId GamblingEventModel.IUpgradeEventInstrumentCancelRequest)]
upgradeListOfIUpgradeEventInstrumentCancelRequest migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingEventModel.UpgradeEventInstrumentCancelRequest with ..

upgradeListOfIUpgradeEventInstrumentReinstateRequest : Party -> [ContractId GamblingEventModel.IUpgradeEventInstrumentReinstateRequest] -> Script [MigrationResult (ContractId GamblingEventModel.IUpgradeEventInstrumentReinstateRequest)]
upgradeListOfIUpgradeEventInstrumentReinstateRequest migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingEventModel.UpgradeEventInstrumentReinstateRequest with ..

upgradeListOfIUpgradeEventInstrumentUpdateOutcomesOddsRequest : Party -> [ContractId GamblingEventModel.IUpgradeEventInstrumentUpdateOutcomesOddsRequest] -> Script [MigrationResult (ContractId GamblingEventModel.IUpgradeEventInstrumentUpdateOutcomesOddsRequest)]
upgradeListOfIUpgradeEventInstrumentUpdateOutcomesOddsRequest migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingEventModel.UpgradeEventInstrumentUpdateOutcomesOddsRequest with ..

upgradeListOfIUpgradeEventInstrumentNoOutcomes : Party -> [ContractId GamblingEventModel.IUpgradeEventInstrumentNoOutcomes] -> Script [MigrationResult (ContractId GamblingEventModel.IUpgradeEventInstrumentNoOutcomes)]
upgradeListOfIUpgradeEventInstrumentNoOutcomes migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingEventModel.UpgradeEventInstrumentNoOutcomes with ..

upgradeListOfIUpgradeMarketMap : Party -> [ContractId GamblingEventModel.IUpgradeMarketMap] -> Script [MigrationResult (ContractId GamblingEventModel.IUpgradeMarketMap)]
upgradeListOfIUpgradeMarketMap migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingEventModel.UpgradeMarketMap with ..

upgradeListOfGamblingEventServiceIUpgradeService : Party -> [ContractId GamblingEventService.IUpgradeService] -> Script [MigrationResult (ContractId GamblingEventService.IUpgradeService)]
upgradeListOfGamblingEventServiceIUpgradeService migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingEventService.UpgradeService with ..

upgradeListOfGamblingEventServiceIUpgradeOffer : Party -> [ContractId GamblingEventService.IUpgradeOffer] -> Script [MigrationResult (ContractId GamblingEventService.IUpgradeOffer)]
upgradeListOfGamblingEventServiceIUpgradeOffer migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingEventService.UpgradeOffer with ..

upgradeListOfGamblingEventServiceIUpgradeRequest : Party -> [ContractId GamblingEventService.IUpgradeRequest] -> Script [MigrationResult (ContractId GamblingEventService.IUpgradeRequest)]
upgradeListOfGamblingEventServiceIUpgradeRequest migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingEventService.UpgradeRequest with ..

upgradeListOfIUpgradeGlobalPromotions : Party -> [ContractId MarketingModel.IUpgradeGlobalPromotions] -> Script [MigrationResult (ContractId MarketingModel.IUpgradeGlobalPromotions)]
upgradeListOfIUpgradeGlobalPromotions migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid MarketingModel.UpgradeGlobalPromotions with ..

upgradeListOfIUpgradePromotion : Party -> [ContractId MarketingModel.IUpgradePromotion] -> Script [MigrationResult (ContractId MarketingModel.IUpgradePromotion)]
upgradeListOfIUpgradePromotion migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid MarketingModel.UpgradePromotion with ..

upgradeListOfIUpgradePromotionRequest : Party -> [ContractId MarketingModel.IUpgradePromotionRequest] -> Script [MigrationResult (ContractId MarketingModel.IUpgradePromotionRequest)]
upgradeListOfIUpgradePromotionRequest migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid MarketingModel.UpgradePromotionRequest with ..

upgradeListOfIUpgradePromotionUpdateRequest : Party -> [ContractId MarketingModel.IUpgradePromotionUpdateRequest] -> Script [MigrationResult (ContractId MarketingModel.IUpgradePromotionUpdateRequest)]
upgradeListOfIUpgradePromotionUpdateRequest migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid MarketingModel.UpgradePromotionUpdateRequest with ..

upgradeListOfIUpgradePromotionWallet : Party -> [ContractId MarketingModel.IUpgradePromotionWallet] -> Script [MigrationResult (ContractId MarketingModel.IUpgradePromotionWallet)]
upgradeListOfIUpgradePromotionWallet migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid MarketingModel.UpgradePromotionWallet with ..

upgradeListOfIUpgradePromotionUsageHistory : Party -> [ContractId MarketingModel.IUpgradePromotionUsageHistory] -> Script [MigrationResult (ContractId MarketingModel.IUpgradePromotionUsageHistory)]
upgradeListOfIUpgradePromotionUsageHistory migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid MarketingModel.UpgradePromotionUsageHistory with ..

upgradeListOfMarketingServiceIUpgradeService : Party -> [ContractId MarketingService.IUpgradeService] -> Script [MigrationResult (ContractId MarketingService.IUpgradeService)]
upgradeListOfMarketingServiceIUpgradeService migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid MarketingService.UpgradeService with ..

upgradeListOfMarketingServiceIUpgradeOffer : Party -> [ContractId MarketingService.IUpgradeOffer] -> Script [MigrationResult (ContractId MarketingService.IUpgradeOffer)]
upgradeListOfMarketingServiceIUpgradeOffer migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid MarketingService.UpgradeOffer with ..

upgradeListOfMarketingServiceIUpgradeRequest : Party -> [ContractId MarketingService.IUpgradeRequest] -> Script [MigrationResult (ContractId MarketingService.IUpgradeRequest)]
upgradeListOfMarketingServiceIUpgradeRequest migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid MarketingService.UpgradeRequest with ..

upgradeListOfIUpgradeCounter : Party -> [ContractId CounterModel.IUpgradeCounter] -> Script [MigrationResult (ContractId CounterModel.IUpgradeCounter)]
upgradeListOfIUpgradeCounter migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid CounterModel.UpgradeCounter with ..

upgradeListOfOperatorRoleIUpgradeRole : Party -> [ContractId OperatorRole.IUpgradeRole] -> Script [MigrationResult (ContractId OperatorRole.IUpgradeRole)]
upgradeListOfOperatorRoleIUpgradeRole migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid OperatorRole.UpgradeRole with ..
