module Gambyl.Migration.Script.RollbackScript where

import Daml.Script

import Gambyl.Migration.Script.RollbackScriptFunctions

import Gambyl.Migration.Common
import Gambyl.Test.TestUtils

rollbackScript : Input -> Script ReportSummary
rollbackScript Input{gambyl} = script do

  debug "START RollingBack ledger"

  r01 <- collectReport "GlobalGamblingConfigurationRollback"                       $ migrateDirectly "r01-IUpgradeGlobalGamblingConfigurationRollback" gambyl upgradeListOfIUpgradeGlobalGamblingConfigurationRollback
  r02 <- collectReport "ActionFailureRollback"                                    $ migrateDirectly "r02-IUpgradeActionFailureRollback" gambyl upgradeListOfIUpgradeActionFailureRollback
  r03 <- collectReport "ActionSuccessRollback"                                    $ migrateDirectly "r03-IUpgradeActionSuccessRollback" gambyl upgradeListOfIUpgradeActionSuccessRollback
  r04 <- collectReport "GamblingRole.RoleRollback"                                $ migrateDirectly "r04-GamblingRole.IUpgradeRoleRollback" gambyl upgradeListOfGamblingRoleIUpgradeRoleRollback
  r05 <- collectReport "GamblingRole.OfferRollback"                               $ migrateDirectly "r05-GamblingRole.IUpgradeOfferRollback" gambyl upgradeListOfGamblingRoleIUpgradeOfferRollback
  r06 <- collectReport "GamblingRole.RequestRollback"                             $ migrateDirectly "r06-GamblingRole.IUpgradeRequestRollback" gambyl upgradeListOfGamblingRoleIUpgradeRequestRollback
  r07 <- collectReport "BetPlacementRequestFlagRollback"                          $ migrateDirectly "r07-IUpgradeBetPlacementRequestFlagRollback" gambyl upgradeListOfIUpgradeBetPlacementRequestFlagRollback
  r08 <- collectReport "BulkBetPlacementRequestFlagRollback"                      $ migrateDirectly "r08-IUpgradeBulkBetPlacementRequestFlagRollback" gambyl upgradeListOfIUpgradeBulkBetPlacementRequestFlagRollback
  r09 <- collectReport "BetPlacementRollback"                                     $ migrateDirectly "r09-IUpgradeBetPlacementRollback" gambyl upgradeListOfIUpgradeBetPlacementRollback

  r10 <- collectReport "BetPlacementSplitRequestRollback"                         $ migrateDirectly "r10-IUpgradeBetPlacementSplitRequestRollback" gambyl upgradeListOfIUpgradeBetPlacementSplitRequestRollback
  r11 <- collectReport "BetPlacementCancelRequestRollback"                        $ migrateDirectly "r11-IUpgradeBetPlacementCancelRequestRollback" gambyl upgradeListOfIUpgradeBetPlacementCancelRequestRollback
  r12 <- collectReport "FinalizeBetPlacementCancelRequestRollback"                $ migrateDirectly "r12-IUpgradeFinalizeBetPlacementCancelRequestRollback" gambyl upgradeListOfIUpgradeFinalizeBetPlacementCancelRequestRollback
  r13 <- collectReport "BetHistoryRollback"                                       $ migrateDirectly "r13-IUpgradeBetHistoryRollback" gambyl upgradeListOfIUpgradeBetHistoryRollback
  -- the order is important for this case
  r14 <- collectReport "GamblingBetModel.BulkBetPlacementRequestRollback"         $ migrateDirectly "r14-GamblingBetModel.IUpgradeBulkBetPlacementRequestRollback" gambyl upgradeListOfIUpgradeBulkBetPlacementRequestRollback
  r15 <- collectReport "BetPlacementRequest"                                      $ migrateDirectly "r15-IUpgradeBetPlacementRequestRollback" gambyl upgradeListOfIUpgradeBetPlacementRequestRollback
  -- the order is important for this case
  r16 <- collectReport "GamblingBetModel.BulkBetPlacementFinalizeRequestRollback" $ migrateDirectly "r16-GamblingBetModel.IUpgradeBulkBetPlacementFinalizeRequestRollback" gambyl upgradeListOfIUpgradeBulkBetPlacementFinalizeRequestRollback
  r17 <- collectReport "BetPlacementFinalizeRequest"                              $ migrateDirectly "r17-IUpgradeBetPlacementFinalizeRequestRollback" gambyl upgradeListOfIUpgradeBetPlacementFinalizeRequestRollback
  r18 <- collectReport "BulkBetPlacementRollback"                                 $ migrateDirectly "r18-IUpgradeBulkBetPlacementRollback" gambyl upgradeListOfIUpgradeBulkBetPlacementRollback
  r19 <- collectReport "GamblingListingRollback"                                  $ migrateDirectly "r19-IUpgradeGamblingListingRollback" gambyl upgradeListOfIUpgradeGamblingListingRollback

  r20 <- collectReport "GamblingUpdateListingRequestRollback"                     $ migrateDirectly "r20-IUpgradeGamblingUpdateListingRequestRollback" gambyl upgradeListOfIUpgradeGamblingUpdateListingRequestRollback
  r21 <- collectReport "FailedGamblingUpdateListingRollback"                      $ migrateDirectly "r21-IUpgradeFailedGamblingUpdateListingRollback" gambyl upgradeListOfIUpgradeFailedGamblingUpdateListingRollback
  r22 <- collectReport "AccountRollback"                                          $ migrateDirectly "r22-IUpgradeAccountRollback" gambyl upgradeListOfIUpgradeAccountRollback
  r23 <- collectReport "WithdrawRequestForApprovalRollback"                       $ migrateDirectly "r23-IUpgradeWithdrawRequestForApprovalRollback" gambyl upgradeListOfIUpgradeWithdrawRequestForApprovalRollback
  r24 <- collectReport "DepositRequestForApprovalRollback"                        $ migrateDirectly "r24-IUpgradeDepositRequestForApprovalRollback" gambyl upgradeListOfIUpgradeDepositRequestForApprovalRollback
  r25 <- collectReport "TransactionHistoryRollback"                               $ migrateDirectly "r25-IUpgradeTransactionHistoryRollback" gambyl upgradeListOfIUpgradeTransactionHistoryRollback
  r26 <- collectReport "PendingTransactionRollback"                               $ migrateDirectly "r26-IUpgradePendingTransactionRollback" gambyl upgradeListOfIUpgradePendingTransactionRollback
  r27 <- collectReport "PendingDepositsRollback"                                  $ migrateDirectly "r27-IUpgradePendingDepositsRollback" gambyl upgradeListOfIUpgradePendingDepositsRollback
  r28 <- collectReport "FlagRollback"                                             $ migrateDirectly "r28-IUpgradeFlagRollback" gambyl upgradeListOfIUpgradeFlagRollback
  r29 <- collectReport "EventInstrumentRequestRollback"                           $ migrateDirectly "r29-IUpgradeEventInstrumentRequestRollback" gambyl upgradeListOfIUpgradeEventInstrumentRequestRollback

  r30 <- collectReport "EventInstrumentStatusUpdateRequestRollback"               $ migrateDirectly "r30-IUpgradeEventInstrumentStatusUpdateRequestRollback" gambyl upgradeListOfIUpgradeEventInstrumentStatusUpdateRequestRollback
  r31 <- collectReport "EventInstrumentUpdateRequestRollback"                     $ migrateDirectly "r31-IUpgradeEventInstrumentUpdateRequestRollback" gambyl upgradeListOfIUpgradeEventInstrumentUpdateRequestRollback
  r32 <- collectReport "EventInstrumentRollback"                                  $ migrateDirectly "r32-IUpgradeEventInstrumentRollback" gambyl upgradeListOfIUpgradeEventInstrumentRollback
  r33 <- collectReport "EventInstrumentToggleFeaturedRequestRollback"             $ migrateDirectly "r33-IUpgradeEventInstrumentToggleFeaturedRequestRollback" gambyl upgradeListOfIUpgradeEventInstrumentToggleFeaturedRequestRollback
  r34 <- collectReport "EventInstrumentCancelRequestRollback"                     $ migrateDirectly "r34-IUpgradeEventInstrumentCancelRequestRollback" gambyl upgradeListOfIUpgradeEventInstrumentCancelRequestRollback
  r35 <- collectReport "EventInstrumentReinstateRequestRollback"                  $ migrateDirectly "r35-IUpgradeEventInstrumentReinstateRequestRollback" gambyl upgradeListOfIUpgradeEventInstrumentReinstateRequestRollback
  r36 <- collectReport "EventInstrumentUpdateOutcomesOddsRequestRollback"         $ migrateDirectly "r36-IUpgradeEventInstrumentUpdateOutcomesOddsRequestRollback" gambyl upgradeListOfIUpgradeEventInstrumentUpdateOutcomesOddsRequestRollback
  r37 <- collectReport "EventInstrumentNoOutcomesRollback"                        $ migrateDirectly "r37-IUpgradeEventInstrumentNoOutcomesRollback" gambyl upgradeListOfIUpgradeEventInstrumentNoOutcomesRollback
  r38 <- collectReport "MarketMapRollback"                                        $ migrateDirectly "r38-IUpgradeMarketMapRollback" gambyl upgradeListOfIUpgradeMarketMapRollback
  r39 <- collectReport "GamblingService.ServiceRollback"                          $ migrateDirectly "r39-GamblingService.IUpgradeServiceRollback" gambyl upgradeListOfGamblingServiceIUpgradeServiceRollback

  r40 <- collectReport "GamblingService.OfferRollback"                            $ migrateDirectly "r40-GamblingService.IUpgradeOfferRollback" gambyl upgradeListOfGamblingServiceIUpgradeOfferRollback
  -- NOTE: this is a customer contract and does not need to be migrated
  r41 <- collectReport "GamblingService.RequestRollback"                          $ migrateDirectly "r41-GamblingService.IUpgradeRequestRollback" gambyl upgradeListOfGamblingServiceIUpgradeRequestRollback
  r42 <- collectReport "BlockedServiceRollback"                                   $ migrateDirectly "r42-IUpgradeBlockedServiceRollback" gambyl upgradeListOfIUpgradeBlockedServiceRollback
  r43 <- collectReport "PendingPromotionApplicationRollback"                      $ migrateDirectly "r43-IUpgradePendingPromotionApplicationRollback" gambyl upgradeListOfIUpgradePendingPromotionApplicationRollback
  r44 <- collectReport "PendingIdentityRollback"                                  $ migrateDirectly "r44-IUpgradePendingIdentityRollback" gambyl upgradeListOfIUpgradePendingIdentityRollback
  r45 <- collectReport "GamblerUnverifiedIdentityRequestRollback"                  $ migrateDirectly "r45-IUpgradeGamblerUnverifiedIdentityRequestRollback" gambyl upgradeListOfIUpgradeGamblerUnverifiedIdentityRequestRollback
  r46 <- collectReport "GamblerUnverifiedIdentityRollback"                         $ migrateDirectly "r46-IUpgradeGamblerUnverifiedIdentityRollback" gambyl upgradeListOfIUpgradeGamblerUnverifiedIdentityRollback
  r47 <- collectReport "GamblerIdentityRollback"                                  $ migrateDirectly "r47-IUpgradeGamblerIdentityRollback" gambyl upgradeListOfIUpgradeGamblerIdentityRollback
  r48 <- collectReport "RejectedIdentityRollback"                                 $ migrateDirectly "r48-IUpgradeRejectedIdentityRollback" gambyl upgradeListOfIUpgradeRejectedIdentityRollback
  r49 <- collectReport "ExecutedBetsRollback"                                     $ migrateDirectly "r49-IUpgradeExecutedBetsRollback" gambyl upgradeListOfIUpgradeExecutedBetsRollback

  r50 <- collectReport "GamblingEventService.ServiceRollback"                     $ migrateDirectly "r50-GamblingEventService.IUpgradeServiceRollback" gambyl upgradeListOfGamblingEventServiceIUpgradeServiceRollback
  r51 <- collectReport "GamblingEventService.OfferRollback"                       $ migrateDirectly "r51-GamblingEventService.IUpgradeOfferRollback" gambyl upgradeListOfGamblingEventServiceIUpgradeOfferRollback
  -- NOTE: this is a customer contract and does not need to be migrated
  r52 <- collectReport "GamblingEventService.RequestRollback"                     $ migrateDirectly "r52-GamblingEventService.IUpgradeRequestRollback" gambyl upgradeListOfGamblingEventServiceIUpgradeRequestRollback
  r53 <- collectReport "GlobalPromotionsRollback"                                 $ migrateDirectly "r53-IUpgradeGlobalPromotionsRollback" gambyl upgradeListOfIUpgradeGlobalPromotionsRollback
  r54 <- collectReport "PromotionRollback"                                        $ migrateDirectly "r54-IUpgradePromotionRollback" gambyl upgradeListOfIUpgradePromotionRollback
  r55 <- collectReport "PromotionRequestRollback"                                 $ migrateDirectly "r55-IUpgradePromotionRequestRollback" gambyl upgradeListOfIUpgradePromotionRequestRollback
  r56 <- collectReport "PromotionUpdateRequestRollback"                           $ migrateDirectly "r56-IUpgradePromotionUpdateRequestRollback" gambyl upgradeListOfIUpgradePromotionUpdateRequestRollback
  r57 <- collectReport "PromotionWalletRollback"                                  $ migrateDirectly "r57-IUpgradePromotionWalletRollback" gambyl upgradeListOfIUpgradePromotionWalletRollback
  r58 <- collectReport "PromotionUsageHistoryRollback"                            $ migrateDirectly "r58-IUpgradePromotionUsageHistoryRollback" gambyl upgradeListOfIUpgradePromotionUsageHistoryRollback
  r59 <- collectReport "MarketingService.ServiceRollback"                         $ migrateDirectly "r59-MarketingService.IUpgradeServiceRollback" gambyl upgradeListOfMarketingServiceIUpgradeServiceRollback

  r60 <- collectReport "MarketingService.OfferRollback"                           $ migrateDirectly "r60-MarketingService.IUpgradeOfferRollback" gambyl upgradeListOfMarketingServiceIUpgradeOfferRollback
  -- NOTE: this is a customer contract and does not need to be migrated
  r61 <- collectReport "MarketingService.RequestRollback"                         $ migrateDirectly "r61-MarketingService.IUpgradeRequestRollback" gambyl upgradeListOfMarketingServiceIUpgradeRequestRollback
  r62 <- collectReport "CounterRollback"                                          $ migrateDirectly "r62-IUpgradeCounterRollback" gambyl upgradeListOfIUpgradeCounterRollback
  r63 <- collectReport "OperatorRole.RoleRollback"                                $ migrateDirectly "r63-OperatorRole.IUpgradeRoleRollback" gambyl upgradeListOfOperatorRoleIUpgradeRoleRollback
  r64 <- collectReport "CancelledBetPlacementRollback"                            $ migrateDirectly "r64-IUpgradeCancelledBetPlacementRollback" gambyl upgradeListOfIUpgradeCancelledBetPlacementRollback

  debug "FINISH RollingBack ledger"

  let report = foldl combineReport (ReportSummary 0 0 0 0 []) [
          r01, r02, r03, r04, r05, r06, r07, r08, r09
          , r10, r11, r12, r13, r14, r15, r16, r17, r18, r19
          , r20, r21, r22, r23, r24, r25, r26, r27, r28, r29
          , r30, r31, r32, r33, r34, r35, r36, r37, r38, r39
          , r40, r41, r42, r43, r44, r45, r46, r47, r48, r49
          , r50, r51, r52, r53, r54, r55, r56, r57, r58, r59
          , r60, r61, r62, r63, r64
        ]

  pure report
