module Gambyl.Migration.Gambyl.Gambling.ModelRollback where

import Gambyl.Migration.Upgrade

import Gambyl.Migration.Gambyl.Gambling.Bet.Odds.ModelRollback ()

import Legacy.Gambyl.Gambling.Model qualified as LegacyGamblingModel
import Current.Gambyl.Gambling.Model qualified as CurrentGamblingModel

instance DAMLUpgrade CurrentGamblingModel.Permissions LegacyGamblingModel.Permissions where
  convert CurrentGamblingModel.Events = LegacyGamblingModel.Events
  convert CurrentGamblingModel.Bets = LegacyGamblingModel.Bets

instance DAMLUpgrade CurrentGamblingModel.Actionable LegacyGamblingModel.Actionable where
  convert CurrentGamblingModel.Deposit = LegacyGamblingModel.Deposit
  convert CurrentGamblingModel.Withdrawal = LegacyGamblingModel.Withdrawal
  convert CurrentGamblingModel.BetPlacement = LegacyGamblingModel.BetPlacement
  convert CurrentGamblingModel.BetCancelation = LegacyGamblingModel.BetCancelation
  convert CurrentGamblingModel.BetMatched = LegacyGamblingModel.BetMatched
  convert CurrentGamblingModel.Promotion = LegacyGamblingModel.Promotion
  convert CurrentGamblingModel.Bonus = LegacyGamblingModel.Bonus
  convert CurrentGamblingModel.Settlement = LegacyGamblingModel.Settlement
  convert CurrentGamblingModel.EventInstrumentOrigination = LegacyGamblingModel.EventInstrumentOrigination
  convert CurrentGamblingModel.EventInstrumentUpdate = LegacyGamblingModel.EventInstrumentUpdate
  convert CurrentGamblingModel.EventInstrumentStatusUpdate = LegacyGamblingModel.EventInstrumentStatusUpdate
  convert CurrentGamblingModel.IdentityVerification = LegacyGamblingModel.IdentityVerification

instance DAMLUpgrade CurrentGamblingModel.GlobalGamblingConfiguration LegacyGamblingModel.GlobalGamblingConfiguration where
  convert CurrentGamblingModel.GlobalGamblingConfiguration{..} = LegacyGamblingModel.GlobalGamblingConfiguration with
    maxOdd = convert maxOdd
    minOdd = convert minOdd
    defaultOdds = convert defaultOdds
    ..

instance DAMLUpgrade CurrentGamblingModel.ActionFailure LegacyGamblingModel.ActionFailure where
  convert CurrentGamblingModel.ActionFailure{..} = LegacyGamblingModel.ActionFailure with
    action = convert action
    ..

instance DAMLUpgrade CurrentGamblingModel.ActionSuccess LegacyGamblingModel.ActionSuccess where
  convert CurrentGamblingModel.ActionSuccess{..} = LegacyGamblingModel.ActionSuccess with
    action = convert action
    ..
