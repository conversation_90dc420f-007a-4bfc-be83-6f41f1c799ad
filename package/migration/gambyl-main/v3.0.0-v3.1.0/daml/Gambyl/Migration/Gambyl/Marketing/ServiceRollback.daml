module Gambyl.Migration.Gambyl.Marketing.ServiceRollback where

import Gambyl.Migration.Upgrade

import Legacy.Gambyl.Marketing.Service qualified as LegacyMarketingService
import Current.Gambyl.Marketing.Service qualified as CurrentMarketingService

instance DAMLUpgrade CurrentMarketingService.Service LegacyMarketingService.Service where
  convert CurrentMarketingService.Service{..} = LegacyMarketingService.Service with
    ..

instance DAMLUpgrade CurrentMarketingService.Offer LegacyMarketingService.Offer where
  convert CurrentMarketingService.Offer{..} = LegacyMarketingService.Offer with
    ..

instance DAMLUpgrade CurrentMarketingService.Request LegacyMarketingService.Request where
  convert CurrentMarketingService.Request{..} = LegacyMarketingService.Request with
    ..
