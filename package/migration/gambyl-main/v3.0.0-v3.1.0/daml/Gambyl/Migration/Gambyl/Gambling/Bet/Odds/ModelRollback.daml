module Gambyl.Migration.Gambyl.Gambling.Bet.Odds.ModelRollback where

import Gambyl.Migration.Upgrade

import Legacy.Gambyl.Gambling.Bet.Odds.Model qualified as LegacyOddsModel
import Current.Gambyl.Gambling.Bet.Odds.Model qualified as CurrentOddsModel

instance DAMLUpgrade CurrentOddsModel.OddType LegacyOddsModel.OddType where
    convert (CurrentOddsModel.Fractional val) = LegacyOddsModel.Fractional val
    convert (CurrentOddsModel.Decimal val) = LegacyOddsModel.Decimal val
    convert (CurrentOddsModel.Moneyline val) = LegacyOddsModel.Moneyline val
