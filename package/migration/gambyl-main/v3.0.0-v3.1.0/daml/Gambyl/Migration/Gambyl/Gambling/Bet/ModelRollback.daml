module Gambyl.Migration.Gambyl.Gambling.Bet.ModelRollback where

import Gambyl.Migration.Upgrade

import Gambyl.Migration.Gambyl.Gambling.Bet.Odds.ModelRollback ()
import Gambyl.Migration.Gambyl.Marketing.ModelRollback ()
import Gambyl.Migration.EnetPulse.EventsRollback ()

import Legacy.Gambyl.Gambling.Bet.Model qualified as LegacyBetModel
import Current.Gambyl.Gambling.Bet.Model qualified as CurrentBetModel

instance DAMLUpgrade CurrentBetModel.Details LegacyBetModel.Details where
    convert CurrentBetModel.Details{..} = LegacyBetModel.Details with
        side = convert side
        ..

instance DAMLUpgrade CurrentBetModel.Side LegacyBetModel.Side where
    convert (CurrentBetModel.Back val) = LegacyBetModel.Back $ convert val
    convert (CurrentBetModel.Lay val) = LegacyBetModel.Lay $ convert val

instance DAMLUpgrade CurrentBetModel.SideDetails LegacyBetModel.SideDetails where
  convert CurrentBetModel.SideDetails{..} = LegacyBetModel.SideDetails with
    odd = convert odd
    -- outcome = convert outcome
    -- TODO: Uncomment when there's changes between an legacy and current versions of Jumio models
    ..

instance DAMLUpgrade CurrentBetModel.Status LegacyBetModel.Status where
  convert CurrentBetModel.Unmatched = LegacyBetModel.Unmatched
  convert CurrentBetModel.Matched = LegacyBetModel.Matched
  convert CurrentBetModel.Closed = LegacyBetModel.Closed

instance DAMLUpgrade CurrentBetModel.Winnings LegacyBetModel.Winnings where
  convert CurrentBetModel.Winnings{..} = LegacyBetModel.Winnings with
        ..

instance DAMLUpgrade CurrentBetModel.BetPlacementRequest LegacyBetModel.BetPlacementRequest where
    convert CurrentBetModel.BetPlacementRequest{..} = LegacyBetModel.BetPlacementRequest with
        details = convert details
        promotion = convert promotion
        ..

instance DAMLUpgrade CurrentBetModel.BetPlacementRequestFlag LegacyBetModel.BetPlacementRequestFlag where
  convert CurrentBetModel.BetPlacementRequestFlag{..} = LegacyBetModel.BetPlacementRequestFlag with
        ..

instance DAMLUpgrade CurrentBetModel.BulkBetPlacementRequestFlag LegacyBetModel.BulkBetPlacementRequestFlag where
  convert CurrentBetModel.BulkBetPlacementRequestFlag{..} = LegacyBetModel.BulkBetPlacementRequestFlag with
        ..

instance DAMLUpgrade CurrentBetModel.BetPlacementFinalizeRequest LegacyBetModel.BetPlacementFinalizeRequest where
    convert CurrentBetModel.BetPlacementFinalizeRequest{..} = LegacyBetModel.BetPlacementFinalizeRequest with
        details = convert details
        promotion = convert promotion
        ..

instance DAMLUpgrade CurrentBetModel.BetPlacement LegacyBetModel.BetPlacement where
  convert CurrentBetModel.BetPlacement{..} = LegacyBetModel.BetPlacement with
        promotion = convert promotion
        details = convert details
        status = convert status
        ..

instance DAMLUpgrade CurrentBetModel.BetPlacementSplitRequest LegacyBetModel.BetPlacementSplitRequest where
  convert CurrentBetModel.BetPlacementSplitRequest{..} = LegacyBetModel.BetPlacementSplitRequest with
        ..

instance DAMLUpgrade CurrentBetModel.BetPlacementCancelRequest LegacyBetModel.BetPlacementCancelRequest where
  convert CurrentBetModel.BetPlacementCancelRequest{..} = LegacyBetModel.BetPlacementCancelRequest with
        ..

instance DAMLUpgrade CurrentBetModel.FinalizeBetPlacementCancelRequest LegacyBetModel.FinalizeBetPlacementCancelRequest where
  convert CurrentBetModel.FinalizeBetPlacementCancelRequest{..} = LegacyBetModel.FinalizeBetPlacementCancelRequest with
        bonusBetAmount = convert bonusBetAmount
        ..

-- TODO: Uncomment in next version (> v3.0.0)
-- instance DAMLUpgrade CurrentBetModel.CancelledBetPlacement LegacyBetModel.CancelledBetPlacement where
--   convert CurrentBetModel.CancelledBetPlacement{..} = LegacyBetModel.CancelledBetPlacement with
--         ..

instance DAMLUpgrade CurrentBetModel.BetHistory LegacyBetModel.BetHistory where
  convert CurrentBetModel.BetHistory{..} = LegacyBetModel.BetHistory with
        winnings = convert winnings
        details = convert details
        ..

instance DAMLUpgrade CurrentBetModel.BulkBetPlacementRequest LegacyBetModel.BulkBetPlacementRequest where
    convert CurrentBetModel.BulkBetPlacementRequest{..} = LegacyBetModel.BulkBetPlacementRequest with
        promotion = convert promotion
        ..

instance DAMLUpgrade CurrentBetModel.BulkBetPlacementFinalizeRequest LegacyBetModel.BulkBetPlacementFinalizeRequest where
    convert CurrentBetModel.BulkBetPlacementFinalizeRequest{..} = LegacyBetModel.BulkBetPlacementFinalizeRequest with
        promotion = convert promotion
        ..

instance DAMLUpgrade CurrentBetModel.BulkBetPlacement LegacyBetModel.BulkBetPlacement where
  convert CurrentBetModel.BulkBetPlacement{..} = LegacyBetModel.BulkBetPlacement with
        promotion = convert promotion
        ..
