module Gambyl.Migration.Gambyl.Gambling.Account.ModelRollback where

import Gambyl.Migration.Upgrade

import Gambyl.Migration.Gambyl.Gambling.Bet.ModelRollback ()
import Gambyl.Migration.Gambyl.Gambling.Event.ModelRollback ()
import Gambyl.Migration.Gambyl.Gambling.ModelRollback ()
import Gambyl.Migration.Gambyl.Marketing.ModelRollback ()

import Legacy.Gambyl.Gambling.Account.Model qualified as LegacyAccountModel
import Current.Gambyl.Gambling.Account.Model qualified as CurrentAccountModel

instance DAMLUpgrade CurrentAccountModel.TimedLimit LegacyAccountModel.TimedLimit where
  convert CurrentAccountModel.Daily = LegacyAccountModel.Daily
  convert CurrentAccountModel.Weekly = LegacyAccountModel.Weekly
  convert CurrentAccountModel.Monthly = LegacyAccountModel.Monthly
  convert CurrentAccountModel.Yearly = LegacyAccountModel.Yearly

instance DAMLUpgrade CurrentAccountModel.AccruedLimit LegacyAccountModel.AccruedLimit where
  convert CurrentAccountModel.AccruedLimit{..} = LegacyAccountModel.AccruedLimit with
        ..

instance DAMLUpgrade CurrentAccountModel.Account LegacyAccountModel.Account where
  convert CurrentAccountModel.Account{..} = LegacyAccountModel.Account with
        totalMainBalance = convert totalMainBalance
        totalWithdrawBalance = convert totalWithdrawBalance
        totalBetBalance = convert totalBetBalance
        totalBonusBalance = convert totalBonusBalance
        totalBonusBetBalance = convert totalBonusBetBalance
        favouriteMarkets = convert favouriteMarkets
        betHistory = convert betHistory
        totalFees = convert totalFees
        transactionHistory = convert transactionHistory
        depositLimit = convert depositLimit
        ..

instance DAMLUpgrade CurrentAccountModel.WithdrawRequestForApproval LegacyAccountModel.WithdrawRequestForApproval where
 convert CurrentAccountModel.WithdrawRequestForApproval{..} = LegacyAccountModel.WithdrawRequestForApproval with
       promotion = convert promotion
       ..

instance DAMLUpgrade CurrentAccountModel.DepositRequestForApproval LegacyAccountModel.DepositRequestForApproval where
 convert CurrentAccountModel.DepositRequestForApproval{..} = LegacyAccountModel.DepositRequestForApproval with
       promotion = convert promotion
       ..

instance DAMLUpgrade CurrentAccountModel.TransactionHistory LegacyAccountModel.TransactionHistory where
  convert CurrentAccountModel.TransactionHistory{..} = LegacyAccountModel.TransactionHistory with
        confirmedAmount = convert confirmedAmount
        transactionType = convert transactionType
        ..

instance DAMLUpgrade CurrentAccountModel.PendingTransaction LegacyAccountModel.PendingTransaction where
  convert CurrentAccountModel.PendingTransaction{..} = LegacyAccountModel.PendingTransaction with
        transactionType = convert transactionType
        requestedAmount = convert requestedAmount
        ..

instance DAMLUpgrade CurrentAccountModel.PendingDeposits LegacyAccountModel.PendingDeposits where
  convert CurrentAccountModel.PendingDeposits{..} = LegacyAccountModel.PendingDeposits with
        ..

instance DAMLUpgrade CurrentAccountModel.Flag LegacyAccountModel.Flag where
  convert CurrentAccountModel.Flag{..} = LegacyAccountModel.Flag with
        amount = convert amount
        ..
