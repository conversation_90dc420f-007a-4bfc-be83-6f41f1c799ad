module Gambyl.Migration.Gambyl.Gambling.Bet.Model where


import Gambyl.Migration.Upgrade

import Gambyl.Migration.Gambyl.Gambling.Bet.Odds.Model ()
import Gambyl.Migration.Gambyl.Marketing.Model ()
import Gambyl.Migration.EnetPulse.Events ()

import Legacy.Gambyl.Gambling.Bet.Model qualified as LegacyBetModel
import Current.Gambyl.Gambling.Bet.Model qualified as CurrentBetModel


instance DAMLUpgrade LegacyBetModel.Details CurrentBetModel.Details where
  convert LegacyBetModel.Details{..} = CurrentBetModel.Details with
        side = convert side
        ..

instance DAMLUpgrade LegacyBetModel.Side CurrentBetModel.Side where
  convert (LegacyBetModel.Back val) = CurrentBetModel.Back $ convert val
  convert (LegacyBetModel.Lay val) = CurrentBetModel.Lay $ convert val

instance DAMLUpgrade LegacyBetModel.SideDetails CurrentBetModel.SideDetails where
  convert LegacyBetModel.SideDetails{..} = CurrentBetModel.SideDetails with
    odd = convert odd
    -- outcome = convert outcome
    -- TODO: Uncomment when there's changes between an legacy and current versions of Jumio models
    ..

instance DAMLUpgrade LegacyBetModel.Status CurrentBetModel.Status where
  convert LegacyBetModel.Unmatched = CurrentBetModel.Unmatched
  convert LegacyBetModel.Matched = CurrentBetModel.Matched
  convert LegacyBetModel.Closed = CurrentBetModel.Closed
  convert LegacyBetModel.Cancelled = error "Bet should be migrated in a standalone process"

instance DAMLUpgrade LegacyBetModel.Winnings CurrentBetModel.Winnings where
  convert LegacyBetModel.Winnings{..} = CurrentBetModel.Winnings with
        ..

instance DAMLUpgrade LegacyBetModel.BetPlacementRequest CurrentBetModel.BetPlacementRequest where
    convert LegacyBetModel.BetPlacementRequest{..} = CurrentBetModel.BetPlacementRequest with
        details = convert details
        promotion = convert promotion
        ..

instance DAMLUpgrade LegacyBetModel.BetPlacementRequestFlag CurrentBetModel.BetPlacementRequestFlag where
  convert LegacyBetModel.BetPlacementRequestFlag{..} = CurrentBetModel.BetPlacementRequestFlag with
        ..

instance DAMLUpgrade LegacyBetModel.BulkBetPlacementRequestFlag CurrentBetModel.BulkBetPlacementRequestFlag where
  convert LegacyBetModel.BulkBetPlacementRequestFlag{..} = CurrentBetModel.BulkBetPlacementRequestFlag with
        ..

instance DAMLUpgrade LegacyBetModel.BetPlacementFinalizeRequest CurrentBetModel.BetPlacementFinalizeRequest where
    convert LegacyBetModel.BetPlacementFinalizeRequest{..} = CurrentBetModel.BetPlacementFinalizeRequest with
        details = convert details
        promotion = convert promotion
        ..

instance DAMLUpgrade LegacyBetModel.BetPlacement CurrentBetModel.BetPlacement where
  convert LegacyBetModel.BetPlacement{..} = CurrentBetModel.BetPlacement with
        promotion = convert promotion
        details = convert details
        status = convert status
        verified
        ..

instance DAMLUpgrade LegacyBetModel.BetPlacementSplitRequest CurrentBetModel.BetPlacementSplitRequest where
  convert LegacyBetModel.BetPlacementSplitRequest{..} = CurrentBetModel.BetPlacementSplitRequest with
        ..

instance DAMLUpgrade LegacyBetModel.BetPlacementCancelRequest CurrentBetModel.BetPlacementCancelRequest where
  convert LegacyBetModel.BetPlacementCancelRequest{..} = CurrentBetModel.BetPlacementCancelRequest with
      ..

instance DAMLUpgrade LegacyBetModel.FinalizeBetPlacementCancelRequest CurrentBetModel.FinalizeBetPlacementCancelRequest where
  convert LegacyBetModel.FinalizeBetPlacementCancelRequest{..} = CurrentBetModel.FinalizeBetPlacementCancelRequest with
        ..

-- TODO: Uncomment in next version (> v3.1.0)
-- instance DAMLUpgrade LegacyBetModel.CancelledBetPlacement CurrentBetModel.CancelledBetPlacement where
--   convert LegacyBetModel.CancelledBetPlacement{..} = CurrentBetModel.CancelledBetPlacement with
--         ..

instance DAMLUpgrade LegacyBetModel.BetHistory CurrentBetModel.BetHistory where
  convert LegacyBetModel.BetHistory{..} = CurrentBetModel.BetHistory with
        winnings = convert winnings
        details = convert details
        orderId = "not implemented (introduced in v3.1.0)" -- TODO: Remove in subsequent versions
        listingId = "not implemented (introduced in v3.1.0)" -- TODO: Remove in subsequent versions
        ..

instance DAMLUpgrade LegacyBetModel.BulkBetPlacementRequest CurrentBetModel.BulkBetPlacementRequest where
  convert LegacyBetModel.BulkBetPlacementRequest{..} = CurrentBetModel.BulkBetPlacementRequest with
        promotion = convert promotion
        ..

instance DAMLUpgrade LegacyBetModel.BulkBetPlacementFinalizeRequest CurrentBetModel.BulkBetPlacementFinalizeRequest where
  convert LegacyBetModel.BulkBetPlacementFinalizeRequest{..} = CurrentBetModel.BulkBetPlacementFinalizeRequest with
        promotion = convert promotion
        ..

instance DAMLUpgrade LegacyBetModel.BulkBetPlacement CurrentBetModel.BulkBetPlacement where
  convert LegacyBetModel.BulkBetPlacement{..} = CurrentBetModel.BulkBetPlacement with
        promotion = convert promotion
        ..
