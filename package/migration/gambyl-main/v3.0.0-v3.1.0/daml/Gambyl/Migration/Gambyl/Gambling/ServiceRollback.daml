module Gambyl.Migration.Gambyl.Gambling.ServiceRollback where

import Gambyl.Migration.Upgrade

import Gambyl.Migration.Gambyl.Gambling.ModelRollback ()
import Gambyl.Migration.Gambyl.Marketing.ModelRollback ()

import Legacy.Gambyl.Gambling.Service qualified as FromGamblingService
import Current.Gambyl.Gambling.Service qualified as ToGamblingService

instance DAMLUpgrade ToGamblingService.Service FromGamblingService.Service where
  convert ToGamblingService.Service{..} = FromGamblingService.Service with
    permissions = convert permissions
    ..

instance DAMLUpgrade ToGamblingService.Offer FromGamblingService.Offer where
  convert ToGamblingService.Offer{..} = FromGamblingService.Offer with
    ..

instance DAMLUpgrade ToGamblingService.Request FromGamblingService.Request where
  convert ToGamblingService.Request{..} = FromGamblingService.Request with
    ..

instance DAMLUpgrade ToGamblingService.BlockedService FromGamblingService.BlockedService where
  convert ToGamblingService.BlockedService{..} = FromGamblingService.BlockedService with
    service = convert service
    ..

instance DAMLUpgrade ToGamblingService.PendingPromotionApplication FromGamblingService.PendingPromotionApplication where
  convert ToGamblingService.PendingPromotionApplication{..} = FromGamblingService.PendingPromotionApplication with
    promotionKey = convert promotionKey
    offeredAmount = convert offeredAmount
    ..
