module Gambyl.Migration.Gambyl.Gambling.Model where

import Gambyl.Migration.Upgrade

import Gambyl.Migration.Gambyl.Gambling.Bet.Odds.Model ()

import Legacy.Gambyl.Gambling.Model qualified as LegacyGamblingModel
import Current.Gambyl.Gambling.Model qualified as CurrentGamblingModel

instance DAMLUpgrade LegacyGamblingModel.Permissions CurrentGamblingModel.Permissions where
  convert LegacyGamblingModel.Events = CurrentGamblingModel.Events
  convert LegacyGamblingModel.Bets = CurrentGamblingModel.Bets

instance DAMLUpgrade LegacyGamblingModel.Actionable CurrentGamblingModel.Actionable where
  convert LegacyGamblingModel.Deposit = CurrentGamblingModel.Deposit
  convert LegacyGamblingModel.Withdrawal = CurrentGamblingModel.Withdrawal
  convert LegacyGamblingModel.BetPlacement = CurrentGamblingModel.BetPlacement
  convert LegacyGamblingModel.BetCancelation = CurrentGamblingModel.BetCancelation
  convert LegacyGamblingModel.BetMatched = CurrentGamblingModel.BetMatched
  convert LegacyGamblingModel.Promotion = CurrentGamblingModel.Promotion
  convert LegacyGamblingModel.Bonus = CurrentGamblingModel.Bonus
  convert LegacyGamblingModel.Settlement = CurrentGamblingModel.Settlement
  convert LegacyGamblingModel.EventInstrumentOrigination = CurrentGamblingModel.EventInstrumentOrigination
  convert LegacyGamblingModel.EventInstrumentUpdate = CurrentGamblingModel.EventInstrumentUpdate
  convert LegacyGamblingModel.EventInstrumentStatusUpdate = CurrentGamblingModel.EventInstrumentStatusUpdate
  convert LegacyGamblingModel.IdentityVerification = CurrentGamblingModel.IdentityVerification


instance DAMLUpgrade LegacyGamblingModel.GlobalGamblingConfiguration CurrentGamblingModel.GlobalGamblingConfiguration where
  convert LegacyGamblingModel.GlobalGamblingConfiguration{..} = CurrentGamblingModel.GlobalGamblingConfiguration with
    maxOdd = convert maxOdd
    minOdd = convert minOdd
    defaultOdds = convert defaultOdds
    ..

instance DAMLUpgrade LegacyGamblingModel.ActionFailure CurrentGamblingModel.ActionFailure where
  convert LegacyGamblingModel.ActionFailure{..} = CurrentGamblingModel.ActionFailure with
    action = convert action
    ..

instance DAMLUpgrade LegacyGamblingModel.ActionSuccess CurrentGamblingModel.ActionSuccess where
  convert LegacyGamblingModel.ActionSuccess{..} = CurrentGamblingModel.ActionSuccess with
    action = convert action
    ..
