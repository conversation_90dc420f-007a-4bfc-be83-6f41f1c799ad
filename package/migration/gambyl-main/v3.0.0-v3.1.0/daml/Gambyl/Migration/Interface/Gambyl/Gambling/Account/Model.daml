module Gambyl.Migration.Interface.Gambyl.Gambling.Account.Model where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Gambling.Account.Model ()

import Legacy.Gambyl.Gambling.Account.Model qualified as LegacyAccountModel
import Current.Gambyl.Gambling.Account.Model qualified as CurrentAccountModel

interface IUpgradeAccount where
  viewtype CurrentAccountModel.Account

  upgradeAccount : ContractId IUpgradeAccount -> Update (ContractId IUpgradeAccount)

  nonconsuming choice UpgradeAccount : MigrationResult (ContractId IUpgradeAccount)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeAccount contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeAccount this self

  interface instance IUpgradeAccount for LegacyAccountModel.Account where
    view = convert this
    upgradeAccount self = do
      archive self
      toInterfaceContractId @IUpgradeAccount <$> create (convert this : CurrentAccountModel.Account)

  interface instance IUpgradeAccount for CurrentAccountModel.Account where
    view = this
    upgradeAccount = pure

interface IUpgradeWithdrawRequestForApproval where
  viewtype CurrentAccountModel.WithdrawRequestForApproval

  upgradeWithdrawRequestForApproval : ContractId IUpgradeWithdrawRequestForApproval -> Update (ContractId IUpgradeWithdrawRequestForApproval)

  nonconsuming choice UpgradeWithdrawRequestForApproval : MigrationResult (ContractId IUpgradeWithdrawRequestForApproval)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeWithdrawRequestForApproval contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeWithdrawRequestForApproval this self

  -- NOTE: this was commented as may result handy
  interface instance IUpgradeWithdrawRequestForApproval for LegacyAccountModel.WithdrawRequestForApproval where
   view = convert this
   upgradeWithdrawRequestForApproval self = do
    archive self
    toInterfaceContractId @IUpgradeWithdrawRequestForApproval <$> create (convert this : CurrentAccountModel.WithdrawRequestForApproval)

  interface instance IUpgradeWithdrawRequestForApproval for CurrentAccountModel.WithdrawRequestForApproval where
    view = this
    upgradeWithdrawRequestForApproval = pure

interface IUpgradeDepositRequestForApproval where
  viewtype CurrentAccountModel.DepositRequestForApproval

  upgradeDepositRequestForApproval : ContractId IUpgradeDepositRequestForApproval -> Update (ContractId IUpgradeDepositRequestForApproval)

  nonconsuming choice UpgradeDepositRequestForApproval : MigrationResult (ContractId IUpgradeDepositRequestForApproval)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeDepositRequestForApproval contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeDepositRequestForApproval this self

  -- NOTE: this was commented as may result handy
  interface instance IUpgradeDepositRequestForApproval for LegacyAccountModel.DepositRequestForApproval where
   view = convert this
   upgradeDepositRequestForApproval self = do
    archive self
    toInterfaceContractId @IUpgradeDepositRequestForApproval <$> create (convert this : CurrentAccountModel.DepositRequestForApproval)

  interface instance IUpgradeDepositRequestForApproval for CurrentAccountModel.DepositRequestForApproval where
    view = this
    upgradeDepositRequestForApproval = pure

interface IUpgradeTransactionHistory where
  viewtype CurrentAccountModel.TransactionHistory

  upgradeTransactionHistory : ContractId IUpgradeTransactionHistory -> Update (ContractId IUpgradeTransactionHistory)

  nonconsuming choice UpgradeTransactionHistory : MigrationResult (ContractId IUpgradeTransactionHistory)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeTransactionHistory contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeTransactionHistory this self

  interface instance IUpgradeTransactionHistory for LegacyAccountModel.TransactionHistory where
    view = convert this
    upgradeTransactionHistory self = do
      archive self
      toInterfaceContractId @IUpgradeTransactionHistory <$> create (convert this : CurrentAccountModel.TransactionHistory)

  interface instance IUpgradeTransactionHistory for CurrentAccountModel.TransactionHistory where
    view = this
    upgradeTransactionHistory = pure

interface IUpgradePendingTransaction where
  viewtype CurrentAccountModel.PendingTransaction

  upgradePendingTransaction : ContractId IUpgradePendingTransaction -> Update (ContractId IUpgradePendingTransaction)

  nonconsuming choice UpgradePendingTransaction : MigrationResult (ContractId IUpgradePendingTransaction)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradePendingTransaction contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradePendingTransaction this self

  interface instance IUpgradePendingTransaction for LegacyAccountModel.PendingTransaction where
    view = convert this
    upgradePendingTransaction self = do
      archive self
      toInterfaceContractId @IUpgradePendingTransaction <$> create (convert this : CurrentAccountModel.PendingTransaction)

  interface instance IUpgradePendingTransaction for CurrentAccountModel.PendingTransaction where
    view = this
    upgradePendingTransaction = pure

interface IUpgradePendingDeposits where
  viewtype CurrentAccountModel.PendingDeposits

  upgradePendingDeposits : ContractId IUpgradePendingDeposits -> Update (ContractId IUpgradePendingDeposits)

  nonconsuming choice UpgradePendingDeposits : MigrationResult (ContractId IUpgradePendingDeposits)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradePendingDeposits contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradePendingDeposits this self

  interface instance IUpgradePendingDeposits for LegacyAccountModel.PendingDeposits where
    view = convert this
    upgradePendingDeposits self = do
      archive self
      toInterfaceContractId @IUpgradePendingDeposits <$> create (convert this : CurrentAccountModel.PendingDeposits)

  interface instance IUpgradePendingDeposits for CurrentAccountModel.PendingDeposits where
    view = this
    upgradePendingDeposits = pure

interface IUpgradeFlag where
  viewtype CurrentAccountModel.Flag

  upgradeFlag : ContractId IUpgradeFlag -> Update (ContractId IUpgradeFlag)

  nonconsuming choice UpgradeFlag : MigrationResult (ContractId IUpgradeFlag)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeFlag contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeFlag this self

  interface instance IUpgradeFlag for LegacyAccountModel.Flag where
    view = convert this
    upgradeFlag self = do
      archive self
      toInterfaceContractId @IUpgradeFlag <$> create (convert this : CurrentAccountModel.Flag)

  interface instance IUpgradeFlag for CurrentAccountModel.Flag where
    view = this
    upgradeFlag = pure
