module Gambyl.Migration.Interface.Gambyl.Counter.Model where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Counter.Model ()

import Legacy.Gambyl.Counter.Model qualified as LegacyCounterModel
import Current.Gambyl.Counter.Model qualified as CurrentCounterModel

interface IUpgradeCounter where
  viewtype CurrentCounterModel.Counter

  upgradeCounter : ContractId IUpgradeCounter -> Update (ContractId IUpgradeCounter)

  nonconsuming choice UpgradeCounter : MigrationResult (ContractId IUpgradeCounter)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeCounter contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeCounter this self

  interface instance IUpgradeCounter for LegacyCounterModel.Counter where
    view = convert this
    upgradeCounter self = do
      archive self
      toInterfaceContractId @IUpgradeCounter <$> create (convert this : CurrentCounterModel.Counter)

  interface instance IUpgradeCounter for CurrentCounterModel.Counter where
    view = this
    upgradeCounter = pure
