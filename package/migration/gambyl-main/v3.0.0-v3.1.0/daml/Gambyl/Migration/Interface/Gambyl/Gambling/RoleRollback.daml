module Gambyl.Migration.Interface.Gambyl.Gambling.RoleRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Gambling.RoleRollback()

import Legacy.Gambyl.Gambling.Role qualified as FromGamblingRole
import Current.Gambyl.Gambling.Role qualified as ToGamblingRole

interface IUpgradeRoleRollback where
  viewtype FromGamblingRole.Role

  upgradeRoleRollback : ContractId IUpgradeRoleRollback -> Update (ContractId IUpgradeRoleRollback)

  nonconsuming choice UpgradeRoleRollback : MigrationResult (ContractId IUpgradeRoleRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeRoleRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeRoleRollback this self

  interface instance IUpgradeRoleRollback for ToGamblingRole.Role where
    view = convert this
    upgradeRoleRollback self = do
      archive self
      toInterfaceContractId @IUpgradeRoleRollback <$> create (convert this : FromGamblingRole.Role)

  interface instance IUpgradeRoleRollback for FromGamblingRole.Role where
    view = this
    upgradeRoleRollback = pure

interface IUpgradeOfferRollback where
  viewtype FromGamblingRole.Offer

  upgradeOfferRollback : ContractId IUpgradeOfferRollback -> Update (ContractId IUpgradeOfferRollback)

  nonconsuming choice UpgradeOfferRollback : MigrationResult (ContractId IUpgradeOfferRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeOfferRollback contract") $
          migratingParty == (view this).operator
        upgradeOfferRollback this self

  interface instance IUpgradeOfferRollback for ToGamblingRole.Offer where
    view = convert this
    upgradeOfferRollback self = do
      archive self
      toInterfaceContractId @IUpgradeOfferRollback <$> create (convert this : FromGamblingRole.Offer)

  interface instance IUpgradeOfferRollback for FromGamblingRole.Offer where
    view = this
    upgradeOfferRollback = pure

interface IUpgradeRequestRollback where
  viewtype FromGamblingRole.Request

  upgradeRequestRollback : ContractId IUpgradeRequestRollback -> Update (ContractId IUpgradeRequestRollback)

  nonconsuming choice UpgradeRequestRollback : MigrationResult (ContractId IUpgradeRequestRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeRequestRollback contract") $
          migratingParty == (view this).provider
          || migratingParty == (view this).operator
        upgradeRequestRollback this self

  interface instance IUpgradeRequestRollback for ToGamblingRole.Request where
    view = convert this
    upgradeRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeRequestRollback <$> create (convert this : FromGamblingRole.Request)

  interface instance IUpgradeRequestRollback for FromGamblingRole.Request where
    view = this
    upgradeRequestRollback = pure
