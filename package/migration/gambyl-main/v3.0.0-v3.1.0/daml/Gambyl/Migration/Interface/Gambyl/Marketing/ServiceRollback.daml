module Gambyl.Migration.Interface.Gambyl.Marketing.ServiceRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Marketing.ServiceRollback()

import Legacy.Gambyl.Marketing.Service qualified as LegacyMarketingService
import Current.Gambyl.Marketing.Service qualified as CurrentMarketingService

interface IUpgradeServiceRollback where
  viewtype LegacyMarketingService.Service

  upgradeServiceRollback : ContractId IUpgradeServiceRollback -> Update (ContractId IUpgradeServiceRollback)

  nonconsuming choice UpgradeServiceRollback : MigrationResult (ContractId IUpgradeServiceRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeServiceRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeServiceRollback this self

  interface instance IUpgradeServiceRollback for CurrentMarketingService.Service where
    view = convert this
    upgradeServiceRollback self = do
      archive self
      toInterfaceContractId @IUpgradeServiceRollback <$> create (convert this : LegacyMarketingService.Service)

  interface instance IUpgradeServiceRollback for LegacyMarketingService.Service where
    view = this
    upgradeServiceRollback = pure

interface IUpgradeOfferRollback where
  viewtype LegacyMarketingService.Offer

  upgradeOfferRollback : ContractId IUpgradeOfferRollback -> Update (ContractId IUpgradeOfferRollback)

  nonconsuming choice UpgradeOfferRollback : MigrationResult (ContractId IUpgradeOfferRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeOfferRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeOfferRollback this self

  interface instance IUpgradeOfferRollback for CurrentMarketingService.Offer where
    view = convert this
    upgradeOfferRollback self = do
      archive self
      toInterfaceContractId @IUpgradeOfferRollback <$> create (convert this : LegacyMarketingService.Offer)

  interface instance IUpgradeOfferRollback for LegacyMarketingService.Offer where
    view = this
    upgradeOfferRollback = pure

interface IUpgradeRequestRollback where
  viewtype LegacyMarketingService.Request

  upgradeRequestRollback : ContractId IUpgradeRequestRollback -> Update (ContractId IUpgradeRequestRollback)

  nonconsuming choice UpgradeRequestRollback : MigrationResult (ContractId IUpgradeRequestRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeRequestRollback contract") $
          migratingParty == (view this).provider
        upgradeRequestRollback this self

  interface instance IUpgradeRequestRollback for CurrentMarketingService.Request where
    view = convert this
    upgradeRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeRequestRollback <$> create (convert this : LegacyMarketingService.Request)

  interface instance IUpgradeRequestRollback for LegacyMarketingService.Request where
    view = this
    upgradeRequestRollback = pure
