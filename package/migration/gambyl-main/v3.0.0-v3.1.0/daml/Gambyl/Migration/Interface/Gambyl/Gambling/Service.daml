module Gambyl.Migration.Interface.Gambyl.Gambling.Service where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Gambling.Service ()

import Legacy.Gambyl.Gambling.Service qualified as FromGamblingService
import Current.Gambyl.Gambling.Service qualified as ToGamblingService

interface IUpgradeService where
  viewtype ToGamblingService.Service

  upgradeService : ContractId IUpgradeService -> Update (ContractId IUpgradeService)

  nonconsuming choice UpgradeService : MigrationResult (ContractId IUpgradeService)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeService contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeService this self

  interface instance IUpgradeService for FromGamblingService.Service where
    view = convert this
    upgradeService self = do
      archive self
      toInterfaceContractId @IUpgradeService <$> create (convert this : ToGamblingService.Service)

  interface instance IUpgradeService for ToGamblingService.Service where
    view = this
    upgradeService = pure

interface IUpgradeOffer where
  viewtype ToGamblingService.Offer

  upgradeOffer : ContractId IUpgradeOffer -> Update (ContractId IUpgradeOffer)

  nonconsuming choice UpgradeOffer : MigrationResult (ContractId IUpgradeOffer)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeOffer contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeOffer this self

  interface instance IUpgradeOffer for FromGamblingService.Offer where
    view = convert this
    upgradeOffer self = do
      archive self
      toInterfaceContractId @IUpgradeOffer <$> create (convert this : ToGamblingService.Offer)

  interface instance IUpgradeOffer for ToGamblingService.Offer where
    view = this
    upgradeOffer = pure

interface IUpgradeRequest where
  viewtype ToGamblingService.Request

  upgradeRequest : ContractId IUpgradeRequest -> Update (ContractId IUpgradeRequest)

  nonconsuming choice UpgradeRequest : MigrationResult (ContractId IUpgradeRequest)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeRequest contract") $
          migratingParty == (view this).provider
        upgradeRequest this self

  interface instance IUpgradeRequest for FromGamblingService.Request where
    view = convert this
    upgradeRequest self = do
      archive self
      toInterfaceContractId @IUpgradeRequest <$> create (convert this : ToGamblingService.Request)

  interface instance IUpgradeRequest for ToGamblingService.Request where
    view = this
    upgradeRequest = pure

interface IUpgradeBlockedService where
  viewtype ToGamblingService.BlockedService

  upgradeBlockedService : ContractId IUpgradeBlockedService -> Update (ContractId IUpgradeBlockedService)

  nonconsuming choice UpgradeBlockedService : MigrationResult (ContractId IUpgradeBlockedService)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeBlockedService contract") $
          migratingParty == (view this).service.operator
          || migratingParty == (view this).service.provider
        upgradeBlockedService this self

  interface instance IUpgradeBlockedService for FromGamblingService.BlockedService where
    view = convert this
    upgradeBlockedService self = do
      archive self
      toInterfaceContractId @IUpgradeBlockedService <$> create (convert this : ToGamblingService.BlockedService)

  interface instance IUpgradeBlockedService for ToGamblingService.BlockedService where
    view = this
    upgradeBlockedService = pure

interface IUpgradePendingPromotionApplication where
  viewtype ToGamblingService.PendingPromotionApplication

  upgradePendingPromotionApplication : ContractId IUpgradePendingPromotionApplication -> Update (ContractId IUpgradePendingPromotionApplication)

  nonconsuming choice UpgradePendingPromotionApplication : MigrationResult (ContractId IUpgradePendingPromotionApplication)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradePendingPromotionApplication contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradePendingPromotionApplication this self

  interface instance IUpgradePendingPromotionApplication for FromGamblingService.PendingPromotionApplication where
    view = convert this
    upgradePendingPromotionApplication self = do
      archive self
      toInterfaceContractId @IUpgradePendingPromotionApplication <$> create (convert this : ToGamblingService.PendingPromotionApplication)

  interface instance IUpgradePendingPromotionApplication for ToGamblingService.PendingPromotionApplication where
    view = this
    upgradePendingPromotionApplication = pure
