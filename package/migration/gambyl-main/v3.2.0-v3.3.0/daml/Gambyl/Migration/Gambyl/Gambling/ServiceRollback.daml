module Gambyl.Migration.Gambyl.Gambling.ServiceRollback where

import Gambyl.Migration.Upgrade

import Gambyl.Migration.Gambyl.Gambling.ModelRollback ()
import Gambyl.Migration.Gambyl.Marketing.ModelRollback ()

import Legacy.Gambyl.Gambling.Service qualified as LegacyGamblingService
import Current.Gambyl.Gambling.Service qualified as CurrentGamblingService

instance DAMLUpgrade CurrentGamblingService.Service LegacyGamblingService.Service where
  convert CurrentGamblingService.Service{..} = LegacyGamblingService.Service with
    permissions = convert permissions
    ..

instance DAMLUpgrade CurrentGamblingService.Offer LegacyGamblingService.Offer where
  convert CurrentGamblingService.Offer{..} = LegacyGamblingService.Offer with
    ..

instance DAMLUpgrade CurrentGamblingService.Request LegacyGamblingService.Request where
  convert CurrentGamblingService.Request{..} = LegacyGamblingService.Request with
    ..

instance DAMLUpgrade CurrentGamblingService.BlockedService LegacyGamblingService.BlockedService where
  convert CurrentGamblingService.BlockedService{..} = LegacyGamblingService.BlockedService with
    service = convert service
    ..

instance DAMLUpgrade CurrentGamblingService.PendingPromotionApplication LegacyGamblingService.PendingPromotionApplication where
  convert CurrentGamblingService.PendingPromotionApplication{..} = LegacyGamblingService.PendingPromotionApplication with
    promotionKey = convert promotionKey
    offeredAmount = convert offeredAmount
    ..
