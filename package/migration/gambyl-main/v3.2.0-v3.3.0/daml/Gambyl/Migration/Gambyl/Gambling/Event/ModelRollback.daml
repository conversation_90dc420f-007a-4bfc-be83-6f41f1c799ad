module Gambyl.Migration.Gambyl.Gambling.Event.ModelRollback where

import Gambyl.Migration.Upgrade

import Gambyl.Migration.Gambyl.Gambling.Bet.Odds.ModelRollback ()
import Gambyl.Migration.EnetPulse.EventsRollback ()

import Legacy.Gambyl.Gambling.Event.Model qualified as LegacyEventModel
import Current.Gambyl.Gambling.Event.Model qualified as CurrentEventModel

instance DAMLUpgrade CurrentEventModel.Market LegacyEventModel.Market where
  convert (CurrentEventModel.Sport val) = LegacyEventModel.Sport $ convert val
  convert CurrentEventModel.Politics = LegacyEventModel.Politics
  convert CurrentEventModel.Entertainment = LegacyEventModel.Entertainment
  convert (CurrentEventModel.Other_Market val) = LegacyEventModel.Other_Market $ convert val

instance DAMLUpgrade CurrentEventModel.Submarket LegacyEventModel.Submarket where

  convert (CurrentEventModel.Tournament text) = LegacyEventModel.Tournament text
  convert (CurrentEventModel.Other_Submarket text) = LegacyEventModel.Other_Submarket text

instance DAMLUpgrade CurrentEventModel.Status LegacyEventModel.Status where
  convert CurrentEventModel.Expired = LegacyEventModel.Expired
  convert CurrentEventModel.Active = LegacyEventModel.Active

instance DAMLUpgrade CurrentEventModel.OutcomeOdds LegacyEventModel.OutcomeOdds where
  convert CurrentEventModel.OutcomeOdds{..} = LegacyEventModel.OutcomeOdds with
    odds = convert odds
    outcome = convert outcome
    -- TODO: Uncomment when there's changes between an legacy and current versions of Enetpulse models
    ..

instance DAMLUpgrade CurrentEventModel.Details LegacyEventModel.Details where
  convert CurrentEventModel.Details{..} = LegacyEventModel.Details with
    market = convert market
    submarkets = convert submarkets
    outcomes = convert outcomes
    origin = convert origin
    eventParticipants = convert eventParticipants
    eventStatus = convert eventStatus
    eventResults = convert eventResults
    -- TODO: Uncomment when there's changes between an legacy and current versions of Enetpulse models
    ..

instance DAMLUpgrade CurrentEventModel.OriginType LegacyEventModel.OriginType where
  convert CurrentEventModel.Customer = LegacyEventModel.Customer
  convert CurrentEventModel.Integration = LegacyEventModel.Integration

instance DAMLUpgrade CurrentEventModel.InputOutcomeOdd LegacyEventModel.InputOutcomeOdd where
  convert CurrentEventModel.InputOutcomeOdd{..} = LegacyEventModel.InputOutcomeOdd with
    odd = convert odd
    outcome = convert outcome
    -- TODO: Uncomment when there's changes between an legacy and current versions of Enetpulse models
    ..

instance DAMLUpgrade CurrentEventModel.EventInstrumentRequest LegacyEventModel.EventInstrumentRequest where
  convert CurrentEventModel.EventInstrumentRequest{..} = LegacyEventModel.EventInstrumentRequest with
    details = convert details
    ..

instance DAMLUpgrade CurrentEventModel.EventInstrumentStatusUpdateRequest LegacyEventModel.EventInstrumentStatusUpdateRequest where
  convert CurrentEventModel.EventInstrumentStatusUpdateRequest{..} = LegacyEventModel.EventInstrumentStatusUpdateRequest with
    newEventStatus = convert newEventStatus
    eventResults = convert eventResults
    -- TODO: Uncomment when there's changes between an legacy and current versions of Enetpulse models
    ..

instance DAMLUpgrade CurrentEventModel.EventInstrumentUpdateRequest LegacyEventModel.EventInstrumentUpdateRequest where
  convert CurrentEventModel.EventInstrumentUpdateRequest{..} = LegacyEventModel.EventInstrumentUpdateRequest with
    details = convert details
    ..

instance DAMLUpgrade CurrentEventModel.EventInstrument LegacyEventModel.EventInstrument where
  convert CurrentEventModel.EventInstrument{..} = LegacyEventModel.EventInstrument with
    details = convert details
    status = convert status
    ..

instance DAMLUpgrade CurrentEventModel.EventInstrumentToggleFeaturedRequest LegacyEventModel.EventInstrumentToggleFeaturedRequest where
  convert CurrentEventModel.EventInstrumentToggleFeaturedRequest{..} = LegacyEventModel.EventInstrumentToggleFeaturedRequest with
    ..

instance DAMLUpgrade CurrentEventModel.EventInstrumentCancelRequest LegacyEventModel.EventInstrumentCancelRequest where
  convert CurrentEventModel.EventInstrumentCancelRequest{..} = LegacyEventModel.EventInstrumentCancelRequest with
    ..

instance DAMLUpgrade CurrentEventModel.EventInstrumentReinstateRequest LegacyEventModel.EventInstrumentReinstateRequest where
  convert CurrentEventModel.EventInstrumentReinstateRequest{..} = LegacyEventModel.EventInstrumentReinstateRequest with
      ..

instance DAMLUpgrade CurrentEventModel.EventInstrumentUpdateOutcomesOddsRequest LegacyEventModel.EventInstrumentUpdateOutcomesOddsRequest where
  convert CurrentEventModel.EventInstrumentUpdateOutcomesOddsRequest{..} = LegacyEventModel.EventInstrumentUpdateOutcomesOddsRequest with
    details = convert details
    status = convert status
    newOutcomes = convert newOutcomes
    ..

instance DAMLUpgrade CurrentEventModel.EventInstrumentNoOutcomes LegacyEventModel.EventInstrumentNoOutcomes where
  convert CurrentEventModel.EventInstrumentNoOutcomes{..} = LegacyEventModel.EventInstrumentNoOutcomes with
    event = convert event
    ..

instance DAMLUpgrade CurrentEventModel.MarketMap LegacyEventModel.MarketMap where
  convert CurrentEventModel.MarketMap{..} = LegacyEventModel.MarketMap with
    map = convert map
    ..

