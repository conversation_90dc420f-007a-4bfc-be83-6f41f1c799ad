module Gambyl.Migration.Gambyl.Gambling.Event.Service where

import Gambyl.Migration.Upgrade

import Legacy.Gambyl.Gambling.Event.Service qualified as LegacyEventService
import Current.Gambyl.Gambling.Event.Service qualified as CurrentEventService

instance DAMLUpgrade LegacyEventService.Service CurrentEventService.Service where
  convert LegacyEventService.Service{..} = CurrentEventService.Service with
        ..

instance DAMLUpgrade LegacyEventService.Offer CurrentEventService.Offer where
  convert LegacyEventService.Offer{..} = CurrentEventService.Offer with
        ..

instance DAMLUpgrade LegacyEventService.Request CurrentEventService.Request where
  convert LegacyEventService.Request{..} = CurrentEventService.Request with
        ..
