module Gambyl.Migration.Gambyl.Gambling.Bet.Odds.Model where

import Gambyl.Migration.Upgrade

import Legacy.Gambyl.Gambling.Bet.Odds.Model qualified as LegacyOddsModel
import Current.Gambyl.Gambling.Bet.Odds.Model qualified as CurrentOddsModel

instance DAMLUpgrade LegacyOddsModel.OddType CurrentOddsModel.OddType where
  convert (LegacyOddsModel.Fractional val) = CurrentOddsModel.Fractional val
  convert (LegacyOddsModel.Decimal val) = CurrentOddsModel.Decimal val
  convert (LegacyOddsModel.Moneyline val) = CurrentOddsModel.Moneyline val

