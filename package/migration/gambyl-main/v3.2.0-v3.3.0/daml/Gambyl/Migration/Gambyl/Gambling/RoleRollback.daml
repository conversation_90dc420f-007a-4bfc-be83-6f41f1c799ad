module Gambyl.Migration.Gambyl.Gambling.RoleRollback where

import Gambyl.Migration.Upgrade

import Gambyl.Migration.Gambyl.Gambling.ServiceRollback ()

import Legacy.Gambyl.Gambling.Role qualified as LegacyGamblingRole
import Current.Gambyl.Gambling.Role qualified as CurrentGamblingRole

instance DAMLUpgrade CurrentGamblingRole.Role LegacyGamblingRole.Role where
  convert CurrentGamblingRole.Role{..} = LegacyGamblingRole.Role with
    ..

instance DAMLUpgrade CurrentGamblingRole.Offer LegacyGamblingRole.Offer where
  convert CurrentGamblingRole.Offer{..} = LegacyGamblingRole.Offer with
    ..

instance DAMLUpgrade CurrentGamblingRole.Request LegacyGamblingRole.Request where
  convert CurrentGamblingRole.Request{..} = LegacyGamblingRole.Request with
    ..
