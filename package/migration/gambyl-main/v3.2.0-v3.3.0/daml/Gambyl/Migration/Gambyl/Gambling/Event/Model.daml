module Gambyl.Migration.Gambyl.Gambling.Event.Model where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.EnetPulse.Events ()
import Gambyl.Migration.Gambyl.Gambling.Bet.Odds.Model ()

import Legacy.Gambyl.Gambling.Event.Model qualified as LegacyEventModel
import Current.Gambyl.Gambling.Event.Model qualified as CurrentEventModel

instance DAMLUpgrade LegacyEventModel.Market CurrentEventModel.Market where
  convert (LegacyEventModel.Sport val) = CurrentEventModel.Sport $ convert val
  convert LegacyEventModel.Politics = CurrentEventModel.Politics
  convert LegacyEventModel.Entertainment = CurrentEventModel.Entertainment
  convert (LegacyEventModel.Other_Market val) = CurrentEventModel.Other_Market $ convert val

instance DAMLUpgrade LegacyEventModel.Submarket CurrentEventModel.Submarket where

  convert (LegacyEventModel.Tournament text) = CurrentEventModel.Tournament text
  convert (LegacyEventModel.Other_Submarket text) = CurrentEventModel.Other_Submarket text

instance DAMLUpgrade LegacyEventModel.Status CurrentEventModel.Status where
  convert LegacyEventModel.Expired = CurrentEventModel.Expired
  convert LegacyEventModel.Active = CurrentEventModel.Active

instance DAMLUpgrade LegacyEventModel.OutcomeOdds CurrentEventModel.OutcomeOdds where
  convert LegacyEventModel.OutcomeOdds{..} = CurrentEventModel.OutcomeOdds with
    odds = convert odds
    outcome = convert outcome
    -- TODO: Uncomment when there's changes between an legacy and current versions of Enetpulse models
    ..

instance DAMLUpgrade LegacyEventModel.Details CurrentEventModel.Details where
  convert LegacyEventModel.Details{..} = CurrentEventModel.Details with
    market = convert market
    submarkets = convert submarkets
    outcomes = convert outcomes
    origin = convert origin
    eventParticipants = convert eventParticipants
    eventStatus = convert eventStatus
    eventResults = convert eventResults
    -- TODO: Uncomment when there's changes between an legacy and current versions of Enetpulse models
    ..

instance DAMLUpgrade LegacyEventModel.OriginType CurrentEventModel.OriginType where
  convert LegacyEventModel.Customer = CurrentEventModel.Customer
  convert LegacyEventModel.Integration = CurrentEventModel.Integration

instance DAMLUpgrade LegacyEventModel.InputOutcomeOdd CurrentEventModel.InputOutcomeOdd where
  convert LegacyEventModel.InputOutcomeOdd{..} = CurrentEventModel.InputOutcomeOdd with
    outcome = convert outcome
    -- TODO: Uncomment when there's changes between an legacy and current versions of Enetpulse models
    odd = convert odd
    ..

instance DAMLUpgrade LegacyEventModel.EventInstrumentRequest CurrentEventModel.EventInstrumentRequest where
  convert LegacyEventModel.EventInstrumentRequest{..} = CurrentEventModel.EventInstrumentRequest with
    details = convert details
    ..

instance DAMLUpgrade LegacyEventModel.EventInstrumentStatusUpdateRequest CurrentEventModel.EventInstrumentStatusUpdateRequest where
  convert LegacyEventModel.EventInstrumentStatusUpdateRequest{..} = CurrentEventModel.EventInstrumentStatusUpdateRequest with
    newEventStatus = convert newEventStatus
    eventResults = convert eventResults
    -- TODO: Uncomment when there's changes between an legacy and current versions of Enetpulse models
    ..

instance DAMLUpgrade LegacyEventModel.EventInstrumentUpdateRequest CurrentEventModel.EventInstrumentUpdateRequest where
  convert LegacyEventModel.EventInstrumentUpdateRequest{..} = CurrentEventModel.EventInstrumentUpdateRequest with
    details = convert details
    ..

instance DAMLUpgrade LegacyEventModel.EventInstrument CurrentEventModel.EventInstrument where
  convert LegacyEventModel.EventInstrument{..} = CurrentEventModel.EventInstrument with
    details = convert details
    status = convert status
    ..

instance DAMLUpgrade LegacyEventModel.EventInstrumentToggleFeaturedRequest CurrentEventModel.EventInstrumentToggleFeaturedRequest where
  convert LegacyEventModel.EventInstrumentToggleFeaturedRequest{..} = CurrentEventModel.EventInstrumentToggleFeaturedRequest with
      ..

instance DAMLUpgrade LegacyEventModel.EventInstrumentCancelRequest CurrentEventModel.EventInstrumentCancelRequest where
  convert LegacyEventModel.EventInstrumentCancelRequest{..} = CurrentEventModel.EventInstrumentCancelRequest with
      ..

instance DAMLUpgrade LegacyEventModel.EventInstrumentReinstateRequest CurrentEventModel.EventInstrumentReinstateRequest where
  convert LegacyEventModel.EventInstrumentReinstateRequest{..} = CurrentEventModel.EventInstrumentReinstateRequest with
    ..

instance DAMLUpgrade LegacyEventModel.EventInstrumentUpdateOutcomesOddsRequest CurrentEventModel.EventInstrumentUpdateOutcomesOddsRequest where
  convert LegacyEventModel.EventInstrumentUpdateOutcomesOddsRequest{..} = CurrentEventModel.EventInstrumentUpdateOutcomesOddsRequest with
    details = convert details
    status = convert status
    newOutcomes = convert newOutcomes
    ..

instance DAMLUpgrade LegacyEventModel.EventInstrumentNoOutcomes CurrentEventModel.EventInstrumentNoOutcomes where
  convert LegacyEventModel.EventInstrumentNoOutcomes{..} = CurrentEventModel.EventInstrumentNoOutcomes with
    event = convert event
    ..

instance DAMLUpgrade LegacyEventModel.MarketMap CurrentEventModel.MarketMap where
  convert LegacyEventModel.MarketMap{..} = CurrentEventModel.MarketMap with
    map = convert map
    ..