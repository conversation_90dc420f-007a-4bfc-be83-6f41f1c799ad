module Gambyl.Migration.Gambyl.Gambling.Service where

import Gambyl.Migration.Upgrade

import Gambyl.Migration.Gambyl.Gambling.Model ()
import Gambyl.Migration.Gambyl.Marketing.Model ()

import Legacy.Gambyl.Gambling.Service qualified as LegacyGamblingService
import Current.Gambyl.Gambling.Service qualified as CurrentGamblingService

instance DAMLUpgrade LegacyGamblingService.Service CurrentGamblingService.Service where
  convert LegacyGamblingService.Service{..} = CurrentGamblingService.Service with
    permissions = convert permissions
    ..

instance DAMLUpgrade LegacyGamblingService.Offer CurrentGamblingService.Offer where
  convert LegacyGamblingService.Offer{..} = CurrentGamblingService.Offer with
    ..

instance DAMLUpgrade LegacyGamblingService.Request CurrentGamblingService.Request where
  convert LegacyGamblingService.Request{..} = CurrentGamblingService.Request with
    ..

instance DAMLUpgrade LegacyGamblingService.BlockedService CurrentGamblingService.BlockedService where
  convert LegacyGamblingService.BlockedService{..} = CurrentGamblingService.BlockedService with
    service = convert service
    blockedAt = convert blockedAt
    ..

instance DAMLUpgrade LegacyGamblingService.PendingPromotionApplication CurrentGamblingService.PendingPromotionApplication where
  convert LegacyGamblingService.PendingPromotionApplication{..} = CurrentGamblingService.PendingPromotionApplication with
    promotionKey = convert promotionKey
    offeredAmount = convert offeredAmount
    ..
