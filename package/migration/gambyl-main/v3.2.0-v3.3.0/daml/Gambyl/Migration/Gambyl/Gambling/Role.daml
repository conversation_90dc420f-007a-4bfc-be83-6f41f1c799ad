module Gambyl.Migration.Gambyl.Gambling.Role where

import Gambyl.Migration.Upgrade

import Gambyl.Migration.Gambyl.Gambling.Service ()

import Legacy.Gambyl.Gambling.Role qualified as LegacyGamblingRole
import Current.Gambyl.Gambling.Role qualified as CurrentGamblingRole

instance DAMLUpgrade LegacyGamblingRole.Role CurrentGamblingRole.Role where
  convert LegacyGamblingRole.Role{..} = CurrentGamblingRole.Role with
    ..

instance DAMLUpgrade LegacyGamblingRole.Offer CurrentGamblingRole.Offer where
  convert LegacyGamblingRole.Offer{..} = CurrentGamblingRole.Offer with
    ..

instance DAMLUpgrade LegacyGamblingRole.Request CurrentGamblingRole.Request where
  convert LegacyGamblingRole.Request{..} = CurrentGamblingRole.Request with
    ..
