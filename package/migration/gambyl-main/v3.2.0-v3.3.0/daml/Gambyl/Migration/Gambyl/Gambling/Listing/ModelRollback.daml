module Gambyl.Migration.Gambyl.Gambling.Listing.ModelRollback where

import Gambyl.Migration.Upgrade

import Gambyl.Migration.Gambyl.Gambling.Bet.Odds.ModelRollback ()
import Gambyl.Migration.EnetPulse.EventsRollback ()

import Legacy.Gambyl.Gambling.Listing.Model qualified as LegacyListingModel
import Current.Gambyl.Gambling.Listing.Model qualified as CurrentListingModel

instance DAMLUpgrade CurrentListingModel.Status LegacyListingModel.Status where
  convert CurrentListingModel.Active = LegacyListingModel.Active
  convert CurrentListingModel.Disabled = LegacyListingModel.Disabled
  convert CurrentListingModel.Fail = LegacyListingModel.Fail

instance DAMLUpgrade CurrentListingModel.GamblingListing LegacyListingModel.GamblingListing where
  convert CurrentListingModel.GamblingListing{..} = LegacyListingModel.GamblingListing with
    layOdds = convert layOdds
    backOdds = convert backOdds
    status = convert status
    outcome = convert outcome
    -- TODO: Uncomment when there's changes between an legacy and current versions of Enetpulse models
    ..

instance DAMLUpgrade CurrentListingModel.GamblingUpdateListingRequest LegacyListingModel.GamblingUpdateListingRequest where
  convert CurrentListingModel.GamblingUpdateListingRequest{..} = LegacyListingModel.GamblingUpdateListingRequest with
    status = convert status
    ..

instance DAMLUpgrade CurrentListingModel.FailedGamblingUpdateListing LegacyListingModel.FailedGamblingUpdateListing where
  convert CurrentListingModel.FailedGamblingUpdateListing{..} = LegacyListingModel.FailedGamblingUpdateListing with
    ..
