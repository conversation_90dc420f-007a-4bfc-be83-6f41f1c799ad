  module Gambyl.Migration.Test.TestFunctionsRollback where

  import Daml.Script

  import DA.Date (toDateUTC, datetime, Month(..))
  import DA.Finance.Types
  import DA.Map qualified as Map
  import DA.Set qualified as Set
  import DA.Text (intercalate)

  import Marketplace.Custody.Role qualified as Custodian
  import Marketplace.Issuance.Service qualified as IssuanceService
  import Marketplace.Operator.Role qualified as Operator
  import Marketplace.Regulator.Model qualified as Regulator
  import Marketplace.Settlement.Model qualified as SettlementModel
  import Marketplace.Trading.Role qualified as Exchange

  import Current.EnetPulseIntegration.Events (Status(Finished))
  import Current.EnetPulseIntegration.Events qualified as IntegrationEvents

  import Current.JumioIntegration.Identity qualified as IntegrationIdentity

  import Current.Gambyl.Counter.Model qualified as CounterModel
  import Current.Gambyl.Gambling.Account.Model qualified as GamblingAccountModel
  import Current.Gambyl.Gambling.Bet.Model qualified as GamblingBetModel
  import Current.Gambyl.Gambling.Bet.Odds.Model                    (OddType(..),)
  import Current.Gambyl.Gambling.Event.Model qualified as GamblingEventModel
  import Current.Gambyl.Gambling.Event.Service qualified as GamblingEventService
  import Current.Gambyl.Gambling.Identity.Model qualified as GamblingIdentityModel
  import Current.Gambyl.Gambling.Listing.Model qualified as GamblingListingModel
  import Current.Gambyl.Gambling.Model qualified as GamblingModel
  import Current.Gambyl.Gambling.Role qualified as GamblingRole
  import Current.Gambyl.Gambling.Service qualified as GamblingService
  import Current.Gambyl.Gambling.Settlement.Model qualified as GamblingSettlementModel
  import Current.Gambyl.Marketing.Model qualified as MarketingModel
  import Current.Gambyl.Marketing.Service qualified as MarketingService
  import Current.Gambyl.Operator.Role qualified as OperatorRole
  import Current.Gambyl.Utils qualified as GambylUtils

  import Gambyl.Test.TestUtils (prepareAssetDeposit, prepareAssetDescription)

  {-- CONTRACTS --}

-- | r01-IUpgradeGlobalGamblingConfigurationRollback
  prepareIUpgradeGlobalGamblingConfiguration : [Party] -> Script (ContractId GamblingModel.GlobalGamblingConfiguration)
  prepareIUpgradeGlobalGamblingConfiguration parties = script do
    let
      (operator :: provider :: _ :: _ :: publicParty :: _) = parties

      flaggedAmount = 2.5
      unverifiedAccountMaxAmount = 2.5
      minDepositAmount = 500.0
      minWithdrawAmount = 100.0
      integrationParties = Map.fromList [("provider", provider)]
      betFee = 1.5
      depositFee = 0.5
      withdrawFee = 0.5
      archiveEventDays = 7
      legalAge = 18
      isOnMaintenance = False
      -- TODO: prepare all types for OddType enum
      maxOdd = Decimal 3.0
      minOdd = Decimal 1.5
      daysPostponedEventExpires = 30
      minutesMarketMapExpires = 180
      allowedPeriod = 7
      defaultOdds = Moneyline 0
      public = Set.fromList [publicParty]

    submitMulti [operator, provider] [] $ do
      createCmd GamblingModel.GlobalGamblingConfiguration with ..

-- | r02-IUpgradeActionFailureRollback
  prepareIUpgradeActionFailure : [Party] -> Script (ContractId GamblingModel.ActionFailure)
  prepareIUpgradeActionFailure parties = script do
    let
      (operator :: provider :: customer :: _) = parties
      actionId = "Action-1"
      -- TODO: prepare all types for GamblingModel.Actionable enum
      action = GamblingModel.EventInstrumentUpdate
      reason = "Some Reason"

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingModel.ActionFailure  with ..

-- | r03-IUpgradeActionSuccessRollback
  prepareIUpgradeActionSuccess : [Party] -> Script (ContractId GamblingModel.ActionSuccess)
  prepareIUpgradeActionSuccess parties = script do
    let
      (operator :: provider :: customer :: _) = parties
      actionId = "Action-1"
      -- TODO: prepare all types for GamblingModel.Actionable enum
      action = GamblingModel.EventInstrumentUpdate
      reason = "Some Reason"

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingModel.ActionSuccess  with ..

-- | r04-GamblingRole.IUpgradeRoleRollback
  prepareGamblingRoleIUpgradeRole : [Party] -> Script (ContractId GamblingRole.Role)
  prepareGamblingRoleIUpgradeRole parties = script do
    let
      (operator :: provider :: _) = parties

    submitMulti [operator, provider] [] $ do
      createCmd GamblingRole.Role with ..

-- | r05-GamblingRole.IUpgradeOfferRollback
  prepareGamblingRoleIUpgradeOffer : [Party] -> Script (ContractId GamblingRole.Offer)
  prepareGamblingRoleIUpgradeOffer parties = script do
    let
      (operator :: provider :: _ :: _ :: publicParty :: _) = parties
      observers = Set.empty

    custodianOfferCid <- submitMulti [operator] [] $ do
      createCmd Custodian.Offer with ..

    exchangeOfferCid <- submitMulti [operator] [] $ do
      createCmd Exchange.Offer with ..

    let
      aggregateFor = Some (custodianOfferCid, exchangeOfferCid)
      public = Set.fromList [publicParty]

    submitMulti [operator] [] $ do
      createCmd GamblingRole.Offer with ..

-- | r06-GamblingRole.IUpgradeRequestRollback
  -- None: currently the only provider is the operator
  prepareGamblingRoleIUpgradeRequest : [Party] -> Script (ContractId GamblingRole.Request)
  prepareGamblingRoleIUpgradeRequest parties = script do
    let
      (operator :: provider :: _) = parties

    custodianRequestCid <- submitMulti [operator] [] $ do
      createCmd Custodian.Request with
        provider = operator
        ..

    exchangeRequestCid <- submitMulti [operator] [] $ do
      createCmd Exchange.Request with
        provider = operator
        ..

    let
      aggregateFor = Some (custodianRequestCid, exchangeRequestCid)
      integrationParties = Map.fromList [("provider", provider)]

    submitMulti [operator] [] $ do
      createCmd GamblingRole.Request with
        provider = operator
        ..

-- | r07-IUpgradeBetPlacementRequestFlagRollback
  prepareIUpgradeBetPlacementRequestFlag : [Party] -> Script (ContractId GamblingBetModel.BetPlacementRequestFlag)
  prepareIUpgradeBetPlacementRequestFlag parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    submitMulti [operator, provider] [] $ do
      createCmd GamblingBetModel.BetPlacementRequestFlag with ..

-- | r08-IUpgradeBulkBetPlacementRequestFlagRollback
  prepareIUpgradeBulkBetPlacementRequestFlag : [Party] -> Script (ContractId GamblingBetModel.BulkBetPlacementRequestFlag)
  prepareIUpgradeBulkBetPlacementRequestFlag parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    submitMulti [operator, provider] [] $ do
      createCmd GamblingBetModel.BulkBetPlacementRequestFlag with ..

-- | r09-IUpgradeBetPlacementRollback
  prepareIUpgradeBetPlacement : [Party] -> Script (ContractId GamblingBetModel.BetPlacement)
  prepareIUpgradeBetPlacement parties = script do

    let
      (operator :: provider :: customer :: other :: _) = parties

    cashDepositCid <- prepareAssetDeposit [operator, provider, customer, other]

    assetDescriptionCid <- prepareAssetDescription [operator, provider, customer, other] 2

    placedAt <- getTime
    let
      orderId = "orderId-1"
      -- TODO: prepare all types for Status enum
      status = GamblingBetModel.Matched
      matchedOdd = Some 3.0
      bonusBetAmount = Some 20.0

      accountId = Id with
          signatories = Set.fromList [provider]
          label = "AccountLabel"
          version = 1
      account = Account with
        id = accountId
        provider = operator
        owner = customer

      senderAccount = account
      depositCid = cashDepositCid
      receiverAccount = Account with
          id = Id with
            signatories = Set.fromList [provider]
            label = "AccountLabel"
            version = 1
          provider = operator
          owner = other
      settlementDetails = SettlementModel.SettlementDetails with
        ..

    settlementInstructionCid <- submitMulti [operator, provider, customer, other] [] $ do
      createCmd SettlementModel.SettlementInstruction with
        details = [settlementDetails]
        ..

    eventStartDate <- getTime
    let
      -- TODO: prepare all types for OddType enum
      odd = Decimal 1.5
      stake = 10.0
      outcome = IntegrationEvents.Outcome with
        participantId = Some "participantId"
        participantOrder = 0
        order = 5
        -- TODO: prepare all types for OutcomeType enum
        type_ = IntegrationEvents.ThreeWay
        -- TODO: prepare all types for OutcomeSubType enum
        subtype = IntegrationEvents.Draw
      sideDetails = GamblingBetModel.SideDetails with ..
      -- TODO: prepare all types for Side enum
      side = GamblingBetModel.Back sideDetails
      eventTitle = Map.fromList [(show GambylUtils.EN_US, "eventTitle")]
      eventKey = (provider, customer, "event-key")

      instruments = Map.fromList [("assetDescription", assetDescriptionCid)]
      betIssuanceKey = (operator, customer, "issuanceKey-1")
      listingId = "listingId-1"

      -- TODO: prepare all types for PromotionAction enum
      promotionAction = MarketingModel.Deposit . MarketingModel.Bonus . MarketingModel.Percentage $ 0.2
      promotionKey = (operator, provider, customer, promotionAction, "promotion-1")
      promotion = Some promotionKey

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingBetModel.BetPlacement with
        settlementInstructionCid = Some settlementInstructionCid
        details = GamblingBetModel.Details with
          betPlacementId = "betPlacementId-2"
          ..
        inBulk = Some "bulk_id-2"
        verified = False
        previousSplitBetPlacementIdList = []
        ..

-- | r10-IUpgradeBetPlacementSplitRequestRollback
  prepareIUpgradeBetPlacementSplitRequest : [Party] -> Script (ContractId GamblingBetModel.BetPlacementSplitRequest)
  prepareIUpgradeBetPlacementSplitRequest parties = script do
    let
      (operator :: provider :: customer :: _) = parties

      remainingQuantity = 10.5

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingBetModel.BetPlacementSplitRequest with
        betPlacementId = "betPlacementId-3"
        ..

-- | r11-IUpgradeBetPlacementCancelRequestRollback
  prepareIUpgradeBetPlacementCancelRequest : [Party] -> Script (ContractId GamblingBetModel.BetPlacementCancelRequest)
  prepareIUpgradeBetPlacementCancelRequest parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingBetModel.BetPlacementCancelRequest with
        betPlacementId = "betPlacementId-4"
        orderId = "orderId-1"
        ..

-- | r12-IUpgradeFinalizeBetPlacementCancelRequestRollback
  prepareIUpgradeFinalizeBetPlacementCancelRequest : [Party] -> Script (ContractId GamblingBetModel.FinalizeBetPlacementCancelRequest)
  prepareIUpgradeFinalizeBetPlacementCancelRequest parties = script do
    let
      (operator :: provider :: customer :: other :: _) = parties

      cancelOrderRequestKey = (customer, "cancel-key-1")
      eventLabel = "event label"
      isUserStarted = True

    _ <- prepareAssetDeposit [operator, provider, customer, other]

    _ <- prepareAssetDescription [operator, provider, customer, other] 3

    let
      betIssuanceKey = (operator, customer, "issuanceKey-1")
      orderId = "orderId-1"
      bonusBetAmount = Some 20.0
      reason = "Some Reason"

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingBetModel.FinalizeBetPlacementCancelRequest with
        betPlacementId = "betPlacementId-5"
        ..

-- | r13-IUpgradeBetHistoryRollback
  prepareIUpgradeBetHistory : [Party] -> Script (ContractId GamblingBetModel.BetHistory)
  prepareIUpgradeBetHistory parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    closedAt <- getTime
    placedAt <- getTime
    eventStartDate <- getTime
    let
      won = True
      winnings = Some GamblingBetModel.Winnings with
        grossAmount = 10.0
        netAmount = 7.5
        appliedFee = 0.5
      bonusBetAmount = Some 20.0
      orderId = "order-id"
      listingId = "listing-id"

      -- TODO: prepare all types for OddType enum
      odd = Decimal 1.5
      stake = 10.0
      outcome = IntegrationEvents.Outcome with
        participantId = Some "participantId"
        participantOrder = 0
        order = 5
        -- TODO: prepare all types for OutcomeType enum
        type_ = IntegrationEvents.ThreeWay
        -- TODO: prepare all types for OutcomeSubType enum
        subtype = IntegrationEvents.Draw
      sideDetails = GamblingBetModel.SideDetails with ..
      -- TODO: prepare all types for Side enum
      side = GamblingBetModel.Back sideDetails
      eventTitle = Map.fromList [(show GambylUtils.EN_US, "eventTitle")]
      eventKey = (provider, customer, "event-key")

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingBetModel.BetHistory with
        counterBetPlacementId = "counterBetPlacementId-6"
        details = GamblingBetModel.Details with
          betPlacementId = "betPlacementId-6"
          ..
        ..

-- | r14-GamblingBetModel.IUpgradeBulkBetPlacementRequestRollback
  prepareGamblingBetModelIUpgradeBulkBetPlacementRequest : [Party] -> Script (ContractId GamblingBetModel.BulkBetPlacementRequest)
  prepareGamblingBetModelIUpgradeBulkBetPlacementRequest parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    let
      -- TODO: prepare all types for PromotionAction enum
      promotionAction = MarketingModel.Deposit . MarketingModel.Bonus . MarketingModel.Percentage $ 0.2
      promotionKey = (operator, provider, customer, promotionAction, "promotion-1")
      promotion = Some promotionKey

    let
      totalStake = 7.5
      totalRealStake = 7.5
      totalPayout = 7.5
      betPlacementReqKeyList = [(operator, provider, customer, "betPlacementId-1")]

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingBetModel.BulkBetPlacementRequest with
        bulkBetPlacementId = "bulkBetPlacementId-1"
        ..

-- | r15-IUpgradeBetPlacementRequestRollback
  prepareIUpgradeBetPlacementRequest : Show a => [Party] -> a -> Script (ContractId GamblingBetModel.BetPlacementRequest)
  prepareIUpgradeBetPlacementRequest parties id = script do
    let
      (operator :: provider :: customer :: other :: _) = parties

    cashDepositCid <- prepareAssetDeposit [operator, provider, customer, other]

    eventStartDate <- getTime

    let
      -- TODO: prepare all types for OddType enum
      odd = Decimal 1.5
      stake = 10.0
      outcome = IntegrationEvents.Outcome with
        participantId = Some "participantId"
        participantOrder = 0
        order = 5
        -- TODO: prepare all types for OutcomeType enum
        type_ = IntegrationEvents.ThreeWay
        -- TODO: prepare all types for OutcomeSubType enum
        subtype = IntegrationEvents.Draw
      sideDetails = GamblingBetModel.SideDetails with ..
      -- TODO: prepare all types for Side enum
      side = GamblingBetModel.Back sideDetails
      eventTitle = Map.fromList [(show GambylUtils.EN_US, "eventTitle")]
      eventKey = (provider, customer, "event-key")
      optBonusAmount = Some 20.0
      -- TODO: prepare all types for PromotionAction enum
      promotionAction = MarketingModel.Deposit . MarketingModel.Bonus . MarketingModel.Percentage $ 0.2
      promotionKey = (operator, provider, customer, promotionAction, "promotion-1")
      promotion = Some promotionKey

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingBetModel.BetPlacementRequest with
        cashDeposit = cashDepositCid
        details = GamblingBetModel.Details with
          betPlacementId = "betPlacementId-" <> show id
          ..
        inBulk = Some "bulk_id-3"
        ..

-- | r16-GamblingBetModel.IUpgradeBulkBetPlacementFinalizeRequestRollback
  prepareGamblingBetModelIUpgradeBulkBetPlacementFinalizeRequest : [Party] -> Script (ContractId GamblingBetModel.BulkBetPlacementFinalizeRequest)
  prepareGamblingBetModelIUpgradeBulkBetPlacementFinalizeRequest parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    -- XXX: excercises scenario 13-IUpgradeBetPlacementFinalizeRequest
    _ <- prepareIUpgradeBetPlacementFinalizeRequest parties 1

    let
      betPlacementListingIds = ["betPlacementListingId-1"]
      totalStake = 7.5
      totalPayout = 7.5
      -- TODO: prepare all types for PromotionAction enum
      promotionAction = MarketingModel.Deposit . MarketingModel.Bonus . MarketingModel.Percentage $ 0.2
      promotionKey = (operator, provider, customer, promotionAction, "promotion-1")
      promotion = Some promotionKey
      betPlacementFinReqKeyList = [(operator, provider, customer, "betPlacementId-1")]

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingBetModel.BulkBetPlacementFinalizeRequest with
        bulkBetPlacementId = "bulkBetPlacementId-2"
        ..

-- | r17-IUpgradeBetPlacementFinalizeRequestRollback
  prepareIUpgradeBetPlacementFinalizeRequest : [Party] -> Int -> Script (ContractId GamblingBetModel.BetPlacementFinalizeRequest)
  prepareIUpgradeBetPlacementFinalizeRequest parties id = script do
    let
      (operator :: provider :: customer :: other :: _) = parties

    assetDescriptionCid <- prepareAssetDescription [operator, provider, customer, other] id

    cashDepositCid <- prepareAssetDeposit [operator, provider, customer, other]

    let
      instruments = Map.fromList [("assetDescription", assetDescriptionCid)]
      betIssuanceKey = (operator, customer, "issuanceKey-1")
      betDepositCid = cashDepositCid
      listingId = "listingId-1"

    eventStartDate <- getTime
    let
      -- TODO: prepare all types for OddType enum
      odd = Decimal 1.5
      stake = 10.0
      outcome = IntegrationEvents.Outcome with
        participantId = Some "participantId"
        participantOrder = 0
        order = 5
        -- TODO: prepare all types for OutcomeType enum
        type_ = IntegrationEvents.ThreeWay
        -- TODO: prepare all types for OutcomeSubType enum
        subtype = IntegrationEvents.Draw
      sideDetails = GamblingBetModel.SideDetails with ..
      -- TODO: prepare all types for Side enum
      side = GamblingBetModel.Back sideDetails
      eventTitle = Map.fromList [(show GambylUtils.EN_US, "eventTitle")]
      eventKey = (provider, customer, "event-key")
      optBonusAmount = Some 20.0
      -- TODO: prepare all types for PromotionAction enum
      promotionAction = MarketingModel.Deposit . MarketingModel.Bonus . MarketingModel.Percentage $ 0.2
      promotionKey = (operator, provider, customer, promotionAction, "promotion-1")
      promotion = Some promotionKey

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingBetModel.BetPlacementFinalizeRequest with
        details = GamblingBetModel.Details with
          betPlacementId = "betPlacementId-" <> show id
          ..
        inBulk = Some "bulk_id-1"
        ..

-- | r18-IUpgradeBulkBetPlacementRollback
  prepareIUpgradeBulkBetPlacement : [Party] -> Script (ContractId GamblingBetModel.BulkBetPlacement)
  prepareIUpgradeBulkBetPlacement parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    let
      betPlacementIdList = ["betPlacementId"]
      totalStake = 7.5
      totalPayout = 7.5
      -- TODO: prepare all types for PromotionAction enum
      promotionAction = MarketingModel.Deposit . MarketingModel.Bonus . MarketingModel.Percentage $ 0.2
      promotionKey = (operator, provider, customer, promotionAction, "promotion-1")
      promotion = Some promotionKey

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingBetModel.BulkBetPlacement with
        bulkBetPlacementId = "bulkBetPlacementId-3"
        ..

-- | r19-IUpgradeGamblingListingRollback
  prepareIUpgradeGamblingListing : [Party] -> Script (ContractId GamblingListingModel.GamblingListing)
  prepareIUpgradeGamblingListing parties = script do
    let
      (operator :: provider :: _ :: publicParty :: _) = parties

      internalId = "internalId-1"
      externalId = "externalId-1"
      layOdds = [(Set.singleton (Decimal 1.5), 5.0)]
      backOdds = [(Set.singleton (Decimal 1.5), 5.0)]
      eventLabel = "event label"
      outcome = IntegrationEvents.Outcome with
        participantId = Some "participantId"
        participantOrder = 0
        order = 5
        -- TODO: prepare all types for IntegrationEvents.OutcomeType enum
        type_ = IntegrationEvents.Winner
        -- TODO: prepare all types for IntegrationEvents.OutcomeSubType enum
        subtype = IntegrationEvents.Win
      public = [publicParty]

    submitMulti [operator, provider] [] $ do
      createCmd GamblingListingModel.GamblingListing with
        -- TODO: prepare all types for GamblingListingModel.Status enum
        status = GamblingListingModel.Active
        matchedAmount = 0.0
        ..

-- | r20-IUpgradeGamblingUpdateListingRequestRollback
  prepareIUpgradeGamblingUpdateListingRequest : [Party] -> Script (ContractId GamblingListingModel.GamblingUpdateListingRequest)
  prepareIUpgradeGamblingUpdateListingRequest parties = script do
    let
      (operator :: provider :: _ :: _) = parties

      internalId = "internalId-1"
      externalId = "externalId-1"

    submitMulti [operator, provider] [] $ do
      createCmd GamblingListingModel.GamblingUpdateListingRequest with
        -- TODO: prepare all types for GamblingListingModel.Status enum
        status = GamblingListingModel.Active
        ..

-- | r21-IUpgradeFailedGamblingUpdateListingRollback
  prepareIUpgradeFailedGamblingUpdateListing : [Party] -> Script (ContractId GamblingListingModel.FailedGamblingUpdateListing)
  prepareIUpgradeFailedGamblingUpdateListing parties = script do
    let
      (operator :: provider :: _ :: _) = parties

      internalId = "internalId-1"
      externalId = "externalId-1"
      reason = "Some Reason"

    submitMulti [operator, provider] [] $ do
      createCmd GamblingListingModel.FailedGamblingUpdateListing with
        ..

-- | r22-IUpgradeAccountRollback
  prepareIUpgradeAccount : [Party] -> Script (ContractId GamblingAccountModel.Account)
  prepareIUpgradeAccount parties = script do
    let
      (operator :: provider :: customer :: other :: _) = parties

    cashDepositCid <- prepareAssetDeposit [operator, provider, customer, other]

    now <- getTime
    let
      assetDepositMain = Some cashDepositCid
      totalMainBalance = 1000.0
      assetDepositWithdraw = Some cashDepositCid
      totalWithdrawBalance = 500.0
      assetDepositBet = Map.fromList [("cashDepositCid-1", cashDepositCid)]
      totalBetBalance = 50.0
      assetDepositBonus = Some cashDepositCid
      totalBonusBalance = 50.0
      totalBonusBetBalance = 20.0
      preferences = Map.fromList [("pref-1", "a")]
      -- TODO: prepare all types for GamblingEventModel.Market enum
      favouriteMarkets = Set.fromList [GamblingEventModel.Entertainment]
      -- TODO: prepare all types for GamblingModel.Actionable enum
      transactionKey = (operator, provider, customer, GamblingModel.Bonus, "transactionKey-1")
      -- TODO: prepare all types for GamblingModel.Actionable enum
      transactionHistory = Map.fromList [(GamblingModel.Bonus, [transactionKey])]
      betPlacementKey = (operator, provider, customer, "betPlacementKey-1")
      betHistory = Set.singleton betPlacementKey
      accruedLimit = GamblingAccountModel.AccruedLimit with
        limit = 2.0
        accrued = 5.0
        date = toDateUTC now
      -- TODO: prepare all types for GamblingAccountModel.TimedLimit enum
      depositLimit = Map.fromList [(GamblingAccountModel.Yearly, accruedLimit)]
      totalFees = 1.5

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingAccountModel.Account with
        ..

-- | r23-IUpgradeWithdrawRequestForApprovalRollback
  prepareIUpgradeWithdrawRequestForApproval : [Party] -> Script (ContractId GamblingAccountModel.WithdrawRequestForApproval)
  prepareIUpgradeWithdrawRequestForApproval parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    now <- getTime
    let
      integrationParty = provider
      requestedAmount = 100.0
      currency = "USD"
      transactionId = "transactionId-1"
      dateOfBirth = toDateUTC now
      -- TODO: prepare all types for PromotionAction enum
      promotionAction = MarketingModel.Deposit . MarketingModel.Bonus . MarketingModel.Percentage $ 0.2
      promotionKey = (operator, provider, customer, promotionAction, "promotion-1")
      promotion = Some promotionKey
      language = "EN"

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingAccountModel.WithdrawRequestForApproval with
        ..

-- | r24-IUpgradeDepositRequestForApprovalRollback
  prepareIUpgradeDepositRequestForApproval : [Party] -> Script (ContractId GamblingAccountModel.DepositRequestForApproval)
  prepareIUpgradeDepositRequestForApproval parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    now <- getTime
    let
      integrationParty = provider
      requestedAmount = 100.0
      currency = "USD"
      transactionId = "transactionId-1"
      dateOfBirth = toDateUTC now
      -- TODO: prepare all types for PromotionAction enum
      promotionAction = MarketingModel.Deposit . MarketingModel.Bonus . MarketingModel.Percentage $ 0.2
      promotionKey = (operator, provider, customer, promotionAction, "promotion-1")
      promotion = Some promotionKey
      language = "EN"

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingAccountModel.DepositRequestForApproval with
        ..

-- | r25-IUpgradeTransactionHistoryRollback
  prepareIUpgradeTransactionHistory : [Party] -> Script (ContractId GamblingAccountModel.TransactionHistory)
  prepareIUpgradeTransactionHistory parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    startedAt <- getTime
    terminatedAt <- getTime
    let
      confirmedAmount = 10.0
      appliedFee = 1.5
      -- TODO: prepare all types for GamblingModel.Actionable enum
      transactionType = GamblingModel.EventInstrumentUpdate
      hasReconciled = True

      currency = "USD"
      transactionId = "transactionId-1"

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingAccountModel.TransactionHistory with
        ..

-- | r26-IUpgradePendingTransactionRollback
  prepareIUpgradePendingTransaction : [Party] -> Script (ContractId GamblingAccountModel.PendingTransaction)
  prepareIUpgradePendingTransaction parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    let
      countryCode = "US"
      requestedAmount = 100.0
      currency = "USD"
      transactionId = "transactionId-1"
      appliedFee = 0.5
      -- TODO: prepare all types for GamblingModel.Actionable enum
      transactionType = GamblingModel.EventInstrumentUpdate

    startedAt <- getTime
    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingAccountModel.PendingTransaction with
        startedAt
        ..

-- | r27-IUpgradePendingDepositsRollback
  prepareIUpgradePendingDeposits : [Party] -> Script (ContractId GamblingAccountModel.PendingDeposits)
  prepareIUpgradePendingDeposits parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    let
      pendingDeposits = Map.fromList [("deposit-1", 500.0)]
      unverifiedPendingDeposits = Map.fromList [("deposit-b", 250.0)]

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingAccountModel.PendingDeposits with
        ..

-- | r28-IUpgradeFlagRollback
  prepareIUpgradeFlag : [Party] -> Script (ContractId GamblingAccountModel.Flag)
  prepareIUpgradeFlag parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    created <- getTime
    let
      amount = 50.0

    submitMulti [operator, provider] [] $ do
      createCmd GamblingAccountModel.Flag with
        ..

-- | r29-IUpgradeEventInstrumentRequestRollback
  prepareIUpgradeEventInstrumentRequest : [Party] -> Script (ContractId GamblingEventModel.EventInstrumentRequest)
  prepareIUpgradeEventInstrumentRequest parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    startDate <- getTime
    let
      eventId = Id with signatories = Set.fromList [provider, customer]; label = "AssetLabel"; version = 0
      description = "description"

      -- TODO: prepare all types for GamblingEventModel.Market enum
      market = GamblingEventModel.Entertainment
      -- TODO: prepare all types for GamblingEventModel.Submarket enum
      submarkets = [GamblingEventModel.Tournament "t1"]
      outcome = IntegrationEvents.Outcome with
        participantId = Some "participantId"
        participantOrder = 0
        order = 5
        -- TODO: prepare all types for IntegrationEvents.OutcomeType enum
        type_ = IntegrationEvents.Winner
        -- TODO: prepare all types for IntegrationEvents.OutcomeSubType enum
        subtype = IntegrationEvents.Win
      outcomes = [
          GamblingEventModel.OutcomeOdds with
            odds = Set.fromList [Decimal 2.5]
            ..
        ]
      participant = IntegrationEvents.Participant with
        name = "alice"
        id = "p1"
        order = 1
        co_op = None
      eventGame = Some "game"
      eventParticipants = [participant]
      -- TODO: prepare all types for IntegrationEvents.Status enum
      eventStatus = IntegrationEvents.NotStarted
      eventResults = [outcome]
      -- TODO: prepare all types for GamblingEventModel.OriginType enum
      origin = GamblingEventModel.Customer
      eventTitle = Map.fromList [(show GambylUtils.EN_US, "eventTitle")]
      details = GamblingEventModel.Details with
        geography = ""
        ..
      integrationTime = Some startDate
      liveEvent = "liveEvent"

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingEventModel.EventInstrumentRequest with
        ..

-- | r30-IUpgradeEventInstrumentStatusUpdateRequestRollback
  prepareIUpgradeEventInstrumentStatusUpdateRequest : [Party] -> Script (ContractId GamblingEventModel.EventInstrumentStatusUpdateRequest)
  prepareIUpgradeEventInstrumentStatusUpdateRequest parties = script do
    let
      (operator :: provider :: customer :: _) = parties
      outcome = IntegrationEvents.Outcome with
              participantId = Some "participantId"
              participantOrder = 0
              order = 5
              -- TODO: prepare all types for IntegrationEvents.OutcomeType enum
              type_ = IntegrationEvents.Winner
              -- TODO: prepare all types for IntegrationEvents.OutcomeSubType enum
              subtype = IntegrationEvents.Win

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingEventModel.EventInstrumentStatusUpdateRequest with
        operator, provider, customer
        oldEventKey = (operator, customer, "event-key-0")
        newEventStatus = Finished
        eventResults = Some [outcome]

-- | r31-IUpgradeEventInstrumentUpdateRequestRollback
  prepareIUpgradeEventInstrumentUpdateRequest : [Party] -> Script (ContractId GamblingEventModel.EventInstrumentUpdateRequest)
  prepareIUpgradeEventInstrumentUpdateRequest parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    startDate <- getTime
    let
      -- TODO: prepare all types for GamblingEventModel.Market enum
      market = GamblingEventModel.Entertainment
      -- TODO: prepare all types for GamblingEventModel.Submarket enum
      submarkets = [GamblingEventModel.Tournament "t1"]
      outcome = IntegrationEvents.Outcome with
        participantId = Some "participantId"
        participantOrder = 0
        order = 5
        -- TODO: prepare all types for IntegrationEvents.OutcomeType enum
        type_ = IntegrationEvents.Winner
        -- TODO: prepare all types for IntegrationEvents.OutcomeSubType enum
        subtype = IntegrationEvents.Win
      outcomes = [
          GamblingEventModel.OutcomeOdds with
            odds = Set.fromList [Decimal 2.5]
            ..
        ]
      participant = IntegrationEvents.Participant with
        name = "alice"
        id = "p1"
        order = 1
        co_op = None
      eventGame = Some "game"
      eventParticipants = [participant]
      -- TODO: prepare all types for IntegrationEvents.Status enum
      eventStatus = IntegrationEvents.NotStarted
      eventResults = [outcome]
      -- TODO: prepare all types for GamblingEventModel.OriginType enum
      origin = GamblingEventModel.Customer
      eventTitle = Map.fromList [(show GambylUtils.EN_US, "eventTitle")]
      oldEventKey = (operator, customer, "event-key-0")
      description = "event description"
      details = GamblingEventModel.Details with
        geography = ""
        ..
      integrationTime = Some startDate
      liveEvent = "liveEvent"

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingEventModel.EventInstrumentUpdateRequest with
        ..

-- | r32-IUpgradeEventInstrumentToggleFeaturedRequestRollback
  prepareIUpgradeEventInstrumentToggleFeaturedRequest : [Party] -> Script (ContractId GamblingEventModel.EventInstrumentToggleFeaturedRequest)
  prepareIUpgradeEventInstrumentToggleFeaturedRequest parties = script do
    let
      (operator :: provider :: customer :: _) = parties

      label = "event-label"

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingEventModel.EventInstrumentToggleFeaturedRequest with
        ..

-- | r33-IUpgradeEventInstrumentCancelRequestRollback
  prepareIUpgradeEventInstrumentCancelRequest : [Party] -> Script (ContractId GamblingEventModel.EventInstrumentCancelRequest)
  prepareIUpgradeEventInstrumentCancelRequest parties = script do
    let
      (operator :: provider :: customer :: _) = parties

      eventLabel = "event-id"

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingEventModel.EventInstrumentCancelRequest with
        ..

-- | r34-IUpgradeEventInstrumentReinstateRequestRollback
  prepareIUpgradeEventInstrumentReinstateRequest : [Party] -> Script (ContractId GamblingEventModel.EventInstrumentReinstateRequest)
  prepareIUpgradeEventInstrumentReinstateRequest parties = script do
    let
      (operator :: provider :: customer :: _) = parties

      eventLabel = "event-id"
    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingEventModel.EventInstrumentReinstateRequest with
        ..

-- | r35-IUpgradeEventInstrumentUpdateOutcomesOddsRequestRollback
  prepareIUpgradeEventInstrumentUpdateOutcomesOddsRequest : [Party] -> Script (ContractId GamblingEventModel.EventInstrumentUpdateOutcomesOddsRequest)
  prepareIUpgradeEventInstrumentUpdateOutcomesOddsRequest parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    let
      outcome = IntegrationEvents.Outcome with
        participantId = Some "participantId"
        participantOrder = 0
        order = 5
        -- TODO: prepare all types for OutcomeType enum
        type_ = IntegrationEvents.ThreeWay
        -- TODO: prepare all types for OutcomeSubType enum
        subtype = IntegrationEvents.Draw
      outcomes = [
          GamblingEventModel.OutcomeOdds with
            odds = Set.fromList [Decimal 2.5]
            ..
        ]
      newOutcomes = outcomes
      eventId = Id with
          signatories = Set.fromList [provider]
          label = "event-id"
          version = 1

    startDate <- getTime
    let
      -- TODO: prepare all types for GamblingEventModel.Market enum
      market = GamblingEventModel.Entertainment
      -- TODO: prepare all types for GamblingEventModel.Submarket enum
      submarkets = [GamblingEventModel.Tournament "t1"]
      outcome = IntegrationEvents.Outcome with
        participantId = Some "participantId"
        participantOrder = 0
        order = 5
        -- TODO: prepare all types for IntegrationEvents.OutcomeType enum
        type_ = IntegrationEvents.Winner
        -- TODO: prepare all types for IntegrationEvents.OutcomeSubType enum
        subtype = IntegrationEvents.Win
      outcomes = [
          GamblingEventModel.OutcomeOdds with
            odds = Set.fromList [Decimal 2.5]
            ..
        ]
      participant = IntegrationEvents.Participant with
        name = "alice"
        id = "p1"
        order = 1
        co_op = None
      eventGame = Some "game"
      eventParticipants = [participant]
      -- TODO: prepare all types for IntegrationEvents.Status enum
      eventStatus = IntegrationEvents.NotStarted
      eventResults = [outcome]
      -- TODO: prepare all types for GamblingEventModel.OriginType enum
      origin = GamblingEventModel.Customer
      eventTitle = Map.fromList [(show GambylUtils.EN_US, "eventTitle")]
      description = "event description"
      details = GamblingEventModel.Details with
        geography = ""
        ..
      liveEvent = "liveEvent"

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingEventModel.EventInstrumentUpdateOutcomesOddsRequest with
        -- TODO: prepare all types for GamblingEventModel.Status enum
        status = GamblingEventModel.Active
        ..

-- | r36-IUpgradeEventInstrumentNoOutcomesRollback
  prepareIUpgradeEventInstrumentNoOutcomes : [Party] -> Script (ContractId GamblingEventModel.EventInstrumentNoOutcomes)
  prepareIUpgradeEventInstrumentNoOutcomes parties = script do
    let
      (operator :: provider :: customer :: _ :: publicParty :: _) = parties

    createdAt <- getTime
    lastUpdate <- getTime
    let
      managers = Set.fromList [operator, customer]
      eventId = Id with
          signatories = Set.fromList [provider]
          label = "event-id"
          version = 1
      featured = True

    startDate <- getTime
    let
      -- TODO: prepare all types for GamblingEventModel.Market enum
      market = GamblingEventModel.Entertainment
      -- TODO: prepare all types for GamblingEventModel.Submarket enum
      submarkets = [GamblingEventModel.Tournament "t1"]
      outcome = IntegrationEvents.Outcome with
        participantId = Some "participantId"
        participantOrder = 0
        order = 5
        -- TODO: prepare all types for IntegrationEvents.OutcomeType enum
        type_ = IntegrationEvents.Winner
        -- TODO: prepare all types for IntegrationEvents.OutcomeSubType enum
        subtype = IntegrationEvents.Win
      outcomes = [
          GamblingEventModel.OutcomeOdds with
            odds = Set.fromList [Decimal 2.5]
            ..
        ]
      participant = IntegrationEvents.Participant with
        name = "alice"
        id = "p1"
        order = 1
        co_op = None
      eventGame = Some "game"
      eventParticipants = [participant]
      -- TODO: prepare all types for IntegrationEvents.Status enum
      eventStatus = IntegrationEvents.NotStarted
      eventResults = [outcome]
      -- TODO: prepare all types for GamblingEventModel.OriginType enum
      origin = GamblingEventModel.Customer
      eventTitle = Map.fromList [(show GambylUtils.EN_US, "eventTitle")]
      description = "event description"
      details = GamblingEventModel.Details with
        geography = ""
        ..
      integrationTime = Some startDate
      liveEvent = "liveEvent"
      public = Set.fromList [publicParty]

    let
      event = GamblingEventModel.EventInstrument with
        -- TODO: prepare all types for GamblingEventModel.Status enum
        status = GamblingEventModel.Active
        ..

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingEventModel.EventInstrumentNoOutcomes with
        ..

-- | r37-IUpgradeEventInstrumentRollback
  prepareIUpgradeEventInstrument : [Party] -> Script (ContractId GamblingEventModel.EventInstrument)
  prepareIUpgradeEventInstrument parties = script do
    let
      (operator :: provider :: customer :: _ :: publicParty :: _) = parties

    createdAt <- getTime
    lastUpdate <- getTime
    let
      managers = Set.fromList [operator, customer]
      eventId = Id with
          signatories = Set.fromList [provider]
          label = "event-id"
          version = 1
      featured = True

    startDate <- getTime
    let
      -- TODO: prepare all types for GamblingEventModel.Market enum
      market = GamblingEventModel.Entertainment
      -- TODO: prepare all types for GamblingEventModel.Submarket enum
      submarkets = [GamblingEventModel.Tournament "t1"]
      outcome1 = IntegrationEvents.Outcome with
        participantId = Some "participantId1"
        participantOrder = 1
        order = 1
        -- TODO: prepare all types for IntegrationEvents.OutcomeType enum
        type_ = IntegrationEvents.TwoWay
        -- TODO: prepare all types for IntegrationEvents.OutcomeSubType enum
        subtype = IntegrationEvents.Win
      outcome2 = IntegrationEvents.Outcome with
        participantId = Some "participantId2"
        participantOrder = 2
        order = 2
        -- TODO: prepare all types for IntegrationEvents.OutcomeType enum
        type_ = IntegrationEvents.TwoWay
        -- TODO: prepare all types for IntegrationEvents.OutcomeSubType enum
        subtype = IntegrationEvents.Win

      eventOutcome1 = GamblingEventModel.OutcomeOdds with
            odds = Set.fromList [Decimal 2.5]
            outcome = outcome1

      eventOutcome2 = GamblingEventModel.OutcomeOdds with
            odds = Set.fromList [Decimal 1.5]
            outcome = outcome2

      outcomes = [eventOutcome1, eventOutcome2]
      participant = IntegrationEvents.Participant with
        name = "alice"
        id = "p1"
        order = 1
        co_op = None
      eventGame = Some "game"
      eventParticipants = [participant]
      -- TODO: prepare all types for IntegrationEvents.Status enum
      eventStatus = IntegrationEvents.Finished
      eventResults = [outcome1]
      -- TODO: prepare all types for GamblingEventModel.OriginType enum
      origin = GamblingEventModel.Customer
      eventTitle = Map.fromList [(show GambylUtils.EN_US, "eventTitle")]
      description = "event description"
      details = GamblingEventModel.Details with
        geography = ""
        ..
      integrationTime = Some startDate
      liveEvent = "liveEvent"
      public = Set.fromList [publicParty]

    _ <- submitMulti [operator, provider] [] $ do
      createCmd IssuanceService.Service with operator, provider, customer = provider

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingEventModel.EventInstrument with
        -- TODO: prepare all types for GamblingEventModel.Status enum
        status = GamblingEventModel.Active
        ..

-- | r38-IUpgradeMarketMapRollback
  prepareIUpgradeMarketMap : [Party] -> Script (ContractId GamblingEventModel.MarketMap)
  prepareIUpgradeMarketMap parties = script do
    let
      (operator :: provider :: _ :: publicParty :: _) = parties

    let
      -- TODO: prepare all types for GamblingEventModel.Market enum
      market = GamblingEventModel.Entertainment
      -- TODO: prepare all types for GamblingEventModel.Submarket enum
      submarkets = [GamblingEventModel.Tournament "t1"]
      values = Map.fromList [("", submarkets)]
      map = Map.fromList [(market, values)]
      public = Set.fromList [publicParty]

    submitMulti [operator, provider] [] $ do
      createCmd GamblingEventModel.MarketMap with
        ..

-- | r39-GamblingService.IUpgradeServiceRollback
  prepareGamblingServiceIUpgradeService : [Party] -> Script (ContractId GamblingService.Service)
  prepareGamblingServiceIUpgradeService parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    createdAt <- getTime
    let
      -- TODO: prepare all types for GamblingModel.Permissions enum
      permissions = Set.fromList [GamblingModel.Events]

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingService.Service with
        ..

-- | r40-GamblingService.IUpgradeOfferRollback
  prepareGamblingServiceIUpgradeOffer : [Party] -> Script (ContractId GamblingService.Offer)
  prepareGamblingServiceIUpgradeOffer parties = script do
    let
      (operator :: provider :: customer :: _) = parties
      observers = Set.empty

    submitMulti [operator, provider] [] $ do
      createCmd GamblingService.Offer with
        ..

-- | r41-GamblingService.IUpgradeRequestRollback
  -- NOTE: this is a customer contract and does not need to be migrated
  prepareGamblingServiceIUpgradeRequest : [Party] -> Script (ContractId GamblingService.Request)
  prepareGamblingServiceIUpgradeRequest parties = script do
    let
      (_ :: provider :: customer :: _) = parties

    submitMulti [customer] [] $ do
      createCmd GamblingService.Request with
        ..

-- | r42-IUpgradeBlockedServiceRollback
  prepareIUpgradeBlockedService : [Party] -> Script (ContractId GamblingService.BlockedService)
  prepareIUpgradeBlockedService parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    createdAt <- getTime
    let
      -- TODO: prepare all types for GamblingModel.Permissions enum
      permissions = Set.fromList [GamblingModel.Events]

    let
      service = GamblingService.Service with
        ..
      reason = "Some Reason"

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingService.BlockedService with
        blockedAt = datetime 1970 Jan 1 0 0 0
        ..

-- | r43-IUpgradePendingPromotionApplicationRollback
  prepareIUpgradePendingPromotionApplication : [Party] -> Script (ContractId GamblingService.PendingPromotionApplication)
  prepareIUpgradePendingPromotionApplication parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    let
      offeredAmount = 20.5
      currency = "USD"
      actionId = "Action-1"
      -- TODO: prepare all types for MarketingModel: PromotionAction, PromotionDetails and Redemption enums
      promotionAction = MarketingModel.Deposit . MarketingModel.Bonus . MarketingModel.Percentage $ 0.2
      promotionKey = (operator, provider, customer, promotionAction, "promotionKey-1")

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingService.PendingPromotionApplication with
        ..

-- | r44-IUpgradePendingIdentityRollback
  prepareFaultyIUpgradePendingIdentity : [Party] -> Script (ContractId GamblingIdentityModel.PendingIdentity)
  prepareFaultyIUpgradePendingIdentity parties = script do
    let
      (operator :: provider :: _) = parties

    createdAt <- getTime
    let
      redirectUrl = "https://gambyl.com/redirect-url"
      requestFrom = provider
      requestFromHashed = "hGambyl"
      integrationParty = provider

    jumioPendingIdCid <- submitMulti [integrationParty, requestFrom] [] $ do
      createCmd IntegrationIdentity.PendingIdentityRequest with
        observers = []
        ..

    submitMulti [integrationParty, requestFrom] [] do archiveCmd jumioPendingIdCid

    submitMulti [operator, provider, provider] [] $ do
      createCmd GamblingIdentityModel.PendingIdentity with
        customer = provider
        jumioPendingIdKey = (integrationParty, requestFrom)
        ..

  prepareIUpgradePendingIdentity : [Party] -> Script (ContractId GamblingIdentityModel.PendingIdentity)
  prepareIUpgradePendingIdentity parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    createdAt <- getTime
    let
      redirectUrl = "https://gambyl.com/redirect-url"
      requestFrom = customer
      requestFromHashed = "hAlice"
      integrationParty = provider
      jumioPendingIdKey = (integrationParty, customer)

    _ <- submitMulti [integrationParty, requestFrom] [] $ do
      createCmd IntegrationIdentity.PendingIdentityRequest with
        observers = []
        ..

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingIdentityModel.PendingIdentity with
        ..

-- | r45-IUpgradeGamblerUnverifiedIdentityRequestRollback
  prepareIUpgradeGamblerUnverifiedIdentityRequest : [Party] -> Script (ContractId GamblingIdentityModel.GamblerUnverifiedIdentityRequest)
  prepareIUpgradeGamblerUnverifiedIdentityRequest parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    now <- getTime
    let
      firstName = "alice"
      lastName = "wonderland"
      emailAddress = "<EMAIL>"
      phoneNumber = "+111"
      birthday = toDateUTC now
      city = "somewhere"
      postalCode = "314"
      subDivision = "A"
      addressLine1 = "magic street 7"
      countryCode = "US"

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingIdentityModel.GamblerUnverifiedIdentityRequest with
        ..

-- | r46-IUpgradeGamblerUnverifiedIdentityRollback
  prepareIUpgradeGamblerUnverifiedIdentity : [Party] -> Script (ContractId GamblingIdentityModel.GamblerUnverifiedIdentity)
  prepareIUpgradeGamblerUnverifiedIdentity parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    now <- getTime
    let
      firstName = "alice"
      lastName = "wonderland"
      emailAddress = "<EMAIL>"
      phoneNumber = "+111"
      birthday = toDateUTC now
      city = "somewhere"
      postalCode = "314"
      subDivision = "A"
      addressLine1 = "magic street 7"
      countryCode = "US"

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingIdentityModel.GamblerUnverifiedIdentity with
        ..

-- | r47-IUpgradeGamblerIdentityRollback
  prepareIUpgradeGamblerIdentity : [Party] -> Script (ContractId GamblingIdentityModel.GamblerIdentity)
  prepareIUpgradeGamblerIdentity parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    now <- getTime
    let
      country = "Wonderland"
      firstName = "alice"
      lastName = "wonderland"
      birthday = toDateUTC now
      city = "somewhere"
      postalCode = "314"
      subDivision = "A"
      addressLine1 = "magic street 7"
      addressLine2 = addressLine1
      jumioUserData = IntegrationIdentity.User with
        ..
      dataUrl = "https://alice-wonderland.io/profile"
      legalName = "alice"
      location = "Wonderland"
      observers = Set.empty

    mpVerifiedIdCid <- submitMulti [operator, provider, customer] [] $ do
      createCmd Regulator.VerifiedIdentity with
        ..

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingIdentityModel.GamblerIdentity with
        ..

-- | r48-IUpgradeRejectedIdentityRollback
  prepareIUpgradeRejectedIdentity : [Party] -> Script (ContractId GamblingIdentityModel.RejectedIdentity)
  prepareIUpgradeRejectedIdentity parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    let
      rejectReason = "Made the queen angry"
      dataUrl = "https://alice-wonderland.io/profile"

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingIdentityModel.RejectedIdentity with
        ..

-- | r49-IUpgradeExecutedBetsRollback
  prepareIUpgradeExecutedBets : [Party] -> Script (ContractId GamblingSettlementModel.ExecutedBets)
  prepareIUpgradeExecutedBets parties = script do
    let
      (operator :: provider :: customer :: other :: _) = parties
      backCustomer = customer
      layCustomer = other
      signers = Set.fromList [customer, other]
      backBetPlacementId = "backBetPlacementId-1"
      layBetPlacementId = "layBetPlacementId-2"
      executedOdd = Decimal 2.5
      isSettled = True
      isCancelled = False
      eventKey = (provider, customer, "event-key")

    cashDepositCid <- prepareAssetDeposit [operator, provider, customer, other]
    let
      accountId = Id with
          signatories = Set.fromList [other]
          label = "AccountLabel"
          version = 1
      account = Account with
        id = accountId
        provider = operator
        owner = customer

      senderAccount = account
      depositCid = cashDepositCid
      receiverAccount = Account with
          id = Id with
            signatories = Set.fromList [other]
            label = "AccountLabel"
            version = 1
          provider = operator
          owner = other
      settlementDetails = SettlementModel.SettlementDetails with
        ..

    settlementInstructionCid <- submitMulti [operator, provider, customer, other] [] $ do
      createCmd SettlementModel.SettlementInstruction with
        details = [settlementDetails]
        ..

    submitMulti [operator, provider, customer, other] [] $ do
      createCmd GamblingSettlementModel.ExecutedBets with
        executionId = intercalate "_" [backBetPlacementId, layBetPlacementId]
        ..

-- | r50-GamblingEventService.IUpgradeServiceRollback
  prepareGamblingEventServiceIUpgradeService : [Party] -> Script (ContractId GamblingEventService.Service)
  prepareGamblingEventServiceIUpgradeService parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingEventService.Service with
        ..

-- | r51-GamblingEventService.IUpgradeOfferRollback
  prepareGamblingEventServiceIUpgradeOffer : [Party] -> Script (ContractId GamblingEventService.Offer)
  prepareGamblingEventServiceIUpgradeOffer parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    submitMulti [operator, provider] [] $ do
      createCmd GamblingEventService.Offer with
        ..

-- | r52-GamblingEventService.IUpgradeRequestRollback
  -- NOTE: this is a customer contract and does not need to be migrated
  prepareGamblingEventServiceIUpgradeRequest : [Party] -> Script (ContractId GamblingEventService.Request)
  prepareGamblingEventServiceIUpgradeRequest parties = script do
    let
      (_ :: provider :: customer :: _) = parties

    submitMulti [customer] [] $ do
      createCmd GamblingEventService.Request with
        ..

-- | r53-IUpgradeGlobalPromotionsRollback
  prepareIUpgradeGlobalPromotions : [Party] -> Script (ContractId MarketingModel.GlobalPromotions)
  prepareIUpgradeGlobalPromotions parties = script do
    let
      (operator :: provider :: customer :: _ :: publicParty :: _) = parties

    let
      -- TODO: prepare all types for MarketingModel: PromotionAction, PromotionDetails and Redemption enums
      promotionAction = MarketingModel.Deposit . MarketingModel.Bonus . MarketingModel.Percentage $ 0.2
      promotionKey = (operator, provider, customer, promotionAction, "promotionKey-1")
      promotions = [promotionKey]
      public = Set.fromList [publicParty]

    submitMulti [operator, provider] [] $ do
      createCmd MarketingModel.GlobalPromotions with
        ..

-- | r54-IUpgradePromotionRollback
  prepareIUpgradePromotion : [Party] -> Script (ContractId MarketingModel.Promotion)
  prepareIUpgradePromotion parties = script do
    let
      (operator :: provider :: customer :: _ :: publicParty :: _) = parties

    now <- getTime
    startDate <- getTime

    let
      promotionId = "promotionId-1"
      title = Map.fromList [("title-1", "a")]
      -- TODO: prepare all types for MarketingModel.PromotionType enum
      promoType = MarketingModel.Reload
      endDate = Some (toDateUTC now)
      limitedPromotion = Some 7
      maxAmount = Some 20.0
      minAmount = Some 10.0
      -- TODO: prepare all types for PromotionAction enum
      promotionAction = MarketingModel.Deposit . MarketingModel.Bonus . MarketingModel.Percentage $ 0.2
      config = MarketingModel.PromotionConfig with
        action = promotionAction
        ..
      shortDescription = Map.fromList [("shortDescription-1", "a")]
      longDescription = Map.fromList [("longDescription-1", "a")]
      thumbnailUrl = Map.fromList [("thumbnailUrl-1", "a")]
      bannerUrl = Map.fromList [("bannerUrl-1", "a")]
      baseUrl = Map.fromList [("baseUrl-1", "a")]
      public = Set.fromList [publicParty]

    submitMulti [operator, provider, customer] [] $ do
      createCmd MarketingModel.Promotion with
        status = MarketingModel.Active
        ..

-- | r55-IUpgradePromotionRequestRollback
  prepareIUpgradePromotionRequest : [Party] -> Script (ContractId MarketingModel.PromotionRequest)
  prepareIUpgradePromotionRequest parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    now <- getTime
    startDate <- getTime
    let
      promotionId = "promotionId-1"
      title = Map.fromList [("title-1", "a")]
      -- TODO: prepare all types for MarketingModel.PromotionType enum
      promoType = MarketingModel.Reload
      endDate = Some (toDateUTC now)
      limitedPromotion = Some 7
      maxAmount = Some 20.0
      minAmount = Some 10.0
      -- TODO: prepare all types for PromotionAction enum
      promotionAction = MarketingModel.Deposit . MarketingModel.Bonus . MarketingModel.Percentage $ 0.2
      config = MarketingModel.PromotionConfig with
        action = promotionAction
        ..
      shortDescription = Map.fromList [("shortDescription-1", "a")]
      longDescription = Map.fromList [("longDescription-1", "a")]
      thumbnailUrl = Map.fromList [("thumbnailUrl-1", "a")]
      bannerUrl = Map.fromList [("bannerUrl-1", "a")]
      baseUrl = Map.fromList [("baseUrl-1", "a")]

    submitMulti [operator, provider, customer] [] $ do
      createCmd MarketingModel.PromotionRequest with
        -- TODO: prepare all types for MarketingModel.Status enum
        status = MarketingModel.Active
        ..

-- | r56-IUpgradePromotionUpdateRequestRollback
  prepareIUpgradePromotionUpdateRequest : [Party] -> Script (ContractId MarketingModel.PromotionUpdateRequest)
  prepareIUpgradePromotionUpdateRequest parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    now <- getTime
    startDate <- getTime
    let
      -- TODO: prepare all types for MarketingModel.PromotionType enum
      promoType = MarketingModel.Reload
      endDate = Some (toDateUTC now)
      limitedPromotion = Some 7
      maxAmount = Some 20.0
      minAmount = Some 10.0
      -- TODO: prepare all types for PromotionAction enum
      promotionAction = MarketingModel.Deposit . MarketingModel.Bonus . MarketingModel.Percentage $ 0.2
      config = MarketingModel.PromotionConfig with
        action = promotionAction
        ..
      thumbnailUrl = Map.fromList [("thumbnailUrl-1", "a")]
      bannerUrl = Map.fromList [("bannerUrl-1", "a")]
      baseUrl = Map.fromList [("baseUrl-1", "a")]
      shortDescription = Map.fromList [("shortDescription-1", "a")]
      longDescription = Map.fromList [("longDescription-1", "a")]
      title = Map.fromList [("title-1", "a")]

      newConfig = Some config
      newStartDate = Some startDate
      newThumbnailUrl = thumbnailUrl
      newBannerUrl = bannerUrl
      newShortDescription = shortDescription
      newLongDescription = longDescription
      newTitle = title
      newBaseUrl = baseUrl

      -- TODO: prepare all types for PromotionAction enum
      promotionAction = MarketingModel.Deposit . MarketingModel.Bonus . MarketingModel.Percentage $ 0.2
      promotionKey = (operator, provider, customer, promotionAction, "promotion-1")

    submitMulti [operator, provider, customer] [] $ do
      createCmd MarketingModel.PromotionUpdateRequest with
        ..

-- | r57-IUpgradePromotionWalletRollback
  prepareIUpgradePromotionWallet : [Party] -> Script (ContractId MarketingModel.PromotionWallet)
  prepareIUpgradePromotionWallet parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    now <- getTime
    let
      promotionUsage = MarketingModel.PromotionUsage with
        usageCounter = 10
        lastUsageDate = now
      -- TODO: prepare all types for PromotionAction enum
      promotionAction = MarketingModel.Deposit . MarketingModel.Bonus . MarketingModel.Percentage $ 0.2
      promotionKey = (operator, provider, customer, promotionAction, "promotion-1")
      promotionMap = Map.fromList [(promotionKey, promotionUsage)]

    submitMulti [operator, provider, customer] [] $ do
      createCmd MarketingModel.PromotionWallet with
        ..

-- | r58-IUpgradePromotionUsageHistoryRollback
  prepareIUpgradePromotionUsageHistory : [Party] -> Script (ContractId MarketingModel.PromotionUsageHistory)
  prepareIUpgradePromotionUsageHistory parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    now <- getTime

    let
      offerType = "Bonus"
      action = MarketingModel.Deposit . MarketingModel.Bonus . MarketingModel.Percentage $ 0.2
      promotionId = "PromotionID-1"
      usageDate = now

    submitMulti [operator, provider, customer] [] $ do
      createCmd MarketingModel.PromotionUsageHistory with
        totalUsedOn = Map.fromList [("action-a", (1.5, 3.5))]
        ..

-- | r59-MarketingService.IUpgradeServiceRollback
  prepareMarketingServiceIUpgradeService : [Party] -> Script (ContractId MarketingService.Service)
  prepareMarketingServiceIUpgradeService parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    submitMulti [operator, provider, customer] [] $ do
      createCmd MarketingService.Service with
        ..

-- | r60-MarketingService.IUpgradeOfferRollback
  prepareMarketingServiceIUpgradeOffer : [Party] -> Script (ContractId MarketingService.Offer)
  prepareMarketingServiceIUpgradeOffer parties = script do
    let
      (operator :: provider :: customer :: _) = parties

    submitMulti [operator, provider] [] $ do
      createCmd MarketingService.Offer with
        ..

-- | r61-MarketingService.IUpgradeRequestRollback
  -- NOTE: this is a customer contract and does not need to be migrated
  prepareMarketingServiceIUpgradeRequest : [Party] -> Script (ContractId MarketingService.Request)
  prepareMarketingServiceIUpgradeRequest parties = script do
    let
      (_ :: provider :: customer :: _) = parties

    submitMulti [customer] [] $ do
      createCmd MarketingService.Request with
        ..

-- | r62-IUpgradeCounterRollback
  prepareIUpgradeCounter : [Party] -> Script (ContractId CounterModel.Counter)
  prepareIUpgradeCounter parties = script do
    let
      (operator :: provider :: _) = parties

    let
      id = "counter-1"
      count = 0

    submitMulti [operator, provider] [] $ do
      createCmd CounterModel.Counter with
        ..

-- | r63-OperatorRole.IUpgradeRoleRollback
  prepareOperatorRoleIUpgradeRole : [Party] -> Script (ContractId OperatorRole.Role)
  prepareOperatorRoleIUpgradeRole parties = script do
    let
      (operator :: _ :: _ :: _ :: publicParty :: _) = parties
      observers = Set.empty

    optMibOperatorCid <- submitMulti [operator] [] $ do
      createCmd Operator.Role with
        ..

    let
      optMibOperator = Some optMibOperatorCid
      public = Set.fromList [publicParty]

    submitMulti [operator] [] $ do
      createCmd OperatorRole.Role with
        ..


-- | r64-IUpgradeCancelledBetPlacementRollback
  prepareIUpgradeCancelledBetPlacement : [Party] -> Script (ContractId GamblingBetModel.CancelledBetPlacement)
  prepareIUpgradeCancelledBetPlacement parties = script do
    let
      (operator :: provider :: customer :: other :: _) = parties

    cashDepositCid <- prepareAssetDeposit [operator, provider, customer, other]
    assetDescriptionCid <- prepareAssetDescription [operator, provider, customer, other] 9
    placedAt <- getTime
    let
      -- TODO: prepare all types for Status enum
      orderId = "orderId-1"
      matchedOdd = Some 3.0

      settlementDetails = SettlementModel.SettlementDetails with
        senderAccount = Account with
          id = Id with
            signatories = Set.fromList [other]
            label = "AccountLabel"
            version = 1
          provider = operator
          owner = customer
        depositCid = cashDepositCid
        receiverAccount = Account with
          id = Id with
            signatories = Set.fromList [other]
            label = "AccountLabel"
            version = 1
          provider = operator
          owner = other

    settlementInstructionCid <- submitMulti [operator, provider, customer, other] [] $ do
      createCmd SettlementModel.SettlementInstruction with
        operator, provider, details = [settlementDetails]

    eventStartDate <- getTime
    let
      sideDetails =
        GamblingBetModel.SideDetails with
          odd = Decimal 1.5
          stake = 10.0
          outcome = IntegrationEvents.Outcome with
            participantId = Some "participantId"
            participantOrder = 0
            order = 5
            -- TODO: prepare all types for OutcomeType enum
            type_ = IntegrationEvents.ThreeWay
            -- TODO: prepare all types for OutcomeSubType enum
            subtype = IntegrationEvents.Draw
          -- TODO: prepare all types for Side enum


      instruments = Map.fromList [("assetDescription", assetDescriptionCid)]
      listingId = "listingId-1"

      -- TODO: prepare all types for PromotionAction enum
      promotionAction = MarketingModel.Deposit . MarketingModel.Bonus . MarketingModel.Percentage $ 0.2

    submitMulti [operator, provider, customer] [] $ do
      createCmd GamblingBetModel.CancelledBetPlacement with
        operator, provider, customer
        instruments, listingId
        matchedOdd, placedAt, orderId
        settlementInstructionCid = Some settlementInstructionCid
        verified = True
        details = GamblingBetModel.Details with
          betPlacementId = "betPlacementId-from-cancelled"
          side = GamblingBetModel.Back sideDetails
          eventTitle = Map.fromList [("en-US", "eventTitle")]
          eventKey = (provider, customer, "event-id")
          eventStartDate
        inBulk = Some "bulk_id-2"
        bonusBetAmount = Some 20.0
        betIssuanceKey = (operator, customer, "issuanceKey-1")
        promotion = Some (operator, provider, customer, promotionAction, "promotion-1")
        previousSplitBetPlacementIdList = []
        cancelledAt = placedAt
        reason = "Had to be cancelled"
        processed = True

-- | Prepare Ledger
  prepareLedger : Party -> Party -> Script ()
  prepareLedger operator provider = script do
    [alice, bob, public] <- mapA allocateParty ["alice", "bob", "public"]

    let parties = [operator, provider, alice, bob, public]

    prepareIUpgradeGlobalGamblingConfiguration parties
    prepareIUpgradeActionFailure parties
    prepareIUpgradeActionSuccess parties
    prepareGamblingRoleIUpgradeRole parties
    prepareGamblingRoleIUpgradeOffer parties
    prepareGamblingRoleIUpgradeRequest parties
    prepareIUpgradeBetPlacementRequest parties 0
    prepareIUpgradeBetPlacementRequestFlag parties
    prepareIUpgradeBulkBetPlacementRequestFlag parties
    prepareIUpgradeBetPlacementFinalizeRequest parties 0
    prepareIUpgradeBetPlacement parties
    prepareIUpgradeBetPlacementSplitRequest parties
    prepareIUpgradeBetPlacementCancelRequest parties
    prepareIUpgradeFinalizeBetPlacementCancelRequest parties
    prepareIUpgradeBetHistory parties
    prepareGamblingBetModelIUpgradeBulkBetPlacementRequest parties
    prepareGamblingBetModelIUpgradeBulkBetPlacementFinalizeRequest parties
    prepareIUpgradeBulkBetPlacement parties
    prepareIUpgradeGamblingListing parties
    prepareIUpgradeGamblingUpdateListingRequest parties
    prepareIUpgradeFailedGamblingUpdateListing parties
    prepareIUpgradeAccount parties
    prepareIUpgradeWithdrawRequestForApproval parties
    prepareIUpgradeDepositRequestForApproval parties
    prepareIUpgradeTransactionHistory parties
    prepareIUpgradePendingTransaction parties
    prepareIUpgradePendingDeposits parties
    prepareIUpgradeFlag parties
    prepareIUpgradeEventInstrumentRequest parties
    prepareIUpgradeEventInstrumentStatusUpdateRequest parties
    prepareIUpgradeEventInstrumentUpdateRequest parties
    prepareIUpgradeEventInstrument parties
    prepareIUpgradeEventInstrumentToggleFeaturedRequest parties
    prepareIUpgradeEventInstrumentCancelRequest parties
    prepareIUpgradeEventInstrumentReinstateRequest parties
    prepareIUpgradeEventInstrumentUpdateOutcomesOddsRequest parties
    prepareIUpgradeEventInstrumentNoOutcomes parties
    prepareIUpgradeMarketMap parties
    prepareGamblingServiceIUpgradeService parties
    prepareGamblingServiceIUpgradeOffer parties
    prepareGamblingServiceIUpgradeRequest parties
    prepareIUpgradeBlockedService parties
    prepareIUpgradePendingPromotionApplication parties
    prepareFaultyIUpgradePendingIdentity parties
    prepareIUpgradePendingIdentity parties
    prepareIUpgradeGamblerUnverifiedIdentityRequest parties
    prepareIUpgradeGamblerUnverifiedIdentity parties
    prepareIUpgradeGamblerIdentity parties
    prepareIUpgradeRejectedIdentity parties
    prepareIUpgradeExecutedBets parties
    prepareGamblingEventServiceIUpgradeService parties
    prepareGamblingEventServiceIUpgradeOffer parties
    prepareGamblingEventServiceIUpgradeRequest parties
    prepareIUpgradeGlobalPromotions parties
    prepareIUpgradePromotion parties
    prepareIUpgradePromotionRequest parties
    prepareIUpgradePromotionUpdateRequest parties
    prepareIUpgradePromotionWallet parties
    prepareIUpgradePromotionUsageHistory parties
    prepareMarketingServiceIUpgradeService parties
    prepareMarketingServiceIUpgradeOffer parties
    prepareMarketingServiceIUpgradeRequest parties
    prepareIUpgradeCounter parties
    prepareOperatorRoleIUpgradeRole parties
    prepareIUpgradeCancelledBetPlacement parties

    pure ()
