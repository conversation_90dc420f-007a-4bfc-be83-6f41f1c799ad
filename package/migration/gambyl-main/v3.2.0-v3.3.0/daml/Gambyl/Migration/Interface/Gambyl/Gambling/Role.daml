module Gambyl.Migration.Interface.Gambyl.Gambling.Role where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Gambling.Role()

import Legacy.Gambyl.Gambling.Role qualified as FromGamblingRole
import Current.Gambyl.Gambling.Role qualified as ToGamblingRole

interface IUpgradeRole where
  viewtype ToGamblingRole.Role

  upgradeRole : ContractId IUpgradeRole -> Update (ContractId IUpgradeRole)

  nonconsuming choice UpgradeRole : MigrationResult (ContractId IUpgradeRole)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeRole contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeRole this self

  interface instance IUpgradeRole for FromGamblingRole.Role where
    view = convert this
    upgradeRole self = do
      archive self
      toInterfaceContractId @IUpgradeRole <$> create (convert this : ToGamblingRole.Role)

  interface instance IUpgradeRole for ToGamblingRole.Role where
    view = this
    upgradeRole = pure

interface IUpgradeOffer where
  viewtype ToGamblingRole.Offer

  upgradeOffer : ContractId IUpgradeOffer -> Update (ContractId IUpgradeOffer)

  nonconsuming choice UpgradeOffer : MigrationResult (ContractId IUpgradeOffer)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeOffer contract") $
          migratingParty == (view this).operator
        upgradeOffer this self

  interface instance IUpgradeOffer for FromGamblingRole.Offer where
    view = convert this
    upgradeOffer self = do
      archive self
      toInterfaceContractId @IUpgradeOffer <$> create (convert this : ToGamblingRole.Offer)

  interface instance IUpgradeOffer for ToGamblingRole.Offer where
    view = this
    upgradeOffer = pure

interface IUpgradeRequest where
  viewtype ToGamblingRole.Request

  upgradeRequest : ContractId IUpgradeRequest -> Update (ContractId IUpgradeRequest)

  nonconsuming choice UpgradeRequest : MigrationResult (ContractId IUpgradeRequest)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeRequest contract") $
          migratingParty == (view this).provider
          || migratingParty == (view this).operator
        upgradeRequest this self

  interface instance IUpgradeRequest for FromGamblingRole.Request where
    view = convert this
    upgradeRequest self = do
      archive self
      toInterfaceContractId @IUpgradeRequest <$> create (convert this : ToGamblingRole.Request)

  interface instance IUpgradeRequest for ToGamblingRole.Request where
    view = this
    upgradeRequest = pure
