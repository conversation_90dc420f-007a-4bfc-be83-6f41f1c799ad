module Gambyl.Migration.Interface.Gambyl.Gambling.Bet.Model where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Gambling.Bet.Model()
import Gambyl.Migration.Gambyl.Gambling.Bet.ModelRollback()

import Legacy.Gambyl.Gambling.Bet.Model qualified as LegacyBetModel
import Current.Gambyl.Gambling.Bet.Model qualified as CurrentBetModel

import Gambyl.Migration.Gambyl.Gambling.Bet.Odds.Model()
import Gambyl.Migration.Gambyl.Marketing.Model()

interface IUpgradeBetPlacementRequest where
  viewtype CurrentBetModel.BetPlacementRequest

  upgradeBetPlacementRequest : ContractId IUpgradeBetPlacementRequest -> Update (ContractId IUpgradeBetPlacementRequest)

  nonconsuming choice UpgradeBetPlacementRequest : MigrationResult (ContractId IUpgradeBetPlacementRequest)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeBetPlacementRequest contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeBetPlacementRequest this self

  interface instance IUpgradeBetPlacementRequest for LegacyBetModel.BetPlacementRequest where
    view = convert this
    upgradeBetPlacementRequest self = do
      archive self
      toInterfaceContractId @IUpgradeBetPlacementRequest <$> create (convert this : CurrentBetModel.BetPlacementRequest)

  interface instance IUpgradeBetPlacementRequest for CurrentBetModel.BetPlacementRequest where
    view = this
    upgradeBetPlacementRequest = pure

interface IUpgradeBetPlacementRequestFlag where
  viewtype CurrentBetModel.BetPlacementRequestFlag

  upgradeBetPlacementRequestFlag : ContractId IUpgradeBetPlacementRequestFlag -> Update (ContractId IUpgradeBetPlacementRequestFlag)

  nonconsuming choice UpgradeBetPlacementRequestFlag : MigrationResult (ContractId IUpgradeBetPlacementRequestFlag)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeBetPlacementRequestFlag contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeBetPlacementRequestFlag this self

  interface instance IUpgradeBetPlacementRequestFlag for LegacyBetModel.BetPlacementRequestFlag where
    view = convert this
    upgradeBetPlacementRequestFlag self = do
      archive self
      toInterfaceContractId @IUpgradeBetPlacementRequestFlag <$> create (convert this : CurrentBetModel.BetPlacementRequestFlag)

  interface instance IUpgradeBetPlacementRequestFlag for CurrentBetModel.BetPlacementRequestFlag where
    view = this
    upgradeBetPlacementRequestFlag = pure

interface IUpgradeBulkBetPlacementRequestFlag where
  viewtype CurrentBetModel.BulkBetPlacementRequestFlag

  upgradeBulkBetPlacementRequestFlag : ContractId IUpgradeBulkBetPlacementRequestFlag -> Update (ContractId IUpgradeBulkBetPlacementRequestFlag)

  nonconsuming choice UpgradeBulkBetPlacementRequestFlag : MigrationResult (ContractId IUpgradeBulkBetPlacementRequestFlag)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeBulkBetPlacementRequestFlag contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeBulkBetPlacementRequestFlag this self

  interface instance IUpgradeBulkBetPlacementRequestFlag for LegacyBetModel.BulkBetPlacementRequestFlag where
    view = convert this
    upgradeBulkBetPlacementRequestFlag self = do
      archive self
      toInterfaceContractId @IUpgradeBulkBetPlacementRequestFlag <$> create (convert this : CurrentBetModel.BulkBetPlacementRequestFlag)

  interface instance IUpgradeBulkBetPlacementRequestFlag for CurrentBetModel.BulkBetPlacementRequestFlag where
    view = this
    upgradeBulkBetPlacementRequestFlag = pure

interface IUpgradeBetPlacementFinalizeRequest where
  viewtype CurrentBetModel.BetPlacementFinalizeRequest

  upgradeBetPlacementFinalizeRequest : ContractId IUpgradeBetPlacementFinalizeRequest -> Update (ContractId IUpgradeBetPlacementFinalizeRequest)

  nonconsuming choice UpgradeBetPlacementFinalizeRequest : MigrationResult (ContractId IUpgradeBetPlacementFinalizeRequest)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeBetPlacementFinalizeRequest contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeBetPlacementFinalizeRequest this self

  interface instance IUpgradeBetPlacementFinalizeRequest for LegacyBetModel.BetPlacementFinalizeRequest where
    view = convert this
    upgradeBetPlacementFinalizeRequest self = do
      archive self
      toInterfaceContractId @IUpgradeBetPlacementFinalizeRequest <$> create (convert this : CurrentBetModel.BetPlacementFinalizeRequest)

  interface instance IUpgradeBetPlacementFinalizeRequest for CurrentBetModel.BetPlacementFinalizeRequest where
    view = this
    upgradeBetPlacementFinalizeRequest = pure


interface IUpgradeBetPlacement where
  viewtype LegacyBetModel.BetPlacement

  upgradeBetPlacement : ContractId IUpgradeBetPlacement -> Update (ContractId IUpgradeBetPlacement)

  nonconsuming choice UpgradeBetPlacement : MigrationResult (ContractId IUpgradeBetPlacement)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeBetPlacement contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider

        coerceInterfaceContractId <$> upgradeBetPlacement this self

  interface instance IUpgradeBetPlacement for LegacyBetModel.BetPlacement where
    view = this
    upgradeBetPlacement self = do
      archive self
      toInterfaceContractId @IUpgradeBetPlacement <$> create (convert this : CurrentBetModel.BetPlacement)

  interface instance IUpgradeBetPlacement for CurrentBetModel.BetPlacement where
    view = convert this
    upgradeBetPlacement = pure

interface IUpgradeBetPlacementSplitRequest where
  viewtype CurrentBetModel.BetPlacementSplitRequest

  upgradeBetPlacementSplitRequest : ContractId IUpgradeBetPlacementSplitRequest -> Update (ContractId IUpgradeBetPlacementSplitRequest)

  nonconsuming choice UpgradeBetPlacementSplitRequest : MigrationResult (ContractId IUpgradeBetPlacementSplitRequest)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeBetPlacementSplitRequest contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeBetPlacementSplitRequest this self

  interface instance IUpgradeBetPlacementSplitRequest for LegacyBetModel.BetPlacementSplitRequest where
    view = convert this
    upgradeBetPlacementSplitRequest self = do
      archive self
      toInterfaceContractId @IUpgradeBetPlacementSplitRequest <$> create (convert this : CurrentBetModel.BetPlacementSplitRequest)

  interface instance IUpgradeBetPlacementSplitRequest for CurrentBetModel.BetPlacementSplitRequest where
    view = this
    upgradeBetPlacementSplitRequest = pure

interface IUpgradeBetPlacementCancelRequest where
  viewtype CurrentBetModel.BetPlacementCancelRequest

  upgradeBetPlacementCancelRequest : ContractId IUpgradeBetPlacementCancelRequest -> Update (ContractId IUpgradeBetPlacementCancelRequest)

  nonconsuming choice UpgradeBetPlacementCancelRequest : MigrationResult (ContractId IUpgradeBetPlacementCancelRequest)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeBetPlacementCancelRequest contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeBetPlacementCancelRequest this self

  interface instance IUpgradeBetPlacementCancelRequest for LegacyBetModel.BetPlacementCancelRequest where
    view = convert this
    upgradeBetPlacementCancelRequest self = do
      archive self
      toInterfaceContractId @IUpgradeBetPlacementCancelRequest <$> create (convert this : CurrentBetModel.BetPlacementCancelRequest)

  interface instance IUpgradeBetPlacementCancelRequest for CurrentBetModel.BetPlacementCancelRequest where
    view = this
    upgradeBetPlacementCancelRequest = pure

interface IUpgradeFinalizeBetPlacementCancelRequest where
  viewtype CurrentBetModel.FinalizeBetPlacementCancelRequest

  upgradeFinalizeBetPlacementCancelRequest : ContractId IUpgradeFinalizeBetPlacementCancelRequest -> Update (ContractId IUpgradeFinalizeBetPlacementCancelRequest)

  nonconsuming choice UpgradeFinalizeBetPlacementCancelRequest : MigrationResult (ContractId IUpgradeFinalizeBetPlacementCancelRequest)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeFinalizeBetPlacementCancelRequest contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeFinalizeBetPlacementCancelRequest this self

  interface instance IUpgradeFinalizeBetPlacementCancelRequest for LegacyBetModel.FinalizeBetPlacementCancelRequest where
    view = convert this
    upgradeFinalizeBetPlacementCancelRequest self = do
      archive self
      toInterfaceContractId @IUpgradeFinalizeBetPlacementCancelRequest <$> create (convert this : CurrentBetModel.FinalizeBetPlacementCancelRequest)

  interface instance IUpgradeFinalizeBetPlacementCancelRequest for CurrentBetModel.FinalizeBetPlacementCancelRequest where
    view = this
    upgradeFinalizeBetPlacementCancelRequest = pure


interface IUpgradeCancelledBetPlacement where
  viewtype CurrentBetModel.CancelledBetPlacement

  upgradeCancelledBetPlacement : ContractId IUpgradeCancelledBetPlacement -> Update (ContractId IUpgradeCancelledBetPlacement)

  nonconsuming choice UpgradeCancelledBetPlacement : MigrationResult (ContractId IUpgradeCancelledBetPlacement)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeBetPlacement contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeCancelledBetPlacement this self

  interface instance IUpgradeCancelledBetPlacement for LegacyBetModel.CancelledBetPlacement where
    view = convert this
    upgradeCancelledBetPlacement self = do
      archive self
      toInterfaceContractId @IUpgradeCancelledBetPlacement <$> create (convert this : CurrentBetModel.CancelledBetPlacement)

  interface instance IUpgradeCancelledBetPlacement for CurrentBetModel.CancelledBetPlacement where
    view = this
    upgradeCancelledBetPlacement = pure


interface IUpgradeBetHistory where
  viewtype CurrentBetModel.BetHistory

  upgradeBetHistory : ContractId IUpgradeBetHistory -> Update (ContractId IUpgradeBetHistory)

  nonconsuming choice UpgradeBetHistory : MigrationResult (ContractId IUpgradeBetHistory)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeBetHistory contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeBetHistory this self

  interface instance IUpgradeBetHistory for LegacyBetModel.BetHistory where
    view = convert this
    upgradeBetHistory self = do
      archive self
      toInterfaceContractId @IUpgradeBetHistory <$> create (convert this : CurrentBetModel.BetHistory)

  interface instance IUpgradeBetHistory for CurrentBetModel.BetHistory where
    view = this
    upgradeBetHistory = pure

interface IUpgradeBulkBetPlacementRequest where
  viewtype CurrentBetModel.BulkBetPlacementRequest

  upgradeBulkBetPlacementRequest : ContractId IUpgradeBulkBetPlacementRequest -> Update (ContractId IUpgradeBulkBetPlacementRequest)

  nonconsuming choice UpgradeBulkBetPlacementRequest : MigrationResult (ContractId IUpgradeBulkBetPlacementRequest)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeBulkBetPlacementRequest contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeBulkBetPlacementRequest this self

  interface instance IUpgradeBulkBetPlacementRequest for LegacyBetModel.BulkBetPlacementRequest where
    view = convert this
    upgradeBulkBetPlacementRequest self = do
      archive self
      toInterfaceContractId @IUpgradeBulkBetPlacementRequest <$> create (convert this : CurrentBetModel.BulkBetPlacementRequest)

  interface instance IUpgradeBulkBetPlacementRequest for CurrentBetModel.BulkBetPlacementRequest where
    view = this
    upgradeBulkBetPlacementRequest = pure

interface IUpgradeBulkBetPlacementFinalizeRequest where
  viewtype CurrentBetModel.BulkBetPlacementFinalizeRequest

  stakeholders : [Party]
  upgradeBulkBetPlacementFinalizeRequest : ContractId IUpgradeBulkBetPlacementFinalizeRequest -> Update (ContractId IUpgradeBulkBetPlacementFinalizeRequest)

  nonconsuming choice UpgradeBulkBetPlacementFinalizeRequest : MigrationResult (ContractId IUpgradeBulkBetPlacementFinalizeRequest)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeBulkBetPlacementFinalizeRequest contract") $
          migratingParty `elem` stakeholders this
        upgradeBulkBetPlacementFinalizeRequest this self

  interface instance IUpgradeBulkBetPlacementFinalizeRequest for LegacyBetModel.BulkBetPlacementFinalizeRequest where
    view = convert this
    stakeholders = [this.operator, this.provider]
    upgradeBulkBetPlacementFinalizeRequest self = do
      archive self
      toInterfaceContractId @IUpgradeBulkBetPlacementFinalizeRequest <$> create (convert this : CurrentBetModel.BulkBetPlacementFinalizeRequest)

  interface instance IUpgradeBulkBetPlacementFinalizeRequest for CurrentBetModel.BulkBetPlacementFinalizeRequest where
    view = this
    stakeholders = [this.operator, this.provider]
    upgradeBulkBetPlacementFinalizeRequest = pure

interface IUpgradeBulkBetPlacement where
  viewtype CurrentBetModel.BulkBetPlacement

  upgradeBulkBetPlacement : ContractId IUpgradeBulkBetPlacement -> Update (ContractId IUpgradeBulkBetPlacement)

  nonconsuming choice UpgradeBulkBetPlacement : MigrationResult (ContractId IUpgradeBulkBetPlacement)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeBulkBetPlacement contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeBulkBetPlacement this self

  interface instance IUpgradeBulkBetPlacement for LegacyBetModel.BulkBetPlacement where
    view = convert this
    upgradeBulkBetPlacement self = do
      archive self
      toInterfaceContractId @IUpgradeBulkBetPlacement <$> create (convert this : CurrentBetModel.BulkBetPlacement)

  interface instance IUpgradeBulkBetPlacement for CurrentBetModel.BulkBetPlacement where
    view = this
    upgradeBulkBetPlacement = pure
