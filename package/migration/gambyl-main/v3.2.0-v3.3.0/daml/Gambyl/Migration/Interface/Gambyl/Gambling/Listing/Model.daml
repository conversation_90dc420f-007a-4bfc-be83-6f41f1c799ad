module Gambyl.Migration.Interface.Gambyl.Gambling.Listing.Model where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Gambling.Listing.Model ()

import Legacy.Gambyl.Gambling.Listing.Model qualified as LegacyListingModel
import Current.Gambyl.Gambling.Listing.Model qualified as CurrentListingModel

interface IUpgradeGamblingListing where
  viewtype CurrentListingModel.GamblingListing

  upgradeGamblingListing : ContractId IUpgradeGamblingListing -> Update (ContractId IUpgradeGamblingListing)

  nonconsuming choice UpgradeGamblingListing : MigrationResult (ContractId IUpgradeGamblingListing)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeGamblingListing contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeGamblingListing this self

  interface instance IUpgradeGamblingListing for LegacyListingModel.GamblingListing where
    view = convert this
    upgradeGamblingListing self = do
      archive self
      toInterfaceContractId @IUpgradeGamblingListing <$> create (convert this : CurrentListingModel.GamblingListing)

  interface instance IUpgradeGamblingListing for CurrentListingModel.GamblingListing where
    view = this
    upgradeGamblingListing = pure

interface IUpgradeGamblingUpdateListingRequest where
  viewtype CurrentListingModel.GamblingUpdateListingRequest

  upgradeGamblingUpdateListingRequest : ContractId IUpgradeGamblingUpdateListingRequest -> Update (ContractId IUpgradeGamblingUpdateListingRequest)

  nonconsuming choice UpgradeGamblingUpdateListingRequest : MigrationResult (ContractId IUpgradeGamblingUpdateListingRequest)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeGamblingUpdateListingRequest contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeGamblingUpdateListingRequest this self

  interface instance IUpgradeGamblingUpdateListingRequest for LegacyListingModel.GamblingUpdateListingRequest where
    view = convert this
    upgradeGamblingUpdateListingRequest self = do
      archive self
      toInterfaceContractId @IUpgradeGamblingUpdateListingRequest <$> create (convert this : CurrentListingModel.GamblingUpdateListingRequest)

  interface instance IUpgradeGamblingUpdateListingRequest for CurrentListingModel.GamblingUpdateListingRequest where
    view = this
    upgradeGamblingUpdateListingRequest = pure

interface IUpgradeFailedGamblingUpdateListing where
  viewtype CurrentListingModel.FailedGamblingUpdateListing

  upgradeFailedGamblingUpdateListing : ContractId IUpgradeFailedGamblingUpdateListing -> Update (ContractId IUpgradeFailedGamblingUpdateListing)

  nonconsuming choice UpgradeFailedGamblingUpdateListing : MigrationResult (ContractId IUpgradeFailedGamblingUpdateListing)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeFailedGamblingUpdateListing contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeFailedGamblingUpdateListing this self

  interface instance IUpgradeFailedGamblingUpdateListing for LegacyListingModel.FailedGamblingUpdateListing where
    view = convert this
    upgradeFailedGamblingUpdateListing self = do
      archive self
      toInterfaceContractId @IUpgradeFailedGamblingUpdateListing <$> create (convert this : CurrentListingModel.FailedGamblingUpdateListing)

  interface instance IUpgradeFailedGamblingUpdateListing for CurrentListingModel.FailedGamblingUpdateListing where
    view = this
    upgradeFailedGamblingUpdateListing = pure
