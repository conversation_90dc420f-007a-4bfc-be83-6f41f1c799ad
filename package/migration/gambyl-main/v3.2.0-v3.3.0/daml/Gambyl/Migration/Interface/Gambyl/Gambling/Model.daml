module Gambyl.Migration.Interface.Gambyl.Gambling.Model where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Gambling.Model ()

import Legacy.Gambyl.Gambling.Model qualified as LegacyGamblingModel
import Current.Gambyl.Gambling.Model qualified as CurrentGamblingModel

interface IUpgradeGlobalGamblingConfiguration where
  viewtype CurrentGamblingModel.GlobalGamblingConfiguration

  upgradeGlobalGamblingConfiguration : ContractId IUpgradeGlobalGamblingConfiguration -> Update (ContractId IUpgradeGlobalGamblingConfiguration)

  nonconsuming choice UpgradeGlobalGamblingConfiguration : MigrationResult (ContractId IUpgradeGlobalGamblingConfiguration)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeGlobalGamblingConfiguration contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeGlobalGamblingConfiguration this self

  interface instance IUpgradeGlobalGamblingConfiguration for LegacyGamblingModel.GlobalGamblingConfiguration where
    view = convert this
    upgradeGlobalGamblingConfiguration self = do
      archive self
      toInterfaceContractId @IUpgradeGlobalGamblingConfiguration <$> create (convert this : CurrentGamblingModel.GlobalGamblingConfiguration)

  interface instance IUpgradeGlobalGamblingConfiguration for CurrentGamblingModel.GlobalGamblingConfiguration where
    view = this
    upgradeGlobalGamblingConfiguration = pure

interface IUpgradeActionFailure where
  viewtype CurrentGamblingModel.ActionFailure

  upgradeActionFailure : ContractId IUpgradeActionFailure -> Update (ContractId IUpgradeActionFailure)

  nonconsuming choice UpgradeActionFailure : MigrationResult (ContractId IUpgradeActionFailure)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeActionFailure contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeActionFailure this self

  interface instance IUpgradeActionFailure for LegacyGamblingModel.ActionFailure where
    view = convert this
    upgradeActionFailure self = do
      archive self
      toInterfaceContractId @IUpgradeActionFailure <$> create (convert this : CurrentGamblingModel.ActionFailure)

  interface instance IUpgradeActionFailure for CurrentGamblingModel.ActionFailure where
    view = this
    upgradeActionFailure = pure

interface IUpgradeActionSuccess where
  viewtype CurrentGamblingModel.ActionSuccess

  upgradeActionSuccess : ContractId IUpgradeActionSuccess -> Update (ContractId IUpgradeActionSuccess)

  nonconsuming choice UpgradeActionSuccess : MigrationResult (ContractId IUpgradeActionSuccess)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeActionSuccess contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeActionSuccess this self

  interface instance IUpgradeActionSuccess for LegacyGamblingModel.ActionSuccess where
    view = convert this
    upgradeActionSuccess self = do
      archive self
      toInterfaceContractId @IUpgradeActionSuccess <$> create (convert this : CurrentGamblingModel.ActionSuccess)

  interface instance IUpgradeActionSuccess for CurrentGamblingModel.ActionSuccess where
    view = this
    upgradeActionSuccess = pure
