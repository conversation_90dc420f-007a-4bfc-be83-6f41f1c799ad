module Gambyl.Migration.Interface.Gambyl.Gambling.ModelRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Gambling.ModelRollback ()

import Legacy.Gambyl.Gambling.Model qualified as LegacyGamblingModel
import Current.Gambyl.Gambling.Model qualified as CurrentGamblingModel

interface IUpgradeGlobalGamblingConfigurationRollback where
  viewtype LegacyGamblingModel.GlobalGamblingConfiguration

  upgradeGlobalGamblingConfigurationRollback : ContractId IUpgradeGlobalGamblingConfigurationRollback -> Update (ContractId IUpgradeGlobalGamblingConfigurationRollback)

  nonconsuming choice UpgradeGlobalGamblingConfigurationRollback : MigrationResult (ContractId IUpgradeGlobalGamblingConfigurationRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeGlobalGamblingConfigurationRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeGlobalGamblingConfigurationRollback this self

  interface instance IUpgradeGlobalGamblingConfigurationRollback for CurrentGamblingModel.GlobalGamblingConfiguration where
    view = convert this
    upgradeGlobalGamblingConfigurationRollback self = do
      archive self
      toInterfaceContractId @IUpgradeGlobalGamblingConfigurationRollback <$> create (convert this : LegacyGamblingModel.GlobalGamblingConfiguration)

  interface instance IUpgradeGlobalGamblingConfigurationRollback for LegacyGamblingModel.GlobalGamblingConfiguration where
    view = this
    upgradeGlobalGamblingConfigurationRollback = pure

interface IUpgradeActionFailureRollback where
  viewtype LegacyGamblingModel.ActionFailure

  upgradeActionFailureRollback : ContractId IUpgradeActionFailureRollback -> Update (ContractId IUpgradeActionFailureRollback)

  nonconsuming choice UpgradeActionFailureRollback : MigrationResult (ContractId IUpgradeActionFailureRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeActionFailureRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeActionFailureRollback this self

  interface instance IUpgradeActionFailureRollback for CurrentGamblingModel.ActionFailure where
    view = convert this
    upgradeActionFailureRollback self = do
      archive self
      toInterfaceContractId @IUpgradeActionFailureRollback <$> create (convert this : LegacyGamblingModel.ActionFailure)

  interface instance IUpgradeActionFailureRollback for LegacyGamblingModel.ActionFailure where
    view = this
    upgradeActionFailureRollback = pure

interface IUpgradeActionSuccessRollback where
  viewtype LegacyGamblingModel.ActionSuccess

  upgradeActionSuccessRollback : ContractId IUpgradeActionSuccessRollback -> Update (ContractId IUpgradeActionSuccessRollback)

  nonconsuming choice UpgradeActionSuccessRollback : MigrationResult (ContractId IUpgradeActionSuccessRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeActionSuccessRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeActionSuccessRollback this self

  interface instance IUpgradeActionSuccessRollback for CurrentGamblingModel.ActionSuccess where
    view = convert this
    upgradeActionSuccessRollback self = do
      archive self
      toInterfaceContractId @IUpgradeActionSuccessRollback <$> create (convert this : LegacyGamblingModel.ActionSuccess)

  interface instance IUpgradeActionSuccessRollback for LegacyGamblingModel.ActionSuccess where
    view = this
    upgradeActionSuccessRollback = pure
