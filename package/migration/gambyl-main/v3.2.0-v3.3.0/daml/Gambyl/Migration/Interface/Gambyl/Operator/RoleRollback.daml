module Gambyl.Migration.Interface.Gambyl.Operator.RoleRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Operator.RoleRollback()

import Legacy.Gambyl.Operator.Role qualified as LegacyOperatorRole
import Current.Gambyl.Operator.Role qualified as CurrentOperatorRole

interface IUpgradeRoleRollback where
  viewtype LegacyOperatorRole.Role

  upgradeRoleRollback : ContractId IUpgradeRoleRollback -> Update (ContractId IUpgradeRoleRollback)

  nonconsuming choice UpgradeRoleRollback : MigrationResult (ContractId IUpgradeRoleRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeRoleRollback contract") $
          migratingParty == (view this).operator
        upgradeRoleRollback this self

  interface instance IUpgradeRoleRollback for CurrentOperatorRole.Role where
    view = convert this
    upgradeRoleRollback self = do
      archive self
      toInterfaceContractId @IUpgradeRoleRollback <$> create (convert this : LegacyOperatorRole.Role)

  interface instance IUpgradeRoleRollback for LegacyOperatorRole.Role where
    view = this
    upgradeRoleRollback = pure
