module Gambyl.Migration.Interface.Gambyl.Gambling.Identity.ModelRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Gambling.Identity.ModelRollback()

import Legacy.Gambyl.Gambling.Identity.Model qualified as LegacyIdentityModel
import Current.Gambyl.Gambling.Identity.Model qualified as CurrentIdentityModel

interface IUpgradePendingIdentityRollback where
  viewtype LegacyIdentityModel.PendingIdentity

  upgradePendingIdentityRollback : ContractId IUpgradePendingIdentityRollback -> Update (ContractId IUpgradePendingIdentityRollback)

  nonconsuming choice UpgradePendingIdentityRollback : MigrationResult (ContractId IUpgradePendingIdentityRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradePendingIdentityRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradePendingIdentityRollback this self

  interface instance IUpgradePendingIdentityRollback for CurrentIdentityModel.PendingIdentity where
    view = convert this
    upgradePendingIdentityRollback self = do
      archive self
      toInterfaceContractId @IUpgradePendingIdentityRollback <$> create (convert this : LegacyIdentityModel.PendingIdentity)

  interface instance IUpgradePendingIdentityRollback for LegacyIdentityModel.PendingIdentity where
    view = this
    upgradePendingIdentityRollback = pure

interface IUpgradeGamblerUnverifiedIdentityRequestRollback where
  viewtype LegacyIdentityModel.GamblerUnverifiedIdentityRequest

  upgradeGamblerUnverifiedIdentityRequestRollback : ContractId IUpgradeGamblerUnverifiedIdentityRequestRollback -> Update (ContractId IUpgradeGamblerUnverifiedIdentityRequestRollback)

  nonconsuming choice UpgradeGamblerUnverifiedIdentityRequestRollback : MigrationResult (ContractId IUpgradeGamblerUnverifiedIdentityRequestRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeGamblerUnverifiedIdentityRequestRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeGamblerUnverifiedIdentityRequestRollback this self

  interface instance IUpgradeGamblerUnverifiedIdentityRequestRollback for CurrentIdentityModel.GamblerUnverifiedIdentityRequest where
    view = convert this
    upgradeGamblerUnverifiedIdentityRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeGamblerUnverifiedIdentityRequestRollback <$> create (convert this : LegacyIdentityModel.GamblerUnverifiedIdentityRequest)

  interface instance IUpgradeGamblerUnverifiedIdentityRequestRollback for LegacyIdentityModel.GamblerUnverifiedIdentityRequest where
    view = this
    upgradeGamblerUnverifiedIdentityRequestRollback = pure

interface IUpgradeGamblerUnverifiedIdentityRollback where
  viewtype LegacyIdentityModel.GamblerUnverifiedIdentity

  upgradeGamblerUnverifiedIdentityRollback : ContractId IUpgradeGamblerUnverifiedIdentityRollback -> Update (ContractId IUpgradeGamblerUnverifiedIdentityRollback)

  nonconsuming choice UpgradeGamblerUnverifiedIdentityRollback : MigrationResult (ContractId IUpgradeGamblerUnverifiedIdentityRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeGamblerUnverifiedIdentityRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeGamblerUnverifiedIdentityRollback this self

  interface instance IUpgradeGamblerUnverifiedIdentityRollback for CurrentIdentityModel.GamblerUnverifiedIdentity where
    view = convert this
    upgradeGamblerUnverifiedIdentityRollback self = do
      archive self
      toInterfaceContractId @IUpgradeGamblerUnverifiedIdentityRollback <$> create (convert this : LegacyIdentityModel.GamblerUnverifiedIdentity)

  interface instance IUpgradeGamblerUnverifiedIdentityRollback for LegacyIdentityModel.GamblerUnverifiedIdentity where
    view = this
    upgradeGamblerUnverifiedIdentityRollback = pure

interface IUpgradeGamblerIdentityRollback where
  viewtype LegacyIdentityModel.GamblerIdentity

  upgradeGamblerIdentityRollback : ContractId IUpgradeGamblerIdentityRollback -> Update (ContractId IUpgradeGamblerIdentityRollback)

  nonconsuming choice UpgradeGamblerIdentityRollback : MigrationResult (ContractId IUpgradeGamblerIdentityRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeGamblerIdentityRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeGamblerIdentityRollback this self

  interface instance IUpgradeGamblerIdentityRollback for CurrentIdentityModel.GamblerIdentity where
    view = convert this
    upgradeGamblerIdentityRollback self = do
      archive self
      toInterfaceContractId @IUpgradeGamblerIdentityRollback <$> create (convert this : LegacyIdentityModel.GamblerIdentity)

  interface instance IUpgradeGamblerIdentityRollback for LegacyIdentityModel.GamblerIdentity where
    view = this
    upgradeGamblerIdentityRollback = pure

interface IUpgradeRejectedIdentityRollback where
  viewtype LegacyIdentityModel.RejectedIdentity

  upgradeRejectedIdentityRollback : ContractId IUpgradeRejectedIdentityRollback -> Update (ContractId IUpgradeRejectedIdentityRollback)

  nonconsuming choice UpgradeRejectedIdentityRollback : MigrationResult (ContractId IUpgradeRejectedIdentityRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeRejectedIdentityRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeRejectedIdentityRollback this self

  interface instance IUpgradeRejectedIdentityRollback for CurrentIdentityModel.RejectedIdentity where
    view = convert this
    upgradeRejectedIdentityRollback self = do
      archive self
      toInterfaceContractId @IUpgradeRejectedIdentityRollback <$> create (convert this : LegacyIdentityModel.RejectedIdentity)

  interface instance IUpgradeRejectedIdentityRollback for LegacyIdentityModel.RejectedIdentity where
    view = this
    upgradeRejectedIdentityRollback = pure
