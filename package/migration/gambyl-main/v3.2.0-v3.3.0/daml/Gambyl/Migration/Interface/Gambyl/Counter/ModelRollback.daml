module Gambyl.Migration.Interface.Gambyl.Counter.ModelRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Gambyl.Counter.ModelRollback()

import Legacy.Gambyl.Counter.Model qualified as LegacyCounterModel
import Current.Gambyl.Counter.Model qualified as CurrentCounterModel

interface IUpgradeCounterRollback where
  viewtype LegacyCounterModel.Counter

  upgradeCounterRollback : ContractId IUpgradeCounterRollback -> Update (ContractId IUpgradeCounterRollback)

  nonconsuming choice UpgradeCounterRollback : MigrationResult (ContractId IUpgradeCounterRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeCounterRollback contract") $
          migratingParty == (view this).operator
          || migratingParty == (view this).provider
        upgradeCounterRollback this self

  interface instance IUpgradeCounterRollback for CurrentCounterModel.Counter where
    view = convert this
    upgradeCounterRollback self = do
      archive self
      toInterfaceContractId @IUpgradeCounterRollback <$> create (convert this : LegacyCounterModel.Counter)

  interface instance IUpgradeCounterRollback for LegacyCounterModel.Counter where
    view = this
    upgradeCounterRollback = pure
