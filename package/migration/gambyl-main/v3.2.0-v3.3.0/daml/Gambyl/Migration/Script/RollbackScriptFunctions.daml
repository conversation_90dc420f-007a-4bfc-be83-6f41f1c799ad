module Gambyl.Migration.Script.RollbackScriptFunctions where

import Daml.Script
import Gambyl.Migration.Common

import Gambyl.Migration.Interface.Gambyl.Counter.ModelRollback qualified as CounterModel
import Gambyl.Migration.Interface.Gambyl.Gambling.Account.ModelRollback qualified as GamblingAccountModel
import Gambyl.Migration.Interface.Gambyl.Gambling.Bet.ModelRollback qualified as GamblingBetModel
import Gambyl.Migration.Interface.Gambyl.Gambling.Event.ModelRollback qualified as GamblingEventModel
import Gambyl.Migration.Interface.Gambyl.Gambling.Event.ServiceRollback qualified as GamblingEventService
import Gambyl.Migration.Interface.Gambyl.Gambling.Identity.ModelRollback qualified as GamblingIdentityModel
import Gambyl.Migration.Interface.Gambyl.Gambling.Listing.ModelRollback qualified as GamblingListingModel
import Gambyl.Migration.Interface.Gambyl.Gambling.ModelRollback qualified as GamblingModel
import Gambyl.Migration.Interface.Gambyl.Gambling.RoleRollback qualified as GamblingRole
import Gambyl.Migration.Interface.Gambyl.Gambling.ServiceRollback qualified as GamblingService
import Gambyl.Migration.Interface.Gambyl.Gambling.Settlement.ModelRollback qualified as GamblingSettlementModel
import Gambyl.Migration.Interface.Gambyl.Marketing.ModelRollback qualified as MarketingModel
import Gambyl.Migration.Interface.Gambyl.Marketing.ServiceRollback qualified as MarketingService
import Gambyl.Migration.Interface.Gambyl.Operator.RoleRollback qualified as OperatorRole

upgradeListOfIUpgradeGlobalGamblingConfigurationRollback : Party -> [ContractId GamblingModel.IUpgradeGlobalGamblingConfigurationRollback] -> Script [MigrationResult (ContractId GamblingModel.IUpgradeGlobalGamblingConfigurationRollback)]
upgradeListOfIUpgradeGlobalGamblingConfigurationRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingModel.UpgradeGlobalGamblingConfigurationRollback with ..

upgradeListOfIUpgradeActionFailureRollback : Party -> [ContractId GamblingModel.IUpgradeActionFailureRollback] -> Script [MigrationResult (ContractId GamblingModel.IUpgradeActionFailureRollback)]
upgradeListOfIUpgradeActionFailureRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingModel.UpgradeActionFailureRollback with ..

upgradeListOfIUpgradeActionSuccessRollback : Party -> [ContractId GamblingModel.IUpgradeActionSuccessRollback] -> Script [MigrationResult (ContractId GamblingModel.IUpgradeActionSuccessRollback)]
upgradeListOfIUpgradeActionSuccessRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingModel.UpgradeActionSuccessRollback with ..

upgradeListOfGamblingRoleIUpgradeRoleRollback : Party -> [ContractId GamblingRole.IUpgradeRoleRollback] -> Script [MigrationResult (ContractId GamblingRole.IUpgradeRoleRollback)]
upgradeListOfGamblingRoleIUpgradeRoleRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingRole.UpgradeRoleRollback with ..

upgradeListOfGamblingRoleIUpgradeOfferRollback : Party -> [ContractId GamblingRole.IUpgradeOfferRollback] -> Script [MigrationResult (ContractId GamblingRole.IUpgradeOfferRollback)]
upgradeListOfGamblingRoleIUpgradeOfferRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingRole.UpgradeOfferRollback with ..

upgradeListOfGamblingRoleIUpgradeRequestRollback : Party -> [ContractId GamblingRole.IUpgradeRequestRollback] -> Script [MigrationResult (ContractId GamblingRole.IUpgradeRequestRollback)]
upgradeListOfGamblingRoleIUpgradeRequestRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingRole.UpgradeRequestRollback with ..

upgradeListOfGamblingServiceIUpgradeServiceRollback : Party -> [ContractId GamblingService.IUpgradeServiceRollback] -> Script [MigrationResult (ContractId GamblingService.IUpgradeServiceRollback)]
upgradeListOfGamblingServiceIUpgradeServiceRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingService.UpgradeServiceRollback with ..

upgradeListOfGamblingServiceIUpgradeOfferRollback : Party -> [ContractId GamblingService.IUpgradeOfferRollback] -> Script [MigrationResult (ContractId GamblingService.IUpgradeOfferRollback)]
upgradeListOfGamblingServiceIUpgradeOfferRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingService.UpgradeOfferRollback with ..

upgradeListOfGamblingServiceIUpgradeRequestRollback : Party -> [ContractId GamblingService.IUpgradeRequestRollback] -> Script [MigrationResult (ContractId GamblingService.IUpgradeRequestRollback)]
upgradeListOfGamblingServiceIUpgradeRequestRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingService.UpgradeRequestRollback with ..

upgradeListOfIUpgradeBlockedServiceRollback : Party -> [ContractId GamblingService.IUpgradeBlockedServiceRollback] -> Script [MigrationResult (ContractId GamblingService.IUpgradeBlockedServiceRollback)]
upgradeListOfIUpgradeBlockedServiceRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingService.UpgradeBlockedServiceRollback with ..

upgradeListOfIUpgradePendingPromotionApplicationRollback : Party -> [ContractId GamblingService.IUpgradePendingPromotionApplicationRollback] -> Script [MigrationResult (ContractId GamblingService.IUpgradePendingPromotionApplicationRollback)]
upgradeListOfIUpgradePendingPromotionApplicationRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingService.UpgradePendingPromotionApplicationRollback with ..

upgradeListOfIUpgradePendingIdentityRollback : Party -> [ContractId GamblingIdentityModel.IUpgradePendingIdentityRollback] -> Script [MigrationResult (ContractId GamblingIdentityModel.IUpgradePendingIdentityRollback)]
upgradeListOfIUpgradePendingIdentityRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingIdentityModel.UpgradePendingIdentityRollback with ..

upgradeListOfIUpgradeGamblerUnverifiedIdentityRequestRollback : Party -> [ContractId GamblingIdentityModel.IUpgradeGamblerUnverifiedIdentityRequestRollback] -> Script [MigrationResult (ContractId GamblingIdentityModel.IUpgradeGamblerUnverifiedIdentityRequestRollback)]
upgradeListOfIUpgradeGamblerUnverifiedIdentityRequestRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingIdentityModel.UpgradeGamblerUnverifiedIdentityRequestRollback with ..

upgradeListOfIUpgradeGamblerUnverifiedIdentityRollback : Party -> [ContractId GamblingIdentityModel.IUpgradeGamblerUnverifiedIdentityRollback] -> Script [MigrationResult (ContractId GamblingIdentityModel.IUpgradeGamblerUnverifiedIdentityRollback)]
upgradeListOfIUpgradeGamblerUnverifiedIdentityRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingIdentityModel.UpgradeGamblerUnverifiedIdentityRollback with ..

upgradeListOfIUpgradeGamblerIdentityRollback : Party -> [ContractId GamblingIdentityModel.IUpgradeGamblerIdentityRollback] -> Script [MigrationResult (ContractId GamblingIdentityModel.IUpgradeGamblerIdentityRollback)]
upgradeListOfIUpgradeGamblerIdentityRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingIdentityModel.UpgradeGamblerIdentityRollback with ..

upgradeListOfIUpgradeRejectedIdentityRollback : Party -> [ContractId GamblingIdentityModel.IUpgradeRejectedIdentityRollback] -> Script [MigrationResult (ContractId GamblingIdentityModel.IUpgradeRejectedIdentityRollback)]
upgradeListOfIUpgradeRejectedIdentityRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingIdentityModel.UpgradeRejectedIdentityRollback with ..

upgradeListOfIUpgradeExecutedBetsRollback : Party -> [ContractId GamblingSettlementModel.IUpgradeExecutedBetsRollback] -> Script [MigrationResult (ContractId GamblingSettlementModel.IUpgradeExecutedBetsRollback)]
upgradeListOfIUpgradeExecutedBetsRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingSettlementModel.UpgradeExecutedBetsRollback with ..

upgradeListOfIUpgradeBetPlacementRequestRollback : Party -> [ContractId GamblingBetModel.IUpgradeBetPlacementRequestRollback] -> Script [MigrationResult (ContractId GamblingBetModel.IUpgradeBetPlacementRequestRollback)]
upgradeListOfIUpgradeBetPlacementRequestRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingBetModel.UpgradeBetPlacementRequestRollback with ..

upgradeListOfIUpgradeBetPlacementRequestFlagRollback : Party -> [ContractId GamblingBetModel.IUpgradeBetPlacementRequestFlagRollback] -> Script [MigrationResult (ContractId GamblingBetModel.IUpgradeBetPlacementRequestFlagRollback)]
upgradeListOfIUpgradeBetPlacementRequestFlagRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingBetModel.UpgradeBetPlacementRequestFlagRollback with ..

upgradeListOfIUpgradeBulkBetPlacementRequestFlagRollback : Party -> [ContractId GamblingBetModel.IUpgradeBulkBetPlacementRequestFlagRollback] -> Script [MigrationResult (ContractId GamblingBetModel.IUpgradeBulkBetPlacementRequestFlagRollback)]
upgradeListOfIUpgradeBulkBetPlacementRequestFlagRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingBetModel.UpgradeBulkBetPlacementRequestFlagRollback with ..

upgradeListOfIUpgradeBetPlacementFinalizeRequestRollback : Party -> [ContractId GamblingBetModel.IUpgradeBetPlacementFinalizeRequestRollback] -> Script [MigrationResult (ContractId GamblingBetModel.IUpgradeBetPlacementFinalizeRequestRollback)]
upgradeListOfIUpgradeBetPlacementFinalizeRequestRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingBetModel.UpgradeBetPlacementFinalizeRequestRollback with ..

upgradeListOfIUpgradeBetPlacementRollback : Party -> [ContractId GamblingBetModel.IUpgradeBetPlacementRollback] -> Script [MigrationResult (ContractId GamblingBetModel.IUpgradeBetPlacementRollback)]
upgradeListOfIUpgradeBetPlacementRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingBetModel.UpgradeBetPlacementRollback with ..

upgradeListOfIUpgradeBetPlacementSplitRequestRollback : Party -> [ContractId GamblingBetModel.IUpgradeBetPlacementSplitRequestRollback] -> Script [MigrationResult (ContractId GamblingBetModel.IUpgradeBetPlacementSplitRequestRollback)]
upgradeListOfIUpgradeBetPlacementSplitRequestRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingBetModel.UpgradeBetPlacementSplitRequestRollback with ..

upgradeListOfIUpgradeBetPlacementCancelRequestRollback : Party -> [ContractId GamblingBetModel.IUpgradeBetPlacementCancelRequestRollback] -> Script [MigrationResult (ContractId GamblingBetModel.IUpgradeBetPlacementCancelRequestRollback)]
upgradeListOfIUpgradeBetPlacementCancelRequestRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingBetModel.UpgradeBetPlacementCancelRequestRollback with ..

upgradeListOfIUpgradeFinalizeBetPlacementCancelRequestRollback : Party -> [ContractId GamblingBetModel.IUpgradeFinalizeBetPlacementCancelRequestRollback] -> Script [MigrationResult (ContractId GamblingBetModel.IUpgradeFinalizeBetPlacementCancelRequestRollback)]
upgradeListOfIUpgradeFinalizeBetPlacementCancelRequestRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingBetModel.UpgradeFinalizeBetPlacementCancelRequestRollback with ..

upgradeListOfIUpgradeCancelledBetPlacementRollback : Party -> [ContractId GamblingBetModel.IUpgradeCancelledBetPlacementRollback] -> Script [MigrationResult (ContractId GamblingBetModel.IUpgradeCancelledBetPlacementRollback)]
upgradeListOfIUpgradeCancelledBetPlacementRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingBetModel.UpgradeCancelledBetPlacementRollback with ..

upgradeListOfIUpgradeBetHistoryRollback : Party -> [ContractId GamblingBetModel.IUpgradeBetHistoryRollback] -> Script [MigrationResult (ContractId GamblingBetModel.IUpgradeBetHistoryRollback)]
upgradeListOfIUpgradeBetHistoryRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingBetModel.UpgradeBetHistoryRollback with ..

upgradeListOfIUpgradeBulkBetPlacementRequestRollback : Party -> [ContractId GamblingBetModel.IUpgradeBulkBetPlacementRequestRollback] -> Script [MigrationResult (ContractId GamblingBetModel.IUpgradeBulkBetPlacementRequestRollback)]
upgradeListOfIUpgradeBulkBetPlacementRequestRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingBetModel.UpgradeBulkBetPlacementRequestRollback with ..

upgradeListOfIUpgradeBulkBetPlacementFinalizeRequestRollback : Party -> [ContractId GamblingBetModel.IUpgradeBulkBetPlacementFinalizeRequestRollback] -> Script [MigrationResult (ContractId GamblingBetModel.IUpgradeBulkBetPlacementFinalizeRequestRollback)]
upgradeListOfIUpgradeBulkBetPlacementFinalizeRequestRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingBetModel.UpgradeBulkBetPlacementFinalizeRequestRollback with ..

upgradeListOfIUpgradeBulkBetPlacementRollback : Party -> [ContractId GamblingBetModel.IUpgradeBulkBetPlacementRollback] -> Script [MigrationResult (ContractId GamblingBetModel.IUpgradeBulkBetPlacementRollback)]
upgradeListOfIUpgradeBulkBetPlacementRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingBetModel.UpgradeBulkBetPlacementRollback with ..

upgradeListOfIUpgradeGamblingListingRollback : Party -> [ContractId GamblingListingModel.IUpgradeGamblingListingRollback] -> Script [MigrationResult (ContractId GamblingListingModel.IUpgradeGamblingListingRollback)]
upgradeListOfIUpgradeGamblingListingRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingListingModel.UpgradeGamblingListingRollback with ..

upgradeListOfIUpgradeGamblingUpdateListingRequestRollback : Party -> [ContractId GamblingListingModel.IUpgradeGamblingUpdateListingRequestRollback] -> Script [MigrationResult (ContractId GamblingListingModel.IUpgradeGamblingUpdateListingRequestRollback)]
upgradeListOfIUpgradeGamblingUpdateListingRequestRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingListingModel.UpgradeGamblingUpdateListingRequestRollback with ..

upgradeListOfIUpgradeFailedGamblingUpdateListingRollback : Party -> [ContractId GamblingListingModel.IUpgradeFailedGamblingUpdateListingRollback] -> Script [MigrationResult (ContractId GamblingListingModel.IUpgradeFailedGamblingUpdateListingRollback)]
upgradeListOfIUpgradeFailedGamblingUpdateListingRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingListingModel.UpgradeFailedGamblingUpdateListingRollback with ..

upgradeListOfIUpgradeAccountRollback : Party -> [ContractId GamblingAccountModel.IUpgradeAccountRollback] -> Script [MigrationResult (ContractId GamblingAccountModel.IUpgradeAccountRollback)]
upgradeListOfIUpgradeAccountRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingAccountModel.UpgradeAccountRollback with ..

upgradeListOfIUpgradeWithdrawRequestForApprovalRollback : Party -> [ContractId GamblingAccountModel.IUpgradeWithdrawRequestForApprovalRollback] -> Script [MigrationResult (ContractId GamblingAccountModel.IUpgradeWithdrawRequestForApprovalRollback)]
upgradeListOfIUpgradeWithdrawRequestForApprovalRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingAccountModel.UpgradeWithdrawRequestForApprovalRollback with ..

upgradeListOfIUpgradeDepositRequestForApprovalRollback : Party -> [ContractId GamblingAccountModel.IUpgradeDepositRequestForApprovalRollback] -> Script [MigrationResult (ContractId GamblingAccountModel.IUpgradeDepositRequestForApprovalRollback)]
upgradeListOfIUpgradeDepositRequestForApprovalRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingAccountModel.UpgradeDepositRequestForApprovalRollback with ..

upgradeListOfIUpgradeTransactionHistoryRollback : Party -> [ContractId GamblingAccountModel.IUpgradeTransactionHistoryRollback] -> Script [MigrationResult (ContractId GamblingAccountModel.IUpgradeTransactionHistoryRollback)]
upgradeListOfIUpgradeTransactionHistoryRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingAccountModel.UpgradeTransactionHistoryRollback with ..

upgradeListOfIUpgradePendingTransactionRollback : Party -> [ContractId GamblingAccountModel.IUpgradePendingTransactionRollback] -> Script [MigrationResult (ContractId GamblingAccountModel.IUpgradePendingTransactionRollback)]
upgradeListOfIUpgradePendingTransactionRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingAccountModel.UpgradePendingTransactionRollback with ..

upgradeListOfIUpgradePendingDepositsRollback : Party -> [ContractId GamblingAccountModel.IUpgradePendingDepositsRollback] -> Script [MigrationResult (ContractId GamblingAccountModel.IUpgradePendingDepositsRollback)]
upgradeListOfIUpgradePendingDepositsRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingAccountModel.UpgradePendingDepositsRollback with ..

upgradeListOfIUpgradeFlagRollback : Party -> [ContractId GamblingAccountModel.IUpgradeFlagRollback] -> Script [MigrationResult (ContractId GamblingAccountModel.IUpgradeFlagRollback)]
upgradeListOfIUpgradeFlagRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingAccountModel.UpgradeFlagRollback with ..

upgradeListOfIUpgradeEventInstrumentRequestRollback : Party -> [ContractId GamblingEventModel.IUpgradeEventInstrumentRequestRollback] -> Script [MigrationResult (ContractId GamblingEventModel.IUpgradeEventInstrumentRequestRollback)]
upgradeListOfIUpgradeEventInstrumentRequestRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingEventModel.UpgradeEventInstrumentRequestRollback with ..

upgradeListOfIUpgradeEventInstrumentStatusUpdateRequestRollback : Party -> [ContractId GamblingEventModel.IUpgradeEventInstrumentStatusUpdateRequestRollback] -> Script [MigrationResult (ContractId GamblingEventModel.IUpgradeEventInstrumentStatusUpdateRequestRollback)]
upgradeListOfIUpgradeEventInstrumentStatusUpdateRequestRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingEventModel.UpgradeEventInstrumentStatusUpdateRequestRollback with ..

upgradeListOfIUpgradeEventInstrumentUpdateRequestRollback : Party -> [ContractId GamblingEventModel.IUpgradeEventInstrumentUpdateRequestRollback] -> Script [MigrationResult (ContractId GamblingEventModel.IUpgradeEventInstrumentUpdateRequestRollback)]
upgradeListOfIUpgradeEventInstrumentUpdateRequestRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingEventModel.UpgradeEventInstrumentUpdateRequestRollback with ..

upgradeListOfIUpgradeEventInstrumentRollback : Party -> [ContractId GamblingEventModel.IUpgradeEventInstrumentRollback] -> Script [MigrationResult (ContractId GamblingEventModel.IUpgradeEventInstrumentRollback)]
upgradeListOfIUpgradeEventInstrumentRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingEventModel.UpgradeEventInstrumentRollback with ..

upgradeListOfIUpgradeEventInstrumentToggleFeaturedRequestRollback : Party -> [ContractId GamblingEventModel.IUpgradeEventInstrumentToggleFeaturedRequestRollback] -> Script [MigrationResult (ContractId GamblingEventModel.IUpgradeEventInstrumentToggleFeaturedRequestRollback)]
upgradeListOfIUpgradeEventInstrumentToggleFeaturedRequestRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingEventModel.UpgradeEventInstrumentToggleFeaturedRequestRollback with ..

upgradeListOfIUpgradeEventInstrumentCancelRequestRollback : Party -> [ContractId GamblingEventModel.IUpgradeEventInstrumentCancelRequestRollback] -> Script [MigrationResult (ContractId GamblingEventModel.IUpgradeEventInstrumentCancelRequestRollback)]
upgradeListOfIUpgradeEventInstrumentCancelRequestRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingEventModel.UpgradeEventInstrumentCancelRequestRollback with ..

upgradeListOfIUpgradeEventInstrumentReinstateRequestRollback : Party -> [ContractId GamblingEventModel.IUpgradeEventInstrumentReinstateRequestRollback] -> Script [MigrationResult (ContractId GamblingEventModel.IUpgradeEventInstrumentReinstateRequestRollback)]
upgradeListOfIUpgradeEventInstrumentReinstateRequestRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingEventModel.UpgradeEventInstrumentReinstateRequestRollback with ..

upgradeListOfIUpgradeEventInstrumentUpdateOutcomesOddsRequestRollback : Party -> [ContractId GamblingEventModel.IUpgradeEventInstrumentUpdateOutcomesOddsRequestRollback] -> Script [MigrationResult (ContractId GamblingEventModel.IUpgradeEventInstrumentUpdateOutcomesOddsRequestRollback)]
upgradeListOfIUpgradeEventInstrumentUpdateOutcomesOddsRequestRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingEventModel.UpgradeEventInstrumentUpdateOutcomesOddsRequestRollback with ..

upgradeListOfIUpgradeEventInstrumentNoOutcomesRollback : Party -> [ContractId GamblingEventModel.IUpgradeEventInstrumentNoOutcomesRollback] -> Script [MigrationResult (ContractId GamblingEventModel.IUpgradeEventInstrumentNoOutcomesRollback)]
upgradeListOfIUpgradeEventInstrumentNoOutcomesRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingEventModel.UpgradeEventInstrumentNoOutcomesRollback with ..

upgradeListOfIUpgradeMarketMapRollback : Party -> [ContractId GamblingEventModel.IUpgradeMarketMapRollback] -> Script [MigrationResult (ContractId GamblingEventModel.IUpgradeMarketMapRollback)]
upgradeListOfIUpgradeMarketMapRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingEventModel.UpgradeMarketMapRollback with ..

upgradeListOfGamblingEventServiceIUpgradeServiceRollback : Party -> [ContractId GamblingEventService.IUpgradeServiceRollback] -> Script [MigrationResult (ContractId GamblingEventService.IUpgradeServiceRollback)]
upgradeListOfGamblingEventServiceIUpgradeServiceRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingEventService.UpgradeServiceRollback with ..

upgradeListOfGamblingEventServiceIUpgradeOfferRollback : Party -> [ContractId GamblingEventService.IUpgradeOfferRollback] -> Script [MigrationResult (ContractId GamblingEventService.IUpgradeOfferRollback)]
upgradeListOfGamblingEventServiceIUpgradeOfferRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingEventService.UpgradeOfferRollback with ..

upgradeListOfGamblingEventServiceIUpgradeRequestRollback : Party -> [ContractId GamblingEventService.IUpgradeRequestRollback] -> Script [MigrationResult (ContractId GamblingEventService.IUpgradeRequestRollback)]
upgradeListOfGamblingEventServiceIUpgradeRequestRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid GamblingEventService.UpgradeRequestRollback with ..

upgradeListOfIUpgradeGlobalPromotionsRollback : Party -> [ContractId MarketingModel.IUpgradeGlobalPromotionsRollback] -> Script [MigrationResult (ContractId MarketingModel.IUpgradeGlobalPromotionsRollback)]
upgradeListOfIUpgradeGlobalPromotionsRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid MarketingModel.UpgradeGlobalPromotionsRollback with ..

upgradeListOfIUpgradePromotionRollback : Party -> [ContractId MarketingModel.IUpgradePromotionRollback] -> Script [MigrationResult (ContractId MarketingModel.IUpgradePromotionRollback)]
upgradeListOfIUpgradePromotionRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid MarketingModel.UpgradePromotionRollback with ..

upgradeListOfIUpgradePromotionRequestRollback : Party -> [ContractId MarketingModel.IUpgradePromotionRequestRollback] -> Script [MigrationResult (ContractId MarketingModel.IUpgradePromotionRequestRollback)]
upgradeListOfIUpgradePromotionRequestRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid MarketingModel.UpgradePromotionRequestRollback with ..

upgradeListOfIUpgradePromotionUpdateRequestRollback : Party -> [ContractId MarketingModel.IUpgradePromotionUpdateRequestRollback] -> Script [MigrationResult (ContractId MarketingModel.IUpgradePromotionUpdateRequestRollback)]
upgradeListOfIUpgradePromotionUpdateRequestRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid MarketingModel.UpgradePromotionUpdateRequestRollback with ..

upgradeListOfIUpgradePromotionWalletRollback : Party -> [ContractId MarketingModel.IUpgradePromotionWalletRollback] -> Script [MigrationResult (ContractId MarketingModel.IUpgradePromotionWalletRollback)]
upgradeListOfIUpgradePromotionWalletRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid MarketingModel.UpgradePromotionWalletRollback with ..

upgradeListOfIUpgradePromotionUsageHistoryRollback : Party -> [ContractId MarketingModel.IUpgradePromotionUsageHistoryRollback] -> Script [MigrationResult (ContractId MarketingModel.IUpgradePromotionUsageHistoryRollback)]
upgradeListOfIUpgradePromotionUsageHistoryRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid MarketingModel.UpgradePromotionUsageHistoryRollback with ..

upgradeListOfMarketingServiceIUpgradeServiceRollback : Party -> [ContractId MarketingService.IUpgradeServiceRollback] -> Script [MigrationResult (ContractId MarketingService.IUpgradeServiceRollback)]
upgradeListOfMarketingServiceIUpgradeServiceRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid MarketingService.UpgradeServiceRollback with ..

upgradeListOfMarketingServiceIUpgradeOfferRollback : Party -> [ContractId MarketingService.IUpgradeOfferRollback] -> Script [MigrationResult (ContractId MarketingService.IUpgradeOfferRollback)]
upgradeListOfMarketingServiceIUpgradeOfferRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid MarketingService.UpgradeOfferRollback with ..

upgradeListOfMarketingServiceIUpgradeRequestRollback : Party -> [ContractId MarketingService.IUpgradeRequestRollback] -> Script [MigrationResult (ContractId MarketingService.IUpgradeRequestRollback)]
upgradeListOfMarketingServiceIUpgradeRequestRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid MarketingService.UpgradeRequestRollback with ..

upgradeListOfIUpgradeCounterRollback : Party -> [ContractId CounterModel.IUpgradeCounterRollback] -> Script [MigrationResult (ContractId CounterModel.IUpgradeCounterRollback)]
upgradeListOfIUpgradeCounterRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid CounterModel.UpgradeCounterRollback with ..

upgradeListOfOperatorRoleIUpgradeRoleRollback : Party -> [ContractId OperatorRole.IUpgradeRoleRollback] -> Script [MigrationResult (ContractId OperatorRole.IUpgradeRoleRollback)]
upgradeListOfOperatorRoleIUpgradeRoleRollback migratingParty cids = forA cids
    \cid -> submit migratingParty do exerciseCmd cid OperatorRole.UpgradeRoleRollback with ..
