module Gambyl.Migration.Script.UpgradeScript where

import Daml.Script

import Gambyl.Migration.Script.ScriptFunctions

import Gambyl.Migration.Common

import Gambyl.Test.TestUtils

upgradeScript : Input -> Script ReportSummary
upgradeScript Input{gambyl} = script do

  debug "START Upgrading ledger"

  r01 <- collectReport "GlobalGamblingConfiguration"                       $ migrateDirectly "r01-IUpgradeGlobalGamblingConfiguration" gambyl upgradeListOfIUpgradeGlobalGamblingConfiguration
  r02 <- collectReport "ActionFailure"                                    $ migrateDirectly "r02-IUpgradeActionFailure" gambyl upgradeListOfIUpgradeActionFailure
  r03 <- collectReport "ActionSuccess"                                    $ migrateDirectly "r03-IUpgradeActionSuccess" gambyl upgradeListOfIUpgradeActionSuccess
  r04 <- collectReport "GamblingRole.Role"                                $ migrateDirectly "r04-GamblingRole.IUpgradeRole" gambyl upgradeListOfGamblingRoleIUpgradeRole
  r05 <- collectReport "GamblingRole.Offer"                               $ migrateDirectly "r05-GamblingRole.IUpgradeOffer" gambyl upgradeListOfGamblingRoleIUpgradeOffer
  r06 <- collectReport "GamblingRole.Request"                             $ migrateDirectly "r06-GamblingRole.IUpgradeRequest" gambyl upgradeListOfGamblingRoleIUpgradeRequest
  r07 <- collectReport "BetPlacementRequestFlag"                          $ migrateDirectly "r07-IUpgradeBetPlacementRequestFlag" gambyl upgradeListOfIUpgradeBetPlacementRequestFlag
  r08 <- collectReport "BulkBetPlacementRequestFlag"                      $ migrateDirectly "r08-IUpgradeBulkBetPlacementRequestFlag" gambyl upgradeListOfIUpgradeBulkBetPlacementRequestFlag
  r09 <- collectReport "BetPlacement"                                     $ migrateDirectly "r09-IUpgradeBetPlacement" gambyl upgradeListOfIUpgradeBetPlacement

  r10 <- collectReport "BetPlacementSplitRequest"                         $ migrateDirectly "r10-IUpgradeBetPlacementSplitRequest" gambyl upgradeListOfIUpgradeBetPlacementSplitRequest
  r11 <- collectReport "BetPlacementCancelRequest"                        $ migrateDirectly "r11-IUpgradeBetPlacementCancelRequest" gambyl upgradeListOfIUpgradeBetPlacementCancelRequest
  r12 <- collectReport "FinalizeBetPlacementCancelRequest"                $ migrateDirectly "r12-IUpgradeFinalizeBetPlacementCancelRequest" gambyl upgradeListOfIUpgradeFinalizeBetPlacementCancelRequest
  r13 <- collectReport "BetHistory"                                       $ migrateDirectly "r13-IUpgradeBetHistory" gambyl upgradeListOfIUpgradeBetHistory
  -- the order is important for this case
  r14 <- collectReport "GamblingBetModel.BulkBetPlacementRequest"         $ migrateDirectly "r14-GamblingBetModel.IUpgradeBulkBetPlacementRequest" gambyl upgradeListOfIUpgradeBulkBetPlacementRequest
  r15 <- collectReport "BetPlacementRequest"                              $ migrateDirectly "r15-IUpgradeBetPlacementRequest" gambyl upgradeListOfIUpgradeBetPlacementRequest
  -- the order is important for this case
  r16 <- collectReport "GamblingBetModel.BulkBetPlacementFinalizeRequest" $ migrateDirectly "r16-GamblingBetModel.IUpgradeBulkBetPlacementFinalizeRequest" gambyl upgradeListOfIUpgradeBulkBetPlacementFinalizeRequest
  r17 <- collectReport "BetPlacementFinalizeRequest"                      $ migrateDirectly "r17-IUpgradeBetPlacementFinalizeRequest" gambyl upgradeListOfIUpgradeBetPlacementFinalizeRequest
  r18 <- collectReport "BulkBetPlacement"                                 $ migrateDirectly "r18-IUpgradeBulkBetPlacement" gambyl upgradeListOfIUpgradeBulkBetPlacement
  r19 <- collectReport "GamblingListing"                                  $ migrateDirectly "r19-IUpgradeGamblingListing" gambyl upgradeListOfIUpgradeGamblingListing

  r20 <- collectReport "GamblingUpdateListingRequest"                     $ migrateDirectly "r20-IUpgradeGamblingUpdateListingRequest" gambyl upgradeListOfIUpgradeGamblingUpdateListingRequest
  r21 <- collectReport "FailedGamblingUpdateListing"                      $ migrateDirectly "r21-IUpgradeFailedGamblingUpdateListing" gambyl upgradeListOfIUpgradeFailedGamblingUpdateListing
  r22 <- collectReport "Account"                                          $ migrateDirectly "r22-IUpgradeAccount" gambyl upgradeListOfIUpgradeAccount
  r23 <- collectReport "WithdrawRequestForApproval"                       $ migrateDirectly "r23-IUpgradeWithdrawRequestForApproval" gambyl upgradeListOfIUpgradeWithdrawRequestForApproval
  r24 <- collectReport "DepositRequestForApproval"                        $ migrateDirectly "r24-IUpgradeDepositRequestForApproval" gambyl upgradeListOfIUpgradeDepositRequestForApproval
  r25 <- collectReport "TransactionHistory"                               $ migrateDirectly "r25-IUpgradeTransactionHistory" gambyl upgradeListOfIUpgradeTransactionHistory
  r26 <- collectReport "PendingTransaction"                               $ migrateDirectly "r26-IUpgradePendingTransaction" gambyl upgradeListOfIUpgradePendingTransaction
  r27 <- collectReport "PendingDeposits"                                  $ migrateDirectly "r27-IUpgradePendingDeposits" gambyl upgradeListOfIUpgradePendingDeposits
  r28 <- collectReport "Flag"                                             $ migrateDirectly "r28-IUpgradeFlag" gambyl upgradeListOfIUpgradeFlag
  r29 <- collectReport "EventInstrumentRequest"                           $ migrateDirectly "r29-IUpgradeEventInstrumentRequest" gambyl upgradeListOfIUpgradeEventInstrumentRequest

  r30 <- collectReport "EventInstrumentStatusUpdateRequest"               $ migrateDirectly "r30-IUpgradeEventInstrumentStatusUpdateRequest" gambyl upgradeListOfIUpgradeEventInstrumentStatusUpdateRequest
  r31 <- collectReport "EventInstrumentUpdateRequest"                     $ migrateDirectly "r31-IUpgradeEventInstrumentUpdateRequest" gambyl upgradeListOfIUpgradeEventInstrumentUpdateRequest
  r32 <- collectReport "EventInstrumentToggleFeaturedRequest"             $ migrateDirectly "r32-IUpgradeEventInstrumentToggleFeaturedRequest" gambyl upgradeListOfIUpgradeEventInstrumentToggleFeaturedRequest
  r33 <- collectReport "EventInstrumentCancelRequest"                     $ migrateDirectly "r33-IUpgradeEventInstrumentCancelRequest" gambyl upgradeListOfIUpgradeEventInstrumentCancelRequest
  r34 <- collectReport "EventInstrumentReinstateRequest"                  $ migrateDirectly "r34-IUpgradeEventInstrumentReinstateRequest" gambyl upgradeListOfIUpgradeEventInstrumentReinstateRequest
  r35 <- collectReport "EventInstrumentUpdateOutcomesOddsRequest"         $ migrateDirectly "r35-IUpgradeEventInstrumentUpdateOutcomesOddsRequest" gambyl upgradeListOfIUpgradeEventInstrumentUpdateOutcomesOddsRequest
  r36 <- collectReport "EventInstrumentNoOutcomes"                        $ migrateDirectly "r36-IUpgradeEventInstrumentNoOutcomes" gambyl upgradeListOfIUpgradeEventInstrumentNoOutcomes
  r37 <- collectReport "EventInstrument"                                  $ migrateDirectly "r37-IUpgradeEventInstrument" gambyl upgradeListOfIUpgradeEventInstrument
  r38 <- collectReport "MarketMap"                                        $ migrateDirectly "r38-IUpgradeMarketMap" gambyl upgradeListOfIUpgradeMarketMap
  r39 <- collectReport "GamblingService.Service"                          $ migrateDirectly "r39-GamblingService.IUpgradeService" gambyl upgradeListOfGamblingServiceIUpgradeService

  r40 <- collectReport "GamblingService.Offer"                            $ migrateDirectly "r40-GamblingService.IUpgradeOffer" gambyl upgradeListOfGamblingServiceIUpgradeOffer
  -- NOTE: this is a customer contract and does not need to be migrated
  r41 <- collectReport "GamblingService.Request"                          $ migrateDirectly "r41-GamblingService.IUpgradeRequest" gambyl upgradeListOfGamblingServiceIUpgradeRequest
  r42 <- collectReport "BlockedService"                                   $ migrateDirectly "r42-IUpgradeBlockedService" gambyl upgradeListOfIUpgradeBlockedService
  r43 <- collectReport "PendingPromotionApplication"                      $ migrateDirectly "r43-IUpgradePendingPromotionApplication" gambyl upgradeListOfIUpgradePendingPromotionApplication
  r44 <- collectReport "PendingIdentity"                                  $ migrateDirectly "r44-IUpgradePendingIdentity" gambyl upgradeListOfIUpgradePendingIdentity
  r45 <- collectReport "GamblerUnverifiedIdentityRequest"                  $ migrateDirectly "r45-IUpgradeGamblerUnverifiedIdentityRequest" gambyl upgradeListOfIUpgradeGamblerUnverifiedIdentityRequest
  r46 <- collectReport "GamblerUnverifiedIdentity"                         $ migrateDirectly "r46-IUpgradeGamblerUnverifiedIdentity" gambyl upgradeListOfIUpgradeGamblerUnverifiedIdentity
  r47 <- collectReport "GamblerIdentity"                                  $ migrateDirectly "r47-IUpgradeGamblerIdentity" gambyl upgradeListOfIUpgradeGamblerIdentity
  r48 <- collectReport "RejectedIdentity"                                 $ migrateDirectly "r48-IUpgradeRejectedIdentity" gambyl upgradeListOfIUpgradeRejectedIdentity
  r49 <- collectReport "ExecutedBets"                                     $ migrateDirectly "r49-IUpgradeExecutedBets" gambyl upgradeListOfIUpgradeExecutedBets
  r50 <- collectReport "GamblingEventService.Service"                     $ migrateDirectly "r50-GamblingEventService.IUpgradeService" gambyl upgradeListOfGamblingEventServiceIUpgradeService

  r51 <- collectReport "GamblingEventService.Offer"                       $ migrateDirectly "r51-GamblingEventService.IUpgradeOffer" gambyl upgradeListOfGamblingEventServiceIUpgradeOffer
  -- NOTE: this is a customer contract and does not need to be migrated
  r52 <- collectReport "GamblingEventService.Request"                     $ migrateDirectly "r52-GamblingEventService.IUpgradeRequest" gambyl upgradeListOfGamblingEventServiceIUpgradeRequest
  r53 <- collectReport "GlobalPromotions"                                 $ migrateDirectly "r53-IUpgradeGlobalPromotions" gambyl upgradeListOfIUpgradeGlobalPromotions
  r54 <- collectReport "Promotion"                                        $ migrateDirectly "r54-IUpgradePromotion" gambyl upgradeListOfIUpgradePromotion
  r55 <- collectReport "PromotionRequest"                                 $ migrateDirectly "r55-IUpgradePromotionRequest" gambyl upgradeListOfIUpgradePromotionRequest
  r56 <- collectReport "PromotionUpdateRequest"                           $ migrateDirectly "r56-IUpgradePromotionUpdateRequest" gambyl upgradeListOfIUpgradePromotionUpdateRequest
  r57 <- collectReport "PromotionWallet"                                  $ migrateDirectly "r57-IUpgradePromotionWallet" gambyl upgradeListOfIUpgradePromotionWallet
  r58 <- collectReport "PromotionUsageHistory"                            $ migrateDirectly "r58-IUpgradePromotionUsageHistory" gambyl upgradeListOfIUpgradePromotionUsageHistory
  r59 <- collectReport "MarketingService.Service"                         $ migrateDirectly "r59-MarketingService.IUpgradeService" gambyl upgradeListOfMarketingServiceIUpgradeService

  r60 <- collectReport "MarketingService.Offer"                           $ migrateDirectly "r60-MarketingService.IUpgradeOffer" gambyl upgradeListOfMarketingServiceIUpgradeOffer
  -- NOTE: this is a customer contract and does not need to be migrated
  r61 <- collectReport "MarketingService.Request"                         $ migrateDirectly "r61-MarketingService.IUpgradeRequest" gambyl upgradeListOfMarketingServiceIUpgradeRequest
  r62 <- collectReport "Counter"                                          $ migrateDirectly "r62-IUpgradeCounter" gambyl upgradeListOfIUpgradeCounter
  r63 <- collectReport "OperatorRole.Role"                                $ migrateDirectly "r63-OperatorRole.IUpgradeRole" gambyl upgradeListOfOperatorRoleIUpgradeRole
  r64 <- collectReport "CancelledBetPlacement"                            $ migrateDirectly "r64-IUpgradeCancelledBetPlacement" gambyl upgradeListOfIUpgradeCancelledBetPlacement

  debug "FINISH Upgrading ledger"

  let report = foldl combineReport (ReportSummary 0 0 0 0 []) [
          r01, r02, r03, r04, r05, r06, r07, r08, r09
          , r10, r11, r12, r13, r14, r15, r16, r17, r18, r19
          , r20, r21, r22, r23, r24, r25, r26, r27, r28, r29
          , r30, r31, r32, r33, r34, r35, r36, r37, r38, r39
          , r40, r41, r42, r43, r44, r45, r46, r47, r48, r49
          , r50, r51, r52, r53, r54, r55, r56, r57, r58, r59
          , r60, r61, r62, r63, r64
        ]
  pure report
