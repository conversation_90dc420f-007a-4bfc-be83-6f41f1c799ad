# for config file options, refer to
# https://docs.daml.com/tools/assistant.html#project-config-file-daml-yaml

sdk-version: 2.9.4
name: gambyl-enetpulse-migration
source: daml
init-script:
parties:
  - Operator
version: 1.0.0
dependencies:
  - daml-prim
  - daml-stdlib
  - daml-script
data-dependencies:
  # External Dependencies
  - ../../../../ext-pkg/integrations/enetpulse/${GAMBYL_ENETPULSE_IMPL_LEGACY_VERSION}/daml/enetpulse-integration-model-${GAMBYL_ENETPULSE_IMPL_LEGACY_VERSION}.dar
  # Local Dependencies
  - ../../../../build/migration/gambyl-migration-utils-${GAMBYL_MIGRATION_UTILS_VERSION}.dar
  - ../../../../build/implementation/gambyl-enetpulse-impl-${GAMBYL_ENETPULSE_IMPL_CURRENT_VERSION}.dar
build-options:
  - --ghc-option=-Wall
  - --ghc-option=-Wno-name-shadowing
  - --ghc-option=-fno-warn-orphans
  - --ghc-option=-Wno-unused-do-bind
  - --ghc-option=-Werror
  - --output=../../../../build/migration/gambyl-enetpulse-v${GAMBYL_ENETPULSE_IMPL_LEGACY_VERSION}-v${GAMBYL_ENETPULSE_IMPL_CURRENT_VERSION}-migration-1.0.0.dar
module-prefixes:
  enetpulse-integration-model-${GAMBYL_ENETPULSE_IMPL_LEGACY_VERSION}: Legacy
  gambyl-enetpulse-impl-${GAMBYL_ENETPULSE_IMPL_CURRENT_VERSION}: Current