module Gambyl.Migration.Script.RollbackScriptFunctions where

import Daml.Script
import Gambyl.Migration.Common

import Gambyl.Migration.Interface.EnetPulse.ConfigurationRollback qualified as Configuration
import Gambyl.Migration.Interface.EnetPulse.EventsRollback qualified as Events
import Gambyl.Migration.Interface.EnetPulse.StaticRollback qualified as Static

upgradeListOfIUpgradeEventInstrumentUpdateRollback : Party -> [ContractId Events.IUpgradeEventInstrumentUpdateRollback] -> Script [MigrationResult (ContractId Events.IUpgradeEventInstrumentUpdateRollback)]
upgradeListOfIUpgradeEventInstrumentUpdateRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Events.UpgradeEventInstrumentUpdateRollback with ..

upgradeListOfIUpgradeEventInstrumentRollback : Party -> [ContractId Events.IUpgradeEventInstrumentRollback] -> Script [MigrationResult (ContractId Events.IUpgradeEventInstrumentRollback)]
upgradeListOfIUpgradeEventInstrumentRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Events.UpgradeEventInstrumentRollback with ..

upgradeListOfIUpgradeEventInstrumentUpdateOnHoldRollback : Party -> [ContractId Events.IUpgradeEventInstrumentUpdateOnHoldRollback] -> Script [MigrationResult (ContractId Events.IUpgradeEventInstrumentUpdateOnHoldRollback)]
upgradeListOfIUpgradeEventInstrumentUpdateOnHoldRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Events.UpgradeEventInstrumentUpdateOnHoldRollback with ..

upgradeListOfIUpgradeFailedEventInstrumentRollback : Party -> [ContractId Events.IUpgradeFailedEventInstrumentRollback] -> Script [MigrationResult (ContractId Events.IUpgradeFailedEventInstrumentRollback)]
upgradeListOfIUpgradeFailedEventInstrumentRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Events.UpgradeFailedEventInstrumentRollback with ..

upgradeListOfIUpgradeOutcomesRollback : Party -> [ContractId Static.IUpgradeOutcomesRollback] -> Script [MigrationResult (ContractId Static.IUpgradeOutcomesRollback)]
upgradeListOfIUpgradeOutcomesRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Static.UpgradeOutcomesRollback with ..

upgradeListOfIUpgradeStatusesRollback : Party -> [ContractId Static.IUpgradeStatusesRollback] -> Script [MigrationResult (ContractId Static.IUpgradeStatusesRollback)]
upgradeListOfIUpgradeStatusesRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Static.UpgradeStatusesRollback with ..

upgradeListOfIUpgradeResultsRollback : Party -> [ContractId Static.IUpgradeResultsRollback] -> Script [MigrationResult (ContractId Static.IUpgradeResultsRollback)]
upgradeListOfIUpgradeResultsRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Static.UpgradeResultsRollback with ..

upgradeListOfIUpgradeSportTranslationsRollback : Party -> [ContractId Static.IUpgradeSportTranslationsRollback] -> Script [MigrationResult (ContractId Static.IUpgradeSportTranslationsRollback)]
upgradeListOfIUpgradeSportTranslationsRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Static.UpgradeSportTranslationsRollback with ..

upgradeListOfIUpgradeConfigurationRollback : Party -> [ContractId Configuration.IUpgradeConfigurationRollback] -> Script [MigrationResult (ContractId Configuration.IUpgradeConfigurationRollback)]
upgradeListOfIUpgradeConfigurationRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Configuration.UpgradeConfigurationRollback with ..
