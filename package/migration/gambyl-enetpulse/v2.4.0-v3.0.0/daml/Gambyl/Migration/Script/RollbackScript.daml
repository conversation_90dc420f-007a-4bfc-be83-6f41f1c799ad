module Gambyl.Migration.Script.RollbackScript where

import Daml.Script

import Gambyl.Migration.Common
import Gambyl.Test.TestUtils

import Gambyl.Migration.Script.RollbackScriptFunctions

rollbackScript : Input -> Script ReportSummary
rollbackScript Input{enetpulse} = script do

  debug "START RollingBack Enetpulse"

  r1 <- collectReport "EventInstrumentRollback" $ migrateDirectly "1-IUpgradeEventInstrumentRollback" enetpulse upgradeListOfIUpgradeEventInstrumentRollback

  -- the order is important for this case
  r2 <- collectReport "EventInstrumentUpdateOnHoldToRollback" $ migrateDirectly "2-IUpgradeEventInstrumentUpdateOnHoldToRollback" enetpulse upgradeListOfIUpgradeEventInstrumentUpdateOnHoldRollback
  r3 <- collectReport "EventInstrumentUpdateRollback" $ migrateDirectly "0-IUpgradeEventInstrumentUpdateRollback" enetpulse upgradeListOfIUpgradeEventInstrumentUpdateRollback

  r4 <- collectReport "FailedEventInstrumentRollback" $ migrateDirectly "3-IUpgradeFailedEventInstrumentRollback" enetpulse upgradeListOfIUpgradeFailedEventInstrumentRollback
  r5 <- collectReport "OutcomesRollback" $ migrateDirectly "4-IUpgradeOutcomesRollback" enetpulse upgradeListOfIUpgradeOutcomesRollback
  r6 <- collectReport "StatusesRollback" $ migrateDirectly "5-IUpgradeStatusesRollback" enetpulse upgradeListOfIUpgradeStatusesRollback
  r7 <- collectReport "ResultsRollback" $ migrateDirectly "6-IUpgradeResultsRollback" enetpulse upgradeListOfIUpgradeResultsRollback
  r8 <- collectReport "SportTranslationsRollback" $ migrateDirectly "7-IUpgradeSportTranslationsRollback" enetpulse upgradeListOfIUpgradeSportTranslationsRollback
  r9 <- collectReport "ConfigurationRollback" $ migrateDirectly "8-IUpgradeConfigurationRollback" enetpulse upgradeListOfIUpgradeConfigurationRollback

  let report = foldl combineReport (ReportSummary 0 0 0 0 []) [r1, r2, r3, r4, r5, r6, r7, r8, r9]

  debug "FINISH RollingBack Enetpulse"

  pure report
