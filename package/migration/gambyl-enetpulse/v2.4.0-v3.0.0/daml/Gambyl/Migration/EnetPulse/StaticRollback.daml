module Gambyl.Migration.EnetPulse.StaticRollback where

import Gambyl.Migration.Upgrade

import Gambyl.Migration.EnetPulse.EventsRollback ()

import Legacy.EnetPulseIntegration.Static qualified as LegacyStatic
import Current.EnetPulseIntegration.Static qualified as CurrentStatic

instance DAMLUpgrade CurrentStatic.Outcomes LegacyStatic.Outcomes where
  convert CurrentStatic.Outcomes{..} = LegacyStatic.Outcomes with
    outcomeMap = convert outcomeMap
    ..

instance DAMLUpgrade CurrentStatic.Statuses LegacyStatic.Statuses where
  convert CurrentStatic.Statuses{..} = LegacyStatic.Statuses with
    statusMap = convert statusMap
    ..

instance DAMLUpgrade CurrentStatic.Results LegacyStatic.Results where
  convert CurrentStatic.Results{..} = LegacyStatic.Results with
    resultsMap = convert resultsMap
    ..

instance DAMLUpgrade CurrentStatic.SportTranslations LegacyStatic.SportTranslations where
  convert CurrentStatic.SportTranslations{..} = LegacyStatic.SportTranslations with
    ..
