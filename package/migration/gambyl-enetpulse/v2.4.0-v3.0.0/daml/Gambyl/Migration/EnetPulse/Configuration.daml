module Gambyl.Migration.EnetPulse.Configuration where

import Gambyl.Migration.Upgrade

import Legacy.EnetPulseIntegration.Configuration qualified as LegacyConfiguration
import Current.EnetPulseIntegration.Configuration qualified as CurrentConfiguration

instance DAMLUpgrade LegacyConfiguration.Configuration CurrentConfiguration.Configuration where
  convert LegacyConfiguration.Configuration{..} = CurrentConfiguration.Configuration with
        ..
