module Gambyl.Migration.EnetPulse.Static where

import Gambyl.Migration.Upgrade

import Gambyl.Migration.EnetPulse.Events ()

import Legacy.EnetPulseIntegration.Static qualified as LegacyStatic
import Current.EnetPulseIntegration.Static qualified as CurrentStatic

instance DAMLUpgrade LegacyStatic.Outcomes CurrentStatic.Outcomes where
  convert LegacyStatic.Outcomes{..} = CurrentStatic.Outcomes with
    outcomeMap = convert outcomeMap
    ..

instance DAMLUpgrade LegacyStatic.Statuses CurrentStatic.Statuses where
  convert LegacyStatic.Statuses{..} = CurrentStatic.Statuses with
    statusMap = convert statusMap
    ..

instance DAMLUpgrade LegacyStatic.Results CurrentStatic.Results where
  convert LegacyStatic.Results{..} = CurrentStatic.Results with
    resultsMap = convert resultsMap
    ..

instance DAMLUpgrade LegacyStatic.SportTranslations CurrentStatic.SportTranslations where
  convert LegacyStatic.SportTranslations{..} = CurrentStatic.SportTranslations with
    ..
