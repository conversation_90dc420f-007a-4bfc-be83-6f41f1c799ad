module Gambyl.Migration.Interface.EnetPulse.Static where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.EnetPulse.Static  ()

import Legacy.EnetPulseIntegration.Static qualified as LegacyStatic
import Current.EnetPulseIntegration.Static qualified as CurrentStatic

interface IUpgradeOutcomes where
  viewtype CurrentStatic.Outcomes

  upgradeOutcomes : ContractId IUpgradeOutcomes -> Update (ContractId IUpgradeOutcomes)

  nonconsuming choice UpgradeOutcomes : MigrationResult (ContractId IUpgradeOutcomes)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeOutcomes contract") $
          migratingParty == (view this).integrationParty
        upgradeOutcomes this self

  interface instance IUpgradeOutcomes for LegacyStatic.Outcomes where
    view = convert this
    upgradeOutcomes self = do
      archive self
      toInterfaceContractId @IUpgradeOutcomes <$> create (convert this : CurrentStatic.Outcomes)

  interface instance IUpgradeOutcomes for CurrentStatic.Outcomes where
    view = this
    upgradeOutcomes = pure

interface IUpgradeStatuses where
  viewtype CurrentStatic.Statuses

  upgradeStatuses : ContractId IUpgradeStatuses -> Update (ContractId IUpgradeStatuses)

  nonconsuming choice UpgradeStatuses : MigrationResult (ContractId IUpgradeStatuses)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeStatuses contract") $
          migratingParty == (view this).integrationParty
        upgradeStatuses this self

  interface instance IUpgradeStatuses for LegacyStatic.Statuses where
    view = convert this
    upgradeStatuses self = do
      archive self
      toInterfaceContractId @IUpgradeStatuses <$> create (convert this : CurrentStatic.Statuses)

  interface instance IUpgradeStatuses for CurrentStatic.Statuses where
    view = this
    upgradeStatuses = pure

interface IUpgradeResults where
  viewtype CurrentStatic.Results

  upgradeResults : ContractId IUpgradeResults -> Update (ContractId IUpgradeResults)

  nonconsuming choice UpgradeResults : MigrationResult (ContractId IUpgradeResults)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeResults contract") $
          migratingParty == (view this).integrationParty
        upgradeResults this self

  interface instance IUpgradeResults for LegacyStatic.Results where
    view = convert this
    upgradeResults self = do
      archive self
      toInterfaceContractId @IUpgradeResults <$> create (convert this : CurrentStatic.Results)

  interface instance IUpgradeResults for CurrentStatic.Results where
    view = this
    upgradeResults = pure

interface IUpgradeSportTranslations where
  viewtype CurrentStatic.SportTranslations

  upgradeSportTranslations : ContractId IUpgradeSportTranslations -> Update (ContractId IUpgradeSportTranslations)

  nonconsuming choice UpgradeSportTranslations : MigrationResult (ContractId IUpgradeSportTranslations)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeSportTranslations contract") $
          migratingParty == (view this).integrationParty
        upgradeSportTranslations this self

  interface instance IUpgradeSportTranslations for LegacyStatic.SportTranslations where
    view = convert this
    upgradeSportTranslations self = do
      archive self
      toInterfaceContractId @IUpgradeSportTranslations <$> create (convert this : CurrentStatic.SportTranslations)

  interface instance IUpgradeSportTranslations for CurrentStatic.SportTranslations where
    view = this
    upgradeSportTranslations = pure
