module Gambyl.Migration.Interface.EnetPulse.ConfigurationRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.EnetPulse.ConfigurationRollback ()

import Legacy.EnetPulseIntegration.Configuration qualified as LegacyConfiguration
import Current.EnetPulseIntegration.Configuration qualified as CurrentConfiguration

interface IUpgradeConfigurationRollback where
  viewtype LegacyConfiguration.Configuration

  upgradeConfigurationRollback : ContractId IUpgradeConfigurationRollback -> Update (ContractId IUpgradeConfigurationRollback)

  nonconsuming choice UpgradeConfigurationRollback : MigrationResult (ContractId IUpgradeConfigurationRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeConfigurationRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeConfigurationRollback this self

  interface instance IUpgradeConfigurationRollback for CurrentConfiguration.Configuration where
    view = convert this
    upgradeConfigurationRollback self = do
      archive self
      toInterfaceContractId @IUpgradeConfigurationRollback <$> create (convert this : LegacyConfiguration.Configuration)

  interface instance IUpgradeConfigurationRollback for LegacyConfiguration.Configuration where
    view = this
    upgradeConfigurationRollback = pure
