module Gambyl.Migration.Interface.EnetPulse.StaticRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.EnetPulse.StaticRollback  ()

import Legacy.EnetPulseIntegration.Static qualified as LegacyStatic
import Current.EnetPulseIntegration.Static qualified as CurrentStatic

interface IUpgradeOutcomesRollback where
  viewtype LegacyStatic.Outcomes

  upgradeOutcomesRollback : ContractId IUpgradeOutcomesRollback -> Update (ContractId IUpgradeOutcomesRollback)

  nonconsuming choice UpgradeOutcomesRollback : MigrationResult (ContractId IUpgradeOutcomesRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeOutcomesRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeOutcomesRollback this self

  interface instance IUpgradeOutcomesRollback for CurrentStatic.Outcomes where
    view = convert this
    upgradeOutcomesRollback self = do
      archive self
      toInterfaceContractId @IUpgradeOutcomesRollback <$> create (convert this : LegacyStatic.Outcomes)

  interface instance IUpgradeOutcomesRollback for LegacyStatic.Outcomes where
    view = this
    upgradeOutcomesRollback = pure

interface IUpgradeStatusesRollback where
  viewtype LegacyStatic.Statuses

  upgradeStatusesRollback : ContractId IUpgradeStatusesRollback -> Update (ContractId IUpgradeStatusesRollback)

  nonconsuming choice UpgradeStatusesRollback : MigrationResult (ContractId IUpgradeStatusesRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeStatusesRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeStatusesRollback this self

  interface instance IUpgradeStatusesRollback for CurrentStatic.Statuses where
    view = convert this
    upgradeStatusesRollback self = do
      archive self
      toInterfaceContractId @IUpgradeStatusesRollback <$> create (convert this : LegacyStatic.Statuses)

  interface instance IUpgradeStatusesRollback for LegacyStatic.Statuses where
    view = this
    upgradeStatusesRollback = pure

interface IUpgradeResultsRollback where
  viewtype LegacyStatic.Results

  upgradeResultsRollback : ContractId IUpgradeResultsRollback -> Update (ContractId IUpgradeResultsRollback)

  nonconsuming choice UpgradeResultsRollback : MigrationResult (ContractId IUpgradeResultsRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeResultsRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeResultsRollback this self

  interface instance IUpgradeResultsRollback for CurrentStatic.Results where
    view = convert this
    upgradeResultsRollback self = do
      archive self
      toInterfaceContractId @IUpgradeResultsRollback <$> create (convert this : LegacyStatic.Results)

  interface instance IUpgradeResultsRollback for LegacyStatic.Results where
    view = this
    upgradeResultsRollback = pure

interface IUpgradeSportTranslationsRollback where
  viewtype LegacyStatic.SportTranslations

  upgradeSportTranslationsRollback : ContractId IUpgradeSportTranslationsRollback -> Update (ContractId IUpgradeSportTranslationsRollback)

  nonconsuming choice UpgradeSportTranslationsRollback : MigrationResult (ContractId IUpgradeSportTranslationsRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeSportTranslationsRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeSportTranslationsRollback this self

  interface instance IUpgradeSportTranslationsRollback for CurrentStatic.SportTranslations where
    view = convert this
    upgradeSportTranslationsRollback self = do
      archive self
      toInterfaceContractId @IUpgradeSportTranslationsRollback <$> create (convert this : LegacyStatic.SportTranslations)

  interface instance IUpgradeSportTranslationsRollback for LegacyStatic.SportTranslations where
    view = this
    upgradeSportTranslationsRollback = pure
