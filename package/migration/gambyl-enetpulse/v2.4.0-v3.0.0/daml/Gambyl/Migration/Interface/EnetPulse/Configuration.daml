module Gambyl.Migration.Interface.EnetPulse.Configuration where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.EnetPulse.Configuration ()

import Legacy.EnetPulseIntegration.Configuration qualified as LegacyConfiguration
import Current.EnetPulseIntegration.Configuration qualified as CurrentConfiguration

interface IUpgradeConfiguration where
  viewtype CurrentConfiguration.Configuration

  upgradeConfiguration : ContractId IUpgradeConfiguration -> Update (ContractId IUpgradeConfiguration)

  nonconsuming choice UpgradeConfiguration : MigrationResult (ContractId IUpgradeConfiguration)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeConfiguration contract") $
          migratingParty == (view this).integrationParty
        upgradeConfiguration this self

  interface instance IUpgradeConfiguration for LegacyConfiguration.Configuration where
    view = convert this
    upgradeConfiguration self = do
      archive self
      toInterfaceContractId @IUpgradeConfiguration <$> create (convert this : CurrentConfiguration.Configuration)

  interface instance IUpgradeConfiguration for CurrentConfiguration.Configuration where
    view = this
    upgradeConfiguration = pure
