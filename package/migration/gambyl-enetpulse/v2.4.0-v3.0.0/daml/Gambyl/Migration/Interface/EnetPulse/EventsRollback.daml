module Gambyl.Migration.Interface.EnetPulse.EventsRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.EnetPulse.EventsRollback  ()

import Legacy.EnetPulseIntegration.Events qualified as LegacyEvents
import Current.EnetPulseIntegration.Events qualified as CurrentEvents

interface IUpgradeEventInstrumentUpdateRollback where
  viewtype LegacyEvents.EventInstrumentUpdate

  upgradeEventInstrumentUpdateRollback : ContractId IUpgradeEventInstrumentUpdateRollback -> Update (ContractId IUpgradeEventInstrumentUpdateRollback)

  nonconsuming choice UpgradeEventInstrumentUpdateRollback : MigrationResult (ContractId IUpgradeEventInstrumentUpdateRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeEventInstrumentUpdateRollback contract") $
          migratingParty == (view this).integrationParty

        upgradeEventInstrumentUpdateRollback this self

  interface instance IUpgradeEventInstrumentUpdateRollback for CurrentEvents.EventInstrumentUpdate where
    view = convert this
    upgradeEventInstrumentUpdateRollback self = do
      archive self
      toInterfaceContractId @IUpgradeEventInstrumentUpdateRollback <$> create (convert this : LegacyEvents.EventInstrumentUpdate)

  interface instance IUpgradeEventInstrumentUpdateRollback for LegacyEvents.EventInstrumentUpdate where
    view = this
    upgradeEventInstrumentUpdateRollback = pure

interface IUpgradeEventInstrumentRollback where
  viewtype LegacyEvents.EventInstrument

  upgradeEventInstrumentRollback : ContractId IUpgradeEventInstrumentRollback -> Update (ContractId IUpgradeEventInstrumentRollback)

  nonconsuming choice UpgradeEventInstrumentRollback : MigrationResult (ContractId IUpgradeEventInstrumentRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeEventInstrumentRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeEventInstrumentRollback this self

  interface instance IUpgradeEventInstrumentRollback for CurrentEvents.EventInstrument where
    view = convert this
    upgradeEventInstrumentRollback self = do
      archive self
      toInterfaceContractId @IUpgradeEventInstrumentRollback <$> create (convert this : LegacyEvents.EventInstrument)

  interface instance IUpgradeEventInstrumentRollback for LegacyEvents.EventInstrument where
    view = this
    upgradeEventInstrumentRollback = pure

interface IUpgradeEventInstrumentUpdateOnHoldRollback where
  viewtype LegacyEvents.EventInstrumentUpdateOnHold

  upgradeEventInstrumentUpdateOnHoldRollback : ContractId IUpgradeEventInstrumentUpdateOnHoldRollback -> Update (ContractId IUpgradeEventInstrumentUpdateOnHoldRollback)

  nonconsuming choice UpgradeEventInstrumentUpdateOnHoldRollback : MigrationResult (ContractId IUpgradeEventInstrumentUpdateOnHoldRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeEventInstrumentUpdateOnHold contract") $
          migratingParty == (view this).integrationParty
        upgradeEventInstrumentUpdateOnHoldRollback this self

  interface instance IUpgradeEventInstrumentUpdateOnHoldRollback for CurrentEvents.EventInstrumentUpdateOnHold where
    view = convert this
    upgradeEventInstrumentUpdateOnHoldRollback self = do
      archive self
      toInterfaceContractId @IUpgradeEventInstrumentUpdateOnHoldRollback <$> create (convert this : LegacyEvents.EventInstrumentUpdateOnHold)

  interface instance IUpgradeEventInstrumentUpdateOnHoldRollback for LegacyEvents.EventInstrumentUpdateOnHold where
    view = this
    upgradeEventInstrumentUpdateOnHoldRollback = pure

interface IUpgradeFailedEventInstrumentRollback where
  viewtype LegacyEvents.FailedEventInstrument

  upgradeFailedEventInstrumentRollback : ContractId IUpgradeFailedEventInstrumentRollback -> Update (ContractId IUpgradeFailedEventInstrumentRollback)

  nonconsuming choice UpgradeFailedEventInstrumentRollback : MigrationResult (ContractId IUpgradeFailedEventInstrumentRollback)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeFailedEventInstrumentRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeFailedEventInstrumentRollback this self

  interface instance IUpgradeFailedEventInstrumentRollback for CurrentEvents.FailedEventInstrument where
    view = convert this
    upgradeFailedEventInstrumentRollback self = do
      archive self
      toInterfaceContractId @IUpgradeFailedEventInstrumentRollback <$> create (convert this : LegacyEvents.FailedEventInstrument)

  interface instance IUpgradeFailedEventInstrumentRollback for LegacyEvents.FailedEventInstrument where
    view = this
    upgradeFailedEventInstrumentRollback = pure
