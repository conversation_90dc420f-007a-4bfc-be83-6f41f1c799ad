module Gambyl.Migration.Interface.EnetPulse.Events where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.EnetPulse.Events  ()

import Legacy.EnetPulseIntegration.Events qualified as LegacyEvents
import Current.EnetPulseIntegration.Events qualified as CurrentEvents

interface IUpgradeEventInstrumentUpdate where
  viewtype CurrentEvents.EventInstrumentUpdate

  upgradeEventInstrumentUpdate : ContractId IUpgradeEventInstrumentUpdate -> Update (ContractId IUpgradeEventInstrumentUpdate)

  nonconsuming choice UpgradeEventInstrumentUpdate : MigrationResult (ContractId IUpgradeEventInstrumentUpdate)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeEventInstrumentUpdate contract") $
          migratingParty == (view this).integrationParty

        upgradeEventInstrumentUpdate this self

  interface instance IUpgradeEventInstrumentUpdate for LegacyEvents.EventInstrumentUpdate where
    view = convert this
    upgradeEventInstrumentUpdate self = do
      archive self
      toInterfaceContractId @IUpgradeEventInstrumentUpdate <$> create (convert this : CurrentEvents.EventInstrumentUpdate)

  interface instance IUpgradeEventInstrumentUpdate for CurrentEvents.EventInstrumentUpdate where
    view = this
    upgradeEventInstrumentUpdate = pure

interface IUpgradeEventInstrument where
  viewtype CurrentEvents.EventInstrument

  upgradeEventInstrument : ContractId IUpgradeEventInstrument -> Update (ContractId IUpgradeEventInstrument)

  nonconsuming choice UpgradeEventInstrument : MigrationResult (ContractId IUpgradeEventInstrument)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeEventInstrument contract") $
          migratingParty == (view this).integrationParty
        upgradeEventInstrument this self

  interface instance IUpgradeEventInstrument for LegacyEvents.EventInstrument where
    view = convert this
    upgradeEventInstrument self = do
      archive self
      toInterfaceContractId @IUpgradeEventInstrument <$> create (convert this : CurrentEvents.EventInstrument)

  interface instance IUpgradeEventInstrument for CurrentEvents.EventInstrument where
    view = this
    upgradeEventInstrument = pure

interface IUpgradeEventInstrumentUpdateOnHold where
  viewtype CurrentEvents.EventInstrumentUpdateOnHold

  upgradeEventInstrumentUpdateOnHold : ContractId IUpgradeEventInstrumentUpdateOnHold -> Update (ContractId IUpgradeEventInstrumentUpdateOnHold)

  nonconsuming choice UpgradeEventInstrumentUpdateOnHold : MigrationResult (ContractId IUpgradeEventInstrumentUpdateOnHold)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeEventInstrumentUpdateOnHold contract") $
          migratingParty == (view this).integrationParty
        upgradeEventInstrumentUpdateOnHold this self

  interface instance IUpgradeEventInstrumentUpdateOnHold for LegacyEvents.EventInstrumentUpdateOnHold where
    view = convert this
    upgradeEventInstrumentUpdateOnHold self = do
      archive self
      toInterfaceContractId @IUpgradeEventInstrumentUpdateOnHold <$> create (convert this : CurrentEvents.EventInstrumentUpdateOnHold)

  interface instance IUpgradeEventInstrumentUpdateOnHold for CurrentEvents.EventInstrumentUpdateOnHold where
    view = this
    upgradeEventInstrumentUpdateOnHold = pure

interface IUpgradeFailedEventInstrument where
  viewtype CurrentEvents.FailedEventInstrument

  upgradeFailedEventInstrument : ContractId IUpgradeFailedEventInstrument -> Update (ContractId IUpgradeFailedEventInstrument)

  nonconsuming choice UpgradeFailedEventInstrument : MigrationResult (ContractId IUpgradeFailedEventInstrument)
    with migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeFailedEventInstrument contract") $
          migratingParty == (view this).integrationParty
        upgradeFailedEventInstrument this self

  interface instance IUpgradeFailedEventInstrument for LegacyEvents.FailedEventInstrument where
    view = convert this
    upgradeFailedEventInstrument self = do
      archive self
      toInterfaceContractId @IUpgradeFailedEventInstrument <$> create (convert this : CurrentEvents.FailedEventInstrument)

  interface instance IUpgradeFailedEventInstrument for CurrentEvents.FailedEventInstrument where
    view = this
    upgradeFailedEventInstrument = pure
