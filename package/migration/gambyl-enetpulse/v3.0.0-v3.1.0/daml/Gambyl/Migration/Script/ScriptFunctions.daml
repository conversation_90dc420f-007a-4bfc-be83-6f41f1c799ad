module Gambyl.Migration.Script.ScriptFunctions where

import Daml.Script
import Gambyl.Migration.Common

import Gambyl.Migration.Interface.EnetPulse.Configuration qualified as Configuration
import Gambyl.Migration.Interface.EnetPulse.Events qualified as Events
import Gambyl.Migration.Interface.EnetPulse.Static qualified as Static

upgradeListOfIUpgradeEventInstrumentUpdate : Party -> [ContractId Events.IUpgradeEventInstrumentUpdate] -> Script [MigrationResult (ContractId Events.IUpgradeEventInstrumentUpdate)]
upgradeListOfIUpgradeEventInstrumentUpdate migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Events.UpgradeEventInstrumentUpdate with ..

upgradeListOfIUpgradeEventInstrument : Party -> [ContractId Events.IUpgradeEventInstrument] -> Script [MigrationResult (ContractId Events.IUpgradeEventInstrument)]
upgradeListOfIUpgradeEventInstrument migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Events.UpgradeEventInstrument with ..

upgradeListOfIUpgradeEventInstrumentUpdateOnHold : Party -> [ContractId Events.IUpgradeEventInstrumentUpdateOnHold] -> Script [MigrationResult (ContractId Events.IUpgradeEventInstrumentUpdateOnHold)]
upgradeListOfIUpgradeEventInstrumentUpdateOnHold migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Events.UpgradeEventInstrumentUpdateOnHold with ..

upgradeListOfIUpgradeFailedEventInstrument : Party -> [ContractId Events.IUpgradeFailedEventInstrument] -> Script [MigrationResult (ContractId Events.IUpgradeFailedEventInstrument)]
upgradeListOfIUpgradeFailedEventInstrument migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Events.UpgradeFailedEventInstrument with ..

upgradeListOfIUpgradeOutcomes : Party -> [ContractId Static.IUpgradeOutcomes] -> Script [MigrationResult (ContractId Static.IUpgradeOutcomes)]
upgradeListOfIUpgradeOutcomes migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Static.UpgradeOutcomes with ..

upgradeListOfIUpgradeStatuses : Party -> [ContractId Static.IUpgradeStatuses] -> Script [MigrationResult (ContractId Static.IUpgradeStatuses)]
upgradeListOfIUpgradeStatuses migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Static.UpgradeStatuses with ..

upgradeListOfIUpgradeResults : Party -> [ContractId Static.IUpgradeResults] -> Script [MigrationResult (ContractId Static.IUpgradeResults)]
upgradeListOfIUpgradeResults migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Static.UpgradeResults with ..

upgradeListOfIUpgradeSportTranslations : Party -> [ContractId Static.IUpgradeSportTranslations] -> Script [MigrationResult (ContractId Static.IUpgradeSportTranslations)]
upgradeListOfIUpgradeSportTranslations migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Static.UpgradeSportTranslations with ..

upgradeListOfIUpgradeConfiguration : Party -> [ContractId Configuration.IUpgradeConfiguration] -> Script [MigrationResult (ContractId Configuration.IUpgradeConfiguration)]
upgradeListOfIUpgradeConfiguration migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid Configuration.UpgradeConfiguration with ..
