module Gambyl.Migration.Script.UpgradeScript where

import Daml.Script

import Gambyl.Migration.Common
import Gambyl.Test.TestUtils

import Gambyl.Migration.Script.ScriptFunctions

upgradeScript : Input -> Script ReportSummary
upgradeScript Input{enetpulse} = script do

  debug "START Upgrading Enetpulse"

  r1 <- collectReport "EventInstrument" $ migrateDirectly "1-IUpgradeEventInstrument" enetpulse upgradeListOfIUpgradeEventInstrument
  -- the order is important for this case
  r2 <- collectReport "EventInstrumentUpdateOnHoldFrom" $ migrateDirectly "2-IUpgradeEventInstrumentUpdateOnHoldFrom" enetpulse upgradeListOfIUpgradeEventInstrumentUpdateOnHold
  r3 <- collectReport "EventInstrumentUpdate" $ migrateDirectly "0-IUpgradeEventInstrumentUpdate" enetpulse upgradeListOfIUpgradeEventInstrumentUpdate
  r4 <- collectReport "FailedEventInstrument" $ migrateDirectly "3-IUpgradeFailedEventInstrument" enetpulse upgradeListOfIUpgradeFailedEventInstrument
  r5 <- collectReport "Outcomes" $ migrateDirectly "4-IUpgradeOutcomes" enetpulse upgradeListOfIUpgradeOutcomes
  r6 <- collectReport "Statuses" $ migrateDirectly "5-IUpgradeStatuses" enetpulse upgradeListOfIUpgradeStatuses
  r7 <- collectReport "Results" $ migrateDirectly "6-IUpgradeResults" enetpulse upgradeListOfIUpgradeResults
  r8 <- collectReport "SportTranslations" $ migrateDirectly "7-IUpgradeSportTranslations" enetpulse upgradeListOfIUpgradeSportTranslations
  r9 <- collectReport "Configuration" $ migrateDirectly "8-IUpgradeConfiguration" enetpulse upgradeListOfIUpgradeConfiguration

  let report = foldl combineReport (ReportSummary 0 0 0 0 []) [r1, r2, r3, r4, r5, r6, r7, r8, r9]

  debug "FINISH Upgrading Enetpulse"

  pure report
