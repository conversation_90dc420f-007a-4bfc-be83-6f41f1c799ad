module Gambyl.Migration.Test.TestFunctions where

import Daml.Script

import DA.Map qualified as Map

import Legacy.EnetPulseIntegration.Configuration qualified as EnetPulseConfiguration
import Legacy.EnetPulseIntegration.Events qualified as EnetPulseEvents
import Legacy.EnetPulseIntegration.Static qualified as EnetPulseStatic

{-- CONTRACTS --}

-- | 0-IUpgradeEventInstrumentUpdate
prepareIUpgradeEventInstrumentUpdate : [Party] -> Text -> <PERSON><PERSON><PERSON> (ContractId EnetPulseEvents.EventInstrumentUpdate)
prepareIUpgradeEventInstrumentUpdate parties eventId = script do
  let
    [operator, enetpulse, _, _] = parties

  startDate <- getTime
  integrationTime <- getTime
  let
    integrationParty = enetpulse
    observers = [operator]
    participant = EnetPulseEvents.Participant with
      name = "participant-a"
      id = "participant-id-1"
      order = 1
      co_op = Some [2,3]
    outcome = EnetPulseEvents.Outcome with
      participantId = Some "participant-id-1"
      participantOrder = 1
      -- TODO: prepare all types for OutcomeType enum
      type_ = EnetPulseEvents.Winner
      -- TODO: prepare all types for OutcomeSubType enum
      subtype = EnetPulseEvents.Win
      order = 1
    outcomesOdds = EnetPulseEvents.OutcomeOdds with
      odd = 2.0
      ..
    eventDetails = EnetPulseEvents.Details with
      sportFK = "sportFK-a"
      tournamentStageName = "tournamentStageName-a"
      eventParticipants = [participant]
      eventTitle = "eventTitle-a"
      eventGame = Some "eventGame-a"
      outcomes = [outcomesOdds]
      geography = "ES"
      ..
    -- TODO: prepare all types for Status enum
    status = EnetPulseEvents.InProgress
    results = [outcome]
    liveEvent = "liveEvent-a"
    newOutcomes = [outcomesOdds]

  submitMulti [enetpulse] [] $ do
    createCmd EnetPulseEvents.EventInstrumentUpdate with ..

-- | 1-IUpgradeEventInstrument
prepareIUpgradeEventInstrument : [Party] -> Script (ContractId EnetPulseEvents.EventInstrument)
prepareIUpgradeEventInstrument parties = script do
  let
    [operator, enetpulse, _, _] = parties

  startDate <- getTime
  integrationTime <- getTime
  let
    integrationParty = enetpulse
    observers = [operator]
    eventId = "event-instrument-1"
    participant = EnetPulseEvents.Participant with
      name = "participant-a"
      id = "participant-id-1"
      order = 1
      co_op = Some [2,3]
    outcome = EnetPulseEvents.Outcome with
      participantId = Some "participant-id-1"
      participantOrder = 1
      -- TODO: prepare all types for OutcomeType enum
      type_ = EnetPulseEvents.Winner
      -- TODO: prepare all types for OutcomeSubType enum
      subtype = EnetPulseEvents.Win
      order = 1
    outcomesOdds = EnetPulseEvents.OutcomeOdds with
      odd = 2.0
      ..
    eventDetails = EnetPulseEvents.Details with
      sportFK = "sportFK-a"
      tournamentStageName = "tournamentStageName-a"
      eventParticipants = [participant]
      eventTitle = "eventTitle-a"
      eventGame = Some "eventGame-a"
      outcomes = [outcomesOdds]
      geography = "ES"
      ..
    -- TODO: prepare all types for Status enum
    status = EnetPulseEvents.InProgress
    results = [outcome]
    liveEvent = "liveEvent-a"

  submitMulti [enetpulse] [] $ do
    createCmd EnetPulseEvents.EventInstrument  with ..

-- | 2-IUpgradeEventInstrumentUpdateOnHoldFrom
prepareIUpgradeEventInstrumentUpdateOnHoldFrom : [Party] -> Script (ContractId EnetPulseEvents.EventInstrumentUpdateOnHold)
prepareIUpgradeEventInstrumentUpdateOnHoldFrom parties = script do
  let
    [operator, enetpulse, customer, _] = parties

  let
    integrationParty = enetpulse
    observers = [operator]
    eventId = "event-instrument-2"
    oldEventKey = (enetpulse, operator, "oldEventKey-a")
    serviceKey = (enetpulse, operator, customer)
    retries = 3

  now <- getTime

  submitMulti [enetpulse] [] $ do
    createCmd EnetPulseEvents.EventInstrumentUpdateOnHold  with
      eventUpdateKey = (integrationParty, eventId, now)
      ..

-- | 3-IUpgradeFailedEventInstrument
prepareIUpgradeFailedEventInstrument : [Party] -> Script (ContractId EnetPulseEvents.FailedEventInstrument)
prepareIUpgradeFailedEventInstrument parties = script do
  let
    [operator, enetpulse, _, _] = parties

  let
    integrationParty = enetpulse
    observers = [operator]
    eventId = "event-instrument-2"
    eventData = "eventData-a"
    eventType = "eventType-a"
    errorMessage = "errorMessage-a"

  submitMulti [enetpulse] [] $ do
    createCmd EnetPulseEvents.FailedEventInstrument  with ..

-- | 4-IUpgradeOutcomes
prepareIUpgradeOutcomes : [Party] -> Script (ContractId EnetPulseStatic.Outcomes)
prepareIUpgradeOutcomes parties = script do
  let
    [operator, enetpulse, _, _] = parties

  let
    integrationParty = enetpulse
    observers = [operator]
    -- TODO: prepare all types for OutcomeType enum
    outcometype = EnetPulseEvents.Winner
    outcomeMap = Map.fromList [("outcometype-a", outcometype)]

  submitMulti [enetpulse] [] $ do
    createCmd EnetPulseStatic.Outcomes  with ..

-- | 5-IUpgradeStatuses
prepareIUpgradeStatuses : [Party] -> Script (ContractId EnetPulseStatic.Statuses)
prepareIUpgradeStatuses parties = script do
  let
    [operator, enetpulse, _, _] = parties

  let
    integrationParty = enetpulse
    observers = [operator]
    -- TODO: prepare all types for Status enum
    status = EnetPulseEvents.InProgress
    statusMap = Map.fromList [("status-a", status)]

  submitMulti [enetpulse] [] $ do
    createCmd EnetPulseStatic.Statuses  with ..

-- | 6-IUpgradeResults
prepareIUpgradeResults : [Party] -> Script (ContractId EnetPulseStatic.Results)
prepareIUpgradeResults parties = script do
  let
    [operator, enetpulse, _, _] = parties

  let
    integrationParty = enetpulse
    observers = [operator]
    outcome = EnetPulseEvents.Outcome with
      participantId = Some "participant-id-1"
      participantOrder = 1
      -- TODO: prepare all types for OutcomeType enum
      type_ = EnetPulseEvents.Winner
      -- TODO: prepare all types for OutcomeSubType enum
      subtype = EnetPulseEvents.Win
      order = 1
    resultsMap = Map.fromList [("result-a", outcome)]

  submitMulti [enetpulse] [] $ do
    createCmd EnetPulseStatic.Results  with ..

-- | 7-IUpgradeSportTranslations
prepareIUpgradeSportTranslations : [Party] -> Script (ContractId EnetPulseStatic.SportTranslations)
prepareIUpgradeSportTranslations parties = script do
  let
    [operator, enetpulse, _, _] = parties

  let
    integrationParty = enetpulse
    observers = [operator]
    sport = Map.fromList [("sportFK-a", "sportName-a")]
    sportsMap = Map.fromList [("ES", sport)]

  submitMulti [enetpulse] [] $ do
    createCmd EnetPulseStatic.SportTranslations  with ..

-- | 8-IUpgradeConfiguration
prepareIUpgradeConfiguration : [Party] -> Script (ContractId EnetPulseConfiguration.Configuration)
prepareIUpgradeConfiguration parties = script do
  let
    [operator, enetpulse, _, _] = parties

  let
    integrationParty = enetpulse
    observers = [operator]
    user = "user-a"
    token = "token-a"

  submitMulti [enetpulse] [] $ do
    createCmd EnetPulseConfiguration.Configuration  with ..

prepareLedger : Party -> Party -> Script ()
prepareLedger operator enetpulse = script do
  [customer, other] <- mapA allocateParty ["alice", "bob"]

  let parties = [operator, enetpulse, customer, other]

  prepareIUpgradeEventInstrumentUpdate parties "event-instrument-1"
  prepareIUpgradeEventInstrument parties
  prepareIUpgradeEventInstrumentUpdateOnHoldFrom parties
  prepareIUpgradeFailedEventInstrument parties
  prepareIUpgradeOutcomes parties
  prepareIUpgradeStatuses parties
  prepareIUpgradeResults parties
  prepareIUpgradeSportTranslations parties
  prepareIUpgradeConfiguration parties

  pure ()
