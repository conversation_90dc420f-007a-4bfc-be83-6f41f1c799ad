module Gambyl.Migration.EnetPulse.EventsRollback where

import Gambyl.Migration.Upgrade

import Legacy.EnetPulseIntegration.Events qualified as LegacyEvents
import Current.EnetPulseIntegration.Events qualified as CurrentEvents

instance DAMLUpgrade CurrentEvents.Details LegacyEvents.Details where
  convert CurrentEvents.Details{..} = LegacyEvents.Details with
    eventParticipants = convert eventParticipants
    outcomes = convert outcomes
    geography = convert geography
    ..

instance DAMLUpgrade CurrentEvents.OutcomeOdds LegacyEvents.OutcomeOdds where
  convert CurrentEvents.OutcomeOdds{..} = LegacyEvents.OutcomeOdds with
    outcome = convert outcome
    ..

instance DAMLUpgrade CurrentEvents.Participant LegacyEvents.Participant where
  convert CurrentEvents.Participant{..} = LegacyEvents.Participant with
    ..

instance DAMLUpgrade CurrentEvents.Outcome LegacyEvents.Outcome where
  convert CurrentEvents.Outcome{..} = LegacyEvents.Outcome with
    type_ = convert type_
    subtype = convert subtype
    ..

instance DAMLUpgrade CurrentEvents.OutcomeType LegacyEvents.OutcomeType where
  convert CurrentEvents.ThreeWay = LegacyEvents.ThreeWay
  convert CurrentEvents.TwoWay = LegacyEvents.TwoWay
  convert CurrentEvents.Winner = LegacyEvents.Winner
  convert CurrentEvents.Default = LegacyEvents.Default
  convert (CurrentEvents.OverUnder decimal) = LegacyEvents.OverUnder $ convert decimal
  convert (CurrentEvents.ThreeWayHandicap decimal) = LegacyEvents.ThreeWayHandicap $ convert decimal

instance DAMLUpgrade CurrentEvents.OutcomeSubType LegacyEvents.OutcomeSubType where
  convert CurrentEvents.Win = LegacyEvents.Win
  convert CurrentEvents.Draw = LegacyEvents.Draw
  convert CurrentEvents.Over = LegacyEvents.Over
  convert CurrentEvents.Under = LegacyEvents.Under

instance DAMLUpgrade CurrentEvents.Status LegacyEvents.Status where
  convert CurrentEvents.NotStarted = LegacyEvents.NotStarted
  convert CurrentEvents.InProgress = LegacyEvents.InProgress
  convert CurrentEvents.Finished = LegacyEvents.Finished
  convert CurrentEvents.Cancelled = LegacyEvents.Cancelled
  convert CurrentEvents.Unknown = LegacyEvents.Unknown
  convert CurrentEvents.Interrupted = LegacyEvents.Interrupted
  convert CurrentEvents.Other = LegacyEvents.Other
  convert CurrentEvents.Postponed = LegacyEvents.Postponed

instance DAMLUpgrade CurrentEvents.EventInstrumentUpdate LegacyEvents.EventInstrumentUpdate where
  convert CurrentEvents.EventInstrumentUpdate{..} =
    let convertedDetails = convert $ eventDetails
        convertedNewOutcomes = convertedDetails.outcomes
    in LegacyEvents.EventInstrumentUpdate with
        eventDetails = convertedDetails
        status = convert status
        results = convert results
        newOutcomes = convertedNewOutcomes
        ..

instance DAMLUpgrade CurrentEvents.EventInstrument LegacyEvents.EventInstrument where
  convert CurrentEvents.EventInstrument{..} = LegacyEvents.EventInstrument with
    eventDetails = convert eventDetails
    status = convert status
    results = convert results
    ..

instance DAMLUpgrade CurrentEvents.EventInstrumentUpdateOnHold LegacyEvents.EventInstrumentUpdateOnHold where
  convert CurrentEvents.EventInstrumentUpdateOnHold{..} = LegacyEvents.EventInstrumentUpdateOnHold with
    ..

instance DAMLUpgrade CurrentEvents.FailedEventInstrument LegacyEvents.FailedEventInstrument where
  convert CurrentEvents.FailedEventInstrument{..} = LegacyEvents.FailedEventInstrument with
    ..
