module Gambyl.Migration.EnetPulse.ConfigurationRollback where

import Gambyl.Migration.Upgrade

import Legacy.EnetPulseIntegration.Configuration qualified as LegacyConfiguration
import Current.EnetPulseIntegration.Configuration qualified as CurrentConfiguration

instance DAMLUpgrade CurrentConfiguration.Configuration LegacyConfiguration.Configuration where
  convert CurrentConfiguration.Configuration{..} = LegacyConfiguration.Configuration with
        ..
