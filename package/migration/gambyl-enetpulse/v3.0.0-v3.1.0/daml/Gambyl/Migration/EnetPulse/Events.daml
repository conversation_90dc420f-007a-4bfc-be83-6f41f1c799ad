module Gambyl.Migration.EnetPulse.Events where

import Gambyl.Migration.Upgrade

import Legacy.EnetPulseIntegration.Events qualified as LegacyEvents
import Current.EnetPulseIntegration.Events qualified as CurrentEvents

instance DAMLUpgrade LegacyEvents.Details CurrentEvents.Details where
  convert LegacyEvents.Details{..} = CurrentEvents.Details with
        geography = convert geography
        eventParticipants = convert eventParticipants
        outcomes = convert outcomes
        ..

instance DAMLUpgrade LegacyEvents.OutcomeOdds CurrentEvents.OutcomeOdds where
  convert LegacyEvents.OutcomeOdds{..} = CurrentEvents.OutcomeOdds with
        outcome = convert outcome
        ..

instance DAMLUpgrade LegacyEvents.Participant CurrentEvents.Participant where
  convert LegacyEvents.Participant{..} = CurrentEvents.Participant with
        ..

instance DAMLUpgrade LegacyEvents.Outcome CurrentEvents.Outcome where
  convert LegacyEvents.Outcome{..} = CurrentEvents.Outcome with
        type_ = convert type_
        subtype = convert subtype
        ..

instance DAMLUpgrade LegacyEvents.OutcomeType CurrentEvents.OutcomeType where
  convert LegacyEvents.ThreeWay = CurrentEvents.ThreeWay
  convert LegacyEvents.TwoWay = CurrentEvents.TwoWay
  convert LegacyEvents.Winner = CurrentEvents.Winner
  convert LegacyEvents.Default = CurrentEvents.Default
  convert (LegacyEvents.OverUnder decimal) = CurrentEvents.OverUnder $ convert decimal
  convert (LegacyEvents.ThreeWayHandicap decimal) = CurrentEvents.ThreeWayHandicap $ convert decimal

instance DAMLUpgrade LegacyEvents.OutcomeSubType CurrentEvents.OutcomeSubType where
  convert LegacyEvents.Win = CurrentEvents.Win
  convert LegacyEvents.Draw = CurrentEvents.Draw
  convert LegacyEvents.Over = CurrentEvents.Over
  convert LegacyEvents.Under = CurrentEvents.Under

instance DAMLUpgrade LegacyEvents.Status CurrentEvents.Status where
  convert LegacyEvents.NotStarted = CurrentEvents.NotStarted
  convert LegacyEvents.InProgress = CurrentEvents.InProgress
  convert LegacyEvents.Finished = CurrentEvents.Finished
  convert LegacyEvents.Cancelled = CurrentEvents.Cancelled
  convert LegacyEvents.Unknown = CurrentEvents.Unknown
  convert LegacyEvents.Interrupted = CurrentEvents.Interrupted
  convert LegacyEvents.Other = CurrentEvents.Other
  convert LegacyEvents.Postponed = CurrentEvents.Postponed

instance DAMLUpgrade LegacyEvents.EventInstrumentUpdate CurrentEvents.EventInstrumentUpdate where
  convert LegacyEvents.EventInstrumentUpdate{..} =
    let convertedDetails = convert $ eventDetails with outcomes = newOutcomes
     in CurrentEvents.EventInstrumentUpdate with
          eventDetails = convertedDetails
          status = convert status
          results = convert results
          ..

instance DAMLUpgrade LegacyEvents.EventInstrument CurrentEvents.EventInstrument where
  convert LegacyEvents.EventInstrument{..} = CurrentEvents.EventInstrument with
        eventDetails = convert eventDetails
        status = convert status
        results = convert results
        ..

instance DAMLUpgrade LegacyEvents.EventInstrumentUpdateOnHold CurrentEvents.EventInstrumentUpdateOnHold where
  convert LegacyEvents.EventInstrumentUpdateOnHold{..} = CurrentEvents.EventInstrumentUpdateOnHold with
        ..

instance DAMLUpgrade LegacyEvents.FailedEventInstrument CurrentEvents.FailedEventInstrument where
  convert LegacyEvents.FailedEventInstrument{..} = CurrentEvents.FailedEventInstrument with
        ..
