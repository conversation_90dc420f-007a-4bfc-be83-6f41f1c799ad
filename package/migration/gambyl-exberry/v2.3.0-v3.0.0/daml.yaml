# for config file options, refer to
# https://docs.daml.com/tools/assistant.html#project-config-file-daml-yaml

sdk-version: 2.9.4
name: gambyl-exberry-migration
source: daml
init-script:
parties:
  - Operator
version: 2.0.0
dependencies:
  - daml-prim
  - daml-stdlib
  - daml-script
data-dependencies:
  # External Dependencies
  - ../../../../ext-pkg/integrations/exberry/${GAMBYL_EXBERRY_IMPL_LEGACY_VERSION}/daml/exberry-integration-model-${GAMBYL_EXBERRY_IMPL_LEGACY_VERSION}.dar
  # Local Dependencies
  - ../../../../build/migration/gambyl-migration-utils-${GAMBYL_MIGRATION_UTILS_VERSION}.dar
  - ../../../../build/implementation/gambyl-exberry-impl-${GAMBYL_EXBERRY_IMPL_CURRENT_VERSION}.dar
build-options:
  - --ghc-option=-Wall
  - --ghc-option=-Wno-name-shadowing
  - --ghc-option=-fno-warn-orphans
  - --ghc-option=-Wno-unused-do-bind
  - --ghc-option=-Werror
  - --output=../../../../build/migration/gambyl-exberry-v${GAMBYL_EXBERRY_IMPL_LEGACY_VERSION}-v${GAMBYL_EXBERRY_IMPL_CURRENT_VERSION}-migration-2.0.0.dar
module-prefixes:
  exberry-integration-model-${GAMBYL_EXBERRY_IMPL_LEGACY_VERSION}: Legacy
  gambyl-exberry-impl-${GAMBYL_EXBERRY_IMPL_CURRENT_VERSION}: Current