module Gambyl.Migration.Interface.Exberry.IntegrationRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Exberry.IntegrationRollback ()

import Legacy.Exberry.Integration qualified as LegacyIntegration
import Current.Exberry.Integration qualified as CurrentIntegration

interface IUpgradeNewOrderRequestRollback where
  viewtype LegacyIntegration.NewOrderRequest

  upgradeNewOrderRequestRollback : ContractId IUpgradeNewOrderRequestRollback -> Update (ContractId IUpgradeNewOrderRequestRollback)

  nonconsuming choice UpgradeNewOrderRequestRollback : MigrationResult (ContractId IUpgradeNewOrderRequestRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeNewOrderRequestRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeNewOrderRequestRollback this self

  interface instance IUpgradeNewOrderRequestRollback for CurrentIntegration.NewOrderRequest where
    view = convert this
    upgradeNewOrderRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeNewOrderRequestRollback <$> create (convert this : LegacyIntegration.NewOrderRequest)

  interface instance IUpgradeNewOrderRequestRollback for LegacyIntegration.NewOrderRequest where
    view = this
    upgradeNewOrderRequestRollback = pure

interface IUpgradeNewOrderSuccessRollback where
  viewtype LegacyIntegration.NewOrderSuccess

  upgradeNewOrderSuccessRollback : ContractId IUpgradeNewOrderSuccessRollback -> Update (ContractId IUpgradeNewOrderSuccessRollback)

  nonconsuming choice UpgradeNewOrderSuccessRollback : MigrationResult (ContractId IUpgradeNewOrderSuccessRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeNewOrderSuccessRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeNewOrderSuccessRollback this self

  interface instance IUpgradeNewOrderSuccessRollback for CurrentIntegration.NewOrderSuccess where
    view = convert this
    upgradeNewOrderSuccessRollback self = do
      archive self
      toInterfaceContractId @IUpgradeNewOrderSuccessRollback <$> create (convert this : LegacyIntegration.NewOrderSuccess)

  interface instance IUpgradeNewOrderSuccessRollback for LegacyIntegration.NewOrderSuccess where
    view = this
    upgradeNewOrderSuccessRollback = pure

interface IUpgradeNewOrderFailureRollback where
  viewtype LegacyIntegration.NewOrderFailure

  upgradeNewOrderFailureRollback : ContractId IUpgradeNewOrderFailureRollback -> Update (ContractId IUpgradeNewOrderFailureRollback)

  nonconsuming choice UpgradeNewOrderFailureRollback : MigrationResult (ContractId IUpgradeNewOrderFailureRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeNewOrderFailureRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeNewOrderFailureRollback this self

  interface instance IUpgradeNewOrderFailureRollback for CurrentIntegration.NewOrderFailure where
    view = convert this
    upgradeNewOrderFailureRollback self = do
      archive self
      toInterfaceContractId @IUpgradeNewOrderFailureRollback <$> create (convert this : LegacyIntegration.NewOrderFailure)

  interface instance IUpgradeNewOrderFailureRollback for LegacyIntegration.NewOrderFailure where
    view = this
    upgradeNewOrderFailureRollback = pure

interface IUpgradeCancelOrderRequestRollback where
  viewtype LegacyIntegration.CancelOrderRequest

  upgradeCancelOrderRequestRollback : ContractId IUpgradeCancelOrderRequestRollback -> Update (ContractId IUpgradeCancelOrderRequestRollback)

  nonconsuming choice UpgradeCancelOrderRequestRollback : MigrationResult (ContractId IUpgradeCancelOrderRequestRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeCancelOrderRequestRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeCancelOrderRequestRollback this self

  interface instance IUpgradeCancelOrderRequestRollback for CurrentIntegration.CancelOrderRequest where
    view = convert this
    upgradeCancelOrderRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeCancelOrderRequestRollback <$> create (convert this : LegacyIntegration.CancelOrderRequest)

  interface instance IUpgradeCancelOrderRequestRollback for LegacyIntegration.CancelOrderRequest where
    view = this
    upgradeCancelOrderRequestRollback = pure

interface IUpgradeCancelOrderSuccessRollback where
  viewtype LegacyIntegration.CancelOrderSuccess

  upgradeCancelOrderSuccessRollback : ContractId IUpgradeCancelOrderSuccessRollback -> Update (ContractId IUpgradeCancelOrderSuccessRollback)

  nonconsuming choice UpgradeCancelOrderSuccessRollback : MigrationResult (ContractId IUpgradeCancelOrderSuccessRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeCancelOrderSuccessRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeCancelOrderSuccessRollback this self

  interface instance IUpgradeCancelOrderSuccessRollback for CurrentIntegration.CancelOrderSuccess where
    view = convert this
    upgradeCancelOrderSuccessRollback self = do
      archive self
      toInterfaceContractId @IUpgradeCancelOrderSuccessRollback <$> create (convert this : LegacyIntegration.CancelOrderSuccess)

  interface instance IUpgradeCancelOrderSuccessRollback for LegacyIntegration.CancelOrderSuccess where
    view = this
    upgradeCancelOrderSuccessRollback = pure

interface IUpgradeCancelOrderFailureRollback where
  viewtype LegacyIntegration.CancelOrderFailure

  upgradeCancelOrderFailureRollback : ContractId IUpgradeCancelOrderFailureRollback -> Update (ContractId IUpgradeCancelOrderFailureRollback)

  nonconsuming choice UpgradeCancelOrderFailureRollback : MigrationResult (ContractId IUpgradeCancelOrderFailureRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeCancelOrderFailureRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeCancelOrderFailureRollback this self

  interface instance IUpgradeCancelOrderFailureRollback for CurrentIntegration.CancelOrderFailure where
    view = convert this
    upgradeCancelOrderFailureRollback self = do
      archive self
      toInterfaceContractId @IUpgradeCancelOrderFailureRollback <$> create (convert this : LegacyIntegration.CancelOrderFailure)

  interface instance IUpgradeCancelOrderFailureRollback for LegacyIntegration.CancelOrderFailure where
    view = this
    upgradeCancelOrderFailureRollback = pure

interface IUpgradeCreateInstrumentRequestRollback where
  viewtype LegacyIntegration.CreateInstrumentRequest

  upgradeCreateInstrumentRequestRollback : ContractId IUpgradeCreateInstrumentRequestRollback -> Update (ContractId IUpgradeCreateInstrumentRequestRollback)

  nonconsuming choice UpgradeCreateInstrumentRequestRollback : MigrationResult (ContractId IUpgradeCreateInstrumentRequestRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeCreateInstrumentRequestRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeCreateInstrumentRequestRollback this self

  interface instance IUpgradeCreateInstrumentRequestRollback for CurrentIntegration.CreateInstrumentRequest where
    view = convert this
    upgradeCreateInstrumentRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeCreateInstrumentRequestRollback <$> create (convert this : LegacyIntegration.CreateInstrumentRequest)

  interface instance IUpgradeCreateInstrumentRequestRollback for LegacyIntegration.CreateInstrumentRequest where
    view = this
    upgradeCreateInstrumentRequestRollback = pure

interface IUpgradeUpdateInstrumentRequestRollback where
  viewtype LegacyIntegration.UpdateInstrumentRequest

  upgradeUpdateInstrumentRequestRollback : ContractId IUpgradeUpdateInstrumentRequestRollback -> Update (ContractId IUpgradeUpdateInstrumentRequestRollback)

  nonconsuming choice UpgradeUpdateInstrumentRequestRollback : MigrationResult (ContractId IUpgradeUpdateInstrumentRequestRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeUpdateInstrumentRequestRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeUpdateInstrumentRequestRollback this self

  interface instance IUpgradeUpdateInstrumentRequestRollback for CurrentIntegration.UpdateInstrumentRequest where
    view = convert this
    upgradeUpdateInstrumentRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeUpdateInstrumentRequestRollback <$> create (convert this : LegacyIntegration.UpdateInstrumentRequest)

  interface instance IUpgradeUpdateInstrumentRequestRollback for LegacyIntegration.UpdateInstrumentRequest where
    view = this
    upgradeUpdateInstrumentRequestRollback = pure

interface IUpgradeInstrumentRollback where
  viewtype LegacyIntegration.Instrument

  upgradeInstrumentRollback : ContractId IUpgradeInstrumentRollback -> Update (ContractId IUpgradeInstrumentRollback)

  nonconsuming choice UpgradeInstrumentRollback : MigrationResult (ContractId IUpgradeInstrumentRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeInstrumentRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeInstrumentRollback this self

  interface instance IUpgradeInstrumentRollback for CurrentIntegration.Instrument where
    view = convert this
    upgradeInstrumentRollback self = do
      archive self
      toInterfaceContractId @IUpgradeInstrumentRollback <$> create (convert this : LegacyIntegration.Instrument)

  interface instance IUpgradeInstrumentRollback for LegacyIntegration.Instrument where
    view = this
    upgradeInstrumentRollback = pure

interface IUpgradeFailedInstrumentRequestRollback where
  viewtype LegacyIntegration.FailedInstrumentRequest

  upgradeFailedInstrumentRequestRollback : ContractId IUpgradeFailedInstrumentRequestRollback -> Update (ContractId IUpgradeFailedInstrumentRequestRollback)

  nonconsuming choice UpgradeFailedInstrumentRequestRollback : MigrationResult (ContractId IUpgradeFailedInstrumentRequestRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeFailedInstrumentRequestRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeFailedInstrumentRequestRollback this self

  interface instance IUpgradeFailedInstrumentRequestRollback for CurrentIntegration.FailedInstrumentRequest where
    view = convert this
    upgradeFailedInstrumentRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeFailedInstrumentRequestRollback <$> create (convert this : LegacyIntegration.FailedInstrumentRequest)

  interface instance IUpgradeFailedInstrumentRequestRollback for LegacyIntegration.FailedInstrumentRequest where
    view = this
    upgradeFailedInstrumentRequestRollback = pure

interface IUpgradeFailedUpdateInstrumentRequestRollback where
  viewtype LegacyIntegration.FailedUpdateInstrumentRequest

  upgradeFailedUpdateInstrumentRequestRollback : ContractId IUpgradeFailedUpdateInstrumentRequestRollback -> Update (ContractId IUpgradeFailedUpdateInstrumentRequestRollback)

  nonconsuming choice UpgradeFailedUpdateInstrumentRequestRollback : MigrationResult (ContractId IUpgradeFailedUpdateInstrumentRequestRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeFailedUpdateInstrumentRequestRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeFailedUpdateInstrumentRequestRollback this self

  interface instance IUpgradeFailedUpdateInstrumentRequestRollback for CurrentIntegration.FailedUpdateInstrumentRequest where
    view = convert this
    upgradeFailedUpdateInstrumentRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeFailedUpdateInstrumentRequestRollback <$> create (convert this : LegacyIntegration.FailedUpdateInstrumentRequest)

  interface instance IUpgradeFailedUpdateInstrumentRequestRollback for LegacyIntegration.FailedUpdateInstrumentRequest where
    view = this
    upgradeFailedUpdateInstrumentRequestRollback = pure

interface IUpgradeExecutionReportRollback where
  viewtype LegacyIntegration.ExecutionReport

  upgradeExecutionReportRollback : ContractId IUpgradeExecutionReportRollback -> Update (ContractId IUpgradeExecutionReportRollback)

  nonconsuming choice UpgradeExecutionReportRollback : MigrationResult (ContractId IUpgradeExecutionReportRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeExecutionReportRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeExecutionReportRollback this self

  interface instance IUpgradeExecutionReportRollback for CurrentIntegration.ExecutionReport where
    view = convert this
    upgradeExecutionReportRollback self = do
      archive self
      toInterfaceContractId @IUpgradeExecutionReportRollback <$> create (convert this : LegacyIntegration.ExecutionReport)

  interface instance IUpgradeExecutionReportRollback for LegacyIntegration.ExecutionReport where
    view = this
    upgradeExecutionReportRollback = pure

interface IUpgradeMassCancelRequestRollback where
  viewtype LegacyIntegration.MassCancelRequest

  upgradeMassCancelRequestRollback : ContractId IUpgradeMassCancelRequestRollback -> Update (ContractId IUpgradeMassCancelRequestRollback)

  nonconsuming choice UpgradeMassCancelRequestRollback : MigrationResult (ContractId IUpgradeMassCancelRequestRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeMassCancelRequestRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeMassCancelRequestRollback this self

  interface instance IUpgradeMassCancelRequestRollback for CurrentIntegration.MassCancelRequest where
    view = convert this
    upgradeMassCancelRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeMassCancelRequestRollback <$> create (convert this : LegacyIntegration.MassCancelRequest)

  interface instance IUpgradeMassCancelRequestRollback for LegacyIntegration.MassCancelRequest where
    view = this
    upgradeMassCancelRequestRollback = pure

interface IUpgradeMassCancelSuccessRollback where
  viewtype LegacyIntegration.MassCancelSuccess

  upgradeMassCancelSuccessRollback : ContractId IUpgradeMassCancelSuccessRollback -> Update (ContractId IUpgradeMassCancelSuccessRollback)

  nonconsuming choice UpgradeMassCancelSuccessRollback : MigrationResult (ContractId IUpgradeMassCancelSuccessRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeMassCancelSuccessRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeMassCancelSuccessRollback this self

  interface instance IUpgradeMassCancelSuccessRollback for CurrentIntegration.MassCancelSuccess where
    view = convert this
    upgradeMassCancelSuccessRollback self = do
      archive self
      toInterfaceContractId @IUpgradeMassCancelSuccessRollback <$> create (convert this : LegacyIntegration.MassCancelSuccess)

  interface instance IUpgradeMassCancelSuccessRollback for LegacyIntegration.MassCancelSuccess where
    view = this
    upgradeMassCancelSuccessRollback = pure

interface IUpgradeMassCancelFailureRollback where
  viewtype LegacyIntegration.MassCancelFailure

  upgradeMassCancelFailureRollback : ContractId IUpgradeMassCancelFailureRollback -> Update (ContractId IUpgradeMassCancelFailureRollback)

  nonconsuming choice UpgradeMassCancelFailureRollback : MigrationResult (ContractId IUpgradeMassCancelFailureRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeMassCancelFailureRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeMassCancelFailureRollback this self

  interface instance IUpgradeMassCancelFailureRollback for CurrentIntegration.MassCancelFailure where
    view = convert this
    upgradeMassCancelFailureRollback self = do
      archive self
      toInterfaceContractId @IUpgradeMassCancelFailureRollback <$> create (convert this : LegacyIntegration.MassCancelFailure)

  interface instance IUpgradeMassCancelFailureRollback for LegacyIntegration.MassCancelFailure where
    view = this
    upgradeMassCancelFailureRollback = pure