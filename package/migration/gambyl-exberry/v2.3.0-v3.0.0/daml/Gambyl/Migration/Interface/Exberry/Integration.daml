module Gambyl.Migration.Interface.Exberry.Integration where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.Exberry.Integration ()

import Legacy.Exberry.Integration qualified as LegacyIntegration
import Current.Exberry.Integration qualified as CurrentIntegration

interface IUpgradeNewOrderRequest where
  viewtype CurrentIntegration.NewOrderRequest

  upgradeNewOrderRequest : ContractId IUpgradeNewOrderRequest -> Update (ContractId IUpgradeNewOrderRequest)

  nonconsuming choice UpgradeNewOrderRequest : MigrationResult (ContractId IUpgradeNewOrderRequest)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeNewOrderRequest contract") $
          migratingParty == (view this).integrationParty
        upgradeNewOrderRequest this self

  interface instance IUpgradeNewOrderRequest for LegacyIntegration.NewOrderRequest where
    view = convert this
    upgradeNewOrderRequest self = do
      archive self
      toInterfaceContractId @IUpgradeNewOrderRequest <$> create (convert this : CurrentIntegration.NewOrderRequest)

  interface instance IUpgradeNewOrderRequest for CurrentIntegration.NewOrderRequest where
    view = this
    upgradeNewOrderRequest = pure

interface IUpgradeNewOrderSuccess where
  viewtype CurrentIntegration.NewOrderSuccess

  upgradeNewOrderSuccess : ContractId IUpgradeNewOrderSuccess -> Update (ContractId IUpgradeNewOrderSuccess)

  nonconsuming choice UpgradeNewOrderSuccess : MigrationResult (ContractId IUpgradeNewOrderSuccess)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeNewOrderSuccess contract") $
          migratingParty == (view this).integrationParty
        upgradeNewOrderSuccess this self

  interface instance IUpgradeNewOrderSuccess for LegacyIntegration.NewOrderSuccess where
    view = convert this
    upgradeNewOrderSuccess self = do
      archive self
      toInterfaceContractId @IUpgradeNewOrderSuccess <$> create (convert this : CurrentIntegration.NewOrderSuccess)

  interface instance IUpgradeNewOrderSuccess for CurrentIntegration.NewOrderSuccess where
    view = this
    upgradeNewOrderSuccess = pure

interface IUpgradeNewOrderFailure where
  viewtype CurrentIntegration.NewOrderFailure

  upgradeNewOrderFailure : ContractId IUpgradeNewOrderFailure -> Update (ContractId IUpgradeNewOrderFailure)

  nonconsuming choice UpgradeNewOrderFailure : MigrationResult (ContractId IUpgradeNewOrderFailure)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeNewOrderFailure contract") $
          migratingParty == (view this).integrationParty
        upgradeNewOrderFailure this self

  interface instance IUpgradeNewOrderFailure for LegacyIntegration.NewOrderFailure where
    view = convert this
    upgradeNewOrderFailure self = do
      archive self
      toInterfaceContractId @IUpgradeNewOrderFailure <$> create (convert this : CurrentIntegration.NewOrderFailure)

  interface instance IUpgradeNewOrderFailure for CurrentIntegration.NewOrderFailure where
    view = this
    upgradeNewOrderFailure = pure

interface IUpgradeCancelOrderRequest where
  viewtype CurrentIntegration.CancelOrderRequest

  upgradeCancelOrderRequest : ContractId IUpgradeCancelOrderRequest -> Update (ContractId IUpgradeCancelOrderRequest)

  nonconsuming choice UpgradeCancelOrderRequest : MigrationResult (ContractId IUpgradeCancelOrderRequest)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeCancelOrderRequest contract") $
          migratingParty == (view this).integrationParty
        upgradeCancelOrderRequest this self

  interface instance IUpgradeCancelOrderRequest for LegacyIntegration.CancelOrderRequest where
    view = convert this
    upgradeCancelOrderRequest self = do
      archive self
      toInterfaceContractId @IUpgradeCancelOrderRequest <$> create (convert this : CurrentIntegration.CancelOrderRequest)

  interface instance IUpgradeCancelOrderRequest for CurrentIntegration.CancelOrderRequest where
    view = this
    upgradeCancelOrderRequest = pure

interface IUpgradeCancelOrderSuccess where
  viewtype CurrentIntegration.CancelOrderSuccess

  upgradeCancelOrderSuccess : ContractId IUpgradeCancelOrderSuccess -> Update (ContractId IUpgradeCancelOrderSuccess)

  nonconsuming choice UpgradeCancelOrderSuccess : MigrationResult (ContractId IUpgradeCancelOrderSuccess)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeCancelOrderSuccess contract") $
          migratingParty == (view this).integrationParty
        upgradeCancelOrderSuccess this self

  interface instance IUpgradeCancelOrderSuccess for LegacyIntegration.CancelOrderSuccess where
    view = convert this
    upgradeCancelOrderSuccess self = do
      archive self
      toInterfaceContractId @IUpgradeCancelOrderSuccess <$> create (convert this : CurrentIntegration.CancelOrderSuccess)

  interface instance IUpgradeCancelOrderSuccess for CurrentIntegration.CancelOrderSuccess where
    view = this
    upgradeCancelOrderSuccess = pure

interface IUpgradeCancelOrderFailure where
  viewtype CurrentIntegration.CancelOrderFailure

  upgradeCancelOrderFailure : ContractId IUpgradeCancelOrderFailure -> Update (ContractId IUpgradeCancelOrderFailure)

  nonconsuming choice UpgradeCancelOrderFailure : MigrationResult (ContractId IUpgradeCancelOrderFailure)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeCancelOrderFailure contract") $
          migratingParty == (view this).integrationParty
        upgradeCancelOrderFailure this self

  interface instance IUpgradeCancelOrderFailure for LegacyIntegration.CancelOrderFailure where
    view = convert this
    upgradeCancelOrderFailure self = do
      archive self
      toInterfaceContractId @IUpgradeCancelOrderFailure <$> create (convert this : CurrentIntegration.CancelOrderFailure)

  interface instance IUpgradeCancelOrderFailure for CurrentIntegration.CancelOrderFailure where
    view = this
    upgradeCancelOrderFailure = pure

interface IUpgradeCreateInstrumentRequest where
  viewtype CurrentIntegration.CreateInstrumentRequest

  upgradeCreateInstrumentRequest : ContractId IUpgradeCreateInstrumentRequest -> Update (ContractId IUpgradeCreateInstrumentRequest)

  nonconsuming choice UpgradeCreateInstrumentRequest : MigrationResult (ContractId IUpgradeCreateInstrumentRequest)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeCreateInstrumentRequest contract") $
          migratingParty == (view this).integrationParty
        upgradeCreateInstrumentRequest this self

  interface instance IUpgradeCreateInstrumentRequest for LegacyIntegration.CreateInstrumentRequest where
    view = convert this
    upgradeCreateInstrumentRequest self = do
      archive self
      toInterfaceContractId @IUpgradeCreateInstrumentRequest <$> create (convert this : CurrentIntegration.CreateInstrumentRequest)

  interface instance IUpgradeCreateInstrumentRequest for CurrentIntegration.CreateInstrumentRequest where
    view = this
    upgradeCreateInstrumentRequest = pure

interface IUpgradeUpdateInstrumentRequest where
  viewtype CurrentIntegration.UpdateInstrumentRequest

  upgradeUpdateInstrumentRequest : ContractId IUpgradeUpdateInstrumentRequest -> Update (ContractId IUpgradeUpdateInstrumentRequest)

  nonconsuming choice UpgradeUpdateInstrumentRequest : MigrationResult (ContractId IUpgradeUpdateInstrumentRequest)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeUpdateInstrumentRequest contract") $
          migratingParty == (view this).integrationParty
        upgradeUpdateInstrumentRequest this self

  interface instance IUpgradeUpdateInstrumentRequest for LegacyIntegration.UpdateInstrumentRequest where
    view = convert this
    upgradeUpdateInstrumentRequest self = do
      archive self
      toInterfaceContractId @IUpgradeUpdateInstrumentRequest <$> create (convert this : CurrentIntegration.UpdateInstrumentRequest)

  interface instance IUpgradeUpdateInstrumentRequest for CurrentIntegration.UpdateInstrumentRequest where
    view = this
    upgradeUpdateInstrumentRequest = pure

interface IUpgradeInstrument where
  viewtype CurrentIntegration.Instrument

  upgradeInstrument : ContractId IUpgradeInstrument -> Update (ContractId IUpgradeInstrument)

  nonconsuming choice UpgradeInstrument : MigrationResult (ContractId IUpgradeInstrument)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeInstrument contract") $
          migratingParty == (view this).integrationParty
        upgradeInstrument this self

  interface instance IUpgradeInstrument for LegacyIntegration.Instrument where
    view = convert this
    upgradeInstrument self = do
      archive self
      toInterfaceContractId @IUpgradeInstrument <$> create (convert this : CurrentIntegration.Instrument)

  interface instance IUpgradeInstrument for CurrentIntegration.Instrument where
    view = this
    upgradeInstrument = pure

interface IUpgradeFailedInstrumentRequest where
  viewtype CurrentIntegration.FailedInstrumentRequest

  upgradeFailedInstrumentRequest : ContractId IUpgradeFailedInstrumentRequest -> Update (ContractId IUpgradeFailedInstrumentRequest)

  nonconsuming choice UpgradeFailedInstrumentRequest : MigrationResult (ContractId IUpgradeFailedInstrumentRequest)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeFailedInstrumentRequest contract") $
          migratingParty == (view this).integrationParty
        upgradeFailedInstrumentRequest this self

  interface instance IUpgradeFailedInstrumentRequest for LegacyIntegration.FailedInstrumentRequest where
    view = convert this
    upgradeFailedInstrumentRequest self = do
      archive self
      toInterfaceContractId @IUpgradeFailedInstrumentRequest <$> create (convert this : CurrentIntegration.FailedInstrumentRequest)

  interface instance IUpgradeFailedInstrumentRequest for CurrentIntegration.FailedInstrumentRequest where
    view = this
    upgradeFailedInstrumentRequest = pure

interface IUpgradeFailedUpdateInstrumentRequest where
  viewtype CurrentIntegration.FailedUpdateInstrumentRequest

  upgradeFailedUpdateInstrumentRequest : ContractId IUpgradeFailedUpdateInstrumentRequest -> Update (ContractId IUpgradeFailedUpdateInstrumentRequest)

  nonconsuming choice UpgradeFailedUpdateInstrumentRequest : MigrationResult (ContractId IUpgradeFailedUpdateInstrumentRequest)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeFailedUpdateInstrumentRequest contract") $
          migratingParty == (view this).integrationParty
        upgradeFailedUpdateInstrumentRequest this self

  interface instance IUpgradeFailedUpdateInstrumentRequest for LegacyIntegration.FailedUpdateInstrumentRequest where
    view = convert this
    upgradeFailedUpdateInstrumentRequest self = do
      archive self
      toInterfaceContractId @IUpgradeFailedUpdateInstrumentRequest <$> create (convert this : CurrentIntegration.FailedUpdateInstrumentRequest)

  interface instance IUpgradeFailedUpdateInstrumentRequest for CurrentIntegration.FailedUpdateInstrumentRequest where
    view = this
    upgradeFailedUpdateInstrumentRequest = pure

interface IUpgradeExecutionReport where
  viewtype CurrentIntegration.ExecutionReport

  upgradeExecutionReport : ContractId IUpgradeExecutionReport -> Update (ContractId IUpgradeExecutionReport)

  nonconsuming choice UpgradeExecutionReport : MigrationResult (ContractId IUpgradeExecutionReport)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeExecutionReport contract") $
          migratingParty == (view this).integrationParty
        upgradeExecutionReport this self

  interface instance IUpgradeExecutionReport for LegacyIntegration.ExecutionReport where
    view = convert this
    upgradeExecutionReport self = do
      archive self
      toInterfaceContractId @IUpgradeExecutionReport <$> create (convert this : CurrentIntegration.ExecutionReport)

  interface instance IUpgradeExecutionReport for CurrentIntegration.ExecutionReport where
    view = this
    upgradeExecutionReport = pure

interface IUpgradeMassCancelRequest where
  viewtype CurrentIntegration.MassCancelRequest

  upgradeMassCancelRequest : ContractId IUpgradeMassCancelRequest -> Update (ContractId IUpgradeMassCancelRequest)

  nonconsuming choice UpgradeMassCancelRequest : MigrationResult (ContractId IUpgradeMassCancelRequest)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeMassCancelRequest contract") $
          migratingParty == (view this).integrationParty
        upgradeMassCancelRequest this self

  interface instance IUpgradeMassCancelRequest for LegacyIntegration.MassCancelRequest where
    view = convert this
    upgradeMassCancelRequest self = do
      archive self
      toInterfaceContractId @IUpgradeMassCancelRequest <$> create (convert this : CurrentIntegration.MassCancelRequest)

  interface instance IUpgradeMassCancelRequest for CurrentIntegration.MassCancelRequest where
    view = this
    upgradeMassCancelRequest = pure

interface IUpgradeMassCancelSuccess where
  viewtype CurrentIntegration.MassCancelSuccess

  upgradeMassCancelSuccess : ContractId IUpgradeMassCancelSuccess -> Update (ContractId IUpgradeMassCancelSuccess)

  nonconsuming choice UpgradeMassCancelSuccess : MigrationResult (ContractId IUpgradeMassCancelSuccess)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeMassCancelSuccess contract") $
          migratingParty == (view this).integrationParty
        upgradeMassCancelSuccess this self

  interface instance IUpgradeMassCancelSuccess for LegacyIntegration.MassCancelSuccess where
    view = convert this
    upgradeMassCancelSuccess self = do
      archive self
      toInterfaceContractId @IUpgradeMassCancelSuccess <$> create (convert this : CurrentIntegration.MassCancelSuccess)

  interface instance IUpgradeMassCancelSuccess for CurrentIntegration.MassCancelSuccess where
    view = this
    upgradeMassCancelSuccess = pure

interface IUpgradeMassCancelFailure where
  viewtype CurrentIntegration.MassCancelFailure

  upgradeMassCancelFailure : ContractId IUpgradeMassCancelFailure -> Update (ContractId IUpgradeMassCancelFailure)

  nonconsuming choice UpgradeMassCancelFailure : MigrationResult (ContractId IUpgradeMassCancelFailure)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeMassCancelFailure contract") $
          migratingParty == (view this).integrationParty
        upgradeMassCancelFailure this self

  interface instance IUpgradeMassCancelFailure for LegacyIntegration.MassCancelFailure where
    view = convert this
    upgradeMassCancelFailure self = do
      archive self
      toInterfaceContractId @IUpgradeMassCancelFailure <$> create (convert this : CurrentIntegration.MassCancelFailure)

  interface instance IUpgradeMassCancelFailure for CurrentIntegration.MassCancelFailure where
    view = this
    upgradeMassCancelFailure = pure