module Gambyl.Migration.Exberry.IntegrationRollback where

import Gambyl.Migration.Upgrade

import Legacy.Exberry.Integration qualified as LegacyIntegration
import Current.Exberry.Integration qualified as CurrentIntegration

instance DAMLUpgrade CurrentIntegration.OrderData LegacyIntegration.OrderData where
  convert CurrentIntegration.OrderData{..} = LegacyIntegration.OrderData with
    ..
instance DAMLUpgrade CurrentIntegration.NewOrderRequest LegacyIntegration.NewOrderRequest where
  convert CurrentIntegration.NewOrderRequest{..} = LegacyIntegration.NewOrderRequest with
    order = convert order
    ..
instance DAMLUpgrade CurrentIntegration.NewOrderSuccess LegacyIntegration.NewOrderSuccess where
  convert CurrentIntegration.NewOrderSuccess{..} = LegacyIntegration.NewOrderSuccess with
    ..
instance DAMLUpgrade CurrentIntegration.NewOrderFailure LegacyIntegration.NewOrderFailure where
  convert CurrentIntegration.NewOrderFailure{..} = LegacyIntegration.NewOrderFailure with
    ..
instance DAMLUpgrade CurrentIntegration.CancelOrderRequest LegacyIntegration.CancelOrderRequest where
  convert CurrentIntegration.CancelOrderRequest{..} = LegacyIntegration.CancelOrderRequest with
    ..
instance DAMLUpgrade CurrentIntegration.CancelOrderSuccess LegacyIntegration.CancelOrderSuccess where
  convert CurrentIntegration.CancelOrderSuccess{..} = LegacyIntegration.CancelOrderSuccess with
    ..
instance DAMLUpgrade CurrentIntegration.CancelOrderFailure LegacyIntegration.CancelOrderFailure where
  convert CurrentIntegration.CancelOrderFailure{..} = LegacyIntegration.CancelOrderFailure with
    ..
instance DAMLUpgrade CurrentIntegration.CreateInstrumentRequest LegacyIntegration.CreateInstrumentRequest where
  convert CurrentIntegration.CreateInstrumentRequest{..} = LegacyIntegration.CreateInstrumentRequest with
    ..
instance DAMLUpgrade CurrentIntegration.UpdateInstrumentRequest LegacyIntegration.UpdateInstrumentRequest where
  convert CurrentIntegration.UpdateInstrumentRequest{..} = LegacyIntegration.UpdateInstrumentRequest with
    ..
instance DAMLUpgrade CurrentIntegration.Instrument LegacyIntegration.Instrument where
  convert CurrentIntegration.Instrument{..} = LegacyIntegration.Instrument with
    ..
instance DAMLUpgrade CurrentIntegration.FailedInstrumentRequest LegacyIntegration.FailedInstrumentRequest where
  convert CurrentIntegration.FailedInstrumentRequest{..} = LegacyIntegration.FailedInstrumentRequest with
    ..
instance DAMLUpgrade CurrentIntegration.FailedUpdateInstrumentRequest LegacyIntegration.FailedUpdateInstrumentRequest where
  convert CurrentIntegration.FailedUpdateInstrumentRequest{..} = LegacyIntegration.FailedUpdateInstrumentRequest with
    ..
instance DAMLUpgrade CurrentIntegration.ExecutionReport LegacyIntegration.ExecutionReport where
  convert CurrentIntegration.ExecutionReport{..} = LegacyIntegration.ExecutionReport with
    ..
instance DAMLUpgrade CurrentIntegration.MassCancelRequest LegacyIntegration.MassCancelRequest where
  convert CurrentIntegration.MassCancelRequest{..} = LegacyIntegration.MassCancelRequest with
    ..
instance DAMLUpgrade CurrentIntegration.MassCancelSuccess LegacyIntegration.MassCancelSuccess where
  convert CurrentIntegration.MassCancelSuccess{..} = LegacyIntegration.MassCancelSuccess with
    ..
instance DAMLUpgrade CurrentIntegration.MassCancelFailure LegacyIntegration.MassCancelFailure where
  convert CurrentIntegration.MassCancelFailure{..} = LegacyIntegration.MassCancelFailure with
    ..