module Gambyl.Migration.Exberry.Integration where

import Gambyl.Migration.Upgrade

import Legacy.Exberry.Integration qualified as LegacyIntegration
import Current.Exberry.Integration qualified as CurrentIntegration

instance DAMLUpgrade LegacyIntegration.OrderData CurrentIntegration.OrderData where
  convert LegacyIntegration.OrderData{..} = CurrentIntegration.OrderData with
    ..
instance DAMLUpgrade LegacyIntegration.NewOrderRequest CurrentIntegration.NewOrderRequest where
  convert LegacyIntegration.NewOrderRequest{..} = CurrentIntegration.NewOrderRequest with
    order = convert order
    ..
instance DAMLUpgrade LegacyIntegration.NewOrderSuccess CurrentIntegration.NewOrderSuccess where
  convert LegacyIntegration.NewOrderSuccess{..} = CurrentIntegration.NewOrderSuccess with
    ..
instance DAMLUpgrade LegacyIntegration.NewOrderFailure CurrentIntegration.NewOrderFailure where
  convert LegacyIntegration.NewOrderFailure{..} = CurrentIntegration.NewOrderFailure with
    ..
instance DAMLUpgrade LegacyIntegration.CancelOrderRequest CurrentIntegration.CancelOrderRequest where
  convert LegacyIntegration.CancelOrderRequest{..} = CurrentIntegration.CancelOrderRequest with
    ..
instance DAMLUpgrade LegacyIntegration.CancelOrderSuccess CurrentIntegration.CancelOrderSuccess where
  convert LegacyIntegration.CancelOrderSuccess{..} = CurrentIntegration.CancelOrderSuccess with
    ..
instance DAMLUpgrade LegacyIntegration.CancelOrderFailure CurrentIntegration.CancelOrderFailure where
  convert LegacyIntegration.CancelOrderFailure{..} = CurrentIntegration.CancelOrderFailure with
    ..
instance DAMLUpgrade LegacyIntegration.CreateInstrumentRequest CurrentIntegration.CreateInstrumentRequest where
  convert LegacyIntegration.CreateInstrumentRequest{..} = CurrentIntegration.CreateInstrumentRequest with
    ..
instance DAMLUpgrade LegacyIntegration.UpdateInstrumentRequest CurrentIntegration.UpdateInstrumentRequest where
  convert LegacyIntegration.UpdateInstrumentRequest{..} = CurrentIntegration.UpdateInstrumentRequest with
    ..
instance DAMLUpgrade LegacyIntegration.Instrument CurrentIntegration.Instrument where
  convert LegacyIntegration.Instrument{..} = CurrentIntegration.Instrument with
    ..
instance DAMLUpgrade LegacyIntegration.FailedInstrumentRequest CurrentIntegration.FailedInstrumentRequest where
  convert LegacyIntegration.FailedInstrumentRequest{..} = CurrentIntegration.FailedInstrumentRequest with
    ..
instance DAMLUpgrade LegacyIntegration.FailedUpdateInstrumentRequest CurrentIntegration.FailedUpdateInstrumentRequest where
  convert LegacyIntegration.FailedUpdateInstrumentRequest{..} = CurrentIntegration.FailedUpdateInstrumentRequest with
    ..
instance DAMLUpgrade LegacyIntegration.ExecutionReport CurrentIntegration.ExecutionReport where
  convert LegacyIntegration.ExecutionReport{..} = CurrentIntegration.ExecutionReport with
    ..
instance DAMLUpgrade LegacyIntegration.MassCancelRequest CurrentIntegration.MassCancelRequest where
  convert LegacyIntegration.MassCancelRequest{..} = CurrentIntegration.MassCancelRequest with
    ..
instance DAMLUpgrade LegacyIntegration.MassCancelSuccess CurrentIntegration.MassCancelSuccess where
  convert LegacyIntegration.MassCancelSuccess{..} = CurrentIntegration.MassCancelSuccess with
    ..
instance DAMLUpgrade LegacyIntegration.MassCancelFailure CurrentIntegration.MassCancelFailure where
  convert LegacyIntegration.MassCancelFailure{..} = CurrentIntegration.MassCancelFailure with
    ..