module Gambyl.Migration.Script.RollbackScriptFunctions where

import Daml.Script
import Gambyl.Migration.Common

import Gambyl.Migration.Interface.Exberry.IntegrationRollback

upgradeListOfIUpgradeNewOrderRequestRollback : Party -> [ContractId IUpgradeNewOrderRequestRollback] -> Script [MigrationResult (ContractId IUpgradeNewOrderRequestRollback)]
upgradeListOfIUpgradeNewOrderRequestRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeNewOrderRequestRollback with ..

upgradeListOfIUpgradeNewOrderSuccessRollback : Party -> [ContractId IUpgradeNewOrderSuccessRollback] -> Script [MigrationResult (ContractId IUpgradeNewOrderSuccessRollback)]
upgradeListOfIUpgradeNewOrderSuccessRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeNewOrderSuccessRollback with ..

upgradeListOfIUpgradeNewOrderFailureRollback : Party -> [ContractId IUpgradeNewOrderFailureRollback] -> Script [MigrationResult (ContractId IUpgradeNewOrderFailureRollback)]
upgradeListOfIUpgradeNewOrderFailureRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeNewOrderFailureRollback with ..

upgradeListOfIUpgradeCancelOrderRequestRollback : Party -> [ContractId IUpgradeCancelOrderRequestRollback] -> Script [MigrationResult (ContractId IUpgradeCancelOrderRequestRollback)]
upgradeListOfIUpgradeCancelOrderRequestRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeCancelOrderRequestRollback with ..

upgradeListOfIUpgradeCancelOrderSuccessRollback : Party -> [ContractId IUpgradeCancelOrderSuccessRollback] -> Script [MigrationResult (ContractId IUpgradeCancelOrderSuccessRollback)]
upgradeListOfIUpgradeCancelOrderSuccessRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeCancelOrderSuccessRollback with ..

upgradeListOfIUpgradeCancelOrderFailureRollback : Party -> [ContractId IUpgradeCancelOrderFailureRollback] -> Script [MigrationResult (ContractId IUpgradeCancelOrderFailureRollback)]
upgradeListOfIUpgradeCancelOrderFailureRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeCancelOrderFailureRollback with ..

upgradeListOfIUpgradeCreateInstrumentRequestRollback : Party -> [ContractId IUpgradeCreateInstrumentRequestRollback] -> Script [MigrationResult (ContractId IUpgradeCreateInstrumentRequestRollback)]
upgradeListOfIUpgradeCreateInstrumentRequestRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeCreateInstrumentRequestRollback with ..

upgradeListOfIUpgradeUpdateInstrumentRequestRollback : Party -> [ContractId IUpgradeUpdateInstrumentRequestRollback] -> Script [MigrationResult (ContractId IUpgradeUpdateInstrumentRequestRollback)]
upgradeListOfIUpgradeUpdateInstrumentRequestRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeUpdateInstrumentRequestRollback with ..

upgradeListOfIUpgradeInstrumentRollback : Party -> [ContractId IUpgradeInstrumentRollback] -> Script [MigrationResult (ContractId IUpgradeInstrumentRollback)]
upgradeListOfIUpgradeInstrumentRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeInstrumentRollback with ..

upgradeListOfIUpgradeFailedInstrumentRequestRollback : Party -> [ContractId IUpgradeFailedInstrumentRequestRollback] -> Script [MigrationResult (ContractId IUpgradeFailedInstrumentRequestRollback)]
upgradeListOfIUpgradeFailedInstrumentRequestRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeFailedInstrumentRequestRollback with ..

upgradeListOfIUpgradeFailedUpdateInstrumentRequestRollback : Party -> [ContractId IUpgradeFailedUpdateInstrumentRequestRollback] -> Script [MigrationResult (ContractId IUpgradeFailedUpdateInstrumentRequestRollback)]
upgradeListOfIUpgradeFailedUpdateInstrumentRequestRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeFailedUpdateInstrumentRequestRollback with ..

upgradeListOfIUpgradeExecutionReportRollback : Party -> [ContractId IUpgradeExecutionReportRollback] -> Script [MigrationResult (ContractId IUpgradeExecutionReportRollback)]
upgradeListOfIUpgradeExecutionReportRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeExecutionReportRollback with ..

upgradeListOfIUpgradeMassCancelRequestRollback : Party -> [ContractId IUpgradeMassCancelRequestRollback] -> Script [MigrationResult (ContractId IUpgradeMassCancelRequestRollback)]
upgradeListOfIUpgradeMassCancelRequestRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeMassCancelRequestRollback with ..

upgradeListOfIUpgradeMassCancelSuccessRollback : Party -> [ContractId IUpgradeMassCancelSuccessRollback] -> Script [MigrationResult (ContractId IUpgradeMassCancelSuccessRollback)]
upgradeListOfIUpgradeMassCancelSuccessRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeMassCancelSuccessRollback with ..

upgradeListOfIUpgradeMassCancelFailureRollback : Party -> [ContractId IUpgradeMassCancelFailureRollback] -> Script [MigrationResult (ContractId IUpgradeMassCancelFailureRollback)]
upgradeListOfIUpgradeMassCancelFailureRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeMassCancelFailureRollback with ..