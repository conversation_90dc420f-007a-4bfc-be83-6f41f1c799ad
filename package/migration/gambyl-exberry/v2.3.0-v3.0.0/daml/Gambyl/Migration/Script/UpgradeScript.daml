module Gambyl.Migration.Script.UpgradeScript where

import Daml.Script

import Gambyl.Migration.Common
import Gambyl.Test.TestUtils

import Gambyl.Migration.Script.ScriptFunctions

upgradeScript : Input -> Script ReportSummary
upgradeScript Input{gambyl} = script do

  debug "START Upgrading Exberry"

  r1 <- collectReport "NewOrderRequest" $ migrateDirectly "0-IUpgradeNewOrderRequest" gambyl upgradeListOfIUpgradeNewOrderRequest
  r2 <- collectReport "NewOrderSuccess" $ migrateDirectly "1-IUpgradeNewOrderSuccess" gambyl upgradeListOfIUpgradeNewOrderSuccess
  r3 <- collectReport "NewOrderFailure" $ migrateDirectly "2-IUpgradeNewOrderFailure" gambyl upgradeListOfIUpgradeNewOrderFailure
  r4 <- collectReport "CancelOrderRequest" $ migrateDirectly "3-IUpgradeCancelOrderRequest" gambyl upgradeListOfIUpgradeCancelOrderRequest
  r5 <- collectReport "CancelOrderSuccess" $ migrateDirectly "4-IUpgradeCancelOrderSuccess" gambyl upgradeListOfIUpgradeCancelOrderSuccess
  r6 <- collectReport "CancelOrderFailure" $ migrateDirectly "5-IUpgradeCancelOrderFailure" gambyl upgradeListOfIUpgradeCancelOrderFailure
  r7 <- collectReport "CreateInstrumentRequest" $ migrateDirectly "6-IUpgradeCreateInstrumentRequest" gambyl upgradeListOfIUpgradeCreateInstrumentRequest
  r8 <- collectReport "UpdateInstrumentRequest" $ migrateDirectly "7-IUpgradeUpdateInstrumentRequest" gambyl upgradeListOfIUpgradeUpdateInstrumentRequest
  r9 <- collectReport "Instrument" $ migrateDirectly "8-IUpgradeInstrument" gambyl upgradeListOfIUpgradeInstrument
  r10 <- collectReport "FailedInstrumentRequest" $ migrateDirectly "9-IUpgradeFailedInstrumentRequest" gambyl upgradeListOfIUpgradeFailedInstrumentRequest
  r11 <- collectReport "FailedUpdateInstrumentRequest" $ migrateDirectly "10-IUpgradeFailedUpdateInstrumentRequest" gambyl upgradeListOfIUpgradeFailedUpdateInstrumentRequest
  r12 <- collectReport "ExecutionReport" $ migrateDirectly "11-IUpgradeExecutionReport" gambyl upgradeListOfIUpgradeExecutionReport
  r13 <- collectReport "MassCancelRequest" $ migrateDirectly "12-IUpgradeMassCancelRequest" gambyl upgradeListOfIUpgradeMassCancelRequest
  r14 <- collectReport "MassCancelSuccess" $ migrateDirectly "13-IUpgradeMassCancelSuccess" gambyl upgradeListOfIUpgradeMassCancelSuccess
  r15 <- collectReport "MassCancelFailure" $ migrateDirectly "14-IUpgradeMassCancelFailure" gambyl upgradeListOfIUpgradeMassCancelFailure

  let report = foldl combineReport (ReportSummary 0 0 0 0 []) [
          r1, r2, r3, r4, r5, r6, r7, r8, r9,
          r10, r11, r12, r13, r14, r15
        ]

  debug "FINISH Upgrading Exberry"

  pure report
