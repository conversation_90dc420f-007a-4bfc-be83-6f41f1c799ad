module Gambyl.Migration.Script.ScriptFunctions where

import Daml.Script
import Gambyl.Migration.Common

import Gambyl.Migration.Interface.Exberry.Integration

upgradeListOfIUpgradeNewOrderRequest : Party -> [ContractId IUpgradeNewOrderRequest] -> Script [MigrationResult (ContractId IUpgradeNewOrderRequest)]
upgradeListOfIUpgradeNewOrderRequest migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeNewOrderRequest with ..

upgradeListOfIUpgradeNewOrderSuccess : Party -> [ContractId IUpgradeNewOrderSuccess] -> Script [MigrationResult (ContractId IUpgradeNewOrderSuccess)]
upgradeListOfIUpgradeNewOrderSuccess migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeNewOrderSuccess with ..

upgradeListOfIUpgradeNewOrderFailure : Party -> [ContractId IUpgradeNewOrderFailure] -> Script [MigrationResult (ContractId IUpgradeNewOrderFailure)]
upgradeListOfIUpgradeNewOrderFailure migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeNewOrderFailure with ..

upgradeListOfIUpgradeCancelOrderRequest : Party -> [ContractId IUpgradeCancelOrderRequest] -> Script [MigrationResult (ContractId IUpgradeCancelOrderRequest)]
upgradeListOfIUpgradeCancelOrderRequest migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeCancelOrderRequest with ..

upgradeListOfIUpgradeCancelOrderSuccess : Party -> [ContractId IUpgradeCancelOrderSuccess] -> Script [MigrationResult (ContractId IUpgradeCancelOrderSuccess)]
upgradeListOfIUpgradeCancelOrderSuccess migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeCancelOrderSuccess with ..

upgradeListOfIUpgradeCancelOrderFailure : Party -> [ContractId IUpgradeCancelOrderFailure] -> Script [MigrationResult (ContractId IUpgradeCancelOrderFailure)]
upgradeListOfIUpgradeCancelOrderFailure migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeCancelOrderFailure with ..

upgradeListOfIUpgradeCreateInstrumentRequest : Party -> [ContractId IUpgradeCreateInstrumentRequest] -> Script [MigrationResult (ContractId IUpgradeCreateInstrumentRequest)]
upgradeListOfIUpgradeCreateInstrumentRequest migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeCreateInstrumentRequest with ..

upgradeListOfIUpgradeUpdateInstrumentRequest : Party -> [ContractId IUpgradeUpdateInstrumentRequest] -> Script [MigrationResult (ContractId IUpgradeUpdateInstrumentRequest)]
upgradeListOfIUpgradeUpdateInstrumentRequest migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeUpdateInstrumentRequest with ..

upgradeListOfIUpgradeInstrument : Party -> [ContractId IUpgradeInstrument] -> Script [MigrationResult (ContractId IUpgradeInstrument)]
upgradeListOfIUpgradeInstrument migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeInstrument with ..

upgradeListOfIUpgradeFailedInstrumentRequest : Party -> [ContractId IUpgradeFailedInstrumentRequest] -> Script [MigrationResult (ContractId IUpgradeFailedInstrumentRequest)]
upgradeListOfIUpgradeFailedInstrumentRequest migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeFailedInstrumentRequest with ..

upgradeListOfIUpgradeFailedUpdateInstrumentRequest : Party -> [ContractId IUpgradeFailedUpdateInstrumentRequest] -> Script [MigrationResult (ContractId IUpgradeFailedUpdateInstrumentRequest)]
upgradeListOfIUpgradeFailedUpdateInstrumentRequest migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeFailedUpdateInstrumentRequest with ..

upgradeListOfIUpgradeExecutionReport : Party -> [ContractId IUpgradeExecutionReport] -> Script [MigrationResult (ContractId IUpgradeExecutionReport)]
upgradeListOfIUpgradeExecutionReport migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeExecutionReport with ..

upgradeListOfIUpgradeMassCancelRequest : Party -> [ContractId IUpgradeMassCancelRequest] -> Script [MigrationResult (ContractId IUpgradeMassCancelRequest)]
upgradeListOfIUpgradeMassCancelRequest migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeMassCancelRequest with ..

upgradeListOfIUpgradeMassCancelSuccess : Party -> [ContractId IUpgradeMassCancelSuccess] -> Script [MigrationResult (ContractId IUpgradeMassCancelSuccess)]
upgradeListOfIUpgradeMassCancelSuccess migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeMassCancelSuccess with ..

upgradeListOfIUpgradeMassCancelFailure : Party -> [ContractId IUpgradeMassCancelFailure] -> Script [MigrationResult (ContractId IUpgradeMassCancelFailure)]
upgradeListOfIUpgradeMassCancelFailure migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeMassCancelFailure with ..