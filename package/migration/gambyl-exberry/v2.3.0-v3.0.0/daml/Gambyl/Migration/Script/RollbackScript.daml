module Gambyl.Migration.Script.RollbackScript where

import Daml.Script

import Gambyl.Migration.Common
import Gambyl.Test.TestUtils

import Gambyl.Migration.Script.RollbackScriptFunctions

rollbackScript : Input -> Script ReportSummary
rollbackScript Input{exberry} = script do

  debug "START RollingBack Exberry"

  r1 <- collectReport "NewOrderRequestRollback" $ migrateDirectly "0-IUpgradeNewOrderRequestRollback" exberry upgradeListOfIUpgradeNewOrderRequestRollback
  r2 <- collectReport "NewOrderSuccessRollback" $ migrateDirectly "1-IUpgradeNewOrderSuccessRollback" exberry upgradeListOfIUpgradeNewOrderSuccessRollback
  r3 <- collectReport "NewOrderFailureRollback" $ migrateDirectly "2-IUpgradeNewOrderFailureRollback" exberry upgradeListOfIUpgradeNewOrderFailureRollback
  r4 <- collectReport "CancelOrderRequestRollback" $ migrateDirectly "3-IUpgradeCancelOrderRequestRollback" exberry upgradeListOfIUpgradeCancelOrderRequestRollback
  r5 <- collectReport "CancelOrderSuccessRollback" $ migrateDirectly "4-IUpgradeCancelOrderSuccessRollback" exberry upgradeListOfIUpgradeCancelOrderSuccessRollback
  r6 <- collectReport "CancelOrderFailureRollback" $ migrateDirectly "5-IUpgradeCancelOrderFailureRollback" exberry upgradeListOfIUpgradeCancelOrderFailureRollback
  r7 <- collectReport "CreateInstrumentRequestRollback" $ migrateDirectly "6-IUpgradeCreateInstrumentRequestRollback" exberry upgradeListOfIUpgradeCreateInstrumentRequestRollback
  r8 <- collectReport "UpdateInstrumentRequestRollback" $ migrateDirectly "7-IUpgradeUpdateInstrumentRequestRollback" exberry upgradeListOfIUpgradeUpdateInstrumentRequestRollback
  r9 <- collectReport "InstrumentRollback" $ migrateDirectly "8-IUpgradeInstrumentRollback" exberry upgradeListOfIUpgradeInstrumentRollback
  r10 <- collectReport "FailedInstrumentRequestRollback" $ migrateDirectly "9-IUpgradeFailedInstrumentRequestRollback" exberry upgradeListOfIUpgradeFailedInstrumentRequestRollback
  r11 <- collectReport "FailedUpdateInstrumentRequestRollback" $ migrateDirectly "10-IUpgradeFailedUpdateInstrumentRequestRollback" exberry upgradeListOfIUpgradeFailedUpdateInstrumentRequestRollback
  r12 <- collectReport "ExecutionReportRollback" $ migrateDirectly "11-IUpgradeExecutionReportRollback" exberry upgradeListOfIUpgradeExecutionReportRollback
  r13 <- collectReport "MassCancelRequestRollback" $ migrateDirectly "12-IUpgradeMassCancelRequestRollback" exberry upgradeListOfIUpgradeMassCancelRequestRollback
  r14 <- collectReport "MassCancelSuccessRollback" $ migrateDirectly "13-IUpgradeMassCancelSuccessRollback" exberry upgradeListOfIUpgradeMassCancelSuccessRollback
  r15 <- collectReport "MassCancelFailureRollback" $ migrateDirectly "14-IUpgradeMassCancelFailureRollback" exberry upgradeListOfIUpgradeMassCancelFailureRollback

  let report = foldl combineReport (ReportSummary 0 0 0 0 []) [
          r1, r2, r3, r4, r5, r6, r7, r8, r9,
          r10, r11, r12, r13, r14, r15
        ]

  debug "FINISH RollingBack Enetpulse"

  pure report
