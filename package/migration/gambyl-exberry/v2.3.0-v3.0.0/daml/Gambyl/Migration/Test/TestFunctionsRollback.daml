module Gambyl.Migration.Test.TestFunctionsRollback where

import Daml.Script

import Current.Exberry.Integration

{-- CONTRACTS --}

-- | 0-IUpgradeNewOrderRequest
prepareIUpgradeNewOrderRequest : [Party] -> <PERSON>ript (ContractId NewOrderRequest)
prepareIUpgradeNewOrderRequest parties = script do
  let
    [operator, _, _, _] = parties

  let
    integrationParty = operator
    order = OrderData with
        orderType = "orderType"
        instrument = "instrument"
        quantity = 1.0
        price = 10.0
        side = "side"
        timeInForce = "time"
        mpOrderId = 1
        userId = "userId"

  submitMulti [operator] [] $ do
    createCmd NewOrderRequest  with ..

-- | 1-IUpgradeNewOrderSuccess
prepareIUpgradeNewOrderSuccess : [Party] -> Script (ContractId NewOrderSuccess)
prepareIUpgradeNewOrderSuccess parties = script do
  let
    [operator, _, _, _] = parties

  let
    integrationParty = operator
    sid = 1
    orderId = 1

  submitMulti [operator] [] $ do
    createCmd NewOrderSuccess  with ..

-- | 2-IUpgradeNewOrderFailure
prepareIUpgradeNewOrderFailure : [Party] -> Script (ContractId NewOrderFailure)
prepareIUpgradeNewOrderFailure parties = script do
  let
    [operator, _, _, _] = parties

  let
    integrationParty = operator
    sid = 1
    errorCode = 1
    errorMessage = "errorMessage"

  submitMulti [operator] [] $ do
    createCmd NewOrderFailure  with ..

-- | 3-IUpgradeCancelOrderRequest
prepareIUpgradeCancelOrderRequest : [Party] -> Script (ContractId CancelOrderRequest)
prepareIUpgradeCancelOrderRequest parties = script do
  let
    [operator, _, _, _] = parties

  let
    integrationParty = operator
    instrument = "instrument"
    orderId = 1
    userId = "userId"

  submitMulti [operator] [] $ do
    createCmd CancelOrderRequest  with ..

-- | 4-IUpgradeCancelOrderSuccess
prepareIUpgradeCancelOrderSuccess : [Party] -> Script (ContractId CancelOrderSuccess)
prepareIUpgradeCancelOrderSuccess parties = script do
  let
    [operator, _, _, _] = parties

  let
    integrationParty = operator
    sid = 1
    quantity = 10.0

  submitMulti [operator] [] $ do
    createCmd CancelOrderSuccess  with ..

-- | 5-IUpgradeCancelOrderFailure
prepareIUpgradeCancelOrderFailure : [Party] -> Script (ContractId CancelOrderFailure)
prepareIUpgradeCancelOrderFailure parties = script do
  let
    [operator, _, _, _] = parties

  let
    integrationParty = operator
    sid = 1
    errorCode = 1
    errorMessage = "errorMessage"

  submitMulti [operator] [] $ do
    createCmd CancelOrderFailure  with ..

-- | 6-IUpgradeCreateInstrumentRequest
prepareIUpgradeCreateInstrumentRequest : [Party] -> Script (ContractId CreateInstrumentRequest)
prepareIUpgradeCreateInstrumentRequest parties = script do
  let
    [operator, _, _, _] = parties

  let
    integrationParty = operator
    symbol = "symbol"
    quoteCurrency = "quoteCurrency"
    instrumentDescription = "instrumentDescription"
    calendarId = "calendarId"
    pricePrecision = 1
    quantityPrecision = 2
    minQuantity = 1.0
    maxQuantity = 10.0
    status = "status"

  submitMulti [operator] [] $ do
    createCmd CreateInstrumentRequest  with ..

-- | 7-IUpgradeUpdateInstrumentRequest
prepareIUpgradeUpdateInstrumentRequest : [Party] -> Script (ContractId UpdateInstrumentRequest)
prepareIUpgradeUpdateInstrumentRequest parties = script do
  let
    [operator, _, _, _] = parties

  let
    integrationParty = operator
    symbol = "symbol"
    quoteCurrency = "quoteCurrency"
    instrumentDescription = "instrumentDescription"
    calendarId = "calendarId"
    pricePrecision = 1
    quantityPrecision = 2
    minQuantity = 1.0
    maxQuantity = 10.0
    status = "status"
    exberry_instrument_id = "exberry_instrument_id"

  submitMulti [operator] [] $ do
    createCmd UpdateInstrumentRequest  with ..

-- | 8-IUpgradeInstrument
prepareIUpgradeInstrument : [Party] -> Script (ContractId Instrument)
prepareIUpgradeInstrument parties = script do
  let
    [operator, _, _, _] = parties

  let
    integrationParty = operator
    symbol = "symbol"
    quoteCurrency = "quoteCurrency"
    instrumentDescription = "instrumentDescription"
    calendarId = "calendarId"
    pricePrecision = 1
    quantityPrecision = 2
    minQuantity = 1.0
    maxQuantity = 10.0
    status = "status"
    instrumentId = "instrumentId"

  submitMulti [operator] [] $ do
    createCmd Instrument  with ..

-- | 9-IUpgradeFailedInstrumentRequest
prepareIUpgradeFailedInstrumentRequest : [Party] -> Script (ContractId FailedInstrumentRequest)
prepareIUpgradeFailedInstrumentRequest parties = script do
  let
    [operator, _, _, _] = parties

  let
    integrationParty = operator
    symbol = "symbol"
    quoteCurrency = "quoteCurrency"
    instrumentDescription = "instrumentDescription"
    calendarId = "calendarId"
    pricePrecision = 1
    quantityPrecision = 2
    minQuantity = 1.0
    maxQuantity = 10.0
    status = "status"
    message = "message"
    name = "name"
    code = "code"

  submitMulti [operator] [] $ do
    createCmd FailedInstrumentRequest  with ..

-- | 10-IUpgradeFailedUpdateInstrumentRequest
prepareIUpgradeFailedUpdateInstrumentRequest : [Party] -> Script (ContractId FailedUpdateInstrumentRequest)
prepareIUpgradeFailedUpdateInstrumentRequest parties = script do
  let
    [operator, _, _, _] = parties

  let
    integrationParty = operator
    symbol = "symbol"
    quoteCurrency = "quoteCurrency"
    instrumentDescription = "instrumentDescription"
    calendarId = "calendarId"
    pricePrecision = 1
    quantityPrecision = 2
    minQuantity = 1.0
    maxQuantity = 10.0
    message = "message"
    name = "name"
    code = "code"

  submitMulti [operator] [] $ do
    createCmd FailedUpdateInstrumentRequest  with ..

-- | 11-IUpgradeExecutionReport
prepareIUpgradeExecutionReport : [Party] -> Script (ContractId ExecutionReport)
prepareIUpgradeExecutionReport parties = script do
  let
    [operator, _, _, _] = parties

  let
    integrationParty = operator
    sid = 1
    eventId = 1
    eventTimestamp = "eventTimestamp"
    instrument = "instrument"
    trackingNumber = 100
    makerMpId = 1
    makerMpOrderId = 1
    makerOrderId = 1
    takerMpId = 1
    takerMpOrderId = 1
    takerOrderId = 1
    matchId = 1
    executedQuantity = 10.0
    executedPrice = 10.0

  submitMulti [operator] [] $ do
    createCmd ExecutionReport  with ..

-- | 12-IUpgradeMassCancelRequest
prepareIUpgradeMassCancelRequest : [Party] -> Script (ContractId MassCancelRequest)
prepareIUpgradeMassCancelRequest parties = script do
  let
    [operator, _, _, _] = parties

  let
    integrationParty = operator
    sid = "1"
    instrument = "instrument"

  submitMulti [operator] [] $ do
    createCmd MassCancelRequest  with ..

-- | 13-IUpgradeMassCancelSuccess
prepareIUpgradeMassCancelSuccess : [Party] -> Script (ContractId MassCancelSuccess)
prepareIUpgradeMassCancelSuccess parties = script do
  let
    [operator, _, _, _] = parties

  let
    integrationParty = operator
    sid = 1
    numberOfOrders = 2

  submitMulti [operator] [] $ do
    createCmd MassCancelSuccess  with ..

-- | 14-IUpgradeMassCancelFailure
prepareIUpgradeMassCancelFailure : [Party] -> Script (ContractId MassCancelFailure)
prepareIUpgradeMassCancelFailure parties = script do
  let
    [operator, _, _, _] = parties

  let
    integrationParty = operator
    sid = 1
    errorCode = 1
    errorMessage = "errorMessage"

  submitMulti [operator] [] $ do
    createCmd MassCancelFailure  with ..

prepareLedger : Party -> Party -> Script ()
prepareLedger operator exberry = script do
  [customer, other] <- mapA allocateParty ["alice", "bob"]

  let parties = [operator, exberry, customer, other]

  prepareIUpgradeNewOrderRequest parties
  prepareIUpgradeNewOrderSuccess parties
  prepareIUpgradeNewOrderFailure parties
  prepareIUpgradeCancelOrderRequest parties
  prepareIUpgradeCancelOrderSuccess parties
  prepareIUpgradeCancelOrderFailure parties
  prepareIUpgradeCreateInstrumentRequest parties
  prepareIUpgradeUpdateInstrumentRequest parties
  prepareIUpgradeInstrument parties
  prepareIUpgradeFailedInstrumentRequest parties
  prepareIUpgradeFailedUpdateInstrumentRequest parties
  prepareIUpgradeExecutionReport parties
  prepareIUpgradeMassCancelRequest parties
  prepareIUpgradeMassCancelSuccess parties
  prepareIUpgradeMassCancelFailure parties

  pure ()
