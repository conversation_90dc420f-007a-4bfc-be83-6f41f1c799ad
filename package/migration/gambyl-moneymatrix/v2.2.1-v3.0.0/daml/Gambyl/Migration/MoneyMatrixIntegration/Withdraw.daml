module Gambyl.Migration.MoneyMatrixIntegration.Withdraw where

import Gambyl.Migration.Upgrade

import Legacy.MoneyMatrixIntegration.Withdraw qualified as LegacyIntegrationWithdraw
import Current.MoneyMatrixIntegration.Withdraw qualified as CurrentIntegrationWithdraw


instance DAMLUpgrade LegacyIntegrationWithdraw.UserInformation CurrentIntegrationWithdraw.UserInformation where
  convert LegacyIntegrationWithdraw.UserInformation{..} = CurrentIntegrationWithdraw.UserInformation with
    ..

convertInitialWithdrawRequest : LegacyIntegrationWithdraw.InitialWithdrawRequest -> CurrentIntegrationWithdraw.InitialWithdrawRequest
convertInitialWithdrawRequest  LegacyIntegrationWithdraw.InitialWithdrawRequest{..} = CurrentIntegrationWithdraw.InitialWithdrawRequest with
    requestedAmount = convert requestedAmount
    observers = convert observers
    userInformation = convert userInformation
    ..

convertWithdrawRequest : LegacyIntegrationWithdraw.WithdrawRequest -> CurrentIntegrationWithdraw.WithdrawRequest
convertWithdrawRequest  LegacyIntegrationWithdraw.WithdrawRequest{..} = CurrentIntegrationWithdraw.WithdrawRequest with
    requestedAmount = convert requestedAmount
    observers = convert observers
    userInformation = convert userInformation
    ..

instance DAMLUpgrade LegacyIntegrationWithdraw.WithdrawTransaction CurrentIntegrationWithdraw.WithdrawTransaction where
  convert LegacyIntegrationWithdraw.WithdrawTransaction{..} = CurrentIntegrationWithdraw.WithdrawTransaction with
    confirmedAmount = convert confirmedAmount
    observers = convert observers
    userInformation = convert userInformation
    ..

instance DAMLUpgrade LegacyIntegrationWithdraw.WithdrawFailure CurrentIntegrationWithdraw.WithdrawFailure where
  convert LegacyIntegrationWithdraw.WithdrawFailure{..} = CurrentIntegrationWithdraw.WithdrawFailure with
    observers = convert observers
    userInformation = convert userInformation
    ..