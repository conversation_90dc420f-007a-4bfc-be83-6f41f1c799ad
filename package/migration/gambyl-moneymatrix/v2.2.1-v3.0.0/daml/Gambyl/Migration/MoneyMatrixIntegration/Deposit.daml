module Gambyl.Migration.MoneyMatrixIntegration.Deposit where

import Gambyl.Migration.Upgrade

import Legacy.MoneyMatrixIntegration.Deposit qualified as LegacyIntegrationDeposit
import Current.MoneyMatrixIntegration.Deposit qualified as CurrentIntegrationDeposit

instance DAMLUpgrade LegacyIntegrationDeposit.UserInformation CurrentIntegrationDeposit.UserInformation where
  convert LegacyIntegrationDeposit.UserInformation{..} = CurrentIntegrationDeposit.UserInformation with
    ..
convertInitialDepositRequest : LegacyIntegrationDeposit.InitialDepositRequest -> CurrentIntegrationDeposit.InitialDepositRequest
convertInitialDepositRequest  LegacyIntegrationDeposit.InitialDepositRequest{..} = CurrentIntegrationDeposit.InitialDepositRequest with
    requestedAmount = convert requestedAmount
    observers = convert observers
    userInformation = convert userInformation
    ..
convertDepositRequest : LegacyIntegrationDeposit.DepositRequest -> CurrentIntegrationDeposit.DepositRequest
convertDepositRequest  LegacyIntegrationDeposit.DepositRequest{..} = CurrentIntegrationDeposit.DepositRequest with
    requestedAmount = convert requestedAmount
    observers = convert observers
    userInformation = convert userInformation
    ..
instance DAMLUpgrade LegacyIntegrationDeposit.DepositTransaction CurrentIntegrationDeposit.DepositTransaction where
  convert LegacyIntegrationDeposit.DepositTransaction{..} = CurrentIntegrationDeposit.DepositTransaction with
    confirmedAmount = convert confirmedAmount
    observers = convert observers
    userInformation = convert userInformation
    ..
instance DAMLUpgrade LegacyIntegrationDeposit.DepositFailure CurrentIntegrationDeposit.DepositFailure where
  convert LegacyIntegrationDeposit.DepositFailure{..} = CurrentIntegrationDeposit.DepositFailure with
    observers = convert observers
    userInformation = convert userInformation
    ..