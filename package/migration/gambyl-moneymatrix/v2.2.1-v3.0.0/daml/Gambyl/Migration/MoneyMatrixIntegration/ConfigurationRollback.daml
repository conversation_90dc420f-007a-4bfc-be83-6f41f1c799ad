module Gambyl.Migration.MoneyMatrixIntegration.ConfigurationRollback where

import Gambyl.Migration.Upgrade

import Legacy.MoneyMatrixIntegration.Configuration qualified as LegacyIntegrationConfiguration
import Current.MoneyMatrixIntegration.Configuration qualified as CurrentIntegrationConfiguration

instance DAMLUpgrade CurrentIntegrationConfiguration.Configuration LegacyIntegrationConfiguration.Configuration where
  convert CurrentIntegrationConfiguration.Configuration{..} = LegacyIntegrationConfiguration.Configuration with
    ..