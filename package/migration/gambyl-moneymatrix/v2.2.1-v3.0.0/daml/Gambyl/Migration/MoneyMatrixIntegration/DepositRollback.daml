module Gambyl.Migration.MoneyMatrixIntegration.DepositRollback where

import Gambyl.Migration.Upgrade

import Legacy.MoneyMatrixIntegration.Deposit qualified as LegacyIntegrationDeposit
import Current.MoneyMatrixIntegration.Deposit qualified as CurrentIntegrationDeposit

instance DAMLUpgrade CurrentIntegrationDeposit.UserInformation LegacyIntegrationDeposit.UserInformation where
  convert CurrentIntegrationDeposit.UserInformation{..} = LegacyIntegrationDeposit.UserInformation with
    ..

convertInitialDepositRequestRollback : CurrentIntegrationDeposit.InitialDepositRequest -> LegacyIntegrationDeposit.InitialDepositRequest
convertInitialDepositRequestRollback  CurrentIntegrationDeposit.InitialDepositRequest{..} = LegacyIntegrationDeposit.InitialDepositRequest with
    requestedAmount = convert requestedAmount
    observers = convert observers
    userInformation = convert userInformation
    ..

convertDepositRequestRollback : CurrentIntegrationDeposit.DepositRequest -> LegacyIntegrationDeposit.DepositRequest
convertDepositRequestRollback  CurrentIntegrationDeposit.DepositRequest{..} = LegacyIntegrationDeposit.DepositRequest with
    requestedAmount = convert requestedAmount
    observers = convert observers
    userInformation = convert userInformation
    ..

instance DAMLUpgrade CurrentIntegrationDeposit.DepositTransaction LegacyIntegrationDeposit.DepositTransaction where
  convert CurrentIntegrationDeposit.DepositTransaction{..} = LegacyIntegrationDeposit.DepositTransaction with
    confirmedAmount = convert confirmedAmount
    observers = convert observers
    userInformation = convert userInformation
    ..

instance DAMLUpgrade CurrentIntegrationDeposit.DepositFailure LegacyIntegrationDeposit.DepositFailure where
  convert CurrentIntegrationDeposit.DepositFailure{..} = LegacyIntegrationDeposit.DepositFailure with
    observers = convert observers
    userInformation = convert userInformation
    ..