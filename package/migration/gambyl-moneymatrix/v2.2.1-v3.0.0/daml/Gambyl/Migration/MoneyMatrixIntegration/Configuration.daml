module Gambyl.Migration.MoneyMatrixIntegration.Configuration where

import Gambyl.Migration.Upgrade



import Legacy.MoneyMatrixIntegration.Configuration qualified as LegacyIntegrationConfiguration
import Current.MoneyMatrixIntegration.Configuration qualified as CurrentIntegrationConfiguration

instance DAMLUpgrade LegacyIntegrationConfiguration.Configuration CurrentIntegrationConfiguration.Configuration where
  convert LegacyIntegrationConfiguration.Configuration{..} = CurrentIntegrationConfiguration.Configuration with
    ..