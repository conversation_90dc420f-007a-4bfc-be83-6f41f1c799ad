module Gambyl.Migration.MoneyMatrixIntegration.WithdrawRollback where

import Gambyl.Migration.Upgrade

import Legacy.MoneyMatrixIntegration.Withdraw qualified as LegacyIntegrationWithdraw
import Current.MoneyMatrixIntegration.Withdraw qualified as CurrentIntegrationWithdraw

instance DAMLUpgrade CurrentIntegrationWithdraw.UserInformation LegacyIntegrationWithdraw.UserInformation where
  convert CurrentIntegrationWithdraw.UserInformation{..} = LegacyIntegrationWithdraw.UserInformation with
    ..

convertInitialWithdrawRequestRollback : CurrentIntegrationWithdraw.InitialWithdrawRequest -> LegacyIntegrationWithdraw.InitialWithdrawRequest
convertInitialWithdrawRequestRollback  CurrentIntegrationWithdraw.InitialWithdrawRequest{..} = LegacyIntegrationWithdraw.InitialWithdrawRequest with
    requestedAmount = convert requestedAmount
    observers = convert observers
    userInformation = convert userInformation
    ..

convertWithdrawRequestRollback : CurrentIntegrationWithdraw.WithdrawRequest -> LegacyIntegrationWithdraw.WithdrawRequest
convertWithdrawRequestRollback  CurrentIntegrationWithdraw.WithdrawRequest{..} = LegacyIntegrationWithdraw.WithdrawRequest with
    requestedAmount = convert requestedAmount
    observers = convert observers
    userInformation = convert userInformation
    ..

instance DAMLUpgrade CurrentIntegrationWithdraw.WithdrawTransaction LegacyIntegrationWithdraw.WithdrawTransaction where
  convert CurrentIntegrationWithdraw.WithdrawTransaction{..} = LegacyIntegrationWithdraw.WithdrawTransaction with
    confirmedAmount = convert confirmedAmount
    observers = convert observers
    userInformation = convert userInformation
    ..

instance DAMLUpgrade CurrentIntegrationWithdraw.WithdrawFailure LegacyIntegrationWithdraw.WithdrawFailure where
  convert CurrentIntegrationWithdraw.WithdrawFailure{..} = LegacyIntegrationWithdraw.WithdrawFailure with
    observers = convert observers
    userInformation = convert userInformation
    ..