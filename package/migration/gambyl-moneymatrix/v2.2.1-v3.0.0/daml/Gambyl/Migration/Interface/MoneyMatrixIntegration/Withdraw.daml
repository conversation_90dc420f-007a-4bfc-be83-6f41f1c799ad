module Gambyl.Migration.Interface.MoneyMatrixIntegration.Withdraw where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.MoneyMatrixIntegration.Withdraw (convertInitialWithdrawRequest, convertWithdrawRequest)

import Legacy.MoneyMatrixIntegration.Withdraw qualified as LegacyIntegrationWithdraw
import Current.MoneyMatrixIntegration.Withdraw qualified as CurrentIntegrationWithdraw
import DA.List (elemIndex)
import DA.Optional (isSome)

interface IUpgradeInitialWithdrawRequest where
  viewtype CurrentIntegrationWithdraw.InitialWithdrawRequest

  upgradeInitialWithdrawRequest : ContractId IUpgradeInitialWithdrawRequest -> Update (ContractId IUpgradeInitialWithdrawRequest)

  nonconsuming choice UpgradeInitialWithdrawRequest : MigrationResult (ContractId IUpgradeInitialWithdrawRequest)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory or an observer of this IUpgradeInitialWithdrawRequest contract") $
          migratingParty == (view this).integrationParty
          || isSome (elemIndex migratingParty ((view this).observers))
        upgradeInitialWithdrawRequest this self

  interface instance IUpgradeInitialWithdrawRequest for LegacyIntegrationWithdraw.InitialWithdrawRequest where
    view = convertInitialWithdrawRequest this
    upgradeInitialWithdrawRequest self = do
      archive self
      toInterfaceContractId @IUpgradeInitialWithdrawRequest <$> create (convertInitialWithdrawRequest this : CurrentIntegrationWithdraw.InitialWithdrawRequest)

  interface instance IUpgradeInitialWithdrawRequest for CurrentIntegrationWithdraw.InitialWithdrawRequest where
    view = this
    upgradeInitialWithdrawRequest = pure

interface IUpgradeWithdrawRequest where
  viewtype CurrentIntegrationWithdraw.WithdrawRequest

  upgradeWithdrawRequest : ContractId IUpgradeWithdrawRequest -> Update (ContractId IUpgradeWithdrawRequest)

  nonconsuming choice UpgradeWithdrawRequest : MigrationResult (ContractId IUpgradeWithdrawRequest)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory or an observer of this IUpgradeWithdrawRequest contract") $
          migratingParty == (view this).integrationParty
          || isSome (elemIndex migratingParty ((view this).observers))
        upgradeWithdrawRequest this self

  interface instance IUpgradeWithdrawRequest for LegacyIntegrationWithdraw.WithdrawRequest where
    view = convertWithdrawRequest this
    upgradeWithdrawRequest self = do
      archive self
      toInterfaceContractId @IUpgradeWithdrawRequest <$> create (convertWithdrawRequest this : CurrentIntegrationWithdraw.WithdrawRequest)

  interface instance IUpgradeWithdrawRequest for CurrentIntegrationWithdraw.WithdrawRequest where
    view = this
    upgradeWithdrawRequest = pure

interface IUpgradeWithdrawTransaction where
  viewtype CurrentIntegrationWithdraw.WithdrawTransaction

  upgradeWithdrawTransaction : ContractId IUpgradeWithdrawTransaction -> Update (ContractId IUpgradeWithdrawTransaction)

  nonconsuming choice UpgradeWithdrawTransaction : MigrationResult (ContractId IUpgradeWithdrawTransaction)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory or an observer of this IUpgradeWithdrawTransaction contract") $
          migratingParty == (view this).integrationParty
          || isSome (elemIndex migratingParty ((view this).observers))
        upgradeWithdrawTransaction this self

  interface instance IUpgradeWithdrawTransaction for LegacyIntegrationWithdraw.WithdrawTransaction where
    view = convert this
    upgradeWithdrawTransaction self = do
      archive self
      toInterfaceContractId @IUpgradeWithdrawTransaction <$> create (convert this : CurrentIntegrationWithdraw.WithdrawTransaction)

  interface instance IUpgradeWithdrawTransaction for CurrentIntegrationWithdraw.WithdrawTransaction where
    view = this
    upgradeWithdrawTransaction = pure

interface IUpgradeWithdrawFailure where
  viewtype CurrentIntegrationWithdraw.WithdrawFailure

  upgradeWithdrawFailure : ContractId IUpgradeWithdrawFailure -> Update (ContractId IUpgradeWithdrawFailure)

  nonconsuming choice UpgradeWithdrawFailure : MigrationResult (ContractId IUpgradeWithdrawFailure)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory or an observer of this IUpgradeWithdrawFailure contract") $
          migratingParty == (view this).integrationParty
          || isSome (elemIndex migratingParty ((view this).observers))
        upgradeWithdrawFailure this self

  interface instance IUpgradeWithdrawFailure for LegacyIntegrationWithdraw.WithdrawFailure where
    view = convert this
    upgradeWithdrawFailure self = do
      archive self
      toInterfaceContractId @IUpgradeWithdrawFailure <$> create (convert this : CurrentIntegrationWithdraw.WithdrawFailure)

  interface instance IUpgradeWithdrawFailure for CurrentIntegrationWithdraw.WithdrawFailure where
    view = this
    upgradeWithdrawFailure = pure