module Gambyl.Migration.Interface.MoneyMatrixIntegration.Configuration where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.MoneyMatrixIntegration.Configuration ()

import Legacy.MoneyMatrixIntegration.Configuration qualified as LegacyIntegrationConfiguration
import Current.MoneyMatrixIntegration.Configuration qualified as CurrentIntegrationConfiguration

interface IUpgradeConfiguration where
  viewtype CurrentIntegrationConfiguration.Configuration

  upgradeConfiguration : ContractId IUpgradeConfiguration -> Update (ContractId IUpgradeConfiguration)

  nonconsuming choice UpgradeConfiguration : MigrationResult (ContractId IUpgradeConfiguration)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeConfiguration contract") $
          migratingParty == (view this).integrationParty
        upgradeConfiguration this self

  interface instance IUpgradeConfiguration for LegacyIntegrationConfiguration.Configuration where
    view = convert this
    upgradeConfiguration self = do
      archive self
      toInterfaceContractId @IUpgradeConfiguration <$> create (convert this : CurrentIntegrationConfiguration.Configuration)

  interface instance IUpgradeConfiguration for CurrentIntegrationConfiguration.Configuration where
    view = this
    upgradeConfiguration = pure