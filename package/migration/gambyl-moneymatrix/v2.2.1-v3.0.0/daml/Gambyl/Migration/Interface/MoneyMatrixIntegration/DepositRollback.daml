module Gambyl.Migration.Interface.MoneyMatrixIntegration.DepositRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.MoneyMatrixIntegration.DepositRollback (convertDepositRequestRollback, convertInitialDepositRequestRollback)

import Legacy.MoneyMatrixIntegration.Deposit qualified as LegacyIntegrationDeposit
import Current.MoneyMatrixIntegration.Deposit qualified as CurrentIntegrationDeposit
import DA.Optional (isSome)
import DA.List (elemIndex)

interface IUpgradeInitialDepositRequestRollback where
  viewtype LegacyIntegrationDeposit.InitialDepositRequest

  upgradeInitialDepositRequestRollback : ContractId IUpgradeInitialDepositRequestRollback -> Update (ContractId IUpgradeInitialDepositRequestRollback)

  nonconsuming choice UpgradeInitialDepositRequestRollback : MigrationResult (ContractId IUpgradeInitialDepositRequestRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be the signatory or an observer integrationParty of this IUpgradeInitialDepositRequestRollback contract") $
          migratingParty == (view this).integrationParty
          || isSome (elemIndex migratingParty ((view this).observers))
        upgradeInitialDepositRequestRollback this self

  interface instance IUpgradeInitialDepositRequestRollback for CurrentIntegrationDeposit.InitialDepositRequest where
    view = convertInitialDepositRequestRollback this
    upgradeInitialDepositRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeInitialDepositRequestRollback <$> create (convertInitialDepositRequestRollback this : LegacyIntegrationDeposit.InitialDepositRequest)

  interface instance IUpgradeInitialDepositRequestRollback for LegacyIntegrationDeposit.InitialDepositRequest where
    view = this
    upgradeInitialDepositRequestRollback = pure

interface IUpgradeDepositRequestRollback where
  viewtype LegacyIntegrationDeposit.DepositRequest

  upgradeDepositRequestRollback : ContractId IUpgradeDepositRequestRollback -> Update (ContractId IUpgradeDepositRequestRollback)

  nonconsuming choice UpgradeDepositRequestRollback : MigrationResult (ContractId IUpgradeDepositRequestRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory or an observer of this IUpgradeDepositRequestRollback contract") $
          migratingParty == (view this).integrationParty
          || isSome (elemIndex migratingParty ((view this).observers))
        upgradeDepositRequestRollback this self

  interface instance IUpgradeDepositRequestRollback for CurrentIntegrationDeposit.DepositRequest where
    view = convertDepositRequestRollback this
    upgradeDepositRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeDepositRequestRollback <$> create (convertDepositRequestRollback this : LegacyIntegrationDeposit.DepositRequest)

  interface instance IUpgradeDepositRequestRollback for LegacyIntegrationDeposit.DepositRequest where
    view = this
    upgradeDepositRequestRollback = pure

interface IUpgradeDepositTransactionRollback where
  viewtype LegacyIntegrationDeposit.DepositTransaction

  upgradeDepositTransactionRollback : ContractId IUpgradeDepositTransactionRollback -> Update (ContractId IUpgradeDepositTransactionRollback)

  nonconsuming choice UpgradeDepositTransactionRollback : MigrationResult (ContractId IUpgradeDepositTransactionRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory or an observer of this IUpgradeDepositTransactionRollback contract") $
          migratingParty == (view this).integrationParty
          || isSome (elemIndex migratingParty ((view this).observers))
        upgradeDepositTransactionRollback this self

  interface instance IUpgradeDepositTransactionRollback for CurrentIntegrationDeposit.DepositTransaction where
    view = convert this
    upgradeDepositTransactionRollback self = do
      archive self
      toInterfaceContractId @IUpgradeDepositTransactionRollback <$> create (convert this : LegacyIntegrationDeposit.DepositTransaction)

  interface instance IUpgradeDepositTransactionRollback for LegacyIntegrationDeposit.DepositTransaction where
    view = this
    upgradeDepositTransactionRollback = pure

interface IUpgradeDepositFailureRollback where
  viewtype LegacyIntegrationDeposit.DepositFailure

  upgradeDepositFailureRollback : ContractId IUpgradeDepositFailureRollback -> Update (ContractId IUpgradeDepositFailureRollback)

  nonconsuming choice UpgradeDepositFailureRollback : MigrationResult (ContractId IUpgradeDepositFailureRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory or an observer of this IUpgradeDepositFailureRollback contract") $
          migratingParty == (view this).integrationParty
          || isSome (elemIndex migratingParty ((view this).observers))
        upgradeDepositFailureRollback this self

  interface instance IUpgradeDepositFailureRollback for CurrentIntegrationDeposit.DepositFailure where
    view = convert this
    upgradeDepositFailureRollback self = do
      archive self
      toInterfaceContractId @IUpgradeDepositFailureRollback <$> create (convert this : LegacyIntegrationDeposit.DepositFailure)

  interface instance IUpgradeDepositFailureRollback for LegacyIntegrationDeposit.DepositFailure where
    view = this
    upgradeDepositFailureRollback = pure