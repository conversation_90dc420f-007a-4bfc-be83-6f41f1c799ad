module Gambyl.Migration.Interface.MoneyMatrixIntegration.Deposit where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.MoneyMatrixIntegration.Deposit (convertDepositRequest, convertInitialDepositRequest)

import Legacy.MoneyMatrixIntegration.Deposit qualified as LegacyIntegrationDeposit
import Current.MoneyMatrixIntegration.Deposit qualified as CurrentIntegrationDeposit
import DA.Optional (isSome)
import DA.List (elemIndex)

interface IUpgradeInitialDepositRequest where
  viewtype CurrentIntegrationDeposit.InitialDepositRequest

  upgradeInitialDepositRequest : ContractId IUpgradeInitialDepositRequest -> Update (ContractId IUpgradeInitialDepositRequest)

  nonconsuming choice UpgradeInitialDepositRequest : MigrationResult (ContractId IUpgradeInitialDepositRequest)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be the integrationParty or an observer of this IUpgradeInitialDepositRequest contract") $
          migratingParty == (view this).integrationParty
          || isSome (elemIndex migratingParty ((view this).observers))
        upgradeInitialDepositRequest this self

  interface instance IUpgradeInitialDepositRequest for LegacyIntegrationDeposit.InitialDepositRequest where
    view = convertInitialDepositRequest this
    upgradeInitialDepositRequest self = do
      archive self
      toInterfaceContractId @IUpgradeInitialDepositRequest <$> create (convertInitialDepositRequest this : CurrentIntegrationDeposit.InitialDepositRequest)

  interface instance IUpgradeInitialDepositRequest for CurrentIntegrationDeposit.InitialDepositRequest where
    view = this
    upgradeInitialDepositRequest = pure

interface IUpgradeDepositRequest where
  viewtype CurrentIntegrationDeposit.DepositRequest

  upgradeDepositRequest : ContractId IUpgradeDepositRequest -> Update (ContractId IUpgradeDepositRequest)

  nonconsuming choice UpgradeDepositRequest : MigrationResult (ContractId IUpgradeDepositRequest)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory or an observer of this IUpgradeDepositRequest contract") $
          migratingParty == (view this).integrationParty
          || isSome (elemIndex migratingParty ((view this).observers))
        upgradeDepositRequest this self

  interface instance IUpgradeDepositRequest for LegacyIntegrationDeposit.DepositRequest where
    view = convertDepositRequest this
    upgradeDepositRequest self = do
      archive self
      toInterfaceContractId @IUpgradeDepositRequest <$> create (convertDepositRequest this : CurrentIntegrationDeposit.DepositRequest)

  interface instance IUpgradeDepositRequest for CurrentIntegrationDeposit.DepositRequest where
    view = this
    upgradeDepositRequest = pure

interface IUpgradeDepositTransaction where
  viewtype CurrentIntegrationDeposit.DepositTransaction

  upgradeDepositTransaction : ContractId IUpgradeDepositTransaction -> Update (ContractId IUpgradeDepositTransaction)

  nonconsuming choice UpgradeDepositTransaction : MigrationResult (ContractId IUpgradeDepositTransaction)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory or an observer of this IUpgradeDepositTransaction contract") $
          migratingParty == (view this).integrationParty
          || isSome (elemIndex migratingParty ((view this).observers))
        upgradeDepositTransaction this self

  interface instance IUpgradeDepositTransaction for LegacyIntegrationDeposit.DepositTransaction where
    view = convert this
    upgradeDepositTransaction self = do
      archive self
      toInterfaceContractId @IUpgradeDepositTransaction <$> create (convert this : CurrentIntegrationDeposit.DepositTransaction)

  interface instance IUpgradeDepositTransaction for CurrentIntegrationDeposit.DepositTransaction where
    view = this
    upgradeDepositTransaction = pure

interface IUpgradeDepositFailure where
  viewtype CurrentIntegrationDeposit.DepositFailure

  upgradeDepositFailure : ContractId IUpgradeDepositFailure -> Update (ContractId IUpgradeDepositFailure)

  nonconsuming choice UpgradeDepositFailure : MigrationResult (ContractId IUpgradeDepositFailure)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory or an observer of this IUpgradeDepositFailure contract") $
          migratingParty == (view this).integrationParty
          || isSome (elemIndex migratingParty ((view this).observers))
        upgradeDepositFailure this self

  interface instance IUpgradeDepositFailure for LegacyIntegrationDeposit.DepositFailure where
    view = convert this
    upgradeDepositFailure self = do
      archive self
      toInterfaceContractId @IUpgradeDepositFailure <$> create (convert this : CurrentIntegrationDeposit.DepositFailure)

  interface instance IUpgradeDepositFailure for CurrentIntegrationDeposit.DepositFailure where
    view = this
    upgradeDepositFailure = pure