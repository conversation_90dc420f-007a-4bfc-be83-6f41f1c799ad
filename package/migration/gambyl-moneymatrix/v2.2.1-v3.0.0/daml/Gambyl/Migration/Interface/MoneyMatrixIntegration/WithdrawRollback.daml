module Gambyl.Migration.Interface.MoneyMatrixIntegration.WithdrawRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.MoneyMatrixIntegration.WithdrawRollback (convertInitialWithdrawRequestRollback, convertWithdrawRequestRollback)

import Legacy.MoneyMatrixIntegration.Withdraw qualified as LegacyIntegrationWithdraw
import Current.MoneyMatrixIntegration.Withdraw qualified as CurrentIntegrationWithdraw
import DA.Optional (isSome)
import DA.List (elemIndex)

interface IUpgradeInitialWithdrawRequestRollback where
  viewtype LegacyIntegrationWithdraw.InitialWithdrawRequest

  upgradeInitialWithdrawRequestRollback : ContractId IUpgradeInitialWithdrawRequestRollback -> Update (ContractId IUpgradeInitialWithdrawRequestRollback)

  nonconsuming choice UpgradeInitialWithdrawRequestRollback : MigrationResult (ContractId IUpgradeInitialWithdrawRequestRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory or an observer of this IUpgradeInitialWithdrawRequestRollback contract") $
          migratingParty == (view this).integrationParty
          || isSome (elemIndex migratingParty ((view this).observers))
        upgradeInitialWithdrawRequestRollback this self

  interface instance IUpgradeInitialWithdrawRequestRollback for CurrentIntegrationWithdraw.InitialWithdrawRequest where
    view = convertInitialWithdrawRequestRollback this
    upgradeInitialWithdrawRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeInitialWithdrawRequestRollback <$> create (convertInitialWithdrawRequestRollback this : LegacyIntegrationWithdraw.InitialWithdrawRequest)

  interface instance IUpgradeInitialWithdrawRequestRollback for LegacyIntegrationWithdraw.InitialWithdrawRequest where
    view = this
    upgradeInitialWithdrawRequestRollback = pure

interface IUpgradeWithdrawRequestRollback where
  viewtype LegacyIntegrationWithdraw.WithdrawRequest

  upgradeWithdrawRequestRollback : ContractId IUpgradeWithdrawRequestRollback -> Update (ContractId IUpgradeWithdrawRequestRollback)

  nonconsuming choice UpgradeWithdrawRequestRollback : MigrationResult (ContractId IUpgradeWithdrawRequestRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory or an observer of this IUpgradeWithdrawRequestRollback contract") $
          migratingParty == (view this).integrationParty
          || isSome (elemIndex migratingParty ((view this).observers))
        upgradeWithdrawRequestRollback this self

  interface instance IUpgradeWithdrawRequestRollback for CurrentIntegrationWithdraw.WithdrawRequest where
    view = convertWithdrawRequestRollback this
    upgradeWithdrawRequestRollback self = do
      archive self
      toInterfaceContractId @IUpgradeWithdrawRequestRollback <$> create (convertWithdrawRequestRollback this : LegacyIntegrationWithdraw.WithdrawRequest)

  interface instance IUpgradeWithdrawRequestRollback for LegacyIntegrationWithdraw.WithdrawRequest where
    view = this
    upgradeWithdrawRequestRollback = pure

interface IUpgradeWithdrawTransactionRollback where
  viewtype LegacyIntegrationWithdraw.WithdrawTransaction

  upgradeWithdrawTransactionRollback : ContractId IUpgradeWithdrawTransactionRollback -> Update (ContractId IUpgradeWithdrawTransactionRollback)

  nonconsuming choice UpgradeWithdrawTransactionRollback : MigrationResult (ContractId IUpgradeWithdrawTransactionRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory or an observer of this IUpgradeWithdrawTransactionRollback contract") $
          migratingParty == (view this).integrationParty
          || isSome (elemIndex migratingParty ((view this).observers))
        upgradeWithdrawTransactionRollback this self

  interface instance IUpgradeWithdrawTransactionRollback for CurrentIntegrationWithdraw.WithdrawTransaction where
    view = convert this
    upgradeWithdrawTransactionRollback self = do
      archive self
      toInterfaceContractId @IUpgradeWithdrawTransactionRollback <$> create (convert this : LegacyIntegrationWithdraw.WithdrawTransaction)

  interface instance IUpgradeWithdrawTransactionRollback for LegacyIntegrationWithdraw.WithdrawTransaction where
    view = this
    upgradeWithdrawTransactionRollback = pure

interface IUpgradeWithdrawFailureRollback where
  viewtype LegacyIntegrationWithdraw.WithdrawFailure

  upgradeWithdrawFailureRollback : ContractId IUpgradeWithdrawFailureRollback -> Update (ContractId IUpgradeWithdrawFailureRollback)

  nonconsuming choice UpgradeWithdrawFailureRollback : MigrationResult (ContractId IUpgradeWithdrawFailureRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory or an observer of this IUpgradeWithdrawFailureRollback contract") $
          migratingParty == (view this).integrationParty
          || isSome (elemIndex migratingParty ((view this).observers))
        upgradeWithdrawFailureRollback this self

  interface instance IUpgradeWithdrawFailureRollback for CurrentIntegrationWithdraw.WithdrawFailure where
    view = convert this
    upgradeWithdrawFailureRollback self = do
      archive self
      toInterfaceContractId @IUpgradeWithdrawFailureRollback <$> create (convert this : LegacyIntegrationWithdraw.WithdrawFailure)

  interface instance IUpgradeWithdrawFailureRollback for LegacyIntegrationWithdraw.WithdrawFailure where
    view = this
    upgradeWithdrawFailureRollback = pure