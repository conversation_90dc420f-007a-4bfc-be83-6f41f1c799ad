module Gambyl.Migration.Interface.MoneyMatrixIntegration.ConfigurationRollback where

import Gambyl.Migration.Upgrade
import Gambyl.Migration.Common

import Gambyl.Migration.MoneyMatrixIntegration.ConfigurationRollback ()

import Legacy.MoneyMatrixIntegration.Configuration qualified as LegacyIntegrationConfiguration
import Current.MoneyMatrixIntegration.Configuration qualified as CurrentIntegrationConfiguration

interface IUpgradeConfigurationRollback where
  viewtype LegacyIntegrationConfiguration.Configuration

  upgradeConfigurationRollback : ContractId IUpgradeConfigurationRollback -> Update (ContractId IUpgradeConfigurationRollback)

  nonconsuming choice UpgradeConfigurationRollback : MigrationResult (ContractId IUpgradeConfigurationRollback)
    with
      migratingParty : Party
    controller migratingParty
    do withErrorHandling $ do
        assertMsg ("Migrating party " <> (partyToText migratingParty) <> " must be a signatory of this IUpgradeConfigurationRollback contract") $
          migratingParty == (view this).integrationParty
        upgradeConfigurationRollback this self

  interface instance IUpgradeConfigurationRollback for CurrentIntegrationConfiguration.Configuration where
    view = convert this
    upgradeConfigurationRollback self = do
      archive self
      toInterfaceContractId @IUpgradeConfigurationRollback <$> create (convert this : LegacyIntegrationConfiguration.Configuration)

  interface instance IUpgradeConfigurationRollback for LegacyIntegrationConfiguration.Configuration where
    view = this
    upgradeConfigurationRollback = pure