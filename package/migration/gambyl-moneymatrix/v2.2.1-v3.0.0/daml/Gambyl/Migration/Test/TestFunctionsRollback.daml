module Gambyl.Migration.Test.TestFunctionsRollback where

import Current.MoneyMatrixIntegration.Configuration qualified as MoneyMatrixConfiguration
import Current.MoneyMatrixIntegration.Deposit qualified as MoneyMatrixDeposit
import Current.MoneyMatrixIntegration.Withdraw qualified as MoneyMatrixWithdraw

import Daml.Script
import DA.Date (Month(Jan), date)

-- | 0-IUpgradeConfiguration
prepareIUpgradeConfiguration : [Party] -> <PERSON><PERSON><PERSON> (ContractId MoneyMatrixConfiguration.Configuration)
prepareIUpgradeConfiguration parties = script do
    let
        [_, moneymatrix, _, _] = parties

        integrationParty = moneymatrix
        merchantId = "MerchantId"
        merchantKey = "MerchantKey"

    submit integrationParty do createCmd MoneyMatrixConfiguration.Configuration with ..

-- | 1-IUpgradeInitialDepositRequest
prepareIUpgradeInitialDepositRequest : [Party] -> <PERSON>ript (ContractId MoneyMatrixDeposit.InitialDepositRequest)
prepareIUpgradeInitialDepositRequest parties = script do
    let
        [operator, moneymatrix, customer, _] = parties

        client = customer
        integrationParty = moneymatrix
        requestedAmount = 100.0
        currency = "USD"
        observers = [operator]
        transactionId = "Transaction Id"
        userInformation = MoneyMatrixDeposit.UserInformation with
            firstName = "First Name"
            lastName = "Last Name"
            emailAddress = "Email Address"
            phoneNumber = "Phone Number"
            birthday = date 1990 Jan 10
            countryCode = "Country Code"
            city = "City"
            postalCode = "Postal Code"
            subDivision = "Sub Division"
            addressLine1 = "Address Line 1"
        language = "EN"

    timestamp <- getTime

    submit client do createCmd MoneyMatrixDeposit.InitialDepositRequest with ..

-- | 2-IUpgradeDepositRequest
prepareIUpgradeDepositRequest : [Party] -> Script (ContractId MoneyMatrixDeposit.DepositRequest)
prepareIUpgradeDepositRequest parties = script do
    now <- getTime
    let
        [operator, moneymatrix, customer, _] = parties

        client = customer
        integrationParty = moneymatrix
        cashierURL = "Cashier URL"
        transactionCode = "Transaction Code"
        requestedAmount = 100.0
        currency = "USD"
        observers = [operator]
        transactionId = "Transaction Id"
        userInformation = MoneyMatrixDeposit.UserInformation with
            firstName = "First Name"
            lastName = "Last Name"
            emailAddress = "Email Address"
            phoneNumber = "Phone Number"
            birthday = date 1990 Jan 10
            countryCode = "Country Code"
            city = "City"
            postalCode = "Postal Code"
            subDivision = "Sub Division"
            addressLine1 = "Address Line 1"
        timestamp = now
        language = "EN"

    submitMulti [integrationParty, client] [] do createCmd MoneyMatrixDeposit.DepositRequest with ..

-- | 3-IUpgradeDepositTransaction
prepareIUpgradeDepositTransaction : [Party] -> Script ()
prepareIUpgradeDepositTransaction parties = script do
    now <- getTime
    let
        [operator, moneymatrix, customer, _] = parties

        client = customer
        integrationParty = moneymatrix
        transactionCode = "Transaction Code"
        confirmedAmount = 100.0
        currency = "USD"
        observers = [operator]
        transactionId = "Transaction Id"
        userInformation = MoneyMatrixDeposit.UserInformation with
            firstName = "First Name"
            lastName = "Last Name"
            emailAddress = "Email Address"
            phoneNumber = "Phone Number"
            birthday = date 1990 Jan 10
            countryCode = "Country Code"
            city = "City"
            postalCode = "Postal Code"
            subDivision = "Sub Division"
            addressLine1 = "Address Line 1"
        timestamp = now

    submit client do createCmd MoneyMatrixDeposit.DepositTransaction with ..

    return ()

-- | 4-IUpgradeDepositFailure
prepareIUpgradeDepositFailure : [Party] -> Script (ContractId MoneyMatrixDeposit.DepositFailure)
prepareIUpgradeDepositFailure parties = script do
    now <- getTime
    let
        [operator, moneymatrix, customer, _] = parties

        client = customer
        integrationParty = moneymatrix
        transactionCode = "Transaction Code"
        observers = [operator]
        transactionId = "Transaction Id"
        userInformation = MoneyMatrixDeposit.UserInformation with
            firstName = "First Name"
            lastName = "Last Name"
            emailAddress = "Email Address"
            phoneNumber = "Phone Number"
            birthday = date 1990 Jan 10
            countryCode = "Country Code"
            city = "City"
            postalCode = "Postal Code"
            subDivision = "Sub Division"
            addressLine1 = "Address Line 1"
        reason = "Reason"
        timestamp = now

    submit client do createCmd MoneyMatrixDeposit.DepositFailure with ..

-- | 5-IUpgradeInitialWithdrawRequest
prepareIUpgradeInitialWithdrawRequest : [Party] -> Script (ContractId MoneyMatrixWithdraw.InitialWithdrawRequest)
prepareIUpgradeInitialWithdrawRequest parties = script do
    let
        [operator, moneymatrix, customer, _] = parties

        client = customer
        integrationParty = moneymatrix
        requestedAmount = 100.0
        currency = "USD"
        observers = [operator]
        transactionId = "Transaction Id"
        userInformation = MoneyMatrixWithdraw.UserInformation with
            firstName = "First Name"
            lastName = "Last Name"
            emailAddress = "Email Address"
            phoneNumber = "Phone Number"
            birthday = date 1990 Jan 10
            countryCode = "Country Code"
            city = "City"
            postalCode = "Postal Code"
            subDivision = "Sub Division"
            addressLine1 = "Address Line 1"
        language = "EN"

    timestamp <- getTime

    submit client do createCmd MoneyMatrixWithdraw.InitialWithdrawRequest with ..

-- | 6-IUpgradeWithdrawRequest
prepareIUpgradeWithdrawRequest : [Party] -> Script (ContractId MoneyMatrixWithdraw.WithdrawRequest)
prepareIUpgradeWithdrawRequest parties = script do
    let
      [operator, moneymatrix, customer, _] = parties
      client = customer
      integrationParty = moneymatrix
      cashierURL = "Cashier URL"
      transactionCode = "Transaction Code"
      requestedAmount = 100.0
      currency = "USD"
      observers = [operator]
      transactionId = "Transaction Id"
      userInformation = MoneyMatrixWithdraw.UserInformation with
          firstName = "First Name"
          lastName = "Last Name"
          emailAddress = "Email Address"
          phoneNumber = "Phone Number"
          birthday = date 1990 Jan 10
          countryCode = "Country Code"
          city = "City"
          postalCode = "Postal Code"
          subDivision = "Sub Division"
          addressLine1 = "Address Line 1"
      language = "EN"

    timestamp <- getTime

    submitMulti [integrationParty, client] [] do createCmd MoneyMatrixWithdraw.WithdrawRequest with ..

-- | 7-IUpgradeWithdrawTransaction
prepareIUpgradeWithdrawTransaction : [Party] -> Script (ContractId MoneyMatrixWithdraw.WithdrawTransaction)
prepareIUpgradeWithdrawTransaction parties = script do
    let
        [operator, moneymatrix, customer, _] = parties

        client = customer
        integrationParty = moneymatrix
        transactionCode = "Transaction Code"
        confirmedAmount = 100.0
        currency = "USD"
        observers = [operator]
        transactionId = "Transaction Id"
        userInformation = MoneyMatrixWithdraw.UserInformation with
            firstName = "First Name"
            lastName = "Last Name"
            emailAddress = "Email Address"
            phoneNumber = "Phone Number"
            birthday = date 1990 Jan 10
            countryCode = "Country Code"
            city = "City"
            postalCode = "Postal Code"
            subDivision = "Sub Division"
            addressLine1 = "Address Line 1"

    timestamp <- getTime

    submit client do createCmd MoneyMatrixWithdraw.WithdrawTransaction with ..

-- | 8-IUpgradeWithdrawFailure
prepareIUpgradeWithdrawFailure : [Party] -> Script (ContractId MoneyMatrixWithdraw.WithdrawFailure)
prepareIUpgradeWithdrawFailure parties = script do
    now <- getTime
    let
        [operator, moneymatrix, customer, _] = parties

        client = customer
        integrationParty = moneymatrix
        transactionCode = "Transaction Code"
        observers = [operator]
        transactionId = "Transaction Id"
        userInformation = MoneyMatrixWithdraw.UserInformation with
            firstName = "First Name"
            lastName = "Last Name"
            emailAddress = "Email Address"
            phoneNumber = "Phone Number"
            birthday = date 1990 Jan 10
            countryCode = "Country Code"
            city = "City"
            postalCode = "Postal Code"
            subDivision = "Sub Division"
            addressLine1 = "Address Line 1"
        timestamp = now
        reason = "Reason"


    submit client do createCmd MoneyMatrixWithdraw.WithdrawFailure with ..

prepareLedger : Party -> Party -> Script ()
prepareLedger operator moneymatrix = script do
  [customer, other] <- mapA allocateParty ["alice", "bob"]

  let parties = [operator, moneymatrix, customer, other]

  prepareIUpgradeConfiguration parties
  prepareIUpgradeInitialDepositRequest parties
  prepareIUpgradeDepositRequest parties
  prepareIUpgradeDepositTransaction parties
  prepareIUpgradeDepositFailure parties
  prepareIUpgradeInitialWithdrawRequest parties
  prepareIUpgradeWithdrawRequest parties
  prepareIUpgradeWithdrawTransaction parties
  prepareIUpgradeWithdrawFailure parties

  pure ()
