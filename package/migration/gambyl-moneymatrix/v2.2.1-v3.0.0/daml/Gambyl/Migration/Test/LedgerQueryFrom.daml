module Gambyl.Migration.Test.LedgerQueryFrom where

import Daml.Script

import Legacy.MoneyMatrixIntegration.Configuration
import Legacy.MoneyMatrixIntegration.Deposit
import Legacy.MoneyMatrixIntegration.Withdraw

queryMoneyMatrixContracts : IsParties p => p -> Script [(Text, Int)]
queryMoneyMatrixContracts party = script do
    r1 <- (handleResponse "InitialWithdrawRequest") <$> query @InitialWithdrawRequest party
    r2 <- (handleResponse "WithdrawRequest") <$> query @WithdrawRequest party
    r3 <- (handleResponse "WithdrawTransaction") <$> query @WithdrawTransaction party
    r4 <- (handleResponse "WithdrawFailure") <$> query @WithdrawFailure party
    r5 <- (handleResponse "Configuration") <$> query @Configuration party
    r6 <- (handleResponse "InitialDepositRequest") <$> query @InitialDepositRequest party
    r7 <- (handleResponse "DepositRequest") <$> query @DepositRequest party
    r8 <- (handleResponse "DepositTransaction") <$> query @DepositTransaction party
    r9 <- (handleResponse "DepositFailure") <$> query @DepositFailure party

    pure [r1, r2, r3, r4, r5, r6, r7, r8, r9]

    where
        handleResponse templateName responses =
            let count = length responses
            in (templateName, count)

checkSingleContract : Show a => a -> (Text, Int) -> Script ()
checkSingleContract party (templateName, count) = script do
    if count == 0
        then debug $ "[WARN] NOT Found FROM." <> templateName <> " contracts for " <> show party
        else pure ()
