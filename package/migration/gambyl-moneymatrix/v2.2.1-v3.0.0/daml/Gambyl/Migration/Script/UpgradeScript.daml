module Gambyl.Migration.Script.UpgradeScript where

import Daml.Script

import Gambyl.Migration.Script.ScriptFunctions

import Gambyl.Migration.Common
import Gambyl.Test.TestUtils

upgradeScript : Input -> Script ReportSummary
upgradeScript Input{moneymatrix} = script do

  debug $ "START Upgrading moneymatrix as party " <> show moneymatrix

  r1 <- collectReport "InitialWithdrawRequest" $ migrateDirectly "1-IUpgradeInitialWithdrawRequest" moneymatrix upgradeListOfIUpgradeInitialWithdrawRequest
  r2 <- collectReport "WithdrawRequest" $ migrateDirectly "2-IUpgradeWithdrawRequest" moneymatrix upgradeListOfIUpgradeWithdrawRequest
  -- NOTE: this is a customer contract and does not need to be migrated
  r3 <- collectReport "WithdrawTransaction" $ migrateDirectly "3-IUpgradeWithdrawTransaction" moneymatrix upgradeListOfIUpgradeWithdrawTransaction
  -- NOTE: this is a customer contract and does not need to be migrated
  r4 <- collectReport "WithdrawFailure" $ migrateDirectly "4-IUpgradeWithdrawFailure" moneymatrix upgradeListOfIUpgradeWithdrawFailure
  r5 <- collectReport "Configuration" $ migrateDirectly "5-IUpgradeConfiguration" moneymatrix upgradeListOfIUpgradeConfiguration
  r6 <- collectReport "InitialDepositRequest" $ migrateDirectly "6-IUpgradeInitialDepositRequest" moneymatrix upgradeListOfIUpgradeInitialDepositRequest
  r7 <- collectReport "DepositRequest" $ migrateDirectly "7-IUpgradeDepositRequest" moneymatrix upgradeListOfIUpgradeDepositRequest
  -- NOTE: this is a customer contract and does not need to be migrated
  r8 <- collectReport "DepositTransaction" $ migrateDirectly "8-IUpgradeDepositTransaction" moneymatrix upgradeListOfIUpgradeDepositTransaction
  -- NOTE: this is a customer contract and does not need to be migrated
  r9 <- collectReport "DepositFailure" $ migrateDirectly "9-IUpgradeDepositFailure" moneymatrix upgradeListOfIUpgradeDepositFailure

  let report = foldl combineReport (ReportSummary 0 0 0 0 []) [r1, r2, r3, r4, r5, r6, r7, r8, r9]

  debug "FINISH Upgrading moneymatrix"

  pure report
