module Gambyl.Migration.Script.RollbackScriptFunctions where

import Dam<PERSON>.Script
import Gambyl.Migration.Common

import Gambyl.Migration.Interface.MoneyMatrixIntegration.DepositRollback
import Gambyl.Migration.Interface.MoneyMatrixIntegration.ConfigurationRollback
import Gambyl.Migration.Interface.MoneyMatrixIntegration.WithdrawRollback

upgradeListOfIUpgradeInitialWithdrawRequestRollback : Party -> [ContractId IUpgradeInitialWithdrawRequestRollback] -> Script [MigrationResult (ContractId IUpgradeInitialWithdrawRequestRollback)]
upgradeListOfIUpgradeInitialWithdrawRequestRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeInitialWithdrawRequestRollback with ..

upgradeListOfIUpgradeWithdrawRequestRollback : Party -> [ContractId IUpgradeWithdrawRequestRollback] -> Script [MigrationR<PERSON>ult (ContractId IUpgradeWithdrawRequestRollback)]
upgradeListOfIUpgradeWithdrawRequestRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeWithdrawRequestRollback with ..

upgradeListOfIUpgradeWithdrawTransactionRollback : Party -> [ContractId IUpgradeWithdrawTransactionRollback] -> Script [MigrationResult (ContractId IUpgradeWithdrawTransactionRollback)]
upgradeListOfIUpgradeWithdrawTransactionRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeWithdrawTransactionRollback with ..

upgradeListOfIUpgradeWithdrawFailureRollback : Party -> [ContractId IUpgradeWithdrawFailureRollback] -> Script [MigrationResult (ContractId IUpgradeWithdrawFailureRollback)]
upgradeListOfIUpgradeWithdrawFailureRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeWithdrawFailureRollback with ..

upgradeListOfIUpgradeConfigurationRollback : Party -> [ContractId IUpgradeConfigurationRollback] -> Script [MigrationResult (ContractId IUpgradeConfigurationRollback)]
upgradeListOfIUpgradeConfigurationRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeConfigurationRollback with ..

upgradeListOfIUpgradeInitialDepositRequestRollback : Party -> [ContractId IUpgradeInitialDepositRequestRollback] -> Script [MigrationResult (ContractId IUpgradeInitialDepositRequestRollback)]
upgradeListOfIUpgradeInitialDepositRequestRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeInitialDepositRequestRollback with ..

upgradeListOfIUpgradeDepositRequestRollback : Party -> [ContractId IUpgradeDepositRequestRollback] -> Script [MigrationResult (ContractId IUpgradeDepositRequestRollback)]
upgradeListOfIUpgradeDepositRequestRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeDepositRequestRollback with ..

upgradeListOfIUpgradeDepositTransactionRollback : Party -> [ContractId IUpgradeDepositTransactionRollback] -> Script [MigrationResult (ContractId IUpgradeDepositTransactionRollback)]
upgradeListOfIUpgradeDepositTransactionRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeDepositTransactionRollback with ..

upgradeListOfIUpgradeDepositFailureRollback : Party -> [ContractId IUpgradeDepositFailureRollback] -> Script [MigrationResult (ContractId IUpgradeDepositFailureRollback)]
upgradeListOfIUpgradeDepositFailureRollback migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeDepositFailureRollback with ..