module Gambyl.Migration.Script.ScriptFunctions where

import Daml.Script
import Gambyl.Migration.Common

import Gambyl.Migration.Interface.MoneyMatrixIntegration.Deposit
import Gambyl.Migration.Interface.MoneyMatrixIntegration.Configuration
import Gambyl.Migration.Interface.MoneyMatrixIntegration.Withdraw

upgradeListOfIUpgradeInitialWithdrawRequest : Party -> [ContractId IUpgradeInitialWithdrawRequest] -> Script [MigrationResult (ContractId IUpgradeInitialWithdrawRequest)]
upgradeListOfIUpgradeInitialWithdrawRequest migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeInitialWithdrawRequest with ..

upgradeListOfIUpgradeWithdrawRequest : Party -> [ContractId IUpgradeWithdrawRequest] -> Script [MigrationResult (ContractId IUpgradeWithdrawRequest)]
upgradeListOfIUpgradeWithdrawRequest migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeWithdrawRequest with ..

upgradeListOfIUpgradeWithdrawTransaction : Party -> [ContractId IUpgradeWithdrawTransaction] -> Script [MigrationResult (ContractId IUpgradeWithdrawTransaction)]
upgradeListOfIUpgradeWithdrawTransaction migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeWithdrawTransaction with ..

upgradeListOfIUpgradeWithdrawFailure : Party -> [ContractId IUpgradeWithdrawFailure] -> Script [MigrationResult (ContractId IUpgradeWithdrawFailure)]
upgradeListOfIUpgradeWithdrawFailure migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeWithdrawFailure with ..

upgradeListOfIUpgradeConfiguration : Party -> [ContractId IUpgradeConfiguration] -> Script [MigrationResult (ContractId IUpgradeConfiguration)]
upgradeListOfIUpgradeConfiguration migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeConfiguration with ..

upgradeListOfIUpgradeInitialDepositRequest : Party -> [ContractId IUpgradeInitialDepositRequest] -> Script [MigrationResult (ContractId IUpgradeInitialDepositRequest)]
upgradeListOfIUpgradeInitialDepositRequest migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeInitialDepositRequest with ..

upgradeListOfIUpgradeDepositRequest : Party -> [ContractId IUpgradeDepositRequest] -> Script [MigrationResult (ContractId IUpgradeDepositRequest)]
upgradeListOfIUpgradeDepositRequest migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeDepositRequest with ..

upgradeListOfIUpgradeDepositTransaction : Party -> [ContractId IUpgradeDepositTransaction] -> Script [MigrationResult (ContractId IUpgradeDepositTransaction)]
upgradeListOfIUpgradeDepositTransaction migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeDepositTransaction with ..

upgradeListOfIUpgradeDepositFailure : Party -> [ContractId IUpgradeDepositFailure] -> Script [MigrationResult (ContractId IUpgradeDepositFailure)]
upgradeListOfIUpgradeDepositFailure migratingParty cids = forA cids
  \cid -> submit migratingParty do exerciseCmd cid UpgradeDepositFailure with ..