module Gambyl.Migration.Script.RollbackScript where

import Daml.Script

import Gambyl.Migration.Script.RollbackScriptFunctions

import Gambyl.Migration.Common
import Gambyl.Test.TestUtils

rollbackScript : Input -> Script ReportSummary
rollbackScript Input{moneymatrix} = script do

  debug $ "START RollingBack moneymatrix as party " <> show moneymatrix

  r1 <- collectReport "InitialWithdrawRequestRollback" $ migrateDirectly "1-IUpgradeInitialWithdrawRequestRollback" moneymatrix upgradeListOfIUpgradeInitialWithdrawRequestRollback
  r2 <- collectReport "WithdrawRequestRollback" $ migrateDirectly "2-IUpgradeWithdrawRequestRollback" moneymatrix upgradeListOfIUpgradeWithdrawRequestRollback
  r3 <- collectReport "WithdrawTransactionRollback" $ migrateDirectly "3-IUpgradeWithdrawTransactionRollback" moneymatrix upgradeListOfIUpgradeWithdrawTransactionRollback
  r4 <- collectReport "WithdrawFailureRollback" $ migrateDirectly "4-IUpgradeWithdrawFailureRollback" moneymatrix upgradeListOfIUpgradeWithdrawFailureRollback
  r5 <- collectReport "ConfigurationRollback" $ migrateDirectly "5-IUpgradeConfigurationRollback" moneymatrix upgradeListOfIUpgradeConfigurationRollback
  r6 <- collectReport "InitialDepositRequestRollback" $ migrateDirectly "6-IUpgradeInitialDepositRequestRollback" moneymatrix upgradeListOfIUpgradeInitialDepositRequestRollback
  r7 <- collectReport "DepositRequestRollback" $ migrateDirectly "7-IUpgradeDepositRequestRollback" moneymatrix upgradeListOfIUpgradeDepositRequestRollback
  r8 <- collectReport "DepositTransactionRollback" $ migrateDirectly "8-IUpgradeDepositTransactionRollback" moneymatrix upgradeListOfIUpgradeDepositTransactionRollback
  r9 <- collectReport "DepositFailureRollback" $ migrateDirectly "9-IUpgradeDepositFailureRollback" moneymatrix upgradeListOfIUpgradeDepositFailureRollback

  let report = foldl combineReport (ReportSummary 0 0 0 0 []) [r1, r2, r3, r4, r5, r6, r7, r8, r9]

  debug "FINISH RollingBack moneymatrix"

  pure report
