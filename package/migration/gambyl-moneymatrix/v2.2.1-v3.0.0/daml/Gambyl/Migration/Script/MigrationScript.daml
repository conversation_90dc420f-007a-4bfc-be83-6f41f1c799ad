module Gambyl.Migration.Script.MigrationScript where

import Daml.Script

import Gambyl.Migration.Test.TestFunctions qualified as Fixture
import Gambyl.Migration.Script.UpgradeScript qualified as Upgrade
import Gambyl.Migration.Script.RollbackScript qualified as Rollback
import Gambyl.Migration.Test.TestFunctionsRollback qualified as RollbackFixture
import Gambyl.Migration.Test.LedgerQueryFrom qualified as LedgerQueryFrom
import Gambyl.Migration.Test.LedgerQueryTo qualified as LedgerQueryTo

import Gambyl.Migration.Common
import Gambyl.Test.TestUtils
import qualified DA.Set as Set

migrationScript : Input -> Script ReportSummary
migrationScript Input{..} = logMigrationTimes do
  if rollback
  then Rollback.rollbackScript (Input{..})
  else Upgrade.upgradeScript (Input{..})

{--TEST--}

testMigration : Script ()
testMigration = script do
  gambyl <- allocateParty "gambyl"
  enetpulse <- allocateParty "enetpulse"
  moneymatrix <- allocateParty "moneymatrix"
  quickbooks <- allocateParty "quickbooks"
  exberry <- allocateParty "exberry"
  jumio <- allocateParty "jumio"

  let rollback = False

  Fixture.prepareLedger gambyl moneymatrix

  -- Check there are no contracts under TO pkg
  toMMContracts <- LedgerQueryTo.queryMoneyMatrixContracts moneymatrix
  toGambylContracts <- LedgerQueryTo.queryMoneyMatrixContracts gambyl
  let allToContracts = toMMContracts <> toGambylContracts
  let (toContracts, _) = partition (\(_, c) -> c > 0) allToContracts
  assertMsg ("No contracts under TO pkg") $ (null $ toContracts)

  -- Get all contracts under FROM pkg
  fromMMContracts' <- LedgerQueryFrom.queryMoneyMatrixContracts moneymatrix
  fromGambylContracts' <- LedgerQueryFrom.queryMoneyMatrixContracts gambyl
  let allFromContracts' = fromMMContracts' <> fromGambylContracts'
  let (fromContracts', _) = partition (\(_, c) -> c > 0) allFromContracts'

  -- Migrate all contracts
  migrationScript (Input{..})
  migrationScript (Input gambyl enetpulse gambyl quickbooks exberry jumio rollback)

  -- Check there are no contracts under FROM pkg
  fromMMContracts <- LedgerQueryFrom.queryMoneyMatrixContracts moneymatrix
  fromGambylContracts <- LedgerQueryFrom.queryMoneyMatrixContracts gambyl
  let allFromContracts = fromMMContracts <> fromGambylContracts
  let (fromContracts, _) = partition (\(_, c) -> c > 0) allFromContracts
  assertMsg ("No contracts under FROM pkg") $ (null $ fromContracts)

  -- Get all contracts under TO pkg
  toMMContracts' <- LedgerQueryTo.queryMoneyMatrixContracts moneymatrix
  toGambylContracts' <- LedgerQueryTo.queryMoneyMatrixContracts gambyl
  let allToContracts' = toMMContracts' <> toGambylContracts'
  let (toContracts', _) = partition (\(_, c) -> c > 0) allToContracts'

  -- Check all contracts have been migrated
  debug toContracts'
  debug fromContracts'
  assertMsg ("Failed to migrate some contracts") $
    Set.fromList toContracts' == Set.fromList fromContracts'

{--TEST--}

testRollback : Script ()
testRollback = script do
  gambyl <- allocateParty "gambyl"
  enetpulse <- allocateParty "enetpulse"
  moneymatrix <- allocateParty "moneymatrix"
  quickbooks <- allocateParty "quickbooks"
  exberry <- allocateParty "exberry"
  jumio <- allocateParty "jumio"

  let rollback = True

  RollbackFixture.prepareLedger gambyl moneymatrix

  -- Check there are no contracts under FROM pkg
  fromMMContracts <- LedgerQueryFrom.queryMoneyMatrixContracts moneymatrix
  fromGambylContracts <- LedgerQueryFrom.queryMoneyMatrixContracts gambyl
  let allFromContracts = fromMMContracts <> fromGambylContracts
  let (fromContracts, _) = partition (\(_, c) -> c > 0) allFromContracts
  assertMsg ("No contracts under FROM pkg") $ (null $ fromContracts)

  -- Get all contracts under TO pkg
  toMMContracts' <- LedgerQueryTo.queryMoneyMatrixContracts moneymatrix
  toGambylContracts' <- LedgerQueryTo.queryMoneyMatrixContracts gambyl
  let allToContracts' = toMMContracts' <> toGambylContracts'
  let (toContracts', _) = partition (\(_, c) -> c > 0) allToContracts'

  -- Migrate all contracts
  migrationScript (Input{..})
  migrationScript (Input gambyl enetpulse gambyl quickbooks exberry jumio rollback)

  -- Check there are no contracts under TO pkg
  toMMContracts <- LedgerQueryTo.queryMoneyMatrixContracts moneymatrix
  toGambylContracts <- LedgerQueryTo.queryMoneyMatrixContracts gambyl
  let allToContracts = toMMContracts <> toGambylContracts
  let (toContracts, _) = partition (\(_, c) -> c > 0) allToContracts
  assertMsg ("No contracts under TO pkg") $ (null $ toContracts)

  -- Get all contracts under FROM pkg
  fromMMContracts' <- LedgerQueryFrom.queryMoneyMatrixContracts moneymatrix
  fromGambylContracts' <- LedgerQueryFrom.queryMoneyMatrixContracts gambyl
  let allFromContracts' = fromMMContracts' <> fromGambylContracts'
  let (fromContracts', _) = partition (\(_, c) -> c > 0) allFromContracts'

  -- Check all contracts have been rollbacked
  assertMsg ("Failed to rollbacked some contracts") $
    Set.fromList toContracts' == Set.fromList fromContracts'
