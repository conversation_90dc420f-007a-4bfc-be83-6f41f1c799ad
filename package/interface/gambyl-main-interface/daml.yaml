# for config file options, refer to
# https://docs.daml.com/tools/assistant.html#project-config-file-daml-yaml
sdk-version: 2.9.4
name: gambyl-main-interface
source: daml
init-script:
parties:
  - Operator
  - Gambyl
  - EnetPulse
  - Alice
  - Bob
  - Charlie
  - Dana
  - Public
version: ${GAMBYL_MAIN_INTERFACE_CURRENT_VERSION}
dependencies:
  - daml-prim
  - daml-stdlib
  - daml-script
sandbox-options:
  - --wall-clock-time
build-options:
  - --ghc-option=-Wall
  - --ghc-option=-Wno-name-shadowing
  - --ghc-option=-Wno-unused-do-bind
  - --ghc-option=-fno-warn-missing-signatures
  - --ghc-option=-fno-warn-orphans
  - --ghc-option=-Werror
  - --output=../../../build/interface/gambyl-main-interface-${GAMBYL_MAIN_INTERFACE_CURRENT_VERSION}.dar