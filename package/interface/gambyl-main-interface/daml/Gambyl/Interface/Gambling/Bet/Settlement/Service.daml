module Gambyl.Interface.Gambling.Bet.Settlement.Service where

import Gambyl.Interface.Gambling.Bet.Settlement.Model (IExecutedBetsCid)
import Gambyl.Interface.Gambling.Model (IActionFailureCid)

type IBetSettlementServiceCid = ContractId IBetSettlementService

data VService = VService with
  operator : Party
  provider : Party
  customer : Party

interface IBetSettlementService where
  viewtype VService

  delegateBetExecutionSign : IBetSettlementServiceCid -> DelegateBetExecutionSign -> Update (Either IActionFailureCid IExecutedBetsCid)

  delegateBetExecutionSignCancelled : IBetSettlementServiceCid -> DelegateBetExecutionSignCancelled -> Update (Either IActionFailureCid ())
  
  settleExecutedBets : IBetSettlementServiceCid -> SettleExecutedBets -> Update (Either IActionFailureCid ())

  archiveExecutedbets : IBetSettlementServiceCid -> IExecutedBetsCid -> Update (Either IActionFailureCid ())

  nonconsuming choice DelegateBetExecutionSign : Either IActionFailureCid IExecutedBetsCid
    with
      executedBetsCid : IExecutedBetsCid
    controller (view this).provider
    do delegateBetExecutionSign this self arg

  nonconsuming choice DelegateBetExecutionSignCancelled : Either IActionFailureCid ()
    with
      executedBetsCid : IExecutedBetsCid
    controller (view this).provider
    do delegateBetExecutionSignCancelled this self arg
  
  nonconsuming choice SettleExecutedBets : Either IActionFailureCid ()
    with
      executedBetsCid : IExecutedBetsCid
    controller (view this).provider
    do settleExecutedBets this self arg
  
  nonconsuming choice ArchiveExecutedBets : Either IActionFailureCid ()
    with
      executedbetsCid : IExecutedBetsCid
    controller (view this).provider
    do archiveExecutedbets this self executedbetsCid