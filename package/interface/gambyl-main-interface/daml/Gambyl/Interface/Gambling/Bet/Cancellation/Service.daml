module Gambyl.Interface.Gambling.Bet.Cancellation.Service where

import Gambyl.Interface.Gambling.Bet.Cancellation.Model (IBetPlacementCancelRequestCid)
import Gambyl.Interface.Gambling.Bet.Model (IBetPlacementCid)

type IBetCancellationServiceCid = ContractId IBetCancellationService

data VService = VService with
  operator : Party
  provider : Party
  customer : Party

interface IBetCancellationService where
  viewtype VService

  cancelBet : IBetCancellationServiceCid -> CancelBet -> Update ()

  finalizeBetPlacementCancel : IBetCancellationServiceCid -> FinalizeBetPlacementCancel -> Update ()

  nonconsuming choice CancelBet : ()
    with
      betPlacementCid : IBetPlacementCid
    controller (view this).provider
    do cancelBet this self arg

  nonconsuming choice FinalizeBetPlacementCancel : ()
    with
      finalizeBetPlacementCancelCid : IBetPlacementCancelRequestCid
      betPlacementCid : Optional IBetPlacementCid
      errorMsg : Optional Text
      eventCancelled : Bool
    controller (view this).provider
    do finalizeBetPlacementCancel this self arg
