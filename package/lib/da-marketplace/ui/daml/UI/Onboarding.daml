module UI.Onboarding where

import qualified Marketplace.Operator.Role as Operator
import qualified Marketplace.Trading.Role as Exchange
import qualified Marketplace.Custody.Role as Custodian
import qualified Marketplace.Distribution.Role as Distributor
import qualified Marketplace.Clearing.Role as Clearinghouse
import qualified Marketplace.Custody.Service as Custody
import qualified Marketplace.Clearing.Service as Clearing
import qualified Marketplace.Clearing.Market.Service as Market
import qualified Marketplace.Issuance.Service as Issuance
import qualified Marketplace.Trading.Service as Trading
import qualified Marketplace.Distribution.Auction.Service as Auction
import qualified Marketplace.Distribution.Bidding.Service as Bidding

import DA.Set (Set)
import DA.Set qualified as Set
import DA.Foldable (mapA_, forA_)
import DA.Optional
import DA.List.Total (dedup)
import DA.Action (void, when)

createOrLookup : forall t k. (Template t, TemplateKey t k) => t -> Update (ContractId t)
createOrLookup t = lookupByKey @t (key t) >>= \case
    (Some cid) -> return cid
    None       -> create t

handleOnboardingInstruction : Party -> Party -> OnboardingInstruction -> Update ()
handleOnboardingInstruction operator customer = \case

    OnboardExchange -> do
      offerCid <- exerciseByKey @Operator.Role operator Operator.OfferExchangeRole with provider = customer
      void $ exercise offerCid Exchange.Accept

    OnboardDistributor -> do
      offerCid <- exerciseByKey @Operator.Role operator Operator.OfferDistributorRole with provider = customer
      void $ exercise offerCid Distributor.Accept

    OnboardCustodian -> do
      offerCid <- exerciseByKey @Operator.Role operator Operator.OfferCustodianRole with provider = customer
      void $ exercise offerCid Custodian.Accept

    OnboardClearinghouse{..} -> do
      optClearingRole <- lookupByKey @Clearinghouse.Role (operator, customer)
      when (isNone optClearingRole) $ do
        let account = Custody.createAccount custodian customer
        custodyCid <- createOrLookup Custody.Service with provider = custodian; ..

        offerCid <- exerciseByKey @Operator.Role operator Operator.OfferClearingRole with provider = customer
        void $ exercise offerCid Clearinghouse.Accept with ccpAccount = account

    OnboardCustody{..} -> do
      offerCid <- exerciseByKey @Custodian.Role (operator, provider) Custodian.OfferCustodyService with ..
      void $ exercise offerCid Custody.Accept

    OnboardTrading{..} -> do
      offerCid <- exerciseByKey @Exchange.Role (operator, provider) Exchange.OfferTradingService with ..
      void $ exercise offerCid Trading.Accept

    OnboardIssuance{..} -> do
      offerCid <- exerciseByKey @Custodian.Role (operator, provider) Custodian.OfferIssuanceService with ..
      void $ exercise offerCid Issuance.Accept

    OnboardClearing{..} -> do
      let account = Custody.createAccount custodian customer
      custodyCid <- createOrLookup Custody.Service with provider = custodian; ..
      (clearingRoleCid,_) <- fetchByKey @Clearinghouse.Role (operator, provider)

      clearingOfferCid <- exercise clearingRoleCid Clearinghouse.OfferClearingService with ..
      void $ exercise clearingOfferCid Clearing.Accept with clearingAccount = account

    OnboardMarketClearing{..} -> do
      (clearingRoleCid,_) <- fetchByKey @Clearinghouse.Role (operator, provider)

      clearingOfferCid <- exercise clearingRoleCid Clearinghouse.OfferMarketService with ..
      void $ exercise clearingOfferCid Market.Accept

    OnboardAuction{..} -> do
      offerCid <- exerciseByKey @Distributor.Role (operator, provider) Distributor.OfferAuctionService with ..
      void $ exercise offerCid Auction.Accept

    OnboardBidding{..} -> do
      offerCid <- exerciseByKey @Distributor.Role (operator, provider) Distributor.OfferBiddingService with ..
      void $ exercise offerCid Bidding.Accept

data OnboardingAccount = OnboardingAccount with
    custodian: Party
    name: Text
  deriving (Show, Eq, Ord)

data OnboardingInstruction
  = OnboardExchange
  | OnboardDistributor
  | OnboardCustodian
  | OnboardClearinghouse with custodian : Party
  | OnboardMarketClearing with provider : Party, custodian : Party

  | OnboardCustody  with provider : Party
  | OnboardTrading  with provider : Party
  | OnboardClearing with provider : Party, custodian : Party
  | OnboardIssuance with provider : Party
  | OnboardAuction  with provider : Party
  | OnboardBidding  with provider : Party
  deriving (Show, Eq, Ord)

-- |List of provider roles needed for this instruction
roleDependencies : OnboardingInstruction -> [(Party, OnboardingInstruction)]
roleDependencies (OnboardCustody c)          = [(c,  OnboardCustodian)]
roleDependencies (OnboardIssuance p)         = [(p,  OnboardCustodian)]
roleDependencies (OnboardTrading e)          = [(e,  OnboardExchange)]
roleDependencies (OnboardClearing ch c )     = [(c,  OnboardCustodian), (ch, OnboardClearinghouse c)]
roleDependencies (OnboardAuction ah)         = [(ah, OnboardDistributor)]
roleDependencies (OnboardBidding ah)         = [(ah, OnboardDistributor)]
roleDependencies (OnboardMarketClearing p c) = [(p,  OnboardClearinghouse c )]
roleDependencies _                           = []

getSignaturesNeeded : OnboardingInstruction -> [Party]
getSignaturesNeeded (OnboardClearinghouse c)    = [c]
getSignaturesNeeded (OnboardCustody c)          = [c]
getSignaturesNeeded (OnboardTrading e)          = [e]
getSignaturesNeeded (OnboardClearing ccp c)     = [ccp, c]
getSignaturesNeeded (OnboardMarketClearing p c) = [p, c]
getSignaturesNeeded (OnboardIssuance p)         = [p]
getSignaturesNeeded (OnboardAuction d)          = [d]
getSignaturesNeeded (OnboardBidding d)          = [d]
getSignaturesNeeded _                           = []

collectPartySignatures : Party -> [Party] -> ContractId OnboardCustomer -> Update (ContractId OnboardCustomer)
collectPartySignatures _        []               cid = return cid
collectPartySignatures operator (party::parties) cid
  = exerciseByKey @PartyOnboarding (operator,party) PartyOnboarding_Sign with requestCid = cid
      >>= collectPartySignatures operator parties

template OnboardCustomer
  with
    operator : Party
    customer : Party
    instructions : [OnboardingInstruction]
    signed : Set Party
  where
    signatory signed

    controller operator can
      nonconsuming OnboardCustomer_Finish : ()
        do
          mapA_ (handleOnboardingInstruction operator customer) instructions
          pure ()

    choice OnboardCustomer_Archive : ()
      with
        ctrl : Party
      controller ctrl
      do return ()

    choice OnboardCustomer_Sign : ContractId OnboardCustomer
      with
        ctrl : Party
      controller ctrl
      do
        create this with signed = Set.insert ctrl signed

template PartyOnboarding
  with
    party : Party
    operator : Party
  where
    signatory party
    key (operator, party) : (Party, Party)
    maintainer key._2

    controller operator can
      nonconsuming PartyOnboarding_Sign : ContractId OnboardCustomer
        with
          requestCid : ContractId OnboardCustomer
        do
          exercise requestCid OnboardCustomer_Sign with ctrl = party

template OperatorOnboarding
  with
    operator : Party
  where
    signatory operator

    controller operator can
      nonconsuming OperatorOnboard_OnboardAll : ()
        with
          instructions : [(Party, [OnboardingInstruction])]
        do
          forA_ instructions (\(p, is) -> exercise self OperatorOnboard_Onboard with party = p; instructions = is)

      nonconsuming OperatorOnboard_Onboard : ()
        with
          party : Party
          instructions : [OnboardingInstruction]
        do
          let roleDeps = concatMap roleDependencies instructions
          forA_ roleDeps (\(p, inst) -> exercise self OperatorOnboard_Onboard with party = p; instructions = [inst])

          let signaturesNeeded = party :: dedup (concatMap getSignaturesNeeded instructions)
          onboardCid <- create OnboardCustomer with signed = Set.fromList [operator]; customer = party; ..
          onboardCid <- collectPartySignatures operator signaturesNeeded onboardCid
          exercise onboardCid OnboardCustomer_Finish
          exercise onboardCid OnboardCustomer_Archive with ctrl = operator

          return ()
