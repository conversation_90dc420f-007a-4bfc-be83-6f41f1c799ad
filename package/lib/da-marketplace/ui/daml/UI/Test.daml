module UI.Test where

import qualified Marketplace.Regulator.Service as RegulatorService
import qualified Marketplace.Regulator.Role as Regulator

import UI.Onboarding
import qualified Marketplace.Operator.Role as Operator
import DA.Set qualified as Set

import Daml.Script

data Parties = Parties with
    operator : Party
    public : Party
    bank : Party
    auctionhouse : Party
    exchange : Party
    issuer :  Party
    ccp : Party
    alice : Party
    bob : Party
  deriving (Show, Eq)

requestFor : Party -> [OnboardingInstruction] -> (Party, [OnboardingInstruction])
requestFor party is = (party, is)

setupTest : Script ()
setupTest = do
  Parties{..} <- setupParties
  ooCid <- operator `submit` createCmd OperatorOnboarding with ..
  return ()

setupParty : Party -> Party -> Text -> Script Party
setupParty operator public name = do

  party <- allocatePartyWithHint name $ PartyIdHint name
  party `submit` createCmd PartyOnboarding with ..

  (Some (regulatorRoleCid, _))    <- queryContractKey @Regulator.Role operator (operator, operator)
  regulatorServiceOfferCid        <- submit operator  do exerciseCmd regulatorRoleCid Regulator.OfferRegulatorService with customer = party
  regulatorServiceCid             <- submit party  do exerciseCmd regulatorServiceOfferCid RegulatorService.Accept
  identityVerificationRequestCid  <- submit party  do exerciseCmd regulatorServiceCid RegulatorService.RequestIdentityVerification with legalName = show party; location = show party <> " Location"; observers = [public]
  verifiedIdentity                <- submit operator  do exerciseCmd regulatorServiceCid RegulatorService.VerifyIdentity with ..
  return party

setupParties : Script Parties
setupParties = do
  operator <- allocatePartyWithHint "Operator" $ PartyIdHint "Operator"
  public   <- allocatePartyWithHint "Public"     $ PartyIdHint "Public"

  operatorRoleCid
    <- operator `submit` createCmd Operator.Role with observers = Set.fromList [public]; ..

  regulatorRoleOfferCid <- submit operator  do exerciseCmd operatorRoleCid Operator.OfferRegulatorRole with provider = operator
  regulatorRoleCid      <- submit operator  do exerciseCmd regulatorRoleOfferCid Regulator.Accept

  bank         <- setupParty operator public "Bank"
  auctionhouse <- setupParty operator public "AuctionHouse"
  exchange     <- setupParty operator public "Exchange"
  issuer       <- setupParty operator public "Issuer"
  ccp          <- setupParty operator public "Ccp"

  alice   <- setupParty operator public "Alice"
  bob     <- setupParty operator public "Bob"
  charlie <- setupParty operator public "Charlie"
  dana    <- setupParty operator public "Dana"

  return Parties with ..

testListOnboarding : Script ()
testListOnboarding = do

  Parties{..} <- setupParties

  ooCid <- operator `submit` createCmd OperatorOnboarding with ..

  let investorInstructions =
        [ OnboardCustody  with provider = bank
        , OnboardTrading  with provider = exchange
        , OnboardBidding  with provider = auctionhouse
        , OnboardClearing with provider = ccp;          custodian = bank
        ]

  let issuerInstructions =
        [ OnboardCustody  with provider = bank
        , OnboardIssuance with provider = bank
        , OnboardAuction  with provider = auctionhouse
        ]

  let exchangeInstructions =
        [ OnboardExchange
        , OnboardMarketClearing with provider = ccp; custodian = bank
        ]

  let partyInstructions =
        [
          requestFor bank         [ OnboardCustodian ]
        , requestFor auctionhouse [ OnboardDistributor ]
        , requestFor ccp          [ OnboardClearinghouse with custodian = bank ]
        , requestFor exchange     [ OnboardExchange, OnboardMarketClearing with provider = ccp, custodian = bank ]

        , requestFor alice investorInstructions
        , requestFor bob investorInstructions

        , requestFor issuer issuerInstructions
        ]

  operator `submit` exerciseCmd ooCid OperatorOnboard_OnboardAll with instructions = partyInstructions
  return ()

testOnboarding : Script ()
testOnboarding = do
  Parties{..} <- setupParties

  ooCid <- operator `submit` createCmd OperatorOnboarding with ..

  -- Onboard Custodian
  let instructions = [ OnboardCustodian ]
  operator `submit` exerciseCmd ooCid OperatorOnboard_Onboard with party = bank; ..

  -- Onboard Distributor
  let instructions = [ OnboardDistributor ]
  operator `submit` exerciseCmd ooCid OperatorOnboard_Onboard with party = auctionhouse; ..

  -- Onboard Clearinghouse
  let instructions = [ OnboardClearinghouse with custodian = bank ]
  operator `submit` exerciseCmd ooCid OperatorOnboard_Onboard with party = ccp; ..

  -- Onboard Exchange
  let instructions = [ OnboardExchange
                     , OnboardMarketClearing with provider = ccp, custodian = bank
                     ]
  operator `submit` exerciseCmd ooCid OperatorOnboard_Onboard with party = exchange; ..

  -- Onboard Investors
  let instructions = [ OnboardCustody  with provider = bank
                     , OnboardTrading  with provider = exchange
                     , OnboardBidding  with provider = auctionhouse
                     , OnboardClearing with provider = ccp; custodian = bank
                     ]

  operator `submit` exerciseCmd ooCid OperatorOnboard_Onboard with party = alice; ..
  operator `submit` exerciseCmd ooCid OperatorOnboard_Onboard with party = bob; ..
  operator `submit` exerciseCmd ooCid OperatorOnboard_Onboard with party = bob; ..

  -- Onboard Issuer
  let instructions = [ OnboardCustody  with provider = bank
                     , OnboardIssuance with provider = bank
                     , OnboardAuction  with provider = auctionhouse ]
  operator `submit` exerciseCmd ooCid OperatorOnboard_Onboard with party = issuer; ..

  return ()
