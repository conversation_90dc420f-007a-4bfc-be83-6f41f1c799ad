{"name": "da-marketplace-ui", "version": "0.2.0-rc.4", "private": true, "dependencies": {"@daml-ui.js/da-marketplace-ui": "file:../daml-ui.js/da-marketplace-ui-0.2.0", "@daml.js/da-marketplace": "file:../daml.js/da-marketplace-0.2.0", "@daml/hub-react": "1.0.2", "@daml/ledger": "1.12.0", "@daml/react": "1.12.0", "@daml/types": "1.12.0", "@date-io/core": "1.3.6", "@date-io/date-fns": "1.x", "@material-ui/core": "^4.3.0", "@material-ui/pickers": "^3.2.10", "@mojotech/json-type-validation": "^3.1.0", "@types/dagre": "^0.7.44", "classnames": "^2.2.6", "d3": "^6.5.0", "dagre": "^0.8.5", "date-fns": "^2.17.0", "jwt-simple": "^0.5.6", "lodash": "^4.17.21", "luxon": "^1.26.0", "prop-types": "15.6.0", "react": "^16.12.0", "react-dom": "^16.12.0", "react-flow-renderer": "^9.6.3", "react-router-dom": "^5.0.1", "react-swipeable-views": "^0.13.9", "recharts": "^2.0.9", "semantic-ui-css": "^2.4.1", "semantic-ui-react": "^2.0.3", "tinycolor2": "^1.4.1", "uuid": "^8.3.1"}, "resolutions": {"**/**/set-value": "^2.0.1", "**/**/mixin-deep": "^1.3.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "format": "yarn prettier --write .", "format-check": "yarn prettier --check .", "lint": "eslint --ext .js,.jsx,.ts,.tsx --max-warnings 0 src/"}, "eslintConfig": {"extends": "react-app"}, "prettier": {"printWidth": 100, "singleQuote": true, "arrowParens": "avoid", "endOfLine": "auto", "importOrder": ["^@daml/(.*)$", "^@daml.js/(.*)$", "^@daml-ui.js/(.*)$", "^[./]"], "importOrderSeparation": true}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:7575", "devDependencies": {"@trivago/prettier-plugin-sort-imports": "^2.0.4", "@types/classnames": "^2.2.10", "@types/d3": "^6.3.0", "@types/jsonwebtoken": "^8.5.0", "@types/jwt-simple": "^0.5.33", "@types/lodash": "^4.14.168", "@types/luxon": "^1.26.1", "@types/react": "^16.9.36", "@types/react-dom": "^16.9.8", "@types/react-router-dom": "^5.1.5", "@types/react-swipeable-views": "^0.13.0", "@types/tinycolor2": "^1.4.2", "@types/uuid": "^8.3.0", "jest": "^24.9.0", "prettier": "^2.2.1", "react-scripts": "^3.4.1", "sass": "^1.32.8", "typescript": "^3.9.5"}}