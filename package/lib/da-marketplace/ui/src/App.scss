@use './themes/variables.scss' as *;

@mixin app {
  .app {
    flex-direction: column;

    .services {
      display: flex;
      flex-wrap: wrap;
    }

    .service-tile {
      margin-right: $spacing-m;
      margin-bottom: $spacing-m;
      max-width: 200px;

      a {
        display: flex;
        flex-direction: column;
        width: 100%;
        color: var(--textcolor);
        background-color: var(--white);
        border: 1px solid var(--cool-grey-90);
        border-radius: 4px;

        &:hover {
          background-color: var(--blue-100);
        }

        img {
          width: 100%;
          height: 100px;
          object-fit: cover;
        }

        .text {
          padding: $spacing-m;
          color: var(--textcolor);
          border-top: 1px solid var(--cool-grey-90);
        }
      }
    }
  }
}
