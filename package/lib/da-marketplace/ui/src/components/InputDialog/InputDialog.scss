@use '../../themes/variables' as *;

@mixin input-dialog {
  .input-dialog {
    display: flex;
    flex-direction: column;
    animation: fadeIn 0.5s;

    h2 {
      margin: $spacing-xs 0 $spacing-s 0;
    }

    .form-select {
      display: flex;
      flex-direction: row;
      align-items: baseline;
    }

    .subtitle {
      display: flex;
      align-items: baseline;
      margin-bottom: $spacing-m;

      .icon {
        margin-right: $spacing-xs;
      }
    }

    form {
      width: 40%;
      margin-bottom: $spacing-m;
    }

    .ghost.checked {
      margin: $spacing-m 0;
    }

    form,
    h2,
    .submit,
    .subtitle {
      margin-left: $spacing-xl;
    }

    .ghost {
      margin-right: $spacing-s;
    }

    .submit,
    a.a2 {
      display: flex;
      flex-direction: row;
      align-items: center;
    }

    .submit-form {
      display: flex;
      flex-direction: row;
      align-items: center;
      a.a2 {
        margin-left: $spacing-xl;

        .icon {
          margin-right: $spacing-xs;
        }
      }
    }

    &.inline {
      form,
      h2,
      .submit,
      .subtitle {
        width: 90%;
        margin-left: unset;
      }
    }

    @media screen and (max-width: 980px) {
      form {
        width: auto;
        margin-left: $spacing-s;
      }

      form,
      h2,
      .submit,
      .subtitle {
        margin-left: $spacing-s;
      }
    }
  }
}
