@use './themes/variables.scss' as *;

@mixin widget {
  .widget {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: var(--cool-grey-20);
    padding: $spacing-m;

    .page-controls {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      width: 100%;

      button.ghost.dark.control-button {
        width: fit-content;
        border-color: var(--cool-grey-20);

        .arrow-right-icon {
          margin-left: $spacing-xs;
        }
      }
    }

    .widget-header {
      display: flex;
      justify-content: center;
      flex-direction: column;
      margin: $spacing-s 0 $spacing-l 0;

      .logo-header,
      h2 {
        display: flex;
        justify-content: center;
        color: var(--white);
        font-family: 'BrownStd-Light';
      }

      .logo-header {
        font-size: 34px;
        line-height: 32px;
        margin-bottom: 0px;

        .open-marketplace-icon {
          margin-right: $spacing-s;
        }
      }
    }

    .widget-tile {
      display: flex;
      justify-self: center;
      align-self: center;
      flex-direction: column;
      border-radius: 6px;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
    }
  }
}
