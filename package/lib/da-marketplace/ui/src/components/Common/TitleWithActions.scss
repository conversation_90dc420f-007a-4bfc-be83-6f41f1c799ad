@use '../../themes/variables.scss' as *;

@mixin title-with-actions {
  .title-with-actions {
    display: flex;
    flex-direction: row;
    align-items: baseline;
    flex-wrap: wrap;

    a {
      margin-left: $spacing-m;
    }

    .children {
      display: flex;
      flex-direction: row;
      align-items: baseline;
      flex-wrap: wrap;
      margin-left: $spacing-m;
      button {
        margin-right: $spacing-m;
      }
    }

    .overflow-menu-title {
      display: none;
    }

    @media screen and (max-width: 980px) {
      .overflow-menu .overflow-menu-title {
        display: flex;
        margin: 0 $spacing-s;
      }
      a {
        display: none;
      }
    }
  }
}
