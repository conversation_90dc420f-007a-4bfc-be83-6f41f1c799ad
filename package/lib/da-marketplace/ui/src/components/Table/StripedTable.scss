@use '../../themes/variables.scss' as *;

@mixin striped-table {
  .striped-table {
    display: flex;
    flex-direction: column;
    width: 100%;
    align-items: flex-start;
    animation: fadeIn 0.5s;
    margin-bottom: $spacing-m;

    .empty-table {
      background-color: var(--white);
      border: 1px solid var(--cool-grey-90);
      min-height: 57px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      width: 100%;

      .ui.loader {
        position: inherit;
        margin-top: $spacing-m;
      }
    }

    table {
      border-collapse: collapse;
      border: none;
      margin: 0px;
      table-layout: fixed;

      th {
        border: none;
        background-color: var(--cool-grey-100);
      }

      tr {
        &.clickable {
          &:hover {
            background-color: var(--cool-grey-90);
            cursor: pointer;
          }
        }
      }

      td {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        .ui.button {
          margin-left: auto;
          padding: 4px;
        }
      }

      tr td {
        border-top: 0px !important;

        .label {
          display: none;
        }
      }

      tbody {
        background-color: var(--white);
        border: 1px solid var(--cool-grey-90);
      }

      tr:nth-child(odd) {
        background-color: var(--blue-100);
      }
    }

    .ui.pagination.menu {
      margin-left: auto;

      a.item {
        border: none;
        color: var(--cool-grey-60);
      }
      a.active.item {
        color: var(--blue-50);
        background-color: transparent;
      }
    }

    @media screen and (max-width: 900px) {
      thead {
        display: none;
      }
      table {
        td {
          text-align: left;
          display: flex;
          flex-direction: row;

          b.label {
            display: flex;
            margin-right: $spacing-xs;
          }

          &.click-icon {
            justify-content: flex-end;
            padding-top: 0px;
          }
        }

        tr {
          position: relative;
        }
      }
    }
  }
}
