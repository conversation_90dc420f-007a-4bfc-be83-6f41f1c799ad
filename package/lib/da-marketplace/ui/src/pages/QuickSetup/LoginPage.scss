@use 'themes/variables' as *;

@mixin login-page {
  .login-page {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    align-items: center;
    justify-content: flex-start;
    background-color: var(--cool-grey-20);

    p {
      margin-bottom: $spacing-s;
    }

    .log-in-tile-grid {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      grid-gap: $spacing-xl;
      width: 80%;

      .log-in-tile {
        display: flex;
        flex-direction: column;
        border-radius: 4px;
        background-color: var(--white);
        width: auto;
        min-height: 88px;

        padding: $spacing-s;

        .log-in-row {
          display: flex;
          justify-content: space-between;
          align-items: baseline;
          overflow: hidden;

          h4 {
            display: unset;
            white-space: nowrap;
            width: unset;
            align-items: unset;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          p.log-in {
            align-items: baseline;
            border: none;
            color: var(--blue-60);
            padding: 0px;
            white-space: nowrap;

            .icon {
              margin-left: $spacing-xs;
              height: 10px;
            }
          }
        }

        &:hover {
          background-color: var(--blue-100);
          cursor: pointer;
        }

        .finished-roles {
          font-size: 12px;
          color: var(--cool-grey-30);
        }
      }
    }
  }
}
