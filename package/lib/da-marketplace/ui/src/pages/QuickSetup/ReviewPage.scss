@use 'themes/variables' as *;

@mixin review-page {
  .review {
    width: 100%;
    height: fit-content(20em);
    background-color: var(--cool-grey-100);
    border-radius: 4px;
    flex: 1;

    .side-bar {
      height: 100%;
      border-right: 1px solid var(--cool-grey-80);
      width: 320px;
      align-self: flex-start;

      .side-bar-item {
        padding: $spacing-s;
        border-bottom: 1px solid var(--cool-grey-80);

        &.is-open {
          border-bottom: none;
        }
        &:hover {
          background-color: var(--blue-100);
          cursor: pointer;
        }
      }
    }

    .request-services,
    .assign-roles,
    .add-account {
      padding: $spacing-s;
      border-bottom: 1px solid var(--cool-grey-80);

      .submit-actions {
        display: flex;
        flex-direction: row;
        align-items: center;
      }
    }

    .add-account {
      height: 440px;
      overflow: auto;

      .add-account-form {
        margin-top: $spacing-s;
      }
    }

    .request-services {
      .message {
        margin: $spacing-s 0;
        max-width: 300px;
        display: flex;
        .icon {
          margin-right: $spacing-xs;
          width: 16px;
        }
      }
      .request-select,
      .required.request-select {
        margin-bottom: $spacing-s;
        a.ui.label {
          background-color: var(--cool-grey-100);
          color: var(--cool-grey-10);
          .delete.icon {
            height: unset;
            color: var(--blue-50);
            font-weight: 100;
            stroke-width: 40%;
          }
        }
      }
      button.ghost.request {
        align-self: flex-start;
      }
    }

    .react-flow {
      .tool-tip {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        position: absolute;
        bottom: 16px;
        left: 43px;
        background-color: var(--blue-100);
        height: fit-content;
        width: 100px;
        padding: $spacing-xs;
        border-radius: 4px;
        border: 1px solid var(--cool-grey-80);
      }
      .tool-tip:after {
        border: 15px solid transparent;
        border-right-color: var(--blue-90);
        content: '';
        margin-left: -1em;
        position: absolute;
        top: 11px;
        left: -16px;
        z-index: 5;

        width: 0;
        height: 0;
      }

      .role-node {
        display: flex;
        padding: $spacing-xs;
        flex-direction: column;
        width: 172px;
        background-color: var(--blue-100);
        justify-content: center;
        align-items: center;
        text-align: center;
        border-radius: 4px;
        border: 1px solid var(--cool-grey-80);

        h4 {
          margin: $spacing-xs 0;
        }
      }
    }
  }
}
