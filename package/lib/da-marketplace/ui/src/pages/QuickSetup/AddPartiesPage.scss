@use 'themes/variables' as *;

@mixin add-parties-page {
  .add-parties-page {
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .party-names {
      height: 450px;
      overflow-y: auto;
      background-color: var(--cool-grey-100);
      border: 1px solid var(--cool-grey-80);
      margin-top: $spacing-xs;
      width: 400px;

      .party-name {
        border-bottom: 1px solid var(--cool-grey-80);
        padding: $spacing-s;

        &:last-of-type {
          border-bottom: none;
        }
      }
    }
    .upload-parties {
      display: flex;
      align-items: center;
      flex-direction: column;
      width: 100%;

      .details {
        margin-bottom: $spacing-xl;
      }

      &.uploaded {
        margin-left: $spacing-xl;
        justify-content: center;
        button {
          margin-top: $spacing-l;
          &.icon-right .icon {
            margin: 0 0 0 $spacing-xs;
          }
        }
      }
    }

    .custom-file-upload {
      width: fit-content;
      height: 44px;
      border-radius: 4px;
      background-color: var(--white);
      border: 1px solid var(--green-50);
      cursor: pointer;

      > input[type='file'] {
        display: none;
      }
      &:hover {
        background-color: var(--blue-100);
        cursor: pointer;
      }
    }
  }
}
