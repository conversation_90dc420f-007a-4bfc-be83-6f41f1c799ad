@use 'themes/variables' as *;

@use './ReviewPage';
@use './LoginPage';
@use './AddPartiesPage';
@use './AssignRolesPage';

@mixin quick-setup {
  .quick-setup {
    display: flex;
    align-items: center;
    flex-direction: column;
    height: 100%;
    width: 100%;
    animation: fadeIn 0.5s;

    .info {
      padding-bottom: $spacing-s;
    }

    @include LoginPage.login-page;
    @include ReviewPage.review-page;

    .checkbox-cleared {
      display: flex;
      flex-direction: row;
      padding-left: $spacing-xs;
      align-items: center;

      p.p2.dark.cleared-exchange {
        padding-left: $spacing-xs;
      }

      .ui.checkbox input:checked ~ label:after {
        color: var(--green-50);
      }
    }

    .advanced-setup {
      color: var(--green-50);
      background-color: transparent;
      border: none;
      text-align: left;
      width: fit-content;
      font-size: 13px;

      &:hover {
        cursor: pointer;
      }
    }

    .setup-page {
      display: flex;
      flex-direction: column;
      animation: fadeIn 0.5s;
      padding: $spacing-m;
      background-color: var(--white);
      border-radius: 4px 4px;
      width: 800px;
      height: 550px;

      @include AddPartiesPage.add-parties-page;
      @include AssignRolesPage.assign-roles;

      h2.title {
        text-align: center;
      }

      form {
        display: grid;
        grid-gap: $spacing-s;
        grid-template-columns: repeat(3, 1fr);
        background-color: var(--cool-grey-100);
        border: 1px solid var(--cool-grey-80);
        padding: $spacing-s;
        border-radius: 4px;
        margin-bottom: $spacing-s;

        &.form-error-handled {
          grid-template-columns: repeat(2, 1fr);
          button {
            height: 44px;
            width: fit-content;
            justify-self: flex-end;
            align-self: flex-end;
          }
        }
      }

      button.ui.button.ghost.submit,
      button.ghost.submit {
        width: fit-content;
        margin-left: auto;
      }

      .empty {
        height: 100%;
        width: 100%;
        text-align: center;
        margin-top: $spacing-l;
      }

      &.main-select {
        background-color: var(--cool-grey-10);
        width: 500px;
        height: fit-content;
        justify-self: flex-start;
        animation: fadeIn 0.5s;

        h4 {
          text-align: center;
        }

        button.main-button {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          width: 100%;
          padding: var(--spacing-m);
          margin-bottom: $spacing-m;
        }
      }

      .page-row {
        display: flex;
        flex-direction: row;

        p.p2.dark.cleared-exchange {
          padding-left: $spacing-xs;
        }

        &.submit-actions {
          padding-top: $spacing-xs;
          justify-content: flex-end;

          button {
            margin-right: $spacing-m;
          }
        }
      }

      .arrow {
        padding: $spacing-l;
        align-self: center;
      }
    }
  }
}
