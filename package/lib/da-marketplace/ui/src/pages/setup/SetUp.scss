@use '../../themes/variables.scss' as *;

@mixin set-up {
  .set-up {
    display: flex;
    flex-direction: column;

    .setup-service {
      p .icon {
        margin-right: 6px;
      }
      .links {
        margin-top: $spacing-m;

        display: flex;
        flex-direction: column;
        margin-left: $spacing-s;

        a {
          margin-bottom: $spacing-s;
          font-weight: 600;
          font-size: 14px;
          line-height: 22px;

          &.disabled {
            color: var(--cool-grey-20);
          }
        }
      }

      .roles {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;

        &:last-child {
          margin-right: 0;
        }

        p.p2.label {
          font-size: 14px;
          color: var(--cool-grey-20);
          background-color: var(--blue-100);
          padding: $spacing-xs $spacing-s;
          border-radius: 6px;
          margin: $spacing-xs $spacing-xs $spacing-xs 0;
          width: fit-content;
          height: fit-content;

          &.no-roles {
            font-style: italic;
          }
        }
      }

      .ghost.secondary-smaller {
        margin: $spacing-xs $spacing-xs $spacing-xs 0;
      }
    }
  }
}
