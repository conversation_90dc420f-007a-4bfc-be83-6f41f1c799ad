@use '../../themes/variables' as *;

@mixin account {
  .account {
    animation: fadeIn 0.5s;
    margin-bottom: $spacing-l;
    width: 100%;

    .account-holding {
      padding: $spacing-s;
      margin-bottom: 0px;
      display: flex;
      border-top: none;
      border-radius: 0px;

      &:last-of-type {
        border-radius: 0px 0px 4px 4px;
      }

      .tile-content {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        width: 100%;
      }
    }

    h4 {
      margin: 0 $spacing-s 0 0;
    }

    .actions {
      display: flex;

      .ghost {
        margin-left: $spacing-s;
      }
    }

    .empty {
      margin: $spacing-s;
    }

    .account-details {
      display: flex;
      flex-direction: column;
      padding: $spacing-s;
      border: 1px solid var(--cool-grey-90);
      border-radius: 4px 4px 0px 0px;

      .account-data {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        &.body {
          justify-content: unset;
        }
      }

      p.p2 {
        margin: $spacing-s $spacing-m 0 0;
      }

      .close-request {
        margin-left: auto;
      }
    }
  }
}
