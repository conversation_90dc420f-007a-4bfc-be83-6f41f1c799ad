@use '../../themes/variables.scss' as *;

@mixin notifications {
  .notifications {
    .notification {
      margin-bottom: $spacing-s;
      background-color: var(--white);
      border: 1px solid var(--cool-grey-90);
      border-radius: 2px;
      padding: $spacing-s;
      display: flex;
      flex-direction: row;
      align-items: center;

      .form-error-handled,
      p.pending {
        margin-left: auto;
      }

      p.pending {
        font-style: italic;
        color: var(--yellow-40);
      }
    }

    .notification-content {
      margin-top: $spacing-m;

      .field {
        display: flex;
        flex-direction: column;
        padding-left: 0;
        padding-right: $spacing-s;
      }

      .button {
        margin-right: $spacing-s;
      }
    }
  }
}
