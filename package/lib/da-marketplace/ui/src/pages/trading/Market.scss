@use '../../themes/variables' as *;

@mixin market {
  .market {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    width: 100%;
    justify-content: space-between;
    margin-top: $spacing-m;

    h3.ui.header {
      margin-bottom: $spacing-m;
    }

    .orders {
      display: flex;
      flex-direction: column;
      width: 66%;
    }

    .order-actions {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }

    .new-order {
      display: flex;
      flex-direction: column;
      width: 33%;

      button {
        margin-bottom: $spacing-m;
      }
    }
  }
}
