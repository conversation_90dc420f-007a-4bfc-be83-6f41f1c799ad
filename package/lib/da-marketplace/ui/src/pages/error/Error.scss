@use './themes/variables.scss' as *;

@mixin error-page {
  .error-page {
    width: 100%;
    height: 100%;

    .error-page-body {
      height: 100%;
      padding: $spacing-s;
      padding-top: $spacing-m;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      background-color: var(--white);

      .header,
      .body {
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      a {
        margin-bottom: $spacing-m;
      }
    }
  }
}
