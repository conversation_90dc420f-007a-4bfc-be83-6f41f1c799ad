import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { But<PERSON>, Form, Header, Icon, Table } from 'semantic-ui-react';

import { CreateEvent } from '@daml/ledger';
import { useLedger, useParty } from '@daml/react';
import { ContractId } from '@daml/types';

import { AssetDeposit } from '@daml.js/da-marketplace/lib/DA/Finance/Asset';
import { Service as CustodyService } from '@daml.js/da-marketplace/lib/Marketplace/Custody/Service';
import {
  Auction as BiddingAuctionContract,
  Bid,
} from '@daml.js/da-marketplace/lib/Marketplace/Distribution/Bidding/Model';
import {
  Service as BiddingService,
  SubmitBid,
} from '@daml.js/da-marketplace/lib/Marketplace/Distribution/Bidding/Service';
import { AssetDescription } from '@daml.js/da-marketplace/lib/Marketplace/Issuance/AssetDescription';

import { useStreamQueries } from '../../../Main';
import { render } from '../../../components/Claims/render';
import { transformClaim } from '../../../components/Claims/util';
import BackButton from '../../../components/Common/BackButton';
import FormErrorHandled from '../../../components/Form/FormErrorHandled';
import StripedTable from '../../../components/Table/StripedTable';
import Tile from '../../../components/Tile/Tile';
import { usePartyName } from '../../../config';
import { getBidAllocation, getBidStatus } from '../Utils';

type Props = {
  biddingServices: Readonly<CreateEvent<BiddingService, any, any>[]>;
  custodyServices: Readonly<CreateEvent<CustodyService, any, any>[]>;
};

export const BiddingAuction: React.FC<Props> = ({ biddingServices, custodyServices }) => {
  const party = useParty();
  const { getName } = usePartyName(party);
  const ledger = useLedger();
  const { contractId } = useParams<any>();

  const [quantity, setQuantity] = useState<number>(0);
  const [price, setPrice] = useState<number>(0);
  const [allowPublishing, setAllowPublishing] = useState<boolean>(false);

  const { contracts: allBiddingAuctions, loading: allBiddingAuctionsLoading } =
    useStreamQueries(BiddingAuctionContract);
  const deposits = useStreamQueries(AssetDeposit).contracts;
  const heldAssets = deposits.filter(c => c.payload.account.owner === party);
  const bids = useStreamQueries(Bid);

  const biddingAuction = allBiddingAuctions.find(b => b.contractId === contractId);

  // TODO : We should refactor the claims into their own component
  const el1 = useRef<HTMLDivElement>(null);
  const el2 = useRef<HTMLDivElement>(null);
  const [showAuctionedAsset, setShowAuctionedAsset] = useState<boolean>(true);
  const [showQuotedAsset, setShowQuotedAsset] = useState<boolean>(false);
  const allAssets = useStreamQueries(AssetDescription).contracts;
  const assets = allAssets.filter(c => c.payload.assetId.version === '0');
  const auctionedAsset = assets.find(a =>
    _.isEqual(a.payload.assetId, biddingAuction?.payload.asset.id)
  );
  const receivableAccount = custodyServices
    .filter(c => c.payload.customer === party)
    .find(c => c.payload.provider === auctionedAsset?.payload.registrar)?.payload.account;
  const service = biddingServices
    .filter(s => s.payload.customer === party)
    .find(s => s.payload.provider === biddingAuction?.payload.provider);

  useEffect(() => {
    if (!el1.current || !biddingAuction) return;
    el1.current.innerHTML = '';
    const auctionedAsset = assets.find(
      c => c.payload.assetId.label === biddingAuction.payload.asset.id.label
    );
    if (!auctionedAsset) return;
    const data = transformClaim(auctionedAsset.payload.claims, 'root');
    render(el1.current, data);
  }, [el1, assets, biddingAuction, showAuctionedAsset]);

  useEffect(() => {
    if (!el2.current || !biddingAuction) return;
    el2.current.innerHTML = '';
    const quotedAsset = assets.find(
      c => c.payload.assetId.label === biddingAuction.payload.quotedAssetId.label
    );
    if (!quotedAsset) return;
    const data = transformClaim(quotedAsset.payload.claims, 'root');
    render(el2.current, data);
  }, [el2, assets, biddingAuction, showQuotedAsset]);

  if (!biddingAuction || !service) return null;

  const bid = bids.contracts.find(b => b.payload.auctionId === biddingAuction.payload.auctionId);

  const rightsizeAsset = async (
    deposit: CreateEvent<AssetDeposit>,
    quantity: string
  ): Promise<ContractId<AssetDeposit>> => {
    if (parseFloat(deposit.payload.asset.quantity) > parseFloat(quantity)) {
      const [[splitDepositCid]] = await ledger.exercise(
        AssetDeposit.AssetDeposit_Split,
        deposit.contractId,
        { quantities: [quantity] }
      );
      return splitDepositCid;
    }
    return deposit.contractId;
  };

  const submitBid = async () => {
    const volume = price * quantity;
    const deposit = heldAssets.find(
      c =>
        c.payload.asset.id.label === biddingAuction.payload.quotedAssetId.label &&
        parseFloat(c.payload.asset.quantity) >= volume
    );
    if (!deposit || !receivableAccount) return;
    const depositCid = await rightsizeAsset(deposit, volume.toString());
    const arg: SubmitBid = {
      auctionCid: biddingAuction.contractId,
      price: price.toString(),
      quantity: quantity.toString(),
      depositCid,
      allowPublishing,
      receivableAccount: receivableAccount,
    };
    await ledger.exercise(BiddingService.SubmitBid, service.contractId, arg);
  };

  return (
    <div className="auction">
      <BackButton />
      <Header as="h2" className="header">
        Auction - {biddingAuction.payload.asset.id.label}
      </Header>
      <div className="bidding">
        <div className="bidding-details">
          <Tile header="Auction Details">
            <Table basic="very">
              <Table.Body>
                <Table.Row key={0}>
                  <Table.Cell key={0}>
                    <b>Issuer</b>
                  </Table.Cell>
                  <Table.Cell key={1}>{getName(biddingAuction.payload.issuer)}</Table.Cell>
                </Table.Row>
                <Table.Row key={1}>
                  <Table.Cell key={0}>
                    <b>Agent</b>
                  </Table.Cell>
                  <Table.Cell key={1}>{getName(biddingAuction.payload.provider)}</Table.Cell>
                </Table.Row>
                <Table.Row key={2}>
                  <Table.Cell key={0}>
                    <b>Auction ID</b>
                  </Table.Cell>
                  <Table.Cell key={1}>{biddingAuction.payload.auctionId}</Table.Cell>
                </Table.Row>
                <Table.Row key={3}>
                  <Table.Cell key={0}>
                    <b>Asset</b>
                  </Table.Cell>
                  <Table.Cell key={1}>
                    <div className="asset-details">
                      <div className="text">{biddingAuction.payload.asset.id.label}</div>
                      <div className="icon">
                        {showAuctionedAsset ? (
                          <Icon
                            name="eye slash"
                            link
                            onClick={() => setShowAuctionedAsset(false)}
                          />
                        ) : (
                          <Icon name="eye" link onClick={() => setShowAuctionedAsset(true)} />
                        )}
                      </div>
                    </div>
                  </Table.Cell>
                </Table.Row>
                <Table.Row key={4}>
                  <Table.Cell key={0}>
                    <b>Quantity</b>
                  </Table.Cell>
                  <Table.Cell key={1}>{biddingAuction.payload.asset.quantity}</Table.Cell>
                </Table.Row>
                <Table.Row key={5}>
                  <Table.Cell key={0}>
                    <b>Quoted Asset</b>
                  </Table.Cell>
                  <Table.Cell key={1}>
                    <div className="asset-details">
                      <div className="text">{biddingAuction.payload.quotedAssetId.label}</div>
                      <div className="icon">
                        {showQuotedAsset ? (
                          <Icon name="eye slash" link onClick={() => setShowQuotedAsset(false)} />
                        ) : (
                          <Icon name="eye" link onClick={() => setShowQuotedAsset(true)} />
                        )}
                      </div>
                    </div>
                  </Table.Cell>
                </Table.Row>
              </Table.Body>
            </Table>
          </Tile>
          <StripedTable
            title="Published Bids"
            headings={['Investor', 'Quantity', 'Allocation %']}
            loading={allBiddingAuctionsLoading}
            rows={biddingAuction.payload.publishedBids.map(c => {
              return {
                elements: [
                  c.investor,
                  c.quantity,
                  (
                    (parseFloat(c.quantity) / parseFloat(biddingAuction.payload.asset.quantity)) *
                    100
                  ).toFixed(2),
                ],
              };
            })}
          />
          {!!bid && (
            <Tile header="Bid">
              <Table basic="very">
                <Table.Body>
                  <Table.Row key={0}>
                    <Table.Cell key={0}>
                      <b>Quantity</b>
                    </Table.Cell>
                    <Table.Cell key={1}>{getName(bid.payload.details.quantity)}</Table.Cell>
                  </Table.Row>
                  <Table.Row key={1}>
                    <Table.Cell key={0}>
                      <b>Price</b>
                    </Table.Cell>
                    <Table.Cell key={1}>
                      {bid.payload.details.price} {bid.payload.quotedAssetId.label}
                    </Table.Cell>
                  </Table.Row>
                  <Table.Row key={2}>
                    <Table.Cell key={0}>
                      <b>Status</b>
                    </Table.Cell>
                    <Table.Cell key={1}>{getBidStatus(bid.payload.status)}</Table.Cell>
                  </Table.Row>
                  {getBidAllocation(bid.payload) && (
                    <Table.Row key={1}>
                      <Table.Cell key={0}>
                        <b>Allocation</b>
                      </Table.Cell>
                      <Table.Cell key={1}>{getBidAllocation(bid.payload)}</Table.Cell>
                    </Table.Row>
                  )}
                </Table.Body>
              </Table>
            </Tile>
          )}
          {!bid && (
            <Tile header="Submit Bid">
              <FormErrorHandled onSubmit={() => submitBid()}>
                <Form.Input
                  label="Quantity"
                  placeholder={biddingAuction.payload.asset.id.label}
                  type="number"
                  required
                  focus
                  onChange={(_, change) => setQuantity(parseFloat(change.value as string))}
                />
                <Form.Input
                  label="Price"
                  placeholder={biddingAuction.payload.quotedAssetId.label}
                  type="number"
                  required
                  onChange={(_, change) => setPrice(parseFloat(change.value as string))}
                />
                <Form.Checkbox
                  label="Allow Publishing of Bid ?"
                  onChange={(_, value) => setAllowPublishing(value.checked as boolean)}
                />
                <Button
                  type="Bid"
                  className="ghost"
                  disabled={price === 0 || quantity === 0 || !receivableAccount}
                  content="Submit"
                />
              </FormErrorHandled>
            </Tile>
          )}
        </div>
        <div className="asset">
          {showAuctionedAsset && (
            <Tile header="Auctioned Asset">
              <div ref={el1} style={{ height: '100%' }} />
            </Tile>
          )}
          {showQuotedAsset && (
            <Tile header="Quoted Asset">
              <div ref={el2} style={{ height: '100%' }} />
            </Tile>
          )}
        </div>
      </div>
    </div>
  );
};
