@use '../../../themes/variables' as *;

@mixin auction {
  .auction {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    width: 100%;
    justify-content: space-between;

    .new-auction {
      display: flex;
      flex-direction: column;
      width: 33%;

      h2 {
        margin-bottom: $spacing-m;
      }

      .form-select {
        display: flex;
        flex-direction: row;
        align-items: center;

        .select {
          width: 90%;
        }

        .icon {
          width: 10%;
        }
      }

      .submit,
      a.a2 {
        display: flex;
        flex-direction: row;
        align-items: center;
      }

      .submit a.a2 {
        margin-left: $spacing-xl;

        .icon {
          margin-right: $spacing-xs;
        }
      }
    }

    .asset {
      display: flex;
      flex-direction: column;
      width: 66%;

      h2 {
        margin-bottom: $spacing-m;
      }

      .submit,
      a.a2 {
        display: flex;
        flex-direction: row;
        align-items: center;
      }

      .submit a.a2 {
        margin-left: $spacing-xl;

        .icon {
          margin-right: $spacing-xs;
        }
      }
    }

    .bids {
      display: flex;
      flex-direction: column;
      width: 66%;

      table {
        th {
          background-color: var(--white);
        }
      }
    }

    .details {
      display: flex;
      flex-direction: column;
      width: 33%;

      table {
        th {
          background-color: var(--white);
        }
      }

      .details-button {
        margin-top: $spacing-s;
      }
    }
  }
}
