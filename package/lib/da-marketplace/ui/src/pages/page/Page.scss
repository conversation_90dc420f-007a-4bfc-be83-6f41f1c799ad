@use './TopMenu';
@use './PageSection';
@use '../../themes/variables' as *;

@mixin page {
  .page {
    @include TopMenu.top-menu();
    @include PageSection.page-section();

    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    width: 100%;
    justify-content: space-between;
    padding: 0;

    .page-loading {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;

      .ui.loader:after {
        border-color: var(--green-50) transparent transparent;
      }
    }

    &.ui.grid {
      display: flex;
      height: 100%;
    }

    > .page-sidemenu {
      &.column {
        padding: 0;
        width: 250px;
        display: flex;
        flex-direction: column;
        align-self: flex-start;
        background-color: var(--cool-grey-10);
        top: 0;
        position: sticky;

        > .ui.secondary.menu {
          margin: 0;
          width: 100%;

          .sub-menu {
            border-top: 2px solid var(--cool-grey-20);

            .empty-item {
              padding-top: 0px;
            }

            p.sub-menu-header {
              color: var(--white);
              font-weight: 200;
              padding: $spacing-m $spacing-m $spacing-s $spacing-m;
              margin: 0;
              font-size: 12px;
            }
          }

          .item {
            display: flex;
            flex-direction: row;
            align-items: center;
            padding: $spacing-m;
            margin: 0;

            p,
            h3 {
              color: var(--white);
            }

            &.active {
              background-color: var(--cool-grey-20);
              border-radius: 0;
            }

            &:hover {
              background-color: var(--cool-grey-20);
              border-radius: 0;
            }

            &.home-item {
              padding: $spacing-l $spacing-m;
              height: 75px;
              display: flex;
              justify-content: space-between;
              align-items: center;

              h1 {
                margin: 0;
              }

              .cog-icon {
                height: 20px;
                width: 20px;
                min-height: 20px; /* prevent icon shrinking if out of space */
                min-width: 20px;
              }
            }

            &.sidemenu-item-normal > p {
              display: flex;
              flex-direction: row;
              align-items: center;
              color: var(--white);

              > .orders-icon {
                width: 18px;
                height: 19px;
              }

              > .icon {
                margin-right: $spacing-s;
                width: 16px;
                height: 16px;
              }
            }
          }
        }
      }
    }

    > .page-body {
      &.column {
        display: flex;
        flex-direction: column;
        width: 100%;
        flex: 1;
        padding: 0;

        .narrow-width-menu {
          display: none;
        }
      }
    }
  }
}
