@use './OverflowMenu';
@use '../../themes/variables' as *;

@mixin top-menu {
  .top-section {
    background-color: var(--white);

    .overflow-menu-item {
      @include OverflowMenu.overflow-menu();
    }

    .ui.menu.top-menu {
      box-shadow: none;
      border: none;
      margin: 0;
      border-radius: 0px;
      min-height: 75px;
      display: flex;
      align-items: center;
      padding: $spacing-s $spacing-l;

      .menu.right.menu,
      .menu.left.menu {
        align-items: center;
      }
      .menu.left.menu {
        margin-top: $spacing-xs;
      }

      .overflow-menu-item {
        display: none;
      }

      .menu > .item {
        padding: 0px;
        &:before {
          display: none;
        }

        > .header > .content {
          display: flex;
          align-items: center;

          .icon-header {
            display: flex;
            align-items: center;
            justify-content: center;

            .icon-wrapper {
              display: inline-flex;
              align-items: center;
              justify-content: center;
              border-radius: 4px;
              margin-right: $spacing-s;

              .icon {
                padding: 0;
                margin: 0;
                width: 20px;
                height: 20px;
                path {
                  stroke: var(--green-50);
                }
                &.wallet-icon,
                &.orders-icon,
                &.tool-icon {
                  path {
                    stroke: unset;
                    fill: var(--green-50);
                  }
                }
              }
            }
          }
        }

        .menu-button {
          animation: fadeIn 0.5s;
        }
      }

      .log-out-button {
        .log-out {
          display: flex;
          align-items: center;

          .icon.logout-icon {
            margin-left: $spacing-s;
          }
        }
      }
    }

    .notification-button {
      margin: 0 $spacing-l;
      padding: $spacing-s !important;
      border-radius: 4px;

      &:hover {
        background-color: var(--cool-grey-100);
        cursor: pointer;
      }

      .ghost.smaller {
        height: 16px;
      }

      .notifications-icon {
        position: relative;
        border-radius: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 16px;
        top: -29px;
        right: -16px;
        color: white;
        font-size: 11px;

        &.active {
          background-color: var(--green-50);
        }
        &.pending {
          background-color: var(--yellow-40);
        }
      }
    }

    @media screen and (max-width: 850px) {
      .overflow-menu-item {
        display: flex !important;
      }

      .right.menu {
        margin: 0px;
      }

      .menu-button {
        display: none;
      }

      .log-out {
        p {
          display: none;
        }

        .icon.logout-icon {
          margin-left: $spacing-xs;
        }
      }
    }
  }
}
