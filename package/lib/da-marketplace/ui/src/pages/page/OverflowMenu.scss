@use '../../themes/variables' as *;

@mixin overflow-menu {
  .overflow-menu {
    position: relative;
    align-items: center;

    &.ui.button.ghost {
      display: inline-flex;
      background: none;
      border: none;
      padding: 0;
      margin: 0;
    }

    &:hover {
      .overflow-menu-title > svg > .fill-blue {
        fill: var(--blue-40);
      }
    }

    &.inactive {
      .overflow-menu-title > svg > .fill-blue {
        fill: var(--cool-grey-60);
      }
    }

    .overflow-menu-title {
      display: flex;
      align-items: center;

      svg {
        .fill-blue {
          fill: var(--blue-50);
        }
      }
    }

    .overflow-menu-modal {
      top: $spacing-m;
      width: 250px;
      display: block;
      position: absolute;
      background-color: var(--white);
      border: 1px solid var(--cool-grey-90);
      border-radius: 4px;
      box-shadow: 0px 4px 4px var(--cool-grey-90);

      z-index: 1;
      text-align: left;

      &.left {
        left: 0px;
      }

      &.right {
        right: 0px;
      }

      .overflow-menu-entry {
        padding: $spacing-s;
        white-space: nowrap;
        color: var(--textcolor);
      }

      .overflow-menu-entry:not(:last-child) {
        border-bottom: 1px solid var(--cool-grey-90);
      }

      .overflow-menu-entry a {
        display: flex;
        align-items: center;
      }

      .overflow-menu-entry:hover {
        background-color: var(--blue-100);
      }
    }
  }
}
