@use 'themes/colors';
@use 'themes/button';
@use 'themes/overrides';

@use 'components/Tile/TilePage';
@use 'components/Tile/Tile';
@use 'components/Table/StripedTable';
@use 'components/Form/CalendarInput';
@use 'components/Widget/Widget';

@use 'components/InputDialog/InputDialog';
@use 'components/Common/TitleWithActions';
@use 'components/Common/InfoCard';

@use 'pages/error/Error';
@use 'pages/login/Login';
@use 'pages/QuickSetup/QuickSetup';
@use 'pages/landing/Landing';
@use 'pages/setup/SetUp';
@use 'pages/page/Page';
@use 'pages/page/WelcomeHeader';
@use 'pages/page/OverflowMenu';
@use 'pages/custody/Account';

@use 'pages/notifications/Notifications';
@use 'pages/distribution/auction/Auction';
@use 'pages/distribution/bidding/Bidding';
@use 'pages/listing/Listing';
@use 'pages/trading/Market';

@use './icons/icons';
@use './App';

@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400&display=swap');

@font-face {
  font-family: 'BrownStd-Regular';
  src: url('../src/fonts/BrownStd-Regular.otf') format('opentype');
}

@font-face {
  font-family: 'BrownStd-Light';
  src: url('../src/fonts/BrownStd-Light.otf') format('opentype');
}

@font-face {
  font-family: 'BrownStd-Bold';
  src: url('../src/fonts/BrownStd-Bold.otf') format('opentype');
}

html {
  height: 100%;

  code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
  }

  h1.ui.header {
    font-family: 'BrownStd-Light';
    font-style: normal;
    font-weight: normal;
    font-size: 20px;
    line-height: 19px;
    color: var(--textcolor);
    margin: 0px;

    &.dark {
      color: var(--white);
    }

    &.bold {
      font-weight: bold;
    }
  }

  h2.ui.header {
    font-family: 'Open sans';
    font-style: normal;
    font-weight: normal;
    font-size: 20px;
    line-height: 27px;
    color: var(--textcolor);
    margin-top: 24px;
    margin-bottom: 12px;

    &.dark {
      color: var(--white);
    }

    &.bold {
      font-weight: 600;
    }
  }

  h3.ui.header {
    font-family: 'Open sans';
    font-style: normal;
    font-weight: normal;
    font-size: 16px;
    line-height: 22px;
    margin: 0px;
    color: var(--textcolor);

    &.dark {
      color: var(--white);
    }

    &.bold {
      font-weight: 600;
    }
  }

  h4.ui.header {
    font-family: 'Open sans';
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 19px;
    color: var(--textcolor);
  }

  p {
    font-family: 'Open sans';
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 19px;
    margin: 0px;
    color: var(--grey-10);

    &.error {
      color: var(--red-50);
    }

    &.bold {
      font-weight: 600;
    }

    &.dark {
      color: var(--white);
    }

    &.p2 {
      font-size: 12px;
      line-height: 16px;
      color: var(--textcolor);

      &.dark {
        color: var(--white);
      }

      &.bold {
        font-weight: 600;
      }
    }
  }

  @mixin link() {
    color: var(--blue-50);
    font-family: Open Sans;
    font-style: normal;
    font-weight: normal;
    font-size: 16px;
    line-height: 22px;

    &.bold {
      font-weight: 600;
    }

    &.a2 {
      font-size: 14px;
      line-height: 19px;

      &.bold {
        font-weight: 600;
      }

      &.with-icon {
        display: flex;
        flex-direction: row;
        align-items: center;
        width: fit-content;

        .icon {
          margin-right: 6px;
        }
      }
    }

    &:hover {
      cursor: pointer;
    }
  }

  a {
    @include link();
  }

  .ui.dimmer {
    background: rgba(255, 255, 255, 0.85);
  }

  body {
    margin: 0;
    height: 100%;
    background-color: colors.$background-dark;

    .ui.page.modals {
      z-index: 3;

      .modal {
        border-radius: 4px;

        .header {
          border-radius: 2px 2px 0px 0px;
          border: none;
          font-weight: normal;
        }

        .field {
          > label {
            font-family: 'Open sans';
            line-height: 19px;
            color: var(--textcolor);
          }
          > .ui.selection.dropdown {
            line-height: inherit;
          }
        }

        h4.ui.header {
          margin-bottom: 6px;
        }

        .actions {
          background-color: var(--white);
          border: none;
          border-radius: 0px 0px 2px 2px;
        }

        .actions > .button {
          color: var(--textcolor);
          background: var(--white);
          border: 1px solid var(--green-50);
          border-radius: 4px;
          font-weight: 400;
        }
      }
    }

    #root {
      height: 100%;

      .app {
        width: 100vw;
        height: 100vh;

        @include overrides.overrides();
        @include WelcomeHeader.welcome-header();
        @include icons.icons();
        @include Tile.tile();
        @include TilePage.tile-page();
        @include StripedTable.striped-table();
        @include CalendarInput.calendar-input();
        @include App.app();
        @include SetUp.set-up();
        @include Page.page();
        @include OverflowMenu.overflow-menu();
        @include InfoCard.info-card();
        @include Error.error-page();
        @include Login.login-screen();
        @include QuickSetup.quick-setup();
        @include Landing.landing();
        @include Account.account();
        @include Auction.auction();
        @include Bidding.bidding();
        @include Listing.listing();
        @include Market.market();
        @include Notifications.notifications();
        @include TitleWithActions.title-with-actions();
        @include InputDialog.input-dialog();
        @include Widget.widget();

        button.button.a,
        button.button.a.ghost {
          @include link();

          display: inline-flex;
          align-items: center;
          justify-content: center;
          background: none;
          padding: 0;
          margin: 0;
          border: none;
        }

        button.ui.button.icon-button {
          display: flex;
          align-items: center;
          justify-content: center;
          background: none;
          border: none;
          padding: 0px;
          margin: 8px;
          width: 32px;
          height: 32px;

          .icon {
            margin: 0;
          }
        }

        .ui.message {
          position: absolute;
          top: 0px;
          right: 20px;
          min-width: 300px;
          min-height: 125px;
          animation: slideInLeft 0.2s;
          z-index: 4;
          background-color: var(--white);
          border-radius: 2px;
          border: 1px solid var(--cool-grey-90);
          box-shadow: unset;

          .content,
          h3 {
            color: var(--textcolor);
          }
        }

        .ui.success.message {
          border-left: 3px solid var(--green-50);
          color: var(--green-50);
        }

        .ui.error.message {
          border-left: 3px solid var(--red-50);
          color: var(--red-50);
        }

        @keyframes slideInLeft {
          0% {
            opacity: 0;
            -webkit-transform: translateX(2000px);
            -ms-transform: translateX(2000px);
            transform: translateX(2000px);
          }
          100% {
            -webkit-transform: translateX(0);
            -ms-transform: translateX(0);
            transform: translateX(0);
          }
        }
      }
    }
  }
}
