@import './button.scss';

@mixin overrides {
  .ui {
    @include ui-button;

    &.grid {
      margin: 0;
      flex: 1;

      & > .row {
        width: unset !important;
      }
    }

    &.form {
      & > .fields.inline-form-group {
        align-items: flex-end;
        margin: 0;

        &.with-error {
          align-items: flex-start;

          button {
            margin-top: 32px;
          }
        }

        .error.field {
          width: 196px;
        }
      }

      &.loading:before {
        background-color: transparent;
      }

      .field {
        > label {
          font-family: 'Open sans';
          font-style: normal;
          font-weight: normal;
          font-size: 16px;
          line-height: 22px;
          padding-bottom: 8px;
        }
        > .ui.selection.dropdown {
          line-height: inherit;
        }
      }
    }
  }
}

.ui.primary.button,
.ui.primary.buttons .button {
  background-color: var(--dabl-color);
}

.ui.secondary.button,
.ui.secondary.buttons .button,
.ui.secondary.button:focus {
  background-color: var(--cool-grey-30);
  border-radius: 4px;
  background-color: white;
  color: var(--textcolor);
}

.ui.secondary.button:hover {
  background: var(--grey2);
  color: var(--textcolor);
}

.ui.secondary.button.disabled,
.ui.secondary.buttons.disabled .button {
  border: 1px solid var(--grey4);
  border-radius: 4px;
  color: var(--textcolor);
}

.ui.button.close-button,
.ui.buttons.close-button .button {
  display: contents;
}
.ui.toggle.checkbox input:focus:checked ~ .box:before,
.ui.toggle.checkbox input:focus:checked ~ label:before,
.ui.toggle.checkbox input:checked ~ .box:before,
.ui.toggle.checkbox input:checked ~ label:before {
  background-color: var(--blue-50) !important;
}

.ui.toggle.checkbox .box:before,
.ui.toggle.checkbox label:before {
  background-color: var(--grey2);
}
