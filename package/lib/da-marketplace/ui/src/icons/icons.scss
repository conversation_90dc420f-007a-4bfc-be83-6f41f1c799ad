@use '../themes/variables.scss' as *;

@mixin icon-size($size) {
  &.icon-size-#{$size} {
    height: #{$size + 'px'};
    width: #{$size + 'px'};
  }
}

@mixin icons {
  .icon {
    height: 16px;

    @include icon-size(24);
    @include icon-size(32);
    @include icon-size(48);
    @include icon-size(64);

    &.arrow-right-icon > path {
      fill-rule: evenodd;
      clip-rule: evenodd;
      fill: var(--textcolor);
    }

    &.arrow-left-icon {
      transform: rotate(180deg);
    }

    &.fill-blue,
    path.fill-blue {
      fill: var(--blue-60);
    }

    &.fill-green,
    path.fill-green {
      fill: var(--green-50);
    }

    &.fill-white,
    path.fill-white {
      fill: var(--white);
    }

    &.fill-grey,
    path.fill-grey {
      fill: var(--cool-grey-40);
    }

    &.circled-check-icon {
      position: absolute;
      top: -8px;
      right: -10px;
      z-index: 2;
    }

    &.megaphone-icon {
      path {
        fill: none;
      }
    }

    &.notification-icon {
      width: 18px;
      height: 18px;
    }
  }
}
