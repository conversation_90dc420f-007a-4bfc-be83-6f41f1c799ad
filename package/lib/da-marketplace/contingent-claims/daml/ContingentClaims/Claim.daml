--
-- Copyright (c) 2021, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0
--

module ContingentClaims.Claim where

import Prelude hiding (or, and)
import Daml.Control.Recursion
import ContingentClaims.Claim.Serializable qualified as Serialized
import ContingentClaims.Observation (Observation)
import ContingentClaims.Observable (Inequality(..))
import ContingentClaims.Observable qualified as Observable
import DA.Foldable (Foldable(..))
import DA.Traversable (Traversable(..))

type T = Claim
type F = ClaimF

-- TODO: f should depend on t ?

-- | Used to model cashflows of instruments.
-- See [quickstart](../QUICKSTART.md) for detailed explanation.
-- In <PERSON><PERSON><PERSON>' paper, this is called 'Contract'.
-- We renamed it to avoid ambiguity.
-- * `f`, `t` and `x` respectively correspond to the `Observable`, it's input type and the resulting output type.
-- * `a` is the representation of an asset, e.g. a `Text` ISIN code.
data Claim f t x a
  = Zero
      -- ^ Represents an absence of claims.
  | One a
      -- ^ The bearer acquires one unit of `a` *immediately*.
  | Give (Claim f t x a)
      -- ^ The obligations of the bearer and issuer are revesed.
  | And with lhs: Claim f t x a, rhs: Claim f t x a
      -- ^ Used to combine multiple rights together.
  | Or with lhs: Claim f t x a, rhs: Claim f t x a
      -- ^ Gives the bearer the right to choose between two claims.
  | Cond with predicate: Inequality f t x a, success: Claim f t x a, failure: Claim f t x a --TODO rename predicate -> `lte` or `inequality`
      -- ^ Gives the bearer the right to the first claim if `predicate` is true, else the second claim.
  | Scale with k: f t x a, claim: Claim f t x a
      -- ^ Multiplies the `claim` by `k` (which can be non-deterministic).
  | When with predicate: Inequality f t x a, claim: Claim f t x a
      -- ^ Defers the acquisition of `claim` until *the first instant* that `predicate` is true.
  | Anytime with predicate: Inequality f t x a, claim: Claim f t x a
      -- ^ Like `When`, but valid any time the predicate is true (not just infinium).
  | Until with predicate: Inequality f t x a, claim: Claim f t x a
      -- ^ Expires said claim on the *first instant* that `predicate` is true.

-- | Replace parameters in an `Claim` with actual values.
mapParams :  Observable.Interpret f
          => (t -> i)
          -> (i -> t)
          -> (a -> a')
          -> (x -> x')
          -> Claim f i x a -> Claim f t x' a'
mapParams ft' ft fk fv =
  let f = Observable.mapParams ft' fk fv
  in cata \case
    ZeroF -> Zero
    OneF a -> One $ fk a
    GiveF c -> Give c
    AndF c c' -> And c c'
    OrF c c' -> Or c c'
    CondF (Lte (x, x')) c c' -> Cond (Lte(f x, f x')) c c'
    CondF (TimeGte t) c c' -> Cond (TimeGte (ft t)) c c'
    ScaleF k c -> Scale (f k) c
    WhenF (Lte (x, x')) c -> When (Lte (f x, f x')) c
    WhenF (TimeGte t) c -> When (TimeGte (ft t)) c
    AnytimeF (Lte (x, x')) c -> Anytime (Lte (f x, f x')) c
    AnytimeF (TimeGte t) c -> Anytime (TimeGte (ft t)) c
    UntilF (Lte (x, x')) c -> Until (Lte (f x, f x')) c
    UntilF (TimeGte t) c -> Until (TimeGte (ft t)) c

-- | Converts a `Serialized.Claim` read from ledger, to a more general `Claim`, for use with `Daml.Control.Recursion`.
deserialize : Serialized.Claim t x a -> Claim Observation t x a
deserialize = ana deserialize'

-- | F-algebra for `deserialize`; it can be composed in unfolds.
deserialize' : Serialized.Claim t x a -> ClaimF Observation t x a (Serialized.Claim t x a)
deserialize' Serialized.Zero = ZeroF
deserialize' (Serialized.One a) = OneF a
deserialize' (Serialized.Give c) = GiveF c
deserialize' (Serialized.And [c]) = deserialize'  c
deserialize' (Serialized.And (c :: cs)) = AndF c (Serialized.And cs)
deserialize' (Serialized.And []) = error "deserialize: Malformed `And` (should have at least two elements)"
deserialize' (Serialized.Or c c') = OrF c c'
deserialize' (Serialized.Cond p c c') = CondF (deserializeInequality p) c c'
deserialize' (Serialized.Scale k c) = ScaleF k c
deserialize' (Serialized.When p c) = WhenF (deserializeInequality p) c
deserialize' (Serialized.Anytime p c) = AnytimeF (deserializeInequality p) c
deserialize' (Serialized.Until p c) = UntilF (deserializeInequality p) c

deserializeInequality : Serialized.Inequality t x a -> Inequality Observation t x a
deserializeInequality (Serialized.TimeGte t) = TimeGte t
deserializeInequality (Serialized.Lte (f, f')) = Lte (f, f')

serializeInequality : Inequality Observation t x a -> Serialized.Inequality t x a
serializeInequality (TimeGte t) = Serialized.TimeGte t
serializeInequality (Lte (f, f')) = Serialized.Lte (f, f')

-- | Converts a `Claim` into a `Serializable.Claim`, so it can be written to the ledger.
serialize : Claim Observation t x a -> Serialized.Claim t x a
serialize = histo serialize'

-- | F-coalgebra for `serialize`; it can be composed with other folds.
serialize' : ClaimF Observation t x a (Cofree (ClaimF Observation t x a) (Serialized.Claim t x a)) -> Serialized.Claim t x a
serialize' ZeroF = Serialized.Zero
serialize' (OneF a) = Serialized.One a
serialize' (GiveF c) = Serialized.Give c.attribute
serialize' (AndF c (Cofree (Serialized.And cs) _) ) = Serialized.And $ c.attribute :: cs
serialize' (AndF c c') = Serialized.And [c.attribute, c'.attribute]
serialize' (OrF c c') = Serialized.Or c.attribute c'.attribute
serialize' (CondF k c c') = Serialized.Cond (serializeInequality k) c.attribute c'.attribute
serialize' (ScaleF k c) = Serialized.Scale k c.attribute
serialize' (WhenF p c) = Serialized.When (serializeInequality p) c.attribute
serialize' (AnytimeF p c) = Serialized.Anytime (serializeInequality p) c.attribute
serialize' (UntilF p c) = Serialized.Until (serializeInequality p) c.attribute

-- | Unfixed version of `Claim`, for use with `Daml.Control.Recursion`.
data ClaimF f t x a b
  = ZeroF
  | OneF a
  | GiveF b
  | AndF with lhs: b, rhs: b
  | OrF with lhs: b, rhs: b
  | CondF with predicate: Inequality f t x a, success: b, failure: b
  | ScaleF with k: f t x a, claim: b
  | WhenF with predicate: Inequality f t x a, claim: b
  | AnytimeF with predicate: Inequality f t x a, claim: b
  | UntilF with predicate: Inequality f t x a, claim: b
  deriving (Functor)

instance Recursive (Claim f t x a) (ClaimF f t x a) where
  project Zero = ZeroF
  project (One a) = OneF a
  project (Give c) = GiveF c
  project (And c c') = (AndF c c')
  project (Or c c') = (OrF c c')
  project (Cond p c c') = CondF p c c'
  project (Scale k c) = ScaleF k c
  project (When p c) = WhenF p c
  project (Anytime p c) = AnytimeF p c
  project (Until p c) = UntilF p c

instance Corecursive (Claim f t x a) (ClaimF f t x a) where
  embed ZeroF = Zero
  embed (OneF x) = One x
  embed (GiveF a) = Give a
  embed (AndF c c') = (And c c')
  embed (OrF c c') = (Or c c')
  embed (CondF p c c') = Cond p c c'
  embed (ScaleF k c) = Scale k c
  embed (WhenF p c) = When p c
  embed (AnytimeF p c) = Anytime p c
  embed (UntilF p c) = Until p c

instance Foldable (ClaimF f t x a) where
  foldr _ seed ZeroF = seed
  foldr _ seed (OneF _) = seed
  foldr f seed (GiveF c) = f c seed
  foldr f seed (WhenF _ c) = f c seed
  foldr f seed (ScaleF _ c) = f c seed
  foldr f seed (AndF c c') = f c $ f c' seed
  foldr f seed (OrF c c') = f c $ f c' seed
  foldr f seed (CondF _ c c') = f c $ f c' seed
  foldr f seed (AnytimeF _ c) = f c seed
  foldr f seed (UntilF _ c) = f c seed

instance Traversable (ClaimF g t x b) where
  sequence ZeroF = pure ZeroF
  sequence (OneF asset) = pure $ OneF asset
  sequence (GiveF fa) = GiveF <$> fa
  sequence (WhenF p fa) = WhenF p <$> fa
  sequence (ScaleF p fa) = ScaleF p <$> fa
  sequence (AndF fa fa') = AndF <$> fa <*> fa'
  sequence (OrF fa fa') = OrF <$> fa <*> fa'
  sequence (CondF p fa fa') = CondF p <$> fa <*> fa'
  sequence (AnytimeF p fa) = AnytimeF p <$> fa
  sequence (UntilF p fa) = UntilF p <$> fa
