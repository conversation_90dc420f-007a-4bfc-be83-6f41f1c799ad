--
-- Copyright (c) 2021, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0
--

{-# OPTIONS -Wno-deprecations #-} -- To supress 'Monad' warnings

module ContingentClaims.Observation (
    Observation(..)
) where

import ContingentClaims.Observable qualified as O
import ContingentClaims.Util.Recursion (cataM)
import Daml.Control.Arrow (Kleisli(..), arr)
import Daml.Control.Recursion (Recursive(..), Corecursive(..), project, embed)
import DA.Traversable (Traversable(..))
import DA.Foldable (Foldable(..))
import Prelude hiding (key, (.))

type T = Observation

-- | Concrete implementation of `Observable`, which can be serialized.
-- Conceptually it's helpful to think of this as the type `t -> x`, or `t -> Update x`.
data Observation t x a
  = Const with value: x
    -- ^ A named constant
  | Observe with key: a
    -- ^ A named parameter
  | Add (Observation t x a, Observation t x a)
  | Neg (Observation t x a)
  | Mul (Observation t x a, Observation t x a)
  | Div (Observation t x a, Observation t x a)
  deriving (Eq, Show, Functor)

data ObservationF t x a b
  = ConstF with value: x
  | ObserveF with key: a
  | AddF (b, b)
  | NegF (b)
  | MulF (b, b)
  | DivF (b, b)
  deriving (Eq, Show, Functor)

instance Recursive (Observation t x a) (ObservationF t x a) where
  project (Const value) = ConstF value
  project (Observe key) = ObserveF key
  project (Add (x, x')) = AddF (x, x')
  project (Neg x) = NegF x
  project (Mul (x, x') ) = MulF (x, x')
  project (Div (x, x') ) = DivF (x, x')

instance Corecursive (Observation t x a) (ObservationF t x a) where
  embed (ConstF value) = Const value
  embed (ObserveF key) = Observe key
  embed (AddF (x, x')) = Add (x, x)
  embed (NegF x) = Neg x
  embed (MulF (x, x') ) = Mul (x, x')
  embed (DivF (x, x') ) = Div (x, x')

instance O.Point Observation t x a where
  pure = Const
  observe = Observe

instance Additive x => Additive (Observation t x a) where
  aunit = Const aunit
  (+) = curry Add
  negate = Neg

instance Multiplicative x => Multiplicative (Observation t x a) where
  munit = Const munit
  (*) = curry Mul
  x ^ 0 = munit
  x ^ 1 = x
  x ^ 2 = x * x
  x ^ _ = error "Observation: power operator < 0 and > 2 not implemented"

instance (Additive x, Multiplicative x) => Number (Observation t x a) where

instance Multiplicative x => Divisible (Observation t x a) where
  (/) = curry Div

instance Foldable (ObservationF t x a) where
  foldr f seed (ConstF _) = seed
  foldr f seed (ObserveF key) = seed
  foldr f seed (NegF b) = f b seed
  foldr f seed (AddF (b, b')) = f b $ f b' seed
  foldr f seed (MulF (b, b')) = f b $ f b' seed
  foldr f seed (DivF (b, b')) = f b $ f b' seed

instance Traversable (ObservationF t x a) where
  sequence (ConstF x) = pure $ ConstF x
  sequence (ObserveF key) = pure $ ObserveF key
  sequence (NegF m) = NegF <$> m
  sequence (AddF (m, m')) = curry AddF <$> m <*> m'
  sequence (MulF (m, m')) = curry MulF <$> m <*> m'
  sequence (DivF (m, m')) = curry DivF <$> m <*> m'

instance O.Interpret Observation where
  eval doObserve = cataM \case
    ConstF x -> pure x
    ObserveF key -> Kleisli (doObserve key)
    AddF (x, x') -> pure $ x Prelude.+ x'
    NegF x -> pure $ negate x
    MulF (x, x') -> pure $ x * x'
    DivF (x, x') -> pure $ x / x'

  compare doObserve (O.Lte (f, f')) = liftA2 (<=) (O.eval doObserve f) (O.eval doObserve f')
  compare _ (O.TimeGte t) = arr (>= t)

  mapParams _ g f = cata \case
    ConstF x -> Const (f x)
    ObserveF key -> Observe (g key)
    AddF (b, b') -> Add (b, b')
    NegF b -> Neg b
    MulF (b, b') -> Mul (b, b')
    DivF (b, b') -> Div (b, b')
