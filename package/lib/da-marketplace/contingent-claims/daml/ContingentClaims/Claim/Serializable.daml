--
-- Copyright (c) 2021, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0
--

module ContingentClaims.Claim.Serializable where

import ContingentClaims.Observation
import Daml.Control.Recursion

type T = Claim
type F = ClaimF

-- | We require a separate data type for a serializable claim, because DAML
-- does not support serialization of higher kinds. i.e. the `f` in `Claim`.
-- Here we replace it with the concrete `Observation`.
data Claim t x a
  = Zero
      -- ^ Represents an absence of claims.
  | One a
      -- ^ The bearer acquires one unit of `a` *immediately*.
  | Give (Claim t x a)
      -- ^ The obligations of the bearer and issuer are revesed.
  | And with claims : [Claim t x a]
      -- ^ Used to combine multiple rights together.
      --   __Note__ This serialized version flattens right-associative `And`s to mitigate language recursion limits.
  | Or with lhs: Claim t x a, rhs: Claim t x a
      -- ^ Gives the bearer the right to choose between two claims.
  | Cond with predicate: Inequality t x a, success: Claim t x a, failure: Claim t x a
      -- ^ Gives the bearer the right to the first claim if `predicate` is true, else the second claim.
  | Scale with k: Observation t x a, claim: Claim t x a
      -- ^ Multiplies the `claim` by `k` (which can be non-detrerministic).
  | When with predicate: Inequality t x a, claim: Claim t x a
      -- ^ Defers the acquisition of `claim` until *the first instant* that `predicate` is true.
  | Anytime with predicate: Inequality t x a, claim: Claim t x a
      -- ^ Like `When`, but valid any time the predicate is true (not just infinium).
  | Until with predicate: Inequality t x a, claim: Claim t x a
      -- ^ Expires said claim on the *first instant* that `predicate` is true.

  deriving (Eq, Show, Functor)

-- | Serializable version of `Observable.Inequality`
data Inequality t x a = TimeGte t | Lte (Observation t x a, Observation t x a) deriving (Eq, Show, Functor)

-- | Unfixed version of `Claim`, for use with `Daml.Control.Recursion`.
data ClaimF t x a b
  = ZeroF
  | OneF a
  | GiveF b
  | AndF with claims: [b]
  | OrF with lhs: b, rhs: b
  | CondF with predicate: Inequality t x a, success: b, failure: b
  | ScaleF with k: Observation t x a, claim: b
  | WhenF with predicate: Inequality t x a, claim: b
  | AnytimeF with predicate: Inequality t x a, claim: b
  | UntilF with predicate: Inequality t x a, claim: b
  deriving Functor

instance Recursive (Claim t x a) (ClaimF t x a) where
  project Zero = ZeroF
  project (One a) = OneF a
  project (Give c) = GiveF c
  project (And cs) = (AndF cs)
  project (Or c c') = (OrF c c')
  project (Cond a c c') = CondF a c c'
  project (Scale k c) = ScaleF k c
  project (When p c) = WhenF p c
  project (Anytime p c) = AnytimeF p c
  project (Until p c) = UntilF p c

instance Corecursive (Claim t x a) (ClaimF t x a) where
  embed ZeroF = Zero
  embed (OneF a) = One a
  embed (GiveF c) = Give c
  embed (AndF cs) = (And cs)
  embed (OrF c c') = (Or c c')
  embed (CondF a c c') = Cond a c c'
  embed (ScaleF k c) = Scale k c
  embed (WhenF p c) = When p c
  embed (AnytimeF p c) = Anytime p c
  embed (UntilF p c) = Until p c
