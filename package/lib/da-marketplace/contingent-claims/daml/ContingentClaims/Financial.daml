--
-- Copyright (c) 2021, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0
--

module ContingentClaims.Financial where

import Prelude hiding (and, or, (<=))
import ContingentClaims.Claim
import ContingentClaims.Observable qualified as O
import ContingentClaims.Observable (Observable, at)
import Daml.Control.Recursion
import DA.Date (date, Month)

-- | Helper function to generate a series of fixing dates, e.g. for coupon payments in `fixed`.
-- This assumes `fixingMonths` and `fixingDates` are ordered.
-- The [finance lib](https://github.com/digital-asset/lib-finance) has more feature-complete date handling functions.
unrollDates : Int -> Int -> [Month] -> Int -> [Date]
unrollDates issueYear maturityYear fixingMonths fixingDay =
  date <$> [issueYear .. maturityYear] <*> fixingMonths <*> [fixingDay]

-- | Forward agreement. Discounted by (potentially stochastic) interest rate `r`.
forward : Observable f t x a => t -> f t x a -> Claim f t x a -> Claim f t x a
forward maturity r payoff = When (at maturity) $ Scale r payoff

-- | Forward rate agreement.
fra : Observable f t x a => t -> t -> f t x a -> f t x a -> Claim f t x a -> Claim f t x a
fra t₁ t₂ r₀ r₁ = forward t₁ r₀ . forward t₂ r₁

-- | Zero Coupon Bond.
zcb : Observable f t x ccy => t -> x -> ccy -> Claim f t x ccy
zcb maturity principal ccy = forward maturity (O.pure principal) (One ccy)

-- | A floating rate bond. The first two arguments are `Observable`s.
floating : Observable f t x ccy => f t x ccy -> f t x ccy -> ccy -> [t] -> Claim f t x ccy
floating principal coupon asset = apo \case
     [maturity] -> Left (forward maturity coupon (One asset)) `AndF`
                   Left (forward maturity principal (One asset))
     (t :: ts) -> Left (forward t coupon (One asset)) `AndF` Right ts
     [] -> ZeroF

-- | A (fixed rate) coupon paying bond.
fixed : Observable f t x ccy => x -> x -> ccy -> [t] -> Claim f t x ccy
fixed principal coupon = floating (O.pure principal) (O.pure coupon)

-- | European option on the passed claim. e.g. call option on S&P 500:
-- ```
-- european (at $ date 2021 05 14) (Zero `Or` observe "SPX" - pure 4200)
-- ```
european : t -> Claim f t x a -> Claim f t x a -- TODO : consider swapping ordre of parameters for consistency with bermudan?
european maturity payoff = When (at maturity) (payoff `Or` Zero)

-- | Bermudan option on the passed claim.
bermudan : Claim f t x a -> [t] -> Claim f t x a
bermudan u = apo \case
  (t :: ts) -> Left (european t u) `OrF` Right ts
  [] -> ZeroF

-- | American option (knock-in). The lead parameter is the first possible acquisition date.
american : t -> t -> Claim f t x a -> Claim f t x a
american t maturity payoff = Anytime ((>=) t) $ Until (at maturity) (payoff `Or` Zero)
  where (>=) = at

-- | Asset swap on specific fixing dates `[t]`. For example:
-- ```
-- fixedUsdVsFloatingEur : [t] -> Serializable.Claim Text
-- fixedUsdVsFloatingEur = fixed 100.0 0.02 "USD" `swap` floating (observe "USDEUR" * pure 100.0) (observe "EUR1M") "EUR"
-- ```
swap : ([t] -> Claim f t x a) -> ([t] -> Claim f t x a) -> [t] -> Claim f t x a
swap receive pay ts = receive ts `And` Give (pay ts)
