--
-- Copyright (c) 2021, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0
--

module ContingentClaims.Observable where

import Prelude hiding (pure)
import Daml.Control.Arrow (Kleisli(..))

type Observable f t x a = (Point f t x a, Number (f t x a), Divisible (f t x a))

-- | This is either `time ≥ t | f t x ≤ f t x`
data Inequality f t x a = TimeGte t | Lte (f t x a, f t x a)

-- | Observable that is true on the passed time. i.e. identity for the time observable.
at : t -> Inequality f t x a
at t = TimeGte t

infix 4 <=
-- | `import Prelude hiding ((<=))` in order to use this.
(<=) : f t x a -> f t x a -> Inequality f t x a
(<=) = curry Lte

deriving instance (Eq t, Eq (f t x a)) => Eq (Inequality f t x a)

deriving instance (Show t, Show (f t x a)) => Show (Inequality f t x a)

class Point f t x a where
  pure : x -> f t x a
  observe : a -> f t x a

class Interpret f where
  -- | Reify the `Observable` into a Kleisli function.
  -- The function is only total when the first argument is too (typically it will fail on `t` > today).
  eval : (Number x, Divisible x, Action m) => (a -> t -> m x) -> f t x a -> Kleisli m t x
  -- | Reify the `Observable.Inequality` into a Kleisli function.
  compare : (Ord t, Ord x, Number x, Divisible x, Action m) => (a -> t -> m x) -> Inequality f t x a -> Kleisli m t Bool
  -- | The functor map operation _and_ also map any parametrs to keys.
  -- For example, could map the param "spot" to an ISIN code "GB123456789".
  -- Also contra-maps time parameter, i.e. from relative time values to absolute ones.
  --
  -- @ mapParams identity = bimap
  --
  mapParams : (t -> i)
            -> (a -> a')
            -> (x -> x')
            -> f i x a -> f t x' a'
