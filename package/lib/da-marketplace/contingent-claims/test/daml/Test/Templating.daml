--
-- Copyright (c) 2021, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0
--

{-# LANGUAGE UndecidableInstances #-} --needed to derive cofree Show,Eq

-- | This gives an example of how to 'template' claims.
-- The basic idea is to store variable names in the 'holes' in the `Observable` leaves; this creates
-- a template with placeholders for the actual values we want. We then map these parameters to values
-- when we're ready to 'issue' the instrument, using `mapParams`.
module Test.Templating where

import Daml.Script
import ContingentClaims.Financial
import ContingentClaims.Observable qualified as O
import ContingentClaims.Observation (Observation(..))
import ContingentClaims.Claim
import DA.Date
import DA.Assert
import DA.Optional (fromSomeNote)
import DA.Map qualified as Map

deriving instance (Show t, Show x, Show a) => Show (Claim Observation t x a)
deriving instance (Eq t, Eq x, Eq a) => Eq (Claim Observation t x a)

instance Additive Text where
  aunit = "0"
  x + y = x <> "+" <> y
  negate x = "-" <> x

instance Multiplicative Text where
  munit = "1"
  x * y = x <> "*" <> y
  x ^ i = x <> "^" <> show i

ccy = "ccy"
usd = "USD"

demoTemplate = script do
  let today = date 2021 Jun 8
      call spot strike = O.observe spot - O.pure strike
      aTemplate = european (date 2021 Jul 15 `subDate` today) (Scale (call "s" "k") (One ccy))

  aTemplate === When (O.TimeGte 37) (Scale (Observe "s" - Const "k") (One ccy) `Or` Zero)

  let constants : Map.Map Text Decimal = Map.fromList [("k", 99.8)]
      observables : Map.Map Text Text = Map.fromList [("s", "VOD.L"), (ccy, usd)]
      get dict k = fromSomeNote ("failed to template " <> k) (Map.lookup k dict)
      f = mapParams (`subDate` today) (today `addDays`) (get observables) (get constants)

  f aTemplate === When (O.TimeGte $ date 2021 Jul 15) (Scale (Observe "VOD.L" - Const 99.8) (One usd) `Or` Zero)

