--
-- Copyright (c) 2021, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0
--

module Test.FinancialContract where

import ContingentClaims.Claim.Serializable (Claim)
import ContingentClaims.Claim (deserialize, serialize)
import DA.Set qualified as Set
import DA.Date
import ContingentClaims.Lifecycle qualified as Lifecycle

type T = FinancialContract
type Days = Int
type Instrument = Text

template Quote
  with
    instrument: Instrument
    quoteDate: Date
    close: Decimal
    source: Party
  where
    signatory source
    key (instrument, quoteDate, source) : (Instrument, Date, Party)
    maintainer key._3

template FinancialContract
  with
    bearer: Party
    counterparty: Party
    claims: Claim Date Decimal Instrument
  where
    signatory Set.fromList [bearer, counterparty]

    let claims = deserialize this.claims

    -- Create dummy contracts to settle 'current' claims
    choice Clear : [ContractId ProposeSettlement]
      controller bearer
        do t <- toDateUTC <$> getTime
           let getSpotRate ccyOrIsin t = do
                 (_, Quote{close}) <- fetchByKey (ccyOrIsin, t, bearer) -- FIXME: maintainer should be the market data provider
                 pure close
           lifecycleResult <- Lifecycle.lifecycle getSpotRate claims t
           settlements <- forA lifecycleResult.pending \(quantity, instrument) ->
             create ProposeSettlement
               with
                 payer = counterparty --FIXME: this breaks with a Give Node
                 receiver = bearer
                 instrument
                 quantity
                 tradeDate = t
           create this with claims = serialize lifecycleResult.remaining
           return settlements

-- Dummy contract to propose settlement
template ProposeSettlement
  with
    payer: Party
    receiver: Party
    quantity: Decimal
    instrument: Instrument
    tradeDate : Date
  where
    signatory Set.fromList [payer, receiver]

    controller receiver can
      Confirm : () do
        debug $ "Settled: " <> show this
        return ()
