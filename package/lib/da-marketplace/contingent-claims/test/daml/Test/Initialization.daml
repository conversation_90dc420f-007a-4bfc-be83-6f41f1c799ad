--
-- Copyright (c) 2021, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0
--

{-# LANGUAGE ApplicativeDo #-}

module Test.Initialization where

import Daml.Script
import DA.Date (date, Month(..))
import DA.Time (time)
import ContingentClaims.Claim
import Test.FinancialContract
import ContingentClaims.Financial

createContracts = script do
  setTime $ time (date 2020 Dec 9) 13 20 30
  buyer <- allocatePartyWithHint "Buyer" (PartyIdHint "Buyer")
  -- vod_l  <- submit buyer . createCmd $ Quote "GB00BH4HKS39" (date 2021 Feb 8) 127.36 buyer
  let mkContract = submit buyer . createCmd . FinancialContract buyer buyer . serialize
  mkContract $ zcb (date 2021 Mar 3) 3400.0 "USD"
  mkContract $ fixed 100.0 4.0 "GBP" (unrollDates 2021 2025 [Jan, Aug] 5)
  mkContract $ european (date 2021 Feb 8) (One "GB00BH4HKS39")

