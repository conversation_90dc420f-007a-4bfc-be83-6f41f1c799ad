--
-- Copyright (c) 2021, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0
--

-- TODO: this should all be moved into the "Contingent claims" library. There is nothing specific to the marketplace here.
{-# LANGUAGE ApplicativeDo #-}
{-# LANGUAGE UndecidableInstances #-} --needed to derive cofree Show,Eq

module Test.Util where

import ContingentClaims.Claim hiding (C, F)
import ContingentClaims.Observable qualified as O
import ContingentClaims.Observation qualified as O
import ContingentClaims.Observation (Observation)
import ContingentClaims.Financial
import DA.Assert ((===))
import DA.Date
import Daml.Control.Recursion
import Daml.Script
import Prelude hiding (enumerate, length, or, and)
import ContingentClaims.Util (enumerateFrom, expiry, payoffs)

type C = Claim Observation Date Decimal Text
type F = ClaimF Observation Date Decimal Text

deriving instance Show C
deriving instance (Show a, Show (f (Cofree f a))) => Show (Cofree f a)
deriving instance (Eq a, Eq (f (Cofree f a))) => Eq (Cofree f a)
deriving instance Show (F (Cofree F Int))
deriving instance Eq (F (Cofree F Int))

[a,b,c] = ["a","b","c"]

enumerate = script do
  let (===) : CanAssert m => Cofree F Int -> Cofree F Int -> m ()
      (===) = (DA.Assert.===)
  enumerateFrom 0 Zero === Cofree 0 ZeroF
  enumerateFrom 0 (Scale (O.pure 2.0) (One a)) === Cofree 0 (ScaleF (O.pure 2.0) (Cofree 1 (OneF a)))
  enumerateFrom 0 (One a `And` Zero) === Cofree 0 (AndF (Cofree 1 (OneF a)) (Cofree 2 ZeroF))
  enumerateFrom 0 (
    Scale (O.pure 3.0)
      ( And
        ( And
          ( Scale (O.pure 5.0)
            (One a)
          )
          (One b)
        )
        ( And
          Zero
          (One c)
        )
      )
    ) ===
   ( Cofree (0 : Int) (ScaleF (O.pure 3.0)
     ( Cofree 1 ( AndF
       ( Cofree 2 ( AndF
         ( Cofree 3 ( ScaleF (O.pure 5.0)
           ( Cofree 4 (OneF a) )
         ) )
         ( Cofree 5 (OneF b) )
       ) )
       ( Cofree 6 ( AndF
         ( Cofree 7 ZeroF )
         ( Cofree 8 (OneF c) )
       ) )
     ) )
   ) )

utils = script do
  let call : O.Observation Date Decimal Text = O.observe "VOD.L" - O.pure 103.4
      t = date 2021 Jul 30
      multiplier : O.Observation Date Decimal Text = O.pure 50_000.0

  expiry (european t (Scale call $ One "USD")) === Some t
  payoffs (european t (Scale call $ One "USD")) === [(call, "USD")]

  expiry (forward t multiplier (One "USD")) === Some t
  payoffs (forward t multiplier (One "USD")) === [(multiplier, "USD")]
