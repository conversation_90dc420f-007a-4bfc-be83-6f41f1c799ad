module Test.Pricing where

import ContingentClaims.Claim
import ContingentClaims.Financial (european, american)
import ContingentClaims.Observable qualified as O
import ContingentClaims.Observation (Observation)
import ContingentClaims.MathML qualified as MathML
import ContingentClaims.Math.Stochastic (fapf, {- simplify, -} riskless, gbm, Expr(..), unitIdentity, IsIdentifier(..))
import Daml.Control.Recursion (cata)

import Daml.Script
import DA.Assert
import Prelude hiding (max)

data Instrument = USD | EUR | AMZN | APPL deriving (Show, Eq)

call : Instrument -> Decimal -> Instrument -> Claim Observation t Decimal Instrument
call s k a = Scale (O.observe s - O.pure k) $ One a

margrabe s1 s2 a = Scale (O.observe s1 - O.observe s2) $ One a

disc USD = riskless "r_USD"
disc EUR = riskless "r_EUR"
disc other = error $ "disc: " <> show other

exch AMZN _ = gbm "μ_AMZN" "σ_AMZN"
exch APPL _ = gbm "μ_APPL" "σ_APPL"
exch a a' = error $ "exch: " <> show a <> "/" <> show a'

t = "t"  -- today
t' = "T" -- maturity

instance IsIdentifier Text where
  localVar i = "τ_" <> show i

instance Additive (Expr t) where
  (+) = curry Add
  negate = Neg
  aunit = Const 0.0

instance Multiplicative (Expr t) where
  (*) = curry Mul
  x ^ y = curry Pow x $ Const (intToDecimal y)
  munit = Const 1.0

instance Divisible (Expr t) where
  x / y = curry Mul x . curry Pow y . Neg . Const $ 1.0

instance Number (Expr t) where

max x y = I (x, y) * x + I (y, x) * y

-- This is needed because Scale x (One USD) = x * 1.0. It would make writing
-- the expressions by hand tedious
multIdentity = cata unitIdentity

-- Helper to compare the output in XML format (paste this into a browser)
print f e = do debug $ "Formula:" <> prnt f
               debug $ "Expected:" <> prnt e
  where prnt = show . MathML.presentation {- . simplify -}

valueCall = script do
  let formula = fapf USD disc exch t $ european t' (call AMZN 3300.0 USD)
      s = Proc "AMZN" (exch AMZN USD)
      k = Const 3300.0
      usd = Proc "USD" (disc USD)
      expect = usd t * E (max (s t' - k) aunit / usd t') t
  print formula expect
  multIdentity formula === expect

valueMargrabe = script do
  let formula = fapf USD disc exch t $ european t' (margrabe AMZN APPL USD)
      s = Proc "AMZN" (exch AMZN USD)
      s' = Proc "APPL" (exch APPL USD)
      usd = Proc "USD" (disc USD)
      expect = usd t * E (max (s t' - s' t') aunit / usd t') t
  print formula expect
  multIdentity formula === expect

valueAmerican = script do
  let formula = fapf USD disc exch t $ american t t' (call APPL 142.50 USD)
      s = Proc "APPL" (exch APPL USD)
      k = Const 142.50
      usd = Proc "USD" (disc USD)
      τ = "τ_0"
      expect = Sup t τ (usd t * E (max (s τ - k) aunit * I (Ident τ, Ident t') / usd τ ) t)
  print formula expect
  multIdentity formula === expect


-- Check to see that the subscript numbering works
testMonadicBind = script do
  let τ₀ = "τ_0"
      τ₁ = "τ_1"
      t₀ = "t_0"
      t₁ = "t_1"
      usd = Proc "USD" (disc USD)
      formula = fapf USD disc exch t $ Anytime (O.TimeGte t₀) (Anytime (O.TimeGte t₁) (One USD))
      expect = Sup t₀ τ₀ (usd t * E (Sup t₁ τ₁ (usd τ₀ * E (munit / usd τ₁) τ₀) / usd τ₀) t) -- note the innermost 1/USD_τ₁ is mult identity
  print formula expect
  multIdentity formula === multIdentity expect
