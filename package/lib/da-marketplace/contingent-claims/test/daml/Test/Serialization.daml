--
-- Copyright (c) 2021, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0
--

module Test.Serialization where

import Daml.Script
import ContingentClaims.Claim
import ContingentClaims.Claim.Serializable qualified as S
import ContingentClaims.Claim.Serializable ()
import ContingentClaims.Financial
import DA.Assert
import DA.Date (Month(..))
import ContingentClaims.Observation (Observation)

deriving instance Show (Claim Observation Date Decimal Text)
deriving instance Eq (Claim Observation Date Decimal Text)

testSerialization = script do
  let fixingDates = unrollDates 2020 2025 [Jan] 15
      bond : Claim Observation Date Decimal Text = fixed 100.0 3.0 "USD" fixingDates
      zero : Claim Observation Date Decimal Text = Zero

  -- identity
  (deserialize . serialize $ bond) === bond

  -- base case
  serialize (zero `And` zero) === S.And [S.Zero, S.Zero]

  -- inductive case
  serialize (zero `And` (zero `And` zero)) === S.And [S.Zero, S.Zero, S.Zero]
