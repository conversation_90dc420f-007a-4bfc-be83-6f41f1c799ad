{-# OPTIONS -Wno-deprecations #-} -- To supress 'Monad' warnings
-- {-# LANGUAGE CPP #-}
-- {-# LANGUAGE FunctionalDependencies #-}
-- {-# LANGUAGE FlexibleInstances #-}
-- {-# LANGUAGE MultiParamTypeClasses #-}
{-# LANGUAGE UndecidableInstances #-}
-- Search for UndecidableInstances to see why this is needed

-----------------------------------------------------------------------------
-- |
-- Module      :  Control.Monad.State.Class
-- Copyright   :  (c) <PERSON> 2001,
--                (c) Oregon Graduate Institute of Science and Technology, 2001
-- License     :  BSD-style (see the file LICENSE)
--
-- Maintainer  :  <EMAIL>
-- Stability   :  experimental
-- Portability :  non-portable (multi-param classes, functional dependencies)
--
-- MonadState class.
--
--      This module is inspired by the paper
--      /Functional Programming with Overloading and Higher-Order Polymorphism/,
--        <PERSON> (<http://web.cecs.pdx.edu/~mpj/>)
--          Advanced School of Functional Programming, 1995.

-----------------------------------------------------------------------------

module Daml.Control.Monad.MonadState (
    MonadState(..),
    modify,
--  modify',
    gets
  ) where

import Daml.Control.Monad.State qualified as Strict
import Daml.Control.Monad.Writer qualified as Strict
-- import Daml.Control.Monad.Reader (ReaderT)
import Daml.Control.Monad.Trans

-- | Minimal definition is either both of @get@ and @put@ or just @state@
class Monad m => MonadState s m | m -> s where
    -- | Return the state from the internals of the monad.
    get : m s
    get = state (\s -> (s, s))

    -- | Replace the state inside the monad.
    put : s -> m ()
    put s = state (const ((), s))

    -- | Embed a simple state action into the monad.
    state : (s -> (a, s)) -> m a
    state f = do
      s <- get
      let ~(a, s') = f s
      put s'
      return a
    {-# MINIMAL state | get, put #-}

-- | Monadic state transformer.
--
--      Maps an old state to a new state inside a state monad.
--      The old state is thrown away.
--
-- >      Main> :t modify ((+1) : Int -> Int)
-- >      modify (...) : (MonadState Int a) => a ()
--
--    This says that @modify (+1)@ acts over any
--    Monad that is a member of the @MonadState@ class,
--    with an @Int@ state.
modify : MonadState s m => (s -> s) -> m ()
modify f = state (\s -> ((), f s))

{-
-- | A variant of 'modify' in which the computation is strict in the
-- new state.
--
-- @since 2.2
modify' : MonadState s m => (s -> s) -> m ()
modify' f = do
  s' <- get
  put $! f s'
-}

-- | Gets specific component of the state, using a projection function
-- supplied.
gets : MonadState s m => (s -> a) -> m a
gets f = do
    s <- get
    return (f s)

instance Monad m => MonadState s (Strict.StateT s m) where
    get = Strict.get
    put = Strict.put
    state = Strict.state

-- ---------------------------------------------------------------------------
-- Instances for other mtl transformers
--
-- All of these instances need UndecidableInstances,
-- because they do not satisfy the coverage condition.
{-
instance MonadState s m => MonadState s (ContT r m) where
    get = lift get
    put = lift . put
    state = lift . state

-- | @since 2.2
instance MonadState s m => MonadState s (ExceptT e m) where
    get = lift get
    put = lift . put
    state = lift . state

instance MonadState s m => MonadState s (IdentityT m) where
    get = lift get
    put = lift . put
    state = lift . state

instance MonadState s m => MonadState s (MaybeT m) where
    get = lift get
    put = lift . put
    state = lift . state

instance MonadState s m => MonadState s (ReaderT r m) where
    get = lift get
    put = lift . put
    state = lift . state

-}

{-
#if MIN_VERSION_transformers(0,5,6)
-- | @since 2.3
instance (Monoid w, MonadState s m) => MonadState s (CPS.WriterT w m) where
    get = lift get
    put = lift . put
    state = lift . state
#endif

instance (Monoid w, MonadState s m) => MonadState s (Lazy.WriterT w m) where
    get = lift get
    put = lift . put
    state = lift . state
-}

instance (Monoid w, MonadState s m) => MonadState s (Strict.WriterT w m) where
    get = lift get
    put = lift . put
    state = lift . state

{-
#if MIN_VERSION_transformers(0,5,3)
-- | @since 2.3
instance
  ( Monoid w
  , MonadState s m
#if !MIN_VERSION_base(4,8,0)
  , Functor m
#endif
  ) => MonadState s (AccumT w m) where
    get = lift get
    put = lift . put
    state = lift . state
#endif
-}
