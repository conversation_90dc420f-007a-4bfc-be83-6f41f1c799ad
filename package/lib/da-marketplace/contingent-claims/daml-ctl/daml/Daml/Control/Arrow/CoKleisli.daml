-------------------------------------------------------------------------------------------
-- |
-- Module : Control.Arrow.CoKleisli
-- Copyright  : 2008 <PERSON>
-- License  : BSD3
--
-- Maintainer : <PERSON> <<EMAIL>>
-- Stability  : experimental
-- Portability  : portable
--
-------------------------------------------------------------------------------------------
module Daml.Control.Arrow.CoKleisli
  ( CoKleisli(..)
  ) where


import Prelude hiding (id,(.))
import Daml.Control.Category
import Daml.Control.Comonad
import Daml.Control.Arrow

newtype CoKleisli w a b = CoKleisli { runCoKleisli : w a -> b }

instance Functor (CoKleisli w a) where
  fmap f (CoKleisli g) = CoKleisli (f . g)

instance Comonad w => Arrow (CoKleisli w) where
  arr f = CoKleisli (f . extract)
  CoKleisli a &&& CoKleisli b = CoKleisli (a &&& b)
  CoKleisli a *** CoKleisli b = CoKleisli (a . fmap fst &&& b . fmap snd)
  first a = a *** CoKleisli extract
  second a = CoKleisli extract *** a

instance Comonad w => Category (CoKleisli w) where
  id = CoKleisli extract
  CoKleisli b . CoKleisli a = CoKleisli (b . fmap a . duplicate)
