    -- The RULES for the methods of class Category may never fire
    -- e.g. identity/left, identity/right, association;  see #10528

-----------------------------------------------------------------------------
-- |
-- Module      :  Ctl.Category
-- Copyright   :  (c) <PERSON> 2007
-- License     :  BSD-style (see the LICENSE file in the distribution)
--
-- Maintainer  :  <EMAIL>
-- Stability   :  experimental
-- Portability :  portable

-- https://gitlab.haskell.org/ghc/ghc/issues/1773

module Daml.Control.Category where

import qualified Prelude (identity,(.))

infixr 9 .
infixr 1 >>>, <<<

-- | A class for categories. Instances should satisfy the laws
--
-- [Right identity] @f '.' 'id'  =  f@
-- [Left identity]  @'id' '.' f  =  f@
-- [Associativity]  @f '.' (g '.' h)  =  (f '.' g) '.' h@
--
class Category cat where
    -- | the identity morphism
    id : cat a a

    -- | morphism composition
    (.) : cat b c -> cat a b -> cat a c

{- Luciano 2021/02/19 - commented to supress warnings (see top of page)
{-# RULES
"identity/left" forall p .
                id . p = p
"identity/right"        forall p .
                p . id = p
"association"   forall p q r .
                (p . q) . r = p . (q . r)
 #-}
 -}

instance Category (->) where
    id = Prelude.identity
    (.) = (Prelude..)

{-
-- | @since 4.7.0.0
instance Category (:~:) where
  id          = Refl
  Refl . Refl = Refl

-- | @since 4.10.0.0
instance Category (:~~:) where
  id            = HRefl
  HRefl . HRefl = HRefl

-- | @since 4.7.0.0
instance Category Coercion where
  id = Coercion
  (.) Coercion = coerce
-}

-- | Right-to-left composition
(<<<) : Category cat => cat b c -> cat a b -> cat a c
(<<<) = (.)

-- | Left-to-right composition
(>>>) : Category cat => cat a b -> cat b c -> cat a c
f >>> g = g . f
