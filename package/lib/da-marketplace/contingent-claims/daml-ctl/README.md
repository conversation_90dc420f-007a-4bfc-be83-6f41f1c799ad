# CTL Library

This is a temporary library for collecting flow-of-control (think Haskell's `Control` modules) code.
The plan is to eventually split this up into separate libraries. Currently, among other things, this includes:

* Contravariant
* Arrow
* Monad Transformers (Reader, Writer, State)
* Recursion Schemes

**Expect breaking changes**

# License & Acknowledgements

Licensed under multiple terms (mainly BSD and GHC - see LICENSE file and individual source file headers), as most of this code is transliterated into Daml from Haskell standard libraries.
