#
# Copyright (c) 2021, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#

version: 2.1

executors:
  daml-executor:
    docker:
      - image: cimg/openjdk:11.0-node

commands:
  install_sdk:
    description: "Install Daml SDK"
    parameters:
      version:
        type: string
    steps:
      - run:
          name: Install Daml SDK
          command: |
            curl -sSL https://get.daml.com/ | sh /dev/stdin << parameters.version >>
            # idea from https://circleci.com/docs/2.0/env-vars/
            >> $BASH_ENV echo 'export PATH="$HOME/.daml/bin:$PATH"'
  init_submodules:
    description: "Init Git Submodules"
    steps:
      - run:
          name: Init submodules
          command: |
            git submodule update --init --recursive
  install_yq:
    description: "Install yq from binary"
    steps:
      - run:
          name: Install yq
          command: |
            mkdir -p $HOME/yq
            curl -L https://github.com/mikefarah/yq/releases/download/3.4.1/yq_linux_amd64 -o $HOME/yq/yq &&\
              chmod +x $HOME/yq/yq &&\
              echo 'export PATH=$HOME/yq/yq:$PATH' >> $BASH_ENV
  install_python:
    description: "Install Python Dependencies"
    steps:
      - run:
          name: Install Python for 3.7
          command: |
            sudo add-apt-repository --yes ppa:deadsnakes/ppa
            sudo apt-get update || sudo apt-get update
            sudo apt-get install python3.7
            sudo apt-get install python3-pip
            pip3 install pipenv
            pip3 install daml-dit-ddit
            pipenv --python /usr/bin/python3.7

jobs:
  daml_test:
    parameters:
      daml_sdk_version:
        type: string
    executor: daml-executor
    steps:
      - checkout
      - init_submodules
      - restore_cache:
          keys:
            - daml-{{ checksum "daml.yaml" }}
      - install_sdk:
          version: << parameters.daml_sdk_version >>
      - run:
          name: Daml tests
          command: |
            make test-daml
      - save_cache:
          paths:
            - ~/.daml
          key: daml-{{ checksum "daml.yaml" }}
      - store_test_results:
          path: da-marketplace-test-report
      - store_artifacts:
          path: da-marketplace-test-report

  ui_test:
    parameters:
      daml_sdk_version:
        type: string
    executor: daml-executor
    steps:
      - checkout
      - init_submodules
      - restore_cache:
          keys:
            - daml-{{ checksum "daml.yaml" }}
      - install_sdk:
          version: << parameters.daml_sdk_version >>
      - install_python
      - install_yq
      - run:
          name: UI test
          command: |
            echo 'export PATH=$HOME/yq:$PATH' >> $BASH_ENV
            source $BASH_ENV
            make test-ui
      - save_cache:
          paths:
            - ~/.daml
          key: daml-{{ checksum "daml.yaml" }}

  publish:
    parameters:
      daml_sdk_version:
        type: string
    executor: daml-executor
    steps:
      - add_ssh_keys:
          fingerprints:
            - "47:af:b2:64:7a:bb:03:49:99:93:e2:74:97:55:f4:69"
      - checkout
      - init_submodules
      - install_sdk:
          version: << parameters.daml_sdk_version >>
      - install_python
      - install_yq
      - run:
          name: Publish
          command: |
            echo 'export PATH=$HOME/yq:$PATH' >> $BASH_ENV
            source $BASH_ENV
            make publish

workflows:
  version: 2
  test:
    jobs:
      - daml_test:
          daml_sdk_version: "1.17.1"
          filters:
            branches:
              ignore:
                - main
      - ui_test:
          daml_sdk_version: "1.17.1"
          filters:
            branches:
              ignore:
                - main
  publish:
    jobs:
      - publish:
          daml_sdk_version: "1.17.1"
          filters:
            branches:
              only:
                - main
