module Marketplace.Issuance.Model where

import DA.Finance.Types (Account, Id)

type T = Issuance

template Issuance
  with
    operator : Party
    provider : Party
    customer : Party
    issuanceId : Text
    assetId : Id
    account : Account
    quantity : Decimal
  where
    signatory operator, provider, customer

    key (operator, provider, issuanceId) : (Party, Party, Text)
    maintainer key._1
