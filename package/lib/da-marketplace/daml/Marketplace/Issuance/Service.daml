module Marketplace.Issuance.Service where

import DA.Finance.Asset
import DA.Finance.Types
import DA.Finance.Utils (fetchAndArchive)
import DA.Map qualified as Map
import DA.Set (Set, fromList, empty)
import Marketplace.Issuance.Model (Issuance(..))
import Marketplace.Issuance.CFI
import Marketplace.Issuance.AssetDescription (AssetDescription(..), Claims)
import Marketplace.Issuance.AssetDescription qualified as AssetDescription
import Marketplace.Utils

template Service
  with
    operator : Party
    provider : Party
    customer : Party
  where
    signatory operator, provider, customer

    key (operator, provider, customer) : (Party, Party, Party)
    maintainer key._1

    nonconsuming choice RequestOrigination: ContractId OriginationRequest
        with
          assetLabel : Text
          cfi : CFI
          description : Text
          claims : Claims
          observers : [Party]
        controller customer
        do
          create OriginationRequest with observers = fromList observers; ..

    nonconsuming choice RequestCreateIssuance : ContractId CreateIssuanceRequest
        with
          issuanceId : Text
          account : Account
          assetId : Id
          quantity : Decimal
        controller customer
        do
          create CreateIssuanceRequest with ..

    nonconsuming choice RequestReduceIssuance : ContractId ReduceIssuanceRequest
        with
          issuanceId : Text
          accountId : Id
          depositCid : ContractId AssetDeposit
        controller customer
        do
          create ReduceIssuanceRequest with ..

    nonconsuming choice Originate: (ContractId AssetDescription, AssetDescription)
        with
          createOriginationCid: ContractId OriginationRequest
        controller provider
        do
          OriginationRequest{assetLabel; cfi; description; claims; observers} <- fetch createOriginationCid
          -- assertMsg "issued must be 0" $ description.issued == 0.0
          -- assertMsg "authorized must be > 0" $ description.authorized > 0.0
          archive createOriginationCid
          let assetId = Id with signatories = fromList [ provider, customer ]; label = assetLabel; version = 0
              issuer = customer
              registrar = provider

          create AssetDescription.Index
                   with
                     assetLabel
                     descriptionSignatories = assetId.signatories
                     versions = Map.insert claims 0 Map.empty
          let ad = AssetDescription
                   with
                     assetId
                     cfi
                     description
                     issuer
                     registrar
                     observers
                     claims
          adCid <- create ad
          pure (adCid, ad)

    nonconsuming choice CreateIssuance : (ContractId Issuance, ContractId AssetDeposit)
        with
          createIssuanceRequestCid : ContractId CreateIssuanceRequest
        controller provider
        do
          CreateIssuanceRequest{issuanceId; assetId; account; quantity} <- fetchAndArchive createIssuanceRequestCid
          issuanceCid <- create Issuance with ..

          (_, AssetDescription{issuer}) <- fetchByKey @AssetDescription assetId
          let asset = Asset with id = assetId; quantity
          depositCid <- create AssetDeposit with account; asset; lockers = empty; observers = fromList [ issuer ]

          pure (issuanceCid, depositCid)

    nonconsuming choice ReduceIssuance : ContractId Issuance
        with
          reduceIssuanceRequestCid : ContractId ReduceIssuanceRequest
        controller provider
        do
          ReduceIssuanceRequest{issuanceId; accountId; depositCid} <- fetchAndArchive reduceIssuanceRequestCid
          deposit <- fetchAndArchive depositCid

          (issuanceCid, issuance) <- fetchByKey @Issuance (operator, provider, issuanceId)
          archive issuanceCid

          create issuance with quantity = issuance.quantity - deposit.asset.quantity

template Offer
  with
    operator : Party
    provider : Party
    customer : Party
  where
    signatory operator, provider
    observer customer

    choice Accept : ContractId Service
        controller customer
        do
          createOrLookup Service with ..

    choice Decline : ()
        controller customer
        do pure ()

    choice Withdraw : ()
        controller provider
        do pure ()

template Request
  with
    customer : Party
    provider : Party
  where
    signatory customer
    observer provider

    choice Cancel : ()
        controller customer
        do pure ()

    choice Reject : ()
        controller provider
        do pure ()

    choice Approve : ContractId Service
      with
        operator : Party
      controller operator, provider
      do
        createOrLookup Service with ..

template CreateIssuanceRequest
  with
    operator : Party
    provider : Party
    customer : Party
    issuanceId : Text
    assetId : Id
    account : Account
    quantity : Decimal
  where
    signatory operator, provider, customer

template ReduceIssuanceRequest
  with
    operator : Party
    provider : Party
    customer : Party
    issuanceId : Text
    accountId : Id
    depositCid : ContractId AssetDeposit
  where
    signatory operator, provider, customer

template OriginationRequest
  with
    operator : Party
    provider : Party
    customer : Party
    assetLabel : Text
    cfi : CFI
    description : Text
    claims : Claims
    observers : Set Party
  where
     signatory operator, provider, customer
