module Marketplace.Issuance.AssetDescription where
-- Consider merging into DA.Finance.Types.Asset

import Marketplace.Issuance.CFI

import DA.Finance.Types (Id)
import DA.Set (Set)
import DA.Map (Map, size)
import DA.Map qualified as Map
import ContingentClaims.Claim qualified as C
import ContingentClaims.Claim.Serializable (Claim(..), Inequality(..))
import ContingentClaims.Observation (Observation(..))
import ContingentClaims.Util (expiry, payoffs)

type T = AssetDescription
type Claims = Claim Date Decimal Id
type Version = Int

deriving instance Ord (Observation Date Decimal Id)
deriving instance Ord (Inequality Date Decimal Id)
deriving instance Ord (Claim Date Decimal Id)

template AssetDescription
  with
    assetId: Id
    -- ^ This is the master record for the asset, and has embedded it's unique identifier
    description: Text
    -- ^ Short description of the asset, mainly for display purposes
    cfi: CFI
    -- ^ Type of asset
    issuer: Party
    -- ^ The counterparty for any @claims@
    claims: Claims
    -- ^ Rights and obligations of the holder (and issuer)
    registrar: Party
    -- ^ Party responsible for recordkeeping
    -- safekeepingAccount: Account
    -- ^ Source account used for issuing the security and any subsequent corporate actions, e.g. paying out a coupon.
    -- terms: Text
    -- ^ Any further contract terms not captured in @claims@; typically a URI.
    -- authorized: Decimal
    -- ^ Number of shares/contracts authorized for issuance
    -- issued: Decimal
    -- ^ Number of shares/contracts already issued for trading
    -- issuePrice: (Decimal, Asset)
    -- ^ The price & currency of the *initial* offer.
    observers : Set Party
  where
    signatory assetId.signatories
    observer observers

    key assetId : Id
    maintainer key.signatories

    nonconsuming choice Expiry : Optional Date
      with
        party : Party
      controller party
      do
        return $ expiry (C.deserialize claims)

    nonconsuming choice Underlying : [Id]
      with
        party : Party
      controller party
        do
          return $ snd <$> payoffs (C.deserialize claims)

    nonconsuming choice Multipliers : [Decimal]
      with
        party : Party
      controller party
        do
          let getConst : (Observation Date Decimal a, b) -> [Decimal]
              getConst (Const x, _) = [x]
              getConst _            = []
          return $ concatMap getConst $ payoffs (C.deserialize claims)

-- Used to do a reverse lookup of the version # given the claims (passed-in from the API)
template Index
  with
    assetLabel: Text
    descriptionSignatories: Set Party
    versions: Map Claims Version
  where
    signatory descriptionSignatories
    key (descriptionSignatories, assetLabel) : (Set Party, Text)
    maintainer key._1

    -- Changed choice to nonconsuming, because when lookup is successful, the index should not be archived
    nonconsuming choice LookupOrInsert : (ContractId Index, Version) with claims : Claims
        controller descriptionSignatories
        do
          case Map.lookup claims versions of
             Some version -> do
                return (self, version)
             None -> do
               let nextVersion = size versions
               {- Since choice is nonconsuming, when lookup is not successful and a new asset is added to the index,
                 this contract must be archived and recreated -}
               archive self
               cid <- create this with versions = Map.insert claims nextVersion versions
               return (cid, nextVersion)

