module Marketplace.Issuance.CFI where

-- |Classification of Finanical Instruments, denoted using ISO 10962:
-- https://en.wikipedia.org/wiki/ISO_10962
newtype CFI = CFI { code : Text } deriving (Eq)

-- TODO: Add CFI Category/Group types
instance Show CFI where
  show (CFI "EXXXXX") = "Equity"
  show (CFI "TCXXXX") = "Currency"
  show (CFI "FXXXXX") = "Future"
  show (CFI "JXXXXX") = "Forward"
  show (CFI "DBXXXX") = "Bond"
  show (CFI "OCEXXX") = "European Call"
  show (CFI "RWXXPX") = "Put Warrant"
  show (CFI "ECXXXX") = "Convertible Share"
  show (CFI "MMMXXX") = "Misc/Other"
  show _              = "Unknown"

equity : CFI
equity = CFI "EXXXXX"

currency : CFI
currency = CFI "TCXXXX"

future : CFI
future = CFI "FXXXXX"

forward : CFI
forward = CFI "JXXXXX"

bond : CFI
bond = CFI "DBXXXX"

europeanCall : CFI
europeanCall = CFI "OCEXXX"

putWarrant : CFI
putWarrant = CFI "RWXXPX"

convertibleShare : CFI
convertibleShare = CFI "ECXXXX"

other : CFI
other = CFI "MMMXXX"
