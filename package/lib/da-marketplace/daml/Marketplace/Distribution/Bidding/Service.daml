module Marketplace.Distribution.Bidding.Service where

import DA.Finance.Asset (AssetDeposit, AssetDeposit_Split(..), AssetDeposit_Lock(..), AssetDeposit_Unlock(..))
import DA.Finance.Types (Asset, Account, Id)
import DA.Set (fromList)
import Marketplace.Distribution.Bidding.Model qualified as Bidding
import Marketplace.Settlement.Model as Settlement (SettlementDetails, SettlementInstruction(..))
import Marketplace.Utils

type S = Service

template Service
  with
    operator : Party
    provider : Party
    customer : Party
  where
    signatory operator, provider, customer

    key (operator, provider, customer) : (Party, Party, Party)
    maintainer key._1

    nonconsuming choice SubmitBid : ContractId Bidding.Bid
        with
          auctionCid : ContractId Bidding.Auction
          price : Decimal
          quantity : Decimal
          depositCid : ContractId AssetDeposit
          receivableAccount : Account
          allowPublishing : Bool
        controller customer
        do
          -- TODO: Check for parallism of this choice
          Bidding.Auction{asset; issuer; auctionId; quotedAssetId} <- fetch auctionCid
          hasBid <- visibleByKey @Bidding.Bid (customer, auctionId)
          assertMsg ("Bid already exists") (not hasBid)

          deposit <- fetch depositCid
          assertMsg ("Deposit does not cover submitted bid") (deposit.asset.quantity >= price * quantity)
          assertMsg ("Deposited asset does not match the auction's quoted asset") (deposit.asset.id.label == quotedAssetId.label)

          depositCid <- exercise depositCid AssetDeposit_Lock with newLockers = fromList [ provider ]
          time <- getTime
          create Bidding.Bid with
              details = Bidding.Details with ..
              status = Bidding.Pending
              assetId = asset.id
              ..

    nonconsuming choice RequestBid : ContractId Bidding.Auction
        with
          issuer : Party
          auctionId : Text
          asset : Asset
          quotedAssetId : Id
          publishedBidCids : [ContractId Bidding.Bid]
        controller provider
        do
          publishedBids <- mapA (\bidCid -> do
            bid <- fetch bidCid
            assert $ bid.auctionId == auctionId
            assert bid.allowPublishing
            pure Bidding.PublishedBid with investor = bid.customer; auctionId = bid.auctionId; quantity = bid.details.quantity) publishedBidCids
          create Bidding.Auction with ..

    nonconsuming choice ProcessAllocation : (ContractId Bidding.Bid, ContractId AssetDeposit)
        with
          bidCid : ContractId Bidding.Bid
          quantity : Decimal
          amount : Decimal
          price : Decimal
        controller provider
        do
          -- Fetch Bid -> Extract their deposit -> Split and return unused allocation (if necessary) to the investor -> Update Bid Status
          bid <- fetch bidCid
          bidDeposit <- fetch bid.depositCid
          bidDepositCid <- if bidDeposit.asset.quantity > amount
                              then do
                                (depositCid :: remainingDepositCid :: _) <- exercise bid.depositCid AssetDeposit_Split with quantities = [amount]
                                exercise remainingDepositCid AssetDeposit_Unlock
                                pure depositCid
                              else pure bid.depositCid

          let newStatus = if bid.details.quantity == quantity
                            then Bidding.FullAllocation with price
                            else Bidding.PartialAllocation with price; quantity

          newBidCid <- exercise bidCid Bidding.UpdateStatus with newStatus
          pure (newBidCid, bidDepositCid)

    nonconsuming choice RejectAllocation : (ContractId Bidding.Bid, ContractId AssetDeposit)
        with
          bidCid : ContractId Bidding.Bid
          newStatus : Bidding.Status
        controller provider
        do
          bid <- fetch bidCid

          returnedDeposit <- exercise bid.depositCid AssetDeposit_Unlock
          newBidCid <- exercise bidCid Bidding.UpdateStatus with newStatus
          pure (newBidCid, returnedDeposit)

    nonconsuming choice GenerateSettlementInstruction : ContractId Settlement.SettlementInstruction
      with
        issuer : Party
        delivery : Settlement.SettlementDetails
        payment : Settlement.SettlementDetails
      controller provider, issuer
      do
        create SettlementInstruction with details = [delivery, payment]; ..

template Offer
  with
    operator : Party
    provider : Party
    customer : Party
  where
    signatory operator, provider
    observer customer

    choice Accept : ContractId Service
        controller customer
        do
          createOrLookup Service with ..

    choice Decline : ()
        controller customer
        do
          return ()

    choice Withdraw : ()
        controller provider
        do pure ()

template Request
  with
    provider : Party
    customer : Party
  where
    signatory customer
    observer provider

    choice Cancel : ()
        controller customer
        do pure ()

    choice Reject : ()
        controller provider
        do pure ()

    choice Approve : ContractId Service
      with
        operator : Party
      controller operator, provider
      do
        createOrLookup Service with ..
