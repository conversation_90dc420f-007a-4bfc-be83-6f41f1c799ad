module Marketplace.Distribution.Role where

import Marketplace.Distribution.Auction.Service qualified as Auction
import Marketplace.Distribution.Bidding.Service qualified as Bidding
import Marketplace.Utils
import DA.Set (Set)

template Role
  with
    operator : Party
    provider : Party
    observers : Set Party
  where
    signatory operator, provider
    observer observers

    key (operator, provider) :  (Party, Party)
    maintainer key._1

    nonconsuming choice OfferAuctionService : ContractId Auction.Offer
        with
          customer : Party
        controller provider
        do
          create Auction.Offer with ..

    nonconsuming choice ApproveAuctionServiceRequest : ContractId Auction.Service
        with
          auctionServiceRequestCid : ContractId Auction.Request
        controller provider
        do
          exercise auctionServiceRequestCid Auction.Approve with ..

    nonconsuming choice TerminateAuctionService : ()
        with
          auctionServiceCid : ContractId Auction.Service
        controller provider
        do
          archive auctionServiceCid

    nonconsuming choice OfferBiddingService : ContractId Bidding.Offer
        with
          customer : Party
        controller provider
        do
          create Bidding.Offer with ..

    nonconsuming choice ApproveBiddingServiceRequest : ContractId Bidding.Service
        with
          biddingServiceRequestCid : ContractId Bidding.Request
        controller provider
        do
          exercise biddingServiceRequestCid Bidding.Approve with ..

    nonconsuming choice TerminateBiddingService : ()
        with
          biddingServiceCid : ContractId Bidding.Service
        controller provider
        do
          archive biddingServiceCid

    choice TerminateRole : ()
        controller operator
        do
          return ()

template Offer
  with
    operator : Party
    provider : Party
    observers : Set Party
  where
    signatory operator
    observer provider

    choice Accept : ContractId Role
        controller provider
        do
          createOrLookup Role with ..

    choice Decline : ()
        controller provider
        do
          return ()

template Request
  with
    provider : Party
    operator : Party
  where
    signatory provider
    observer operator

    choice Approve : ContractId Role
        with
          observers : Set Party
        controller operator
        do
          createOrLookup Role with ..

    choice Reject : ()
        controller operator
        do
          return ()
