module Marketplace.Distribution.Auction.Utils where

import DA.Finance.Asset (AssetDeposit, AssetDeposit_Split(..))
import Marketplace.Settlement.Model qualified as Settlement
import Marketplace.Distribution.Auction.Model qualified as Auction
import Marketplace.Distribution.Bidding.Service qualified as Bidding
import Marketplace.Distribution.Bidding.Model qualified as Bidding
import Marketplace.Distribution.Bidding.Model (Bid(..))

-- | Takes a list and splits it based off a predicate.
splitList : (a -> Bool) -> [a] -> [Either a a]
splitList p xs = foldr (\x acc -> if p x then Right x :: acc else Left x :: acc) [] xs

-- | Updates a bid's status
updateBidStatus : Bidding.Status -> Bidding.Bid -> Update (ContractId Bidding.Bid)
updateBidStatus newStatus Bidding.Bid{customer, auctionId} =
    exerciseByKey @Bidding.Bid (customer, auctionId) Bidding.UpdateStatus with newStatus

-- | Reject a bid
rejectBid : Bidding.Status -> Bidding.Bid -> Update (ContractId Bidding.Bid, ContractId AssetDeposit)
rejectBid newStatus Bidding.Bid{operator, provider, customer, auctionId} = do
    (bidCid, _) <- fetchByKey @Bidding.Bid (customer, auctionId)
    exerciseByKey @Bidding.Service (operator, provider, customer) Bidding.RejectAllocation with bidCid ; newStatus

-- | A map/accumulator function for processing a submitted bid.
-- Accepts the remaining quantity and price as an accululator along the current Bid as input
-- Output is the updated accululator (if applicable) along with the Bid mapped to an Allocation (depending on the accumulator and bid information)
dutchAuction : (Decimal, Decimal) -> Bidding.Bid -> ((Decimal, Decimal), Auction.Allocation)
dutchAuction acc@(0.0, _)   bid@Bid{..} = (acc, Auction.Allocation with quantity = 0.0; ..)
dutchAuction (remaining, _) bid@Bid{..} =
  let
    quantity = details.quantity
    bidPrice = details.price
  in
    if remaining >= quantity
      then ((remaining - quantity, bidPrice), Auction.Allocation with ..)
      else ((0.0, bidPrice), Auction.Allocation with quantity = remaining; ..)

-- | An accumulator function which processes generates the settlement instruction for both an issuer and a bidder based of the output of the dutch auction
generateSettlementInstruction : (Optional (ContractId AssetDeposit), [ContractId Settlement.SettlementInstruction]) -> Auction.SettleAllocation -> Update (Optional (ContractId AssetDeposit), [ContractId Settlement.SettlementInstruction])
generateSettlementInstruction (None, _) _ = error "Auction:generateSettlementInstruction - Issuers AssetDeposit has no remaining units to allocate"
generateSettlementInstruction (Some issuerDepositCid, settlementInstructionCids) Auction.SettleAllocation{allocation, price, issuer, issuerReceivableAccount} = do
    let
      allocatedQuantity = allocation.quantity
      bid = allocation.bid
      serviceKey = (bid.operator, bid.provider, bid.customer)
      allocatedAmount = allocatedQuantity * price
      newStatus = if bid.details.quantity == allocatedQuantity
                    then Bidding.FullAllocation with ..
                    else Bidding.PartialAllocation with quantity = allocatedQuantity; ..

    -- | Process bid side
    (bidCid, _) <- fetchByKey @Bidding.Bid (bid.customer, bid.auctionId)
    (_, bidDepositCid) <- exerciseByKey @Bidding.Service serviceKey Bidding.ProcessAllocation with bidCid; quantity = allocatedQuantity; amount = allocatedAmount; price
    bidDeposit <- fetch bidDepositCid
    let
      buyer = bid.customer
      payment = Settlement.SettlementDetails with senderAccount = bidDeposit.account; depositCid = bidDepositCid; receiverAccount = issuerReceivableAccount

    -- | Process issuer side
    -- 1/ Extract their deposit
    -- 2/ Split allocation quantity from this deposit, get the deposit matching the allocation. Can only result in either :
        -- two asset deposits - One with the allocation, one with the remaining
        -- one asset deposit - Occurs when processing the last bid when the auction is fully allocated
    -- 3/ Create the settlement details for delivery of the asset to the buyer
    issuerDeposit <- fetch issuerDepositCid
    issuerDepositCids <- exercise issuerDepositCid AssetDeposit_Split with quantities = [allocatedQuantity]
    let
      seller = issuer
      (allocatedDepositCid, remainingDepositCidOpt) = case issuerDepositCids of
        [x]    -> (x, None)
        [x, y] -> (x, Some y)
        _ -> error "Auction:generateSettlementInstruction - Unexpected AssetDeposit split"
      delivery = Settlement.SettlementDetails with senderAccount = issuerDeposit.account; depositCid = allocatedDepositCid; receiverAccount = bid.receivableAccount

    -- | Create the Settlement Instruction to deliver the asset against the payment atomically
    settlementInstructionCid <- exerciseByKey @Bidding.Service serviceKey Bidding.GenerateSettlementInstruction with issuer; delivery; payment
    pure (remainingDepositCidOpt, settlementInstructionCid :: settlementInstructionCids)
