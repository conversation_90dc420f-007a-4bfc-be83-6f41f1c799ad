{-# LANGUAGE MultiWayIf #-}

module Marketplace.Distribution.Auction.Service where

import DA.Action (foldlA)
import DA.Either (partitionEithers)
import DA.Foldable (forA_)
import DA.List (sortOn, mapAccumL)
import DA.Optional (whenSome)
import DA.Set (fromList)
import DA.Finance.Asset (AssetDeposit, AssetDeposit_Lock(..), AssetDeposit_Unlock(..))
import DA.Finance.Types (Account, Asset, Id)
import DA.Finance.Utils (fetchAndArchive)
import Marketplace.Settlement.Model qualified as Settlement
import Marketplace.Distribution.Auction.Model qualified as Auction
import Marketplace.Distribution.Auction.Model (Auction(..))
import Marketplace.Distribution.Auction.Utils (dutchAuction, splitList, generateSettlementInstruction, rejectBid)
import Marketplace.Distribution.Bidding.Model qualified as Bidding

type S = Service

template Service
  with
    operator : Party
    provider : Party
    customer : Party
  where
    signatory operator, provider, customer

    nonconsuming choice RequestCreateAuction : ContractId CreateAuctionRequest
        with
          auctionId : Text
          asset : Asset
          quotedAssetId : Id
          floorPrice : Decimal
          depositCid : ContractId AssetDeposit
          receivableAccount : Account
        controller customer
        do
          create CreateAuctionRequest with ..

    nonconsuming choice CancelAuctionRequest : ()
        with
          createAuctionRequestCid : ContractId CreateAuctionRequest
        controller customer
        do
          archive createAuctionRequestCid

    nonconsuming choice CreateAuction : ContractId Auction.T
        with
          createAuctionRequestCid : ContractId CreateAuctionRequest
        controller provider
        do
          CreateAuctionRequest{auctionId; asset; quotedAssetId; floorPrice; depositCid, receivableAccount} <- fetchAndArchive createAuctionRequestCid

          depositCid <- exercise depositCid AssetDeposit_Lock with newLockers = fromList [ provider ]
          create Auction with status = Auction.Open; ..

    nonconsuming choice RejectAuction : ()
        with
          createAuctionRequestCid : ContractId CreateAuctionRequest
        controller provider
        do
          archive createAuctionRequestCid

    nonconsuming choice ProcessAuction : (ContractId Auction.T, [ContractId Settlement.SettlementInstruction])
        with
          auctionCid : ContractId Auction.T
          bidCids : [ContractId Bidding.Bid]
        controller provider
        do
          auction@Auction{floorPrice ,asset , depositCid, status, receivableAccount} <- fetch auctionCid
          assertMsg "Auction already successfully processed" (status `elem` [Auction.Open, Auction.NoValidBids])

          bids <- forA bidCids fetch
          -- Split bids on those above / below the floor price -> Sort valid bids descendingly -> Execute Auction -> Split auction result on allocated / unallocated (ie, zero allocation)
          let
            (invalidBids, validBids) = partitionEithers $ splitList (\bid -> bid.details.price >= floorPrice) bids
            sortedBids = sortOn (\bid -> Down bid.details) validBids
            ((remaining, finalPrice), allocations) = mapAccumL dutchAuction (asset.quantity, 0.0) sortedBids
            (allocatedBids, unallocatedBids) = partitionEithers $ splitList (\allocation -> allocation.quantity == 0.0) allocations

          -- Return assets of all invalid and unallocated bids. Also update their bid status accordingly
          forA_ invalidBids $ rejectBid Bidding.Invalid
          forA_ ((.bid) `map` unallocatedBids) $ rejectBid Bidding.NoAllocation

          -- Generate Settlement instructions for allocated bids
          let settleAllocations = (\allocation -> Auction.SettleAllocation with allocation; price = finalPrice; issuer = customer; issuerReceivableAccount = receivableAccount) <$> allocatedBids
          (remainingDepositCidOpt, siCids) <- foldlA generateSettlementInstruction (Some depositCid, []) settleAllocations

          -- Return unallocated deposit back to the customer
          whenSome remainingDepositCidOpt \depositCid -> exercise depositCid AssetDeposit_Unlock >> pure ()

          -- Update Auction contract with the outcome of the auction processing
          let newStatus = if
                | remaining == asset.quantity -> Auction.NoValidBids
                | remaining > 0.0             -> Auction.PartiallyAllocated with ..
                | otherwise                   -> Auction.FullyAllocated with ..
          auctionCid <- archive auctionCid *> create auction with status = newStatus

          pure (auctionCid, siCids)

template Offer
  with
    operator : Party
    provider : Party
    customer : Party
  where
    signatory operator, provider
    observer customer

    choice Accept : ContractId Service
        controller customer
        do
          create Service with ..

    choice Decline : ()
        controller customer
        do
          return ()

    choice Withdraw : ()
        controller provider
        do pure ()

template Request
  with
    provider : Party
    customer : Party
  where
    signatory customer
    observer provider

    choice Cancel : ()
        controller customer
        do pure ()

    choice Reject : ()
        controller provider
        do pure ()

    choice Approve : ContractId Service
      with
        operator : Party
      controller operator, provider
      do
        create Service with ..

template CreateAuctionRequest
  with
    operator : Party
    provider : Party
    customer : Party
    auctionId : Text
    asset : Asset
    quotedAssetId : Id
    floorPrice : Decimal
    depositCid : ContractId AssetDeposit
    receivableAccount : Account
  where
    signatory operator, provider, customer
