module Marketplace.Utils where

createWhenNotExists : forall t k. (HasCreate t, Template t, Template<PERSON>ey t k) => t -> Update (Optional (ContractId t))
createWhenNotExists t = visibleBy<PERSON>ey @t (key t) >>= \case
    True  -> Some <$> create t
    False -> return None

createOrLookup : forall t k. (HasCreate t, Template t, TemplateKey t k) => t -> Update (ContractId t)
createOrLookup t = lookupByKey @t (key t) >>= \case
    (Some cid) -> return cid
    None       -> create t
