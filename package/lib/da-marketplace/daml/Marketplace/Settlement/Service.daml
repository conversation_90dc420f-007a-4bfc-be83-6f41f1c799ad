module Marketplace.Settlement.Service where

import DA.Finance.Asset (AssetDeposit)
import Marketplace.Settlement.Model (SettlementInstruction(..), Settle(..))
import Marketplace.Utils

type T = Service

template Service
  with
    operator : Party
    provider : Party
  where
    signatory operator, provider

    key (operator, provider) : (Party, Party)
    maintainer key._1

    nonconsuming choice SettleInstruction : [ContractId AssetDeposit]
      with
        settlementInstructionCid : ContractId SettlementInstruction
      controller provider
      do
        exercise settlementInstructionCid Settle

    choice Terminate : ()
      controller operator
      do return ()

template Offer
  with
    operator : Party
    provider : Party
  where
    signatory operator
    observer provider

    choice Accept : ContractId Service
      controller provider
      do createOrLookup Service with ..

    choice Decline : ()
      controller provider
      do return ()

template Request
  with
    provider : Party
    operator : Party
  where
    signatory provider
    observer operator

    choice Approve : ContractId Service
      controller operator
        do createOrLookup Service with ..

    choice Reject : ()
      controller operator
      do return ()
