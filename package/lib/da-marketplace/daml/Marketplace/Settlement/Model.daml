module Marketplace.Settlement.Model where

import DA.Finance.Types (Account)
import DA.Finance.Asset (AssetDeposit, AssetDeposit_Transfer(..))
import DA.Set (fromList)

type T = SettlementInstruction

data SettlementDetails = SettlementDetails
  with
    senderAccount : Account
    depositCid : ContractId AssetDeposit
    receiverAccount : Account
  deriving (Eq, Show)

template SettlementInstruction
  with
    operator : Party
    provider : Party
    details : [SettlementDetails]
  where
    let senders   = map (.senderAccount.owner) details
        receivers = map (.receiverAccount.owner) details
    signatory fromList $ [operator, provider] <> senders <> receivers


    choice Settle : [ContractId AssetDeposit]
      controller provider
        do forA details (\settlementDetails -> exercise settlementDetails.depositCid $ AssetDeposit_Transfer settlementDetails.receiverAccount)

    choice ArchiveInstruction : ()
      controller provider
      do pure ()