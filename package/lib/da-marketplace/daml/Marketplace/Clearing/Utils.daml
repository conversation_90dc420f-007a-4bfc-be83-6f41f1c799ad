module Marketplace.Clearing.Utils where

import DA.List
import DA.Finance.Asset

mergeAndSplit : [ContractId AssetDeposit] -> Decimal -> Update [ContractId AssetDeposit]
mergeAndSplit depositCids amount = do
    depositCid <- exercise (head depositCids) AssetDeposit_Merge with depositCids = tail depositCids
    exercise depositCid AssetDeposit_Split with quantities = [amount]

getDepositQuantities : [ContractId AssetDeposit] -> Update Decimal
getDepositQuantities depositCids = sum <$> forA depositCids (fmap (.asset.quantity) . fetch)
