{-# LANGUAGE MultiWayIf #-}

module Marketplace.Clearing.Service where

import DA.Set qualified as Set

import DA.Finance.Asset
import DA.Finance.Types

import Marketplace.Clearing.Model
import Marketplace.Clearing.Utils
import Marketplace.Utils

data DepositWithRemaining = DepositWithRemaining {
   deposit   : ContractId AssetDeposit
  ,remaining : [ContractId AssetDeposit]
} deriving (Show,Eq)

data CalculationResult a b
  = CalculationSuccess { successCid : ContractId a, deposits: Optional DepositWithRemaining}
  | CalculationFailure { failureCid : (ContractId b)}

template Service
  with
    operator : Party
    provider : Party
    customer : Party
    clearingAccount : Account
    ccpAccount : Account
  where
    signatory operator, provider, customer

    key (operator, provider, customer) : (Party, Party, Party)
    maintainer key._1

    let
      transferDeposit : ContractId AssetDeposit -> Account -> Update (ContractId AssetDeposit)
      transferDeposit = \depositCid receiverAccount -> exercise depositCid AssetDeposit_Transfer with receiverAccount

      lockDeposit : ContractId AssetDeposit -> Update (ContractId AssetDeposit)
      lockDeposit = \depositCid -> exercise depositCid AssetDeposit_Lock with newLockers = Set.fromList [ provider ]

      unlockDeposit : ContractId AssetDeposit -> Update (ContractId AssetDeposit)
      unlockDeposit = flip exercise AssetDeposit_Unlock

      allocateDeposit : ContractId AssetDeposit -> Update (ContractId AssetDeposit)
      allocateDeposit = \depositCid -> (.observers) <$> fetch depositCid
        >>= \observers -> exercise depositCid AssetDeposit_SetObservers with newObservers = provider `Set.insert` observers

      failMarginCalculation : ContractId MarginCalculation -> Update (ContractId RejectedMarginCalculation)
      failMarginCalculation = \calculationCid -> do
        exerciseByKey @MemberStanding (provider, customer) MemberStanding_UpdateMargin with
            newMarginSatisfied = False; ..
        exercise calculationCid MarginCalculation_Reject with note = ""; ..

      fulfillMarginCalculation : ContractId MarginCalculation -> Update (ContractId FulfilledMarginCalculation)
      fulfillMarginCalculation = \calculationCid -> do
        exerciseByKey @MemberStanding (provider, customer) MemberStanding_UpdateMargin with
            newMarginSatisfied = True; ..
        exercise calculationCid MarginCalculation_Resolve with note = ""; ..

    nonconsuming choice ApproveTrade : Bool
        controller customer
        do
          (_,memberStanding) <- fetchByKey @MemberStanding (provider,customer)
          return $ memberStanding.marginSatisfied && memberStanding.mtmSatisfied

    nonconsuming choice CreateMarginCalculation : ContractId MarginCalculation
        with
          targetAmount : Decimal
          currency : Id -- TODO: make different currencies work
          calculationId : Text
        controller provider
        do
          calculationTime <- getTime
          create MarginCalculation with accountId = clearingAccount.id; ..

    nonconsuming choice CreateMarkToMarket : ContractId MarkToMarketCalculation
        with
          mtmAmount : Decimal
          currency : Id -- TODO: make different currencies work
          calculationId : Text
        controller provider
        do
          calculationTime <- getTime
          create MarkToMarketCalculation with accountId = clearingAccount.id; ..

    nonconsuming choice PerformMarginFill : CalculationResult FulfilledMarginCalculation
                                                         RejectedMarginCalculation
        with
          depositCids : [ContractId AssetDeposit]
          marginDepositCids : [ContractId AssetDeposit]
          calculationCid : ContractId MarginCalculation
        controller provider
        do
          targetAmount  <- (.targetAmount) <$> fetch calculationCid
          depositAmount <- getDepositQuantities depositCids
          marginAmount  <- getDepositQuantities marginDepositCids

          let diff = targetAmount - marginAmount
          if diff > 0.0 && depositAmount < diff
          then CalculationFailure <$> failMarginCalculation calculationCid
          else do
            cCid <- fulfillMarginCalculation calculationCid
            optDepositRemaing <-
              if | diff == 0.0 -> return None
                 | diff > 0.0  -> Some <$> exercise self TransferToMargin with amount = diff; ..
                 | otherwise   -> Some <$> exercise self TransferFromMargin with amount = abs diff; ..
            return $ CalculationSuccess cCid optDepositRemaing

    nonconsuming choice PerformMarkToMarket : CalculationResult FulfilledMarkToMarketCalculation
                                                           RejectedMarkToMarketCalculation
        with
          providerDepositCids : [ContractId AssetDeposit]
          customerDepositCids : [ContractId AssetDeposit]
          calculationCid : ContractId MarkToMarketCalculation
        controller provider
        do
          mtmAmount      <- (.mtmAmount) <$> fetch calculationCid
          providerAmount <- getDepositQuantities providerDepositCids
          customerAmount <- getDepositQuantities customerDepositCids

          let reject = do
                exerciseByKey @MemberStanding (provider, customer) MemberStanding_UpdateMTM with
                    newMtmSatisied = False; ..
                CalculationFailure <$> exercise calculationCid MarkToMarketCalculation_Reject with note = ""; ..

          let succeed depositFn = do
                exerciseByKey @MemberStanding (provider, customer) MemberStanding_UpdateMTM with
                    newMtmSatisied = False; ..
                resolvedCalcCid <- exercise calculationCid MarkToMarketCalculation_Resolve with note = ""; ..
                CalculationSuccess resolvedCalcCid <$> depositFn

          if
            | mtmAmount == 0.0 -> succeed $ return None
            | mtmAmount > 0.0  ->
              if providerAmount < mtmAmount
              then reject
              else succeed $ Some <$> exercise self TransferFromProvider with amount = mtmAmount; depositCids = providerDepositCids
            | otherwise ->
              if customerAmount < abs mtmAmount
              then reject
              else succeed $ Some <$> exercise self TransferToProvider with amount = abs mtmAmount; depositCids = customerDepositCids

    nonconsuming choice TransferFromProvider : DepositWithRemaining
        with
          depositCids : [ContractId AssetDeposit]
          amount : Decimal
        controller provider
        do
          assertMsg "deposits are not enough to transfer from provider" =<< (amount <=) <$> getDepositQuantities depositCids
          (depositCid::remainingCids) <- mergeAndSplit depositCids amount
          DepositWithRemaining <$> (transferDeposit depositCid clearingAccount >>= allocateDeposit) <*> pure remainingCids

      -- TODO: check that the deposit cid matches the clearing account?
    nonconsuming choice TransferToProvider : DepositWithRemaining
        with
          amount : Decimal
          depositCids : [ContractId AssetDeposit]
        controller provider
        do
          assertMsg "deposits are not enough to transfer to provider" =<< (amount <=) <$> getDepositQuantities depositCids
          (depositCid::remainingCids) <- mergeAndSplit depositCids amount
          DepositWithRemaining <$> transferDeposit depositCid ccpAccount <*> pure remainingCids

    nonconsuming choice TransferFromMargin : DepositWithRemaining
        with
          marginDepositCids : [ContractId AssetDeposit]
          amount : Decimal
        controller provider
        do
          assertMsg "deposits are not enough to transfer from margin" =<< (amount <=) <$> getDepositQuantities marginDepositCids
          (depositCid::remainingCids) <- mergeAndSplit marginDepositCids amount
          DepositWithRemaining <$> unlockDeposit depositCid <*> pure remainingCids

    nonconsuming choice TransferToMargin : DepositWithRemaining
        with
          depositCids : [ContractId AssetDeposit]
          amount : Decimal
        controller provider
        do
          assertMsg "deposits are not enough to transfer from provider" =<< (amount <=) <$> getDepositQuantities depositCids
          (depositCid::remainingCids) <- mergeAndSplit depositCids amount
          DepositWithRemaining <$> lockDeposit depositCid <*> pure remainingCids

    choice Terminate : ()
      with
        ctrl : Party
      controller ctrl
      do
        (standingCid,_) <- fetchByKey @MemberStanding (provider,customer)
        archive standingCid

template Offer
  with
    operator : Party
    provider : Party
    customer : Party
    ccpAccount : Account
  where
    signatory operator, provider
    observer customer

    choice Accept : ContractId Service
        with
          clearingAccount : Account
        controller customer
        do
          createOrLookup MemberStanding with marginSatisfied = True, mtmSatisfied = True, observers = mempty; ..
          createOrLookup Service with ..

    choice Decline : ()
        controller customer
        do
          return ()

    choice Withdraw : ()
        controller provider
        do pure ()

template Request
  with
    customer : Party
    provider : Party
    clearingAccount : Account
  where
    signatory customer
    observer provider

    choice Cancel : ()
        controller customer
        do pure ()

    choice Reject : ()
        controller provider
        do pure ()

    choice Approve : ContractId Service
      with
        operator : Party
        ccpAccount : Account
      controller operator, provider
      do
        createOrLookup MemberStanding with marginSatisfied = True, mtmSatisfied = True, observers = mempty; ..
        createOrLookup Service with ..
