module Marketplace.Clearing.Role where

import DA.Finance.Types

import Marketplace.Clearing.Service qualified as Clearing
import Marketplace.Clearing.Market.Service qualified as Market
import Marketplace.Utils
import DA.Set (Set)

template Role
  with
    operator : Party
    provider : Party
    observers : Set Party
    ccpAccount : Account
  where
    signatory operator, provider
    observer observers

    key (operator, provider) :  (Party, Party)
    maintainer key._1

    nonconsuming choice OfferClearingService : ContractId Clearing.Offer
        with
          customer : Party
        controller provider
        do
          create Clearing.Offer with ..

    nonconsuming choice ApproveClearingRequest : ContractId Clearing.Service
        with
          clearingRequestCid : ContractId Clearing.Request
        controller provider
        do
          exercise clearingRequestCid Clearing.Approve with ..

    nonconsuming choice RejectClearingRequest : ()
        with
          clearingRequestCid : ContractId Clearing.Request
        controller provider
        do
          exercise clearingRequestCid Clearing.Reject

    nonconsuming choice TerminateClearingService : ()
        with
          custodyServiceCid : ContractId Clearing.Service
        controller provider
        do
          archive custodyServiceCid

    nonconsuming choice OfferMarketService : ContractId Market.Offer
        with
          customer : Party
        controller provider
        do
          create Market.Offer with ..

    nonconsuming choice ApproveMarketRequest : ContractId Market.Service
        with
          marketRequestCid : ContractId Market.Request
        controller provider
        do
          exercise marketRequestCid Market.Approve with ..

    nonconsuming choice RejectMarketRequest : ()
        with
          marketRequestCid : ContractId Market.Request
        controller provider
        do
          exercise marketRequestCid Market.Reject

    nonconsuming choice TerminateMarketService : ()
        with
          marketServiceCid : ContractId Market.Service
        controller provider
        do
          archive marketServiceCid

    choice TerminateRole : ()
        controller operator
        do
          return ()

template Offer
  with
    operator : Party
    provider : Party
    observers : Set Party
  where
    signatory operator
    observer provider

    choice Accept : (ContractId Role, ContractId Market.Service)
        with
          ccpAccount : Account
        controller provider
        do
          marketCid <- createOrLookup Market.Service with customer = provider, ..
          roleCid   <- createOrLookup Role with ..
          return (roleCid, marketCid)

    choice Decline : ()
        controller provider
        do
          return ()

template Request
  with
    provider : Party
    operator : Party
    ccpAccount : Account
  where
    signatory provider
    observer operator

    choice Approve : (ContractId Role, ContractId Market.Service)
        with
          observers : Set Party
        controller operator
        do
          marketCid <- createOrLookup Market.Service with customer = provider, ..
          roleCid   <- createOrLookup Role with ..
          return (roleCid, marketCid)

    choice Reject : ()
        controller operator
        do
          return ()
