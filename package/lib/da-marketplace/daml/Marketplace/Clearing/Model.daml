module Marketplace.Clearing.Model where

import Marketplace.Trading.Model qualified as Order
import Marketplace.Trading.Model (Order(..))

import DA.Finance.Types
import DA.Set (Set)
import qualified DA.Set as Set

template MemberStanding
  with
    operator : Party
    provider : Party
    customer : Party
    marginSatisfied : Bool
    mtmSatisfied : Bool
    observers : Set Party
  where
    signatory provider, operator
    observer Set.insert customer observers
    key (provider, customer) : (Party, Party)
    maintainer key._1

    choice MemberStanding_UpdateMargin : ContractId MemberStanding
        with
          newMarginSatisfied : Bool
        controller provider
        do
          create this with marginSatisfied = newMarginSatisfied

    choice MemberStanding_UpdateMTM : ContractId MemberStanding
        with
          newMtmSatisied : Bool
        controller provider
        do
          create this with mtmSatisfied = newMtmSatisied

    choice MemberStanding_AddObservers : ContractId MemberStanding
        with
          newObservers : Set Party
        controller provider
        do
          create this with observers = observers <> newObservers

template MarginCalculation
  with
    provider : Party
    customer : Party
    accountId : Id
    currency : Id
    targetAmount : Decimal
    calculationTime : Time
    calculationId : Text
  where
    signatory provider
    observer customer
    -- TODO: should a zero target amount margin amount be allowed?
    ensure targetAmount > 0.0

    key (provider, customer, calculationId) : (Party, Party, Text)
    maintainer key._1

    choice MarginCalculation_Resolve : ContractId FulfilledMarginCalculation
        with
          note : Text
        controller provider
        do
          create FulfilledMarginCalculation with calculation = this; ..

    choice MarginCalculation_Reject : ContractId RejectedMarginCalculation
        with
          note : Text
        controller provider
        do
          create RejectedMarginCalculation with calculation = this; ..

-- TODO: Possibly change Text notes to specific fields, eg for fulfilled calculation:
-- fundsMoved : Decimal, or failed calculation, fundsMissing : Decimal
template FulfilledMarginCalculation
  with
    provider : Party
    customer : Party
    calculation : MarginCalculation
    note : Text
  where
    signatory provider
    observer customer

    key (provider, customer, calculation.calculationId) : (Party, Party, Text)
    maintainer key._1

template RejectedMarginCalculation
  with
    provider : Party
    customer : Party
    calculation : MarginCalculation
    note : Text
  where
    signatory provider
    observer customer

    key (provider, customer, calculation.calculationId) : (Party, Party, Text)
    maintainer key._1

    choice RejectedMarginCalculation_Retry : ContractId MarginCalculation
      with
        ctrl : Party
      controller ctrl
      do create calculation

    choice RejectedMarginCalculation_Cancel : ()
        controller provider
        do return ()

template MarkToMarketCalculation
  with
    provider : Party
    customer : Party
    accountId : Id
    currency : Id
    mtmAmount : Decimal
    calculationTime : Time
    calculationId : Text
  where
    signatory provider
    observer customer

    key (provider, customer, calculationId) : (Party, Party, Text)
    maintainer key._1

    choice MarkToMarketCalculation_Resolve : ContractId FulfilledMarkToMarketCalculation
        with
          note : Text
        controller customer
        do
          create FulfilledMarkToMarketCalculation with calculation = this, ..

    choice MarkToMarketCalculation_Reject : ContractId RejectedMarkToMarketCalculation
        with
          note : Text
        controller customer
        do
          create RejectedMarkToMarketCalculation with calculation = this, ..

template RejectedMarkToMarketCalculation
  with
    provider : Party
    customer : Party
    calculation : MarkToMarketCalculation
    note : Text
  where
    signatory provider
    observer customer

    key (provider, customer, calculation.calculationId) : (Party, Party, Text)
    maintainer key._1

    choice RejectedMarkToMarketCalculation_Retry : ContractId MarkToMarketCalculation
      with
        ctrl : Party
      controller ctrl
      do create calculation

    choice RejectedMarkToMarketCalculation_Cancel : ()
        controller provider
        do return ()

template FulfilledMarkToMarketCalculation
  with
    provider : Party
    customer : Party
    calculation : MarkToMarketCalculation
    note : Text

  where
    signatory provider
    observer customer

    key (provider, customer, calculation.calculationId) : (Party, Party, Text)
    maintainer key._1

template ClearedTrade
  with
    operator : Party
    provider : Party
    clearinghouse : Party
    makerOrder : Order
    takerOrder : Order
    execution : Order.Execution
  where
    signatory provider
    observer clearinghouse

    choice ClearedTrade_Novate : (ContractId ClearedTradeSide, ContractId ClearedTradeSide)
        controller clearinghouse
        do
          timeNovated <- getTime

          makerCid <- create ClearedTradeSide with
            exchange = provider
            order = makerOrder
            participant = makerOrder.customer
            ..

          takerCid <- create ClearedTradeSide with
            exchange = provider
            order = takerOrder
            participant = takerOrder.customer
            ..

          return (makerCid, takerCid)

template ClearedTradeSide
  with
    clearinghouse : Party
    exchange : Party
    participant : Party
    order : Order
    execution : Order.Execution
    timeNovated : Time
  where
    signatory clearinghouse, exchange
    observer exchange, participant
