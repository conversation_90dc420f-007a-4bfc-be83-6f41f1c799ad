module Marketplace.Clearing.Market.Service where

import DA.Finance.Types

import Marketplace.Clearing.Market.Model
import Marketplace.Listing.Model
import Marketplace.Utils

import DA.Set (Set)
import DA.Set qualified as Set

template Service
  with
    operator : Party
    provider : Party
    customer : Party
  where
    signatory operator, provider, customer

    key (operator, provider, customer) : (Party, Party, Party)
    maintainer key._1

    nonconsuming choice RequestAllFairValues : ContractId FairValueCalculationRequest
      with
        party : Party
        calculationId : Text
        upTo : Time
        currency : Id
      controller party
      do
        assert $ Set.member party $ Set.fromList [provider, customer]
        create FairValueCalculationRequest with optListingIds = None; ..

    nonconsuming choice RequestFairValues : ContractId FairValueCalculationRequest
      with
        party : Party
        listingIds : [Text]
        calculationId : Text
        upTo : Time
        currency : Id
      controller party
      do
        assert $ Set.member party $ Set.fromList [provider, customer]
        create FairValueCalculationRequest with optListingIds = Some listingIds; ..

    nonconsuming choice CreateFairValue : ContractId FairValue
        with
          listingId : Text
          calculationId : Text
          price : Decimal
          currency : Id
          timestamp : Time
          upTo : Time
          observers : Set Party
        controller provider
        do
          create FairValue with ..

    nonconsuming choice CreateManualFairValueRequest : ContractId ManualFairValueCalculation
        with
          listingId : Text
          calculationId : Text
          currency : Id
          upTo : Time
          observers : Set Party
        controller provider
        do
          create ManualFairValueCalculation with ..

    nonconsuming choice ApproveClearedListing : ContractId ClearedListingApproval
        with
          symbol : Text
          observers : Set Party
        controller customer
        do
          create ClearedListingApproval with provider = customer; clearinghouse = provider; ..

    choice Terminate : ()
      with
        ctrl : Party
      controller ctrl
      do
        pure ()

template Offer
  with
    operator : Party
    provider : Party
    customer : Party
  where
    signatory operator, provider
    observer customer

    choice Accept : ContractId Service
        controller customer
        do
          createOrLookup Service with ..

    choice Decline : ()
        controller customer
        do pure ()

    choice Withdraw : ()
        controller provider
        do pure ()

template Request
  with
    customer : Party
    provider : Party
  where
    signatory customer
    observer provider

    choice Cancel : ()
        controller customer
        do pure ()

    choice Reject : ()
        controller provider
        do pure ()

    choice Approve : ContractId Service
      with
        operator : Party
      controller operator, provider
      do
        create Service with ..
