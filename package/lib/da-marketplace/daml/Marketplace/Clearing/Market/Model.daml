module Marketplace.Clearing.Market.Model where

import DA.Finance.Types
import DA.Set (Set)

template FairValueCalculationRequest
  with
    operator : Party
    provider : Party
    customer : Party
    optListingIds : Optional [Text]
    calculationId : Text
    currency : Id
    upTo : Time
  where
    signatory operator, provider

    choice FairValueCalculationRequest_Ack : ()
        controller provider
        do return ()

template ManualFairValueCalculation
  with
    operator : Party
    provider : Party
    customer : Party
    listingId : Text
    calculationId : Text
    currency : Id
    upTo : Time
    observers : Set Party
  where
    signatory operator, provider
    observer customer
    let exchange = customer

    choice ManualFairValueCalculation_Calculate : ContractId FairValue
        with
          price : Decimal
        controller exchange
        do
          timestamp <- getTime
          create FairValue with ..

template FairValue
  with
    operator : Party
    provider : Party
    customer : Party
    listingId : Text
    calculationId : Text
    price : Decimal
    currency : Id
    timestamp : Time
    upTo : Time
    observers : Set Party
  where
    signatory operator, provider
    observer customer, observers
