module Marketplace.Operator.Role where

import Marketplace.Regulator.Role qualified as Regulator
import Marketplace.Custody.Role qualified as Custodian
import Marketplace.Clearing.Role qualified as Clearing
import Marketplace.Distribution.Role qualified as Distributor
import Marketplace.Settlement.Service qualified as Settlement
import Marketplace.Issuance.Service qualified as Issuance
import Marketplace.Custody.Service qualified as Custody
import Marketplace.Clearing.Market.Service qualified as Market
import Marketplace.Trading.Role qualified as Exchange
import Marketplace.Trading.Matching.Service qualified as Matching
import Marketplace.Listing.Service qualified as Listing

import DA.Set (Set)

template Role
  with
    operator : Party
    observers : Set Party
  where
    signatory operator
    observer observers

    key operator : Party
    maintainer key

    nonconsuming choice  OfferCustodianRole : ContractId Custodian.Offer
      with
        provider : Party
      controller operator
        do create Custodian.Offer with ..

    nonconsuming choice  ApproveCustodianRequest : (ContractId Custodian.Role, ContractId Issuance.Service, ContractId Custody.Service)
      with
        custodianRequestCid : ContractId Custodian.Request
      controller operator
        do exercise custodianRequestCid Custodian.Approve with ..

    nonconsuming choice  OfferExchangeRole : ContractId Exchange.Offer
      with
        provider : Party
      controller operator
      do
        create Exchange.Offer with ..

    nonconsuming choice ApproveExchangeRequest : (ContractId Exchange.Role, ContractId Matching.Service, ContractId Listing.Service, ContractId Settlement.Service)
      with
        exchangeRequestCid : ContractId Exchange.Request
      controller operator
      do
        exercise exchangeRequestCid Exchange.Approve with ..

    nonconsuming choice OfferRegulatorRole: ContractId Regulator.Offer
      with
        provider : Party
      controller operator
      do
        create Regulator.Offer with ..

    nonconsuming choice ApproveRegulatorRequest : ContractId Regulator.Role
      with
        regulatorRequestCid : ContractId Regulator.Request
      controller operator
      do
        exercise regulatorRequestCid Regulator.Approve with ..

    nonconsuming choice OfferMatchingService : ContractId Matching.Offer
      with
        provider : Party
      controller operator
      do
        create Matching.Offer with ..

    nonconsuming choice ApproveMatchingRequest : ContractId Matching.Service
      with
        matchingRequestCid : ContractId Matching.Request
      controller operator
      do
        exercise matchingRequestCid Matching.Approve

    nonconsuming choice OfferSettlementService : ContractId Settlement.Offer
      with
        provider : Party
      controller operator
      do
        create Settlement.Offer with ..

    nonconsuming choice ApproveSettlementRequest : ContractId Settlement.Service
      with
        settlementRequestCid : ContractId Settlement.Request
      controller operator
      do
        exercise settlementRequestCid Settlement.Approve

    nonconsuming choice OfferDistributorRole : ContractId Distributor.Offer
      with
        provider : Party
      controller operator
      do
        create Distributor.Offer with ..

    nonconsuming choice ApproveDistributorRequest : ContractId Distributor.Role
      with
        distributorRequestCid : ContractId Distributor.Request
      controller operator
      do
        exercise distributorRequestCid Distributor.Approve with ..

    nonconsuming choice OfferClearingRole : ContractId Clearing.Offer
      with
        provider : Party
      controller operator
      do
        create Clearing.Offer with ..

    nonconsuming choice ApproveClearingRequest : (ContractId Clearing.Role, ContractId Market.Service)
      with
        clearingRequestCid : ContractId Clearing.Request
      controller operator
      do
        exercise clearingRequestCid Clearing.Approve with ..
