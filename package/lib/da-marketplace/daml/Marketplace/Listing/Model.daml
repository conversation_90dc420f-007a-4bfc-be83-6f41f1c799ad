module Marketplace.Listing.Model where

-- import Marketplace.Clearing.Market.Service qualified as Market
import DA.Finance.Types (Id)
import DA.Set (Set)
import DA.Set qualified as Set

type T = Listing

data Status
    = Active
    | Disabled
  deriving (Eq, Show)

data ListingType
    = Cleared with
        clearinghouse : Party
        approvalCid : (ContractId ClearedListingApproval)
    | Collateralized
  deriving (Eq, Show)

template Listing
  with
    operator : Party
    provider : Party
    customer : Party
    listingId : Text
    listingType : ListingType
    calendarId : Text
    description : Text
    tradedAssetId : Id
    quotedAssetId : Id
    tradedAssetPrecision : Int
    quotedAssetPrecision : Int
    minimumTradableQuantity : Decimal
    maximumTradableQuantity : Decimal
    providerId : Text
    status : Status
    observers : Set Party
  where
    signatory operator, provider, customer
    observer observers
      <> Set.fromList case listingType of
            (Cleared p _) -> [p]; _ -> []

    -- TODO: Should we key by traded and quoted asset instead? listing id could be opaque / random
    key (operator, provider, listingId) : (Party, Party, Text)
    maintainer key._1, key._2

template ClearedListingApproval
  with
    provider : Party
    clearinghouse : Party
    symbol : Text
    observers : Set Party
  where
    signatory provider, clearinghouse
    observer observers

    key (provider, clearinghouse, symbol) : (Party, Party, Text)
    maintainer key._1
