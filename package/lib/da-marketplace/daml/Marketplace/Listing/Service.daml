module Marketplace.Listing.Service where

import DA.Finance.Types
import DA.Set (Set, fromList)
import Marketplace.Clearing.Market.Service qualified as Clearing
import Marketplace.Trading.Error qualified as Error
import Marketplace.Listing.Model ( ListingType(..), Listing(..), Status(..))
import Marketplace.Utils

data ListingTypeRequest = CollateralizedRequest | ClearedRequest Party deriving (Show, Eq)

template Service
  with
    operator : Party
    provider : Party
    customer : Party
  where
    signatory operator, provider, customer

    key (operator, provider, customer) : (Party, Party, Party)
    maintainer key._1

    nonconsuming choice RequestCreateListing : ContractId CreateListingRequest
        with
          listingType : ListingTypeRequest
          symbol : Text
          calendarId : Text
          description : Text
          tradedAssetId : Id
          quotedAssetId : Id
          tradedAssetPrecision : Int
          quotedAssetPrecision : Int
          minimumTradableQuantity : Decimal
          maximumTradableQuantity : Decimal
          observers : [Party]
        controller customer
        do
          listingType <- case listingType of
            CollateralizedRequest -> return Collateralized
            (ClearedRequest p)    -> Cleared p <$> (exercise<PERSON><PERSON><PERSON><PERSON> @Clearing.Service (operator, p, customer) Clearing.ApproveClearedListing with observers = fromList observers; ..)

          create CreateListingRequest with status = Active; processed = False, observers = fromList observers, ..

    nonconsuming choice RequestDisableListing : ContractId DisableListingRequest
        with
          listingCid : ContractId Listing
        controller customer
        do
          create DisableListingRequest with processed = False, ..

    nonconsuming choice CreateListing : ContractId Listing
        with
          createListingRequestCid : ContractId CreateListingRequest
          providerId : Text
        controller provider
        do
          CreateListingRequest{..} <- fetch createListingRequestCid
          archive createListingRequestCid
          create Listing with listingId = symbol; ..

    nonconsuming choice ListingFailure : ContractId FailedListingCreation
        with
          createListingRequestCid : ContractId CreateListingRequest
          message : Text
          name : Text
          code : Text
        controller provider
        do
          CreateListingRequest{..} <- fetch createListingRequestCid
          archive createListingRequestCid

          let error = Error.Error with message = name <> " : " <> message, ..
          create FailedListingCreation with ..

    nonconsuming choice DisableListing : ContractId Listing
        with
          disableListingRequestCid : ContractId DisableListingRequest
        controller provider
        do
          DisableListingRequest{..} <- fetch disableListingRequestCid
          archive disableListingRequestCid
          listing <- fetch listingCid
          archive listingCid
          create listing with status = Disabled

    choice Terminate : ()
      with
        ctrl : Party
      controller ctrl
      do pure ()

template Offer
  with
    operator : Party
    provider : Party
    customer : Party
  where
    signatory operator, provider
    observer customer

    choice Accept : ContractId Service
        controller customer
        do
          createOrLookup Service with ..

    choice Decline : ()
        controller customer
        do pure ()

    choice Withdraw : ()
        controller provider
        do pure ()

template Request
  with
    customer : Party
    provider : Party
  where
    signatory customer
    observer provider

    choice Cancel : ()
        controller customer
        do pure ()

    choice Reject : ()
        controller provider
        do pure ()

    choice Approve : ContractId Service
      with
        operator : Party
      controller operator, provider
      do
        createOrLookup Service with ..

template CreateListingRequest
  with
    operator : Party
    provider : Party
    customer : Party
    symbol : Text
    listingType : ListingType
    calendarId : Text
    description : Text
    tradedAssetId : Id
    quotedAssetId : Id
    tradedAssetPrecision : Int
    quotedAssetPrecision : Int
    minimumTradableQuantity : Decimal
    maximumTradableQuantity : Decimal
    status : Status
    observers : Set Party
    processed : Bool
  where
    signatory operator, provider, customer

    key (provider, symbol) : (Party, Text)
    maintainer key._1

    let serviceKey = (operator, provider, customer)

    nonconsuming choice ListingRequestSuccess : ContractId Listing
        with
          providerId : Text
        controller provider
        do
          exerciseByKey @Service serviceKey CreateListing with createListingRequestCid = self, ..

    nonconsuming choice ListingRequestFailure : ContractId FailedListingCreation
        with
          message : Text
          name : Text
          code : Text
        controller provider
        do
          exerciseByKey @Service serviceKey ListingFailure with createListingRequestCid = self, ..

    choice FlipCreateListing : ContractId CreateListingRequest
        controller provider
        do
            assertMsg "This Contract has already been Processed by the Adapter" $ not processed
            create CreateListingRequest with processed = True, ..

template FailedListingCreation
  with
    operator : Party
    provider : Party
    customer : Party
    error : Error.T
    symbol : Text
    listingType : ListingType
    calendarId : Text
    description : Text
    tradedAssetId : Id
    quotedAssetId : Id
    tradedAssetPrecision : Int
    quotedAssetPrecision : Int
    minimumTradableQuantity : Decimal
    maximumTradableQuantity : Decimal
    status : Status
  where
    signatory operator, provider, customer

template DisableListingRequest
  with
    operator : Party
    provider : Party
    customer : Party
    listingCid : ContractId Listing
    processed : Bool
  where
    signatory operator, provider, customer

    choice FlipDisableListing : ContractId DisableListingRequest
        controller provider
        do
            assertMsg "This Contract has already been Processed by the Adapter" $ not processed
            create DisableListingRequest with processed = True, ..
