module Marketplace.Regulator.Service where

import DA.Set (Set, fromList)
import Marketplace.Regulator.Model (VerifiedIdentity(..))
import Marketplace.Utils

template Service
  with
    operator : Party
    provider : Party
    customer : Party
  where
    signatory operator, provider, customer

    key (operator, provider, customer) : (Party, Party, Party)
    maintainer key._1

    nonconsuming choice RequestIdentityVerification : ContractId IdentityVerificationRequest
      with
        legalName : Text
        location : Text
        observers : [Party]
      controller customer
        do create IdentityVerificationRequest with observers = fromList observers; ..

    nonconsuming choice VerifyIdentity : ContractId VerifiedIdentity
      with
        identityVerificationRequestCid : ContractId IdentityVerificationRequest
      controller provider
      do
        IdentityVerificationRequest{..} <- fetch identityVerificationRequestCid
        archive identityVerificationRequestCid
        create VerifiedIdentity with ..

template Offer
  with
    operator : Party
    provider : Party
    customer : Party
  where
    signatory operator, provider
    observer customer

    choice Accept : ContractId Service
      controller customer
        do createOrLookup Service with ..

    choice Decline : ()
      controller customer
        do pure ()


    choice Withdraw : ()
      controller provider
        do pure ()

template Request
  with
    customer : Party
    provider : Party
  where
    signatory customer
    observer provider

    choice Cancel : ()
      controller customer
        do pure ()

    choice Reject : ()
      controller provider
        do pure ()

    choice Approve : ContractId Service
      with
        operator : Party
      controller operator, provider
      do
        createOrLookup Service with ..

template IdentityVerificationRequest
  with
    operator : Party
    provider : Party
    customer : Party
    observers : Set Party
    legalName : Text
    location : Text
  where
    signatory operator, provider, customer
    observer observers
