module Marketplace.Regulator.Role where
import Marketplace.Regulator.Service qualified as Regulator
import Marketplace.Utils
import DA.Set (Set)

template Role
  with
    operator : Party
    provider : Party
    observers : Set Party
  where
    signatory operator, provider
    observer observers

    key (operator, provider) :  (Party, Party)
    maintainer key._1

    nonconsuming choice OfferRegulatorService : ContractId Regulator.Offer
      with
        customer : Party
      controller provider
      do create Regulator.Offer with ..

    nonconsuming choice ApproveRegulatorRequest : ContractId Regulator.Service
      with
        regulatorRequestCid : ContractId Regulator.Request
      controller provider
      do exercise regulatorRequestCid Regulator.Approve with ..

    nonconsuming choice TerminateRegulatorService : ()
      with
        regulatorServiceCid : ContractId Regulator.Service
      controller provider
      do archive regulatorServiceCid

    choice TerminateRole : ()
      controller operator
      do return ()

template Offer
  with
    operator : Party
    provider : Party
    observers : Set Party
  where
    signatory operator
    observer provider

    choice Accept : ContractId Role
      controller provider
        do
          createOrLookup Role with ..

    choice Decline : ()
        controller provider
        do
          return ()

template Request
  with
    provider : Party
    operator : Party
  where
    signatory provider
    observer operator

    choice Approve : ContractId Role
      with
        observers : Set Party
      controller operator
        do
          createOrLookup Role with ..

    choice Reject : ()
      controller operator
        do
          return ()
