module Marketplace.Custody.Role where

import Marketplace.Custody.Service qualified as Custody
import Marketplace.Issuance.Service qualified as Issuance
import Marketplace.Utils
import DA.Set (Set)

template Role
  with
    operator : Party
    provider : Party
    observers : Set Party
  where
    signatory operator, provider
    observer observers

    key (operator, provider) :  (Party, Party)
    maintainer key._1

    nonconsuming choice OfferCustodyService : ContractId Custody.Offer
        with
          customer : Party
        controller provider
        do
          create Custody.Offer with ..

    nonconsuming choice ApproveCustodyRequest : ContractId Custody.Service
        with
          custodyRequestCid : ContractId Custody.Request
        controller provider
        do
          exercise custodyRequestCid Custody.Approve with ..

    nonconsuming choice TerminateCustodyService : ()
        with
          custodyServiceCid : ContractId Custody.Service
        controller provider
        do
          archive custodyServiceCid

    nonconsuming choice OfferIssuanceService : ContractId Issuance.Offer
        with
          customer : Party
        controller provider
        do
          create Issuance.Offer with ..

    nonconsuming choice ApproveIssuanceRequest : ContractId Issuance.Service
        with
          issuanceServiceRequestCid : ContractId Issuance.Request
        controller provider
        do
          exercise issuanceServiceRequestCid Issuance.Approve with ..

    nonconsuming choice TerminateIssuanceService : ()
        with
          issuanceServiceCid : ContractId Issuance.Service
        controller provider
        do
          archive issuanceServiceCid

    choice TerminateRole: ()
        controller operator
        do
          return ()

template Offer
  with
    operator : Party
    provider : Party
    observers : Set Party
  where
    signatory operator
    observer provider

    choice Accept : (ContractId Role, ContractId Issuance.Service, ContractId Custody.Service)
        controller provider
        do
          let account = Custody.createAccount provider provider

          custodyCid  <- createOrLookup Custody.Service with customer = provider, ..
          issuanceCid <- createOrLookup Issuance.Service with customer = provider, ..
          roleCid     <- createOrLookup Role with ..
          return (roleCid, issuanceCid, custodyCid)

    choice Decline : ()
        controller provider
        do
          return ()

template Request
  with
    provider : Party
    operator : Party
  where
    signatory provider
    observer operator

    choice Approve : (ContractId Role, ContractId Issuance.Service, ContractId Custody.Service)
        with
          observers : Set Party
        controller operator
        do
          let account = Custody.createAccount provider provider

          custodyCid  <- createOrLookup Custody.Service with customer = provider, ..
          issuanceCid <- createOrLookup Issuance.Service with customer = provider, ..
          roleCid     <- createOrLookup Role with ..
          return (roleCid, issuanceCid, custodyCid)

    choice Reject : ()
        controller operator
        do
          return ()
