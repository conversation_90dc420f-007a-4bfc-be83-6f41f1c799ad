module Marketplace.Custody.Model (
    DepositRequest (..)
  , WithdrawalRequest (..)
  , LifecycleRequest(..)
  , Process(..)
) where

import ContingentClaims.Claim (Claim(Zero), deserialize, serialize)
import ContingentClaims.Lifecycle qualified as Lifecycle
import DA.Date (toDateUTC)
import DA.Finance.Asset
import DA.Finance.Types
import DA.Map (Map)
import DA.Map qualified as Map
import DA.Set
import DA.Optional (fromSomeNote)
import Marketplace.Issuance.AssetDescription (AssetDescription, Claims)
import Marketplace.Issuance.AssetDescription qualified as AssetDescription
import Marketplace.Settlement.Model (SettlementInstruction(..), SettlementDetails(..))
import DA.Action (void, when, foldlA)

template DepositRequest
  with
    operator : Party
    provider : Party
    customer : Party
    asset : Asset
  where
    signatory operator, provider, customer

template WithdrawalRequest
  with
    operator : Party
    provider : Party
    customer : Party
    depositCid : ContractId AssetDeposit
  where
    signatory operator, provider, customer

template LifecycleRequest
  with
    operator : Party
    provider : Party
    customer : Party
    assetDepositCid: ContractId AssetDeposit
    choice: Claims
  where
    signatory operator, provider, customer

    -- TODO: this shouldn't be exported; it's only called from CustodyService.Lifecycle
    choice Process : (ContractId AssetDeposit, [ContractId SettlementInstruction])
      with
          investor: Party
          safekeepingDepositCid: ContractId AssetDeposit
          fixings: Map Text (Map Date Decimal) --TODO these should be looked up from a contract on-ledger, not provided by the custodian!
          uniquePayoutId: Text
      controller investor
        do
          t <- toDateUTC <$> getTime
          assetDeposit <- fetch assetDepositCid
          (_, description) <- fetchByKey @AssetDescription assetDeposit.asset.id
          (AssetDeposit{account = safekeepingAccount}) <- fetch safekeepingDepositCid
          (versionsCid, versions) <- fetchByKey @AssetDescription.Index (fromList . signatory $ description, assetDeposit.asset.id.label)


          let spot : Id -> Date -> Update Decimal
              spot id dt = pure $ fromSomeNote
                ("Lifecycle: Underlying data '" <> id.label <> "' missing on " <> show dt)
                (Map.lookup id.label fixings >>= Map.lookup dt)
              election = (True, deserialize choice)
          remaining <- if election._2 /= Zero
            then Lifecycle.exercise spot election (deserialize description.claims) t
            else pure (deserialize description.claims)
          Lifecycle.Result{remaining, pending} <- Lifecycle.lifecycle spot remaining t


          let serializedRemaining = serialize remaining
          (_, postAssetVersion) <- exercise versionsCid AssetDescription.LookupOrInsert with claims = serializedRemaining


          when (postAssetVersion /= assetDeposit.asset.id.version) do
            createOrLookupNewVersionOfDescription description postAssetVersion serializedRemaining


          let payoutSplitQuantities = (\(qty, _) -> qty * assetDeposit.asset.quantity) <$> pending
          {- Refactor so no asset split happens when nothing is pending -}
          payoutCids <-
            if Prelude.null payoutSplitQuantities then do
                return []
            else do
                {- init splitResultDeposits ==>
                    Removed due to exact split being done before lifecycle and
                    was causing issues where no payout would be present -}
                --exercise safekeepingDepositCid AssetDeposit_Split with quantities = payoutSplitQuantities
                return [safekeepingDepositCid]



          let masterAgreementSignatories = [provider, customer, investor]

          payoutInstructions <- createPayoutInstructions payoutCids uniquePayoutId safekeepingAccount assetDeposit operator provider
          assetDepositCid' <- create assetDeposit with asset = (assetDeposit.asset with id = (assetDeposit.asset.id with version = postAssetVersion))


          archive assetDepositCid
          return (assetDepositCid', payoutInstructions)

createPayoutInstructions : [ContractId AssetDeposit] -> Text -> Account -> AssetDeposit -> Party -> Party -> Update [ContractId SettlementInstruction]
createPayoutInstructions payoutCids uniqueId safekeepingAccount assetDeposit operator provider =
  snd <$> foldlA createSI (1, []) payoutCids
  where
    createSettlementDetails payoutDepositCid =
      SettlementDetails with
        senderAccount = safekeepingAccount
        receiverAccount = assetDeposit.account
        depositCid = payoutDepositCid
    createSI (index, accumulator) payoutDepositCid = do
      siCid <- create SettlementInstruction with operator; provider; details = [ createSettlementDetails payoutDepositCid ]
      pure (index + 1, siCid :: accumulator)

createOrLookupNewVersionOfDescription : AssetDescription -> AssetDescription.Version -> Claims -> Update ()
createOrLookupNewVersionOfDescription description postAssetVersion serializedRemainingClaims = do

  lookupByKey @AssetDescription.AssetDescription adjustedId >>=
    void . optional
        (create description with
          assetId = adjustedId
          claims = serializedRemainingClaims) (return)
 where
  adjustedId = description.assetId with version = postAssetVersion

createTradeId : Id -> Int -> Text -> Id
createTradeId assetId transferNumber uniqueId =
  Id with
    signatories = assetId.signatories
    label = assetId.label <> " " <> show transferNumber <> "th transfer, ID: " <> uniqueId
    version = assetId.version
