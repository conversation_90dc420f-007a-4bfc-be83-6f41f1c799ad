module Marketplace.Custody.Service where

import DA.Finance.Asset
import DA.Finance.Types
import DA.Finance.Utils (fetchAndArchive)
import DA.Map (Map)
import DA.Set
import Marketplace.Issuance.AssetDescription (Claims, AssetDescription(..))
import Marketplace.Custody.Model qualified as Custody
import Marketplace.Settlement.Model (SettlementInstruction)
import Marketplace.Utils

template Service
  with
    operator : Party
    provider : Party
    customer : Party
    account : Account
  where
    signatory operator, provider, customer

    key (operator, provider, customer) : (Party, Party, Party)
    maintainer key._1

    nonconsuming choice RequestDeposit : ContractId Custody.DepositRequest
        with
          asset : Asset
        controller customer
        do
          create Custody.DepositRequest with ..

    nonconsuming choice RequestWithdrawal : ContractId Custody.WithdrawalRequest
        with
          depositCid : ContractId AssetDeposit
        controller customer
        do
          create Custody.WithdrawalRequest with ..

    nonconsuming choice RequestLifecycle: ContractId Custody.LifecycleRequest
        with
          assetDepositCid: ContractId AssetDeposit
          choice: Claims
        controller customer
        do
          create Custody.LifecycleRequest with ..

    nonconsuming choice Deposit : ContractId AssetDeposit
        with
          depositRequestCid : ContractId Custody.DepositRequest
        controller provider
        do
          Custody.DepositRequest{asset} <- fetchAndArchive depositRequestCid
          (_, AssetDescription{issuer}) <- fetchByKey @AssetDescription asset.id

          create AssetDeposit with asset; account; lockers = empty; observers = fromList [ issuer ]

    nonconsuming choice Withdrawal : ()
        with
          withdrawalRequestCid : ContractId Custody.WithdrawalRequest
        controller provider
        do
          Custody.WithdrawalRequest{depositCid} <- fetchAndArchive withdrawalRequestCid
          deposit <- fetch depositCid
          assertMsg "Deposit account provider and service provider do not match" $ deposit.account.provider == provider

          archive depositCid

    nonconsuming choice Lifecycle : (ContractId AssetDeposit, [ContractId SettlementInstruction])
        with
          lifecycleRequestCid : ContractId Custody.LifecycleRequest
          safekeepingDepositCid : ContractId AssetDeposit
          fixings : Map Text (Map Date Decimal) --TODO these should be looked up from a contract on-ledger, not provided by the custodian!
          uniquePayoutId : Text
        controller provider
        do
          exercise lifecycleRequestCid Custody.Process with investor = customer; ..


    choice Terminate : ()
      with
        ctrl : Party
      controller ctrl
      do pure ()

template Offer
  with
    operator : Party
    provider : Party
    customer : Party
  where
    signatory operator, provider
    observer customer

    choice Accept : ContractId Service
        controller customer
        do
          let account = createAccount provider customer
          createOrLookup Service with ..

    choice Decline : ()
        controller customer
        do pure ()

    choice Withdraw : ()
        controller provider
        do pure ()

template Request
  with
    customer : Party
    provider : Party
  where
    signatory customer
    observer provider

    choice Cancel : ()
        controller customer
        do pure ()

    choice Reject : ()
        controller provider
        do pure ()

    choice Approve : ContractId Service
      with
        operator : Party
      controller operator, provider
      do
        let account = createAccount provider customer
        createOrLookup Service with ..

createAccount : Party -> Party -> Account
createAccount provider customer = do
  let id = Id with signatories = fromList [customer, provider]; label = partyToText customer <> "@" <> partyToText provider; version = 0
      owner = customer
  Account with id; owner; provider
