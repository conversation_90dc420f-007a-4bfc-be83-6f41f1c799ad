module Marketplace.Trading.Confirmation.Model where

import DA.Foldable (forA_)
import DA.Finance.Asset (AssetDeposit, AssetDeposit_Split(..), AssetDeposit_Unlock(..))
import DA.Finance.Utils (fetchAndArchive)
import DA.Set (Set, insert)
import Marketplace.Settlement.Model qualified as Settlement
import Marketplace.Trading.Model qualified as Order
import DA.Optional (optionalToList)

-- TODO: Try to turn this into a service

type T = Confirmation

template Confirmation
  with
    operator : Party
    provider : Party
    buyer : Party
    seller : Party
    execution : Order.Execution
    signed : Set Party
  where
    signatory signed
    observer provider, buyer, seller

    choice Sign : ContractId Confirmation
      with
        ctrl : Party
      controller ctrl
      do
        assert (ctrl == buyer || ctrl == seller)
        create this with
          signed = insert ctrl signed


    choice ProcessCleared : ()
      with
        buyCid : ContractId Order.T
        sellCid : ContractId Order.T
      controller provider
      do
        forA_ [buyCid, sellCid] $ \orderCid -> do
          order <- fetchAndArchive orderCid
          let
            isPartialFill = execution.quantity < order.remainingQuantity
            executions = execution :: order.executions
            remainingQuantity = order.remainingQuantity - execution.quantity
            quantities = [case order.details.side of
              Order.Buy  -> execution.quantity * execution.price
              Order.Sell -> execution.quantity]
          if isPartialFill
            then do
              create order with executions, remainingQuantity, status = Order.PartiallyExecuted
            else do
              create order with executions, remainingQuantity, status = Order.FullyExecuted
          return ()

    choice Process : ContractId Settlement.SettlementInstruction
        with
          buyCid : ContractId Order.T
          sellCid : ContractId Order.T
        controller provider
        do
          [buyOrder, sellOrder] <- forA [buyCid, sellCid] fetchAndArchive

          -- TODO: Check that price matches limit order price, quantity is less than order qty, rounding, etc
          -- paymentCurrencyId <- (.quotedAssetId) . snd <$> fetchByKey @Listing (operator, provider, buyOrder.details.symbol)

          let (Order.Collateralized _ buyerReceivableAccount) = buyOrder.details.marketType
              (Order.Collateralized _ sellerReceivableAccount) = sellOrder.details.marketType

          details <- concat <$> forA [(buyOrder, sellerReceivableAccount), (sellOrder, buyerReceivableAccount)] (\(order, counterPartyAccount) -> do
            let
              executions = execution :: order.executions
              (Order.Collateralized depositCid _) = order.details.marketType

              {- Changed logic of obtaining quantities, the partial fill boolean and remaining amount, to take into account
                the price of each asset in match, from the perpective of the Buying customer's side -}
              (quantities, isPartialFill, remainingQuantity) =
                ([execution.quantity], execution.quantity < order.remainingQuantity, order.remainingQuantity - execution.quantity)

            let createFeeDetails : ContractId AssetDeposit -> Update Settlement.SettlementDetails
                createFeeDetails depositCid = do
                  feeAccount <- (.feeAccount) . snd <$> fetchByKey @Order.FeeSchedule (operator, provider)
                  senderAccount <- (.account) <$> fetch depositCid
                  pure $ Settlement.SettlementDetails with depositCid; senderAccount; receiverAccount = feeAccount

            -- TODO: Handle when error scenarios such as when execution quantity is larger than remainingQuantity
            (depositCid, optFeeDetails) <- if isPartialFill
              then do
                (newOptExchangeFee, feeDetails) <- case order.details.optExchangeFee of
                  None               -> return (None, None)
                  Some feeDepositCid -> do
                    feeQuantity <- (.asset.quantity) <$> fetch feeDepositCid
                    let feeToTake = roundBankers 2 $ feeQuantity * (execution.quantity / order.remainingQuantity)
                    [feeDepositCid, remainingFeeDepositCid] <- exercise feeDepositCid AssetDeposit_Split with quantities = [feeToTake]
                    feeDetails <- createFeeDetails feeDepositCid
                    return $ (Some remainingFeeDepositCid, Some feeDetails)

                (depositCid :: depositRemainingCid :: _) <- exercise depositCid $ AssetDeposit_Split with quantities

                create order with
                    executions
                    remainingQuantity
                    status = Order.PartiallyExecuted
                    details = order.details with
                        optExchangeFee = newOptExchangeFee
                        marketType = order.details.marketType with depositCid = depositRemainingCid
                pure (depositCid, feeDetails)
              else do
                optFeeDetails <- case order.details.optExchangeFee of
                  Some feeCid -> Some <$> createFeeDetails feeCid
                  _           -> return None

                depositCid <- exercise depositCid AssetDeposit_Split with quantities >>= \case
                  [depositCid] -> pure depositCid
                  (depositCid :: remainingDepositCid :: _) -> exercise remainingDepositCid AssetDeposit_Unlock >> pure depositCid
                  [] -> pure depositCid

                create order with
                    executions
                    remainingQuantity
                    status = Order.FullyExecuted
                    details = order.details with
                        optExchangeFee = None
                        marketType = order.details.marketType with depositCid = depositCid

                pure (depositCid, optFeeDetails)

            senderAccount <- (.account) <$> fetch depositCid
            let tradeDetails = Settlement.SettlementDetails with depositCid; senderAccount; receiverAccount = counterPartyAccount
            pure $ optionalToList optFeeDetails <> [tradeDetails]
            )

          create Settlement.SettlementInstruction with ..
