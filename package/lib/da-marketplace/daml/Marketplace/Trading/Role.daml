module Marketplace.Trading.Role where

import DA.Finance.Types

import Marketplace.Settlement.Service qualified as Settlement
import Marketplace.Trading.Matching.Service qualified as Matching
import Marketplace.Listing.Service qualified as Listing
import Marketplace.Trading.Service qualified as Trading
import Marketplace.Trading.Model qualified as Model
import Marketplace.Utils
import DA.Set (Set)

template Role
  with
    operator : Party
    provider : Party
    observers : Set Party
  where
    signatory operator, provider
    observer observers

    key (operator, provider) : (Party, Party)
    maintainer key._1

    nonconsuming choice OfferTradingService : ContractId Trading.Offer
      with
        customer : Party
      controller provider
      do
        create Trading.Offer with ..

    nonconsuming choice ApproveTradingServiceRequest : ContractId Trading.T
      with
        tradingRequestCid : ContractId Trading.Request
      controller provider
      do
        exercise tradingRequestCid Trading.Approve with ..

    nonconsuming choice TerminateTradingService : ()
      with
          tradingServiceCid : ContractId Trading.T
      controller provider
        do archive tradingServiceCid

    nonconsuming choice OfferListingService : ContractId Listing.Offer
      with
          customer : Party
      controller provider
        do
          create Listing.Offer with ..

    nonconsuming choice ApproveListingServiceRequest : ContractId Listing.Service
      with
          listingRequestCid : ContractId Listing.Request
      controller provider
        do
          exercise listingRequestCid Listing.Approve with ..

    nonconsuming choice TerminateListingService : ()
      with
          listingServiceCid : ContractId Listing.Service
      controller provider
        do
          archive listingServiceCid

    nonconsuming choice CreateFeeSchedule : ContractId Model.FeeSchedule
      with
          currency : Id
          feeAccount : Account
          quantity : Decimal
      controller provider
        do
          time <- getTime
          create Model.FeeSchedule with currentFee = Model.Fee quantity currency time; pastFees = []; ..

    choice TerminateRole : ()
      controller operator
        do return ()

template Offer
  with
    operator : Party
    provider : Party
    observers : Set Party
  where
    signatory operator
    observer provider

    choice Accept : (ContractId Role, ContractId Matching.Service, ContractId Listing.Service, ContractId Settlement.Service)
      controller provider
        do
          matchingCid   <- createOrLookup Matching.Service with ..
          settlementCid <- createOrLookup Settlement.Service with ..
          listingCid    <- createOrLookup Listing.Service with customer = provider, ..
          roleCid       <- createOrLookup Role with ..

          return (roleCid, matchingCid, listingCid, settlementCid)

    choice Decline : ()
      controller provider
        do
          return ()

template Request
  with
    provider : Party
    operator : Party
  where
    signatory provider
    observer operator

    choice Approve : (ContractId Role, ContractId Matching.Service,ContractId Listing.Service,  ContractId Settlement.Service)
      with
          observers : Set Party
      controller operator
        do
          matchingCid   <- createOrLookup Matching.Service with ..
          settlementCid <- createOrLookup Settlement.Service with ..
          listingCid    <- createOrLookup Listing.Service with customer = provider, ..
          roleCid       <- createOrLookup Role with ..
          return (roleCid, matchingCid, listingCid, settlementCid)

    choice Reject : ()
        controller operator
        do return ()
