module Marketplace.Trading.Model where

import DA.Set (Set)
import DA.Finance.Asset (AssetDeposit)
import DA.Finance.Types (Account, Id(..), Asset(..))
import Marketplace.Trading.Error qualified as Error

data Side
    = Buy
    | Sell
  deriving (Eq, Show)

data OrderType
    = Market
    | Limit with
        price : Decimal
  deriving (Eq, Show)

data TimeInForce
    = GTC
      -- ^ Good Till Cancelled (Rests on book until cancellation)
    | GTD with
        expiryDate : Int
      -- ^ Good Till Date (At expiryDate, order will be automatically cancelled).
      -- UTC date and time in seconds
    | GAA
      -- ^ Good At Auction (Expires after auction if not filled)
    | IOC
      -- ^ Immediate Or Cancel (Allows for partial fills)
    | FOK
      -- ^ Fill Or Kill (All or nothing)
  deriving (Eq, Show)

data MarketType
    = Cleared with
        clearinghouse : Party
    | Collateralized with
        depositCid : ContractId AssetDeposit
        receivableAccount : Account
  deriving (Show, Eq)

data Details = Details with
    id : Text
    listingId : Text
    asset : Asset
    side : Side
    orderType : OrderType
    timeInForce : TimeInForce
    marketType : MarketType
    optExchangeFee : Optional (ContractId AssetDeposit)
  deriving (Eq, Show)

data Status
    = New
    | PendingExecution
    | PartiallyExecuted
    | FullyExecuted
    | Rejected with
        reason : Error.T
    | PendingCancellation
    | CancellationRejected with
        reason : Error.T
    | Cancelled
  deriving (Eq, Show)

data Execution = Execution with
    matchId : Text
    makerOrderId : Text
    takerOrderId : Text
    quantity : Decimal
    price : Decimal
    timestamp : Text -- Should this be an Int so we can order if necessary?
  deriving (Eq, Show)

type T = Order

template Order
  with
    operator : Party
    provider : Party
    customer : Party
    status : Status
    details : Details
    providerOrderId : Optional Text
    executions : [Execution]
    remainingQuantity : Decimal
    createdAt : Time
  where
    signatory operator, provider, customer
    observer case details.marketType of
      (Cleared ch) -> [ch]
      _            -> []

    key (provider, details.id) : (Party, Text)
    maintainer key._1

data Fee = Fee with
    amount : Decimal
    currency : Id
    timeInEffect : Time
  deriving (Show, Eq)

template FeeSchedule
  with
    operator : Party
    provider : Party
    currentFee : Fee
    pastFees : [Fee]
    feeAccount : Account
    observers : Set Party
  where
    signatory provider
    key (operator, provider) : (Party, Party)
    maintainer key._2
    observer observers

    choice UpdateFeeSchedule : ContractId FeeSchedule
      with
        amount : Decimal
        currency : Id
      controller provider
      do
        timeInEffect <- getTime
        let newFee = Fee with ..
        create this with currentFee = newFee; pastFees = currentFee :: pastFees

isClearedOrder : Order -> Bool
isClearedOrder order = case order.details.marketType of
  Cleared _ -> True
  _         -> False
