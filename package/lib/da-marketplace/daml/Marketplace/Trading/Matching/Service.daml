module Marketplace.Trading.Matching.Service where

import DA.Set (fromList)
import Marketplace.Clearing.Model qualified as Clearing
import Marketplace.Settlement.Model qualified as Settlement
import Marketplace.Trading.Model qualified as Order
import Marketplace.Trading.Service qualified as TradingService
import Marketplace.Trading.Confirmation.Model qualified as Confirmation
import Marketplace.Utils

data MatchResult
  = Cleared (ContractId Clearing.ClearedTrade)
  | Collateralized (ContractId Settlement.SettlementInstruction)

template Service
  with
    operator : Party
    provider : Party
  where
    signatory operator, provider

    key provider : Party
    maintainer key


    nonconsuming choice MatchOrders : MatchResult
      with
        execution : Order.Execution
      controller provider
      do
        [(makerOrderCid, makerOrder), (takerOrderCid, takerOrder)] <- forA [execution.makerOrderId, execution.takerOrderId] (\orderId -> fetchByKey @Order.T (provider, orderId))
        let
          (buy, buyCid, sell, sellCid) = case makerOrder.details.side of
            Order.Buy  -> (makerOrder, makerOrderCid, takerOrder, takerOrderCid)
            Order.Sell -> (takerOrder, takerOrderCid, makerOrder, makerOrderCid)
          signConfirmation customer confirmationCid = exerciseByKey @TradingService.T (operator, provider, customer) TradingService.SignConfirmation with confirmationCid

        confirmationCid <- create Confirmation.Confirmation with signed = fromList [operator, provider]; buyer = buy.customer; seller = sell.customer; ..
          >>= signConfirmation buy.customer
          >>= signConfirmation sell.customer

        if not $ Order.isClearedOrder makerOrder
        then Collateralized <$> exercise confirmationCid Confirmation.Process with buyCid; sellCid
        else do
          exercise confirmationCid Confirmation.ProcessCleared with buyCid; sellCid
          let clearinghouse = makerOrder.details.marketType.clearinghouse
          Cleared <$> create Clearing.ClearedTrade with ..


    choice Terminate : ()
      controller operator
        do return ()

template Offer
  with
    operator : Party
    provider : Party
  where
    signatory operator
    observer provider

    choice Accept : ContractId Service
      controller provider
      do createOrLookup Service with ..

    choice Decline : ()
      controller provider
      do return ()

template Request
  with
    provider : Party
    operator : Party
  where
    signatory provider
    observer operator

    choice Approve : ContractId Service
      controller operator
        do createOrLookup Service with ..

    choice Reject : ()
      controller operator
        do return ()
