module Marketplace.Trading.Service where

import DA.Foldable (sequence_)
import DA.Optional (whenSome)
import DA.Set qualified as Set
import DA.Finance.Asset (AssetDeposit, AssetDeposit_Lock(..), AssetDeposit_Unlock(..))
import DA.Finance.Utils (fetchAndArchive)
import Marketplace.Custody.Service qualified as Custody
import Marketplace.Trading.Model qualified as Order
import Marketplace.Clearing.Service qualified as Clearing
import Marketplace.Trading.Confirmation.Model qualified as Confirmation
import Marketplace.Trading.Error (Error (..))
import Marketplace.Utils

type T = Service

template Service
  with
    operator : Party
    provider : Party
    customer : Party
  where
    signatory operator, provider, customer

    key (operator, provider, customer) : (Party, Party, Party)
    maintainer key._1

    let
      unlockDeposit_ : ContractId AssetDeposit -> Update (ContractId AssetDeposit)
      unlockDeposit_ = (`exercise` AssetDeposit_Unlock)
      unlockCollateral_ : Order.MarketType -> Update (ContractId AssetDeposit)
      unlockCollateral_ ~Order.Collateralized{depositCid} = unlockDeposit_ depositCid
      unlockDeposit : ContractId AssetDeposit -> Update ()
      unlockDeposit = \depositCid -> exercise depositCid AssetDeposit_Unlock >> pure ()
      unlockCollateral : Order.MarketType -> Update ()
      unlockCollateral = \case
        Order.Collateralized depositCid _ -> unlockDeposit depositCid
        _ -> pure ()


    nonconsuming choice RequestCreateOrder : ContractId Order.T
        with
          details : Order.Details
        controller customer
        do
          createdAt <- getTime

          let
            status = Order.New
            providerOrderId = None
            executions = []
            observers = Set.empty
            collateral = None
            remainingQuantity = details.asset.quantity
            detailsWithDepositedFees : Update Order.Details
            detailsWithDepositedFees = case details.optExchangeFee of
              Some feeDepositCid -> do
                newDepositCid <- exercise feeDepositCid AssetDeposit_Lock with newLockers = Set.fromList [ provider ]
                return details with optExchangeFee = Some newDepositCid
              None -> return details
            rejectOrder : Text -> Text -> Update (ContractId Order.T)
            rejectOrder = \code message -> do
              let reason = Error with ..
              create Order.Order with status = Order.Rejected{reason}; ..

          case details.marketType of
            Order.Collateralized depositCid receivableAccount ->
              lookupByKey @Custody.Service (operator, receivableAccount.provider, customer) >>= \case
                None   -> rejectOrder "3003" "Customer does not have a custodial relationship with the custodian of the asset to be delivered"
                Some _ -> do
                  lockedDepositCid <- exercise depositCid AssetDeposit_Lock with newLockers = Set.fromList [ provider ]
                  details <- (\details -> details with marketType = details.marketType with depositCid = lockedDepositCid) <$> detailsWithDepositedFees

                  create CreateOrderRequest with processed = False,..
                  create Order.Order with ..

            (Order.Cleared clearinghouse) ->
              lookupByKey @Clearing.Service (operator,clearinghouse,customer) >>= \case
                None            -> rejectOrder "3001" "Customer is not a member of clearinghouse"
                Some serviceCid -> exercise serviceCid Clearing.ApproveTrade >>= \case
                    True -> do
                      details <- detailsWithDepositedFees

                      create CreateOrderRequest with processed = False,..
                      create Order.Order with ..
                    False -> do
                      rejectOrder "3002" "Rejected by Clearinghouse"

    nonconsuming choice RequestCancelOrder : (ContractId Order.T, ContractId CancelOrderRequest)
        with
          orderCid : ContractId Order.T
        controller customer
        do
          <EMAIL>{..} <- fetchAndArchive orderCid

          orderCid <- create order with status = Order.PendingCancellation
          cancelOrderRequestCid <- create CancelOrderRequest with processed = False,..

          return (orderCid, cancelOrderRequestCid)


    nonconsuming choice AcknowledgeOrderRequest : ContractId Order.T
        with
          createOrderRequestCid : ContractId CreateOrderRequest
          providerOrderId : Text
        controller provider
        do
          CreateOrderRequest{..} <- fetchAndArchive createOrderRequestCid
          (orderCid, order) <- fetchByKey (provider, details.id)

          -- TODO: Confirm what to do with this business ACK when received out of the expected order.
          --  Should we set the providerOrderId but leave the status as is ? Should we check if this value is already set ? What to do if it's already set ? etc.
          case order.status of
            Order.New -> do
              archive orderCid
              create order with status = Order.PendingExecution; providerOrderId = Some providerOrderId
            _ -> return orderCid

    nonconsuming choice RejectOrderRequest : ContractId Order.T
        with
          createOrderRequestCid : ContractId CreateOrderRequest
          errorCode : Int
          errorMessage : Text
        controller provider
        do
          CreateOrderRequest{details} <- fetch createOrderRequestCid
          (orderCid, order) <- fetchByKey (provider, details.id)
          archive orderCid

          unlockCollateral order.details.marketType
          whenSome details.optExchangeFee \depositCid -> unlockDeposit depositCid
          let reason = Error with code = show errorCode; message = errorMessage

          create order with status = Order.Rejected with reason

    nonconsuming choice MarketOrderCancelRequest : ContractId Order.T
        with
          createOrderRequestCid : ContractId CreateOrderRequest
          providerOrderId : Text
          cancelledQuantity : Decimal
        controller provider
        do
          CreateOrderRequest{details} <- fetch createOrderRequestCid
          (orderCid, order) <- fetchByKey (provider, details.id)

          let isFullCancellation = cancelledQuantity >= order.details.asset.quantity
              isWaitingExecution = order.status `elem` [Order.New, Order.PendingExecution]
              isMarketOrder = order.details.orderType == Order.Market

          if isMarketOrder && isFullCancellation && isWaitingExecution
            then do
              sequence_ [archive orderCid, archive createOrderRequestCid]
              unlockCollateral order.details.marketType

              create order with status = Order.Cancelled, providerOrderId = Some providerOrderId
            else pure orderCid

    nonconsuming choice CancelOrder : (ContractId Order.T, ContractId AssetDeposit)
        with
          cancelOrderRequestCid : ContractId CancelOrderRequest
        controller provider
        do
          CancelOrderRequest{..} <- fetchAndArchive cancelOrderRequestCid
          (orderCid, <EMAIL>{..}) <- fetchByKey (provider, details.id)
          archive orderCid

          collateralDepositCid <- unlockCollateral_ order.details.marketType
          whenSome details.optExchangeFee \depositCid -> unlockDeposit depositCid

          orderCid <- create order with status = Order.Cancelled
          return (orderCid, collateralDepositCid)

    nonconsuming choice RejectCancellation : ContractId Order.T
        with
          cancelOrderRequestCid : ContractId CancelOrderRequest
          errorCode : Int
          errorMessage : Text
        controller provider
          do
            CancelOrderRequest{..} <- fetchAndArchive cancelOrderRequestCid
            (orderCid, order) <- fetchByKey (provider, details.id)
            archive orderCid
            let reason = Error with code = show errorCode; message = errorMessage
            create order with status = Order.CancellationRejected with reason


    nonconsuming choice SignConfirmation : ContractId Confirmation.T
        with
          confirmationCid : ContractId Confirmation.T
        controller operator, provider
          do exercise confirmationCid Confirmation.Sign with ctrl = customer, ..

    choice Terminate : ()
      with
        ctrl : Party
      controller ctrl
      do pure ()

template Offer
  with
    operator : Party
    provider : Party
    customer : Party
  where
    signatory operator, provider
    observer customer

    choice Accept : ContractId Service
      controller customer
        do
          createOrLookup Service with ..

    choice Decline : ()
      controller customer
        do
          return ()


    choice Withdraw : ()
      controller provider
        do pure ()

template Request
  with
    customer : Party
    provider : Party
  where
    signatory customer
    observer provider

    choice Cancel : ()
      controller customer
        do pure ()


    choice Reject : ()
      controller provider
        do pure ()

    choice Approve : ContractId Service
      with
        operator : Party
      controller operator, provider
      do
        createOrLookup Service with ..

template CreateOrderRequest
  with
    provider : Party
    customer : Party
    operator : Party
    details : Order.Details
    processed : Bool
  where
    signatory provider, customer

    key (provider, details.id) : (Party, Text)
    maintainer key._1

    let serviceKey = (operator, provider, customer)


    nonconsuming choice AcknowledgeRequest : ContractId Order.T
      with
        providerOrderId : Text
      controller provider
        do exerciseByKey @Service serviceKey AcknowledgeOrderRequest with createOrderRequestCid = self, ..

    nonconsuming choice RejectRequest : ContractId Order.T
      with
        errorCode : Int
        errorMessage : Text
      controller provider
        do exerciseByKey @Service serviceKey RejectOrderRequest with createOrderRequestCid = self, ..

    nonconsuming choice CancelRequest : ContractId Order.T
        with
          providerOrderId : Text
          cancelledQuantity : Decimal
        controller provider
        do exerciseByKey @Service serviceKey MarketOrderCancelRequest with createOrderRequestCid = self, ..

    choice FlipCreateOrder : ContractId CreateOrderRequest
      controller provider
        do
          assertMsg "This Contract has already been Processed by the Adapter" $ not processed
          create CreateOrderRequest with processed = True, ..


template CancelOrderRequest
  with
    provider : Party
    customer : Party
    operator : Party
    details : Order.Details
    processed : Bool
  where
    signatory provider, customer

    key (provider, details.id) : (Party, Text)
    maintainer key._1

    let serviceKey = (operator, provider, customer)


    nonconsuming choice AcknowledgeCancel : (ContractId Order.T, ContractId AssetDeposit)
      controller provider
        do
          exerciseByKey @Service serviceKey CancelOrder with cancelOrderRequestCid = self

    nonconsuming choice FailureCancel : ContractId Order.T
      with
        errorCode : Int
        errorMessage : Text
      controller provider
      do exerciseByKey @Service serviceKey RejectCancellation with cancelOrderRequestCid = self, ..

    choice FlipCancelOrder : ContractId CancelOrderRequest
      controller provider
        do
          assertMsg "This Contract has already been Processed by the Adapter" $ not processed
          create CancelOrderRequest with processed = True, ..

