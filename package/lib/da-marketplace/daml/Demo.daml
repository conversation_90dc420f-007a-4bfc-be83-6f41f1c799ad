module Demo where

import Common
import ContingentClaims.Claim (Claim(Zero), serialize)
import ContingentClaims.Financial (unrollDates)
import Daml.Script
import DA.Date (date, Month(..))
import DA.Finance.Asset (AssetDeposit, AssetDeposit_Split(..), AssetDeposit_Transfer(..))
import DA.Set
import Marketplace.Distribution.Role qualified as Distributor
import Marketplace.Distribution.Auction.Service qualified as Auction
import Marketplace.Issuance.Service qualified as Issuance
import Marketplace.Issuance.CFI qualified as CFI
import Tests.Utils

demo : Script ()
demo = allocateParties >>= runDemo

runDemo : Parties -> Script ()
runDemo parties@Parties{..} = do
  providers@Providers{..} <- onboardProviders parties

  issuer      <- onboardCustomer providers "Issuer" issuer
  alice       <- onboardCustomer providers "Alice" alice
  bob         <- onboardCustomer providers "Bob" bob
  charlie     <- onboardCustomer providers "Charlie" charlie

  assets@Assets{..} <- onboardAssets providers issuer

  aliceUsd    <- depositAsset providers alice     (usd with quantity = 1_000_000.0) alice.mainAccount.id empty
  bobUsd      <- depositAsset providers bob       (usd with quantity = 1_000_000.0) bob.mainAccount.id empty
  charlieUsd  <- depositAsset providers charlie   (usd with quantity = 1_000_000.0) charlie.mainAccount.id empty

  auctionOfferCid <- submit bank do exerciseCmd distributorRoleCid Distributor.OfferAuctionService with customer = issuer.customer; ..
  auctionServiceCid <- submit issuer.customer do exerciseCmd auctionOfferCid Auction.Accept

  examples providers assets issuer

  pure ()

examples : Providers -> Assets -> Customer -> Script ()
examples Providers{..} Assets{..} issuer = do

  let
    safekeepingAccount = issuer.mainAccount
    account = issuer.mainAccount
    observers = [public]

    originate assetLabel description cfi claims = do
      createOriginationCid <- submit issuer.customer do exerciseCmd issuer.issuanceServiceCid Issuance.RequestOrigination with claims = serialize claims; ..
      submit bank do exerciseCmd (issuer.issuanceServiceCid) Issuance.Originate with ..

    issue issuanceId assetId quantity = do
      createIssuanceRequestCid <- submit issuer.customer $ exerciseCmd issuer.issuanceServiceCid Issuance.RequestCreateIssuance with ..
      submit bank $ exerciseCmd issuer.issuanceServiceCid $ Issuance.CreateIssuance with ..

    transfer : Customer -> Customer -> ContractId AssetDeposit -> Decimal -> Script (ContractId AssetDeposit, Optional (ContractId AssetDeposit))
    transfer from to depositCid quantity = do
      Some deposit <- queryContractId bank depositCid
      if deposit.asset.quantity > quantity then do
        [ exactDepositCid, remainderDepositCid ] <- submit from.customer do exerciseCmd depositCid AssetDeposit_Split with quantities = [ quantity ]
        transferredDepositCid <- submit from.customer do exerciseCmd exactDepositCid AssetDeposit_Transfer with receiverAccount = to.mainAccount
        pure (transferredDepositCid, Some remainderDepositCid)
      else do
        transferredDepositCid <- submit from.customer do exerciseCmd depositCid AssetDeposit_Transfer with receiverAccount = to.mainAccount
        pure (transferredDepositCid, None)

  -- Origination
  (_, dax)    <- originate "DAX" "DAX Index" CFI.equity Zero
  (_, pmt)    <- originate "PMT-USD" "Payment Example" CFI.other $ payment usd.id 10_000.0
  (_, del)    <- originate "DEL-TSLA" "Delivery Example" CFI.other $ delivery tsla.id 20.0
  (_, dvp)    <- originate "DVP-TSLA-USD" "DvP Example" CFI.other $ dvp tsla.id 20.0 usd.id 10_000.0
  (_, fwd)    <- originate "FWD-TSLA-USD-********" "Forward Example" CFI.forward $ futureDvp tsla.id 20.0 usd.id 10_000.0 (date 2022 Oct 22)
  (_, bnd)    <- originate "BOND-USD-4.875" "Bond Example" CFI.bond $ bond usd.id 1000000.0 0.04875 $ unrollDates 2021 2023 [Jan,Jul] 15
  (_, opt)    <- originate "OPT-TSLA-USD-********" "Call Option Example" CFI.europeanCall $ callOptionPhysical tsla.id 20.0 usd.id 10_000.0 (date 2022 Oct 22)
  (_, bopt)   <- originate "BIN-TSLA-USD-********" "Binary Option Example" CFI.europeanCall $ binaryCallOption tsla.id usd.id 700.0 (date 2022 Oct 22)
  (_, wrnt)   <- originate "WRNT-KO-PUT-DAX" "Open-end Knock-out Put Warrant Example" CFI.putWarrant $ koPutWarrant 10110.0 10110.0 dax.assetId eur.id
  (_, swpt)   <- originate "SWPT-BERMUDAN-LIBOR" "Bermudan Swaption Example" CFI.other $ bermudanSwaption libor.id 0.015 usd.id 500_000.0 (unrollDates 2021 2022 [Jan, Jul] 15) (date 2021 <$> [Feb,Mar,Apr] <*> [1])
  (_, conv)   <- originate "CONV-TSLA-********" "Convertible Note Example" CFI.convertibleShare $ convertibleNote nflx.id usd.id 100_000.0 0.20 (date 2021 Jun 06) 0.04 550.0

  pure ()
