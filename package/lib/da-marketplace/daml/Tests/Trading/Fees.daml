module Tests.Trading.Fees where

import Common
import Tests.Utils
import Daml.Script
import DA.Set qualified as Set
import DA.Finance.Types
import Marketplace.Custody.Role qualified as Custodian
import Marketplace.Trading.Model qualified as Trading
import Marketplace.Custody.Service qualified as Custody
import qualified Marketplace.Settlement.Model as Settlement
import qualified Marketplace.Trading.Matching.Service as Matching
import qualified Marketplace.Trading.Model as Order
import qualified Marketplace.Trading.Service as TradingService
import qualified Marketplace.Listing.Service as Listing
import qualified Marketplace.Listing.Model as Listing

import DA.List

setup : Script ()
setup = do
  parties@Parties{..} <- allocateParties
  providers@Providers{public; operator; bank; exchange; matchingServiceCid, custodianRoleCid} <- onboardProviders parties

  alice <- onboardCustomer providers "Alice" alice
  bob <- onboardCustomer providers "Bob" bob
  issuer <- onboardCustomer providers "Issuer" issuer

  Assets{usd; tsla} <- onboardAssets providers issuer

  -- set up exchange fee schedule
  time <- getTime
  let feeAccountId = Id with signatories = Set.fromList [ bank, exchange]; label = "FeeAccount-" <> show exchange; version = 0
      feeAccount   = Account with provider = bank; owner = exchange; id = feeAccountId
      currency     = usd.id

  custodyServiceOfferCid <- submit bank do exerciseCmd custodianRoleCid Custodian.OfferCustodyService with customer = exchange, ..
  custodyServiceCid <- submit exchange do exerciseCmd custodyServiceOfferCid Custody.Accept

  exchange `submit` createCmd Trading.FeeSchedule with currentFee = Trading.Fee 0.0 currency time; provider = exchange; pastFees = []; observers = Set.fromList [public]; ..
  exchange `submit` exerciseByKeyCmd @Trading.FeeSchedule (operator,exchange) Trading.UpdateFeeSchedule with amount = 2.0; currency = usd.id

  -- Assets
  let
    shareAsset = tsla with quantity = 200.0
    cashAsset = usd with quantity = 20000.0
    priceAsset = usd with quantity = 100.0
    feeAsset = usd with quantity = 2.0
    observers = Set.fromList [ ]

  aliceDepositCid <- depositAsset providers alice cashAsset alice.mainAccount.id observers
  aliceFeeDepositCid <- depositAsset providers alice feeAsset alice.mainAccount.id observers
  bobDepositCid <- depositAsset providers bob shareAsset bob.mainAccount.id observers
  bobFeeDepositCid <- depositAsset providers bob feeAsset bob.mainAccount.id observers

  -- List a Security to trade
  (listingServiceCid, _) <- head <$> query @Listing.Service alice.customer
  let
    symbol = "TSLAUSD"
    listingType = Listing.CollateralizedRequest
    calendarId = "1"
    description = "Tesla Inc."
    tradedAssetId = shareAsset.id
    quotedAssetId = cashAsset.id
    tradedAssetPrecision = 2
    quotedAssetPrecision = 2
    minimumTradableQuantity = 1.0
    maximumTradableQuantity = 1000000.0
    providerId = "12345"
    observers = []
    processed = False
  createListingRequestCid <- alice.customer `submit` do exerciseCmd listingServiceCid Listing.RequestCreateListing with ..
  listingCid <- exchange `submit` do exerciseCmd listingServiceCid Listing.CreateListing with ..
  (Some Listing.Listing{listingId}) <- queryContractId @Listing.Listing exchange listingCid

  -- > Alice places a bid for shares
  submit alice.customer do
    exerciseByKeyCmd @TradingService.T (operator, exchange, alice.customer) TradingService.RequestCreateOrder
      with
        details = Order.Details with
          id = "123"
          listingId
          asset = shareAsset
          optExchangeFee = Some aliceFeeDepositCid
          orderType = Order.Limit with
            price = 100.0
          side = Order.Buy
          timeInForce = Order.GTC
          marketType = Order.Collateralized aliceDepositCid alice.mainAccount

  -- > Bob places a new offer for shares
  submit bob.customer do
    exerciseByKeyCmd @TradingService.T (operator, exchange, bob.customer) TradingService.RequestCreateOrder
      with
        details = Order.Details with
          id = "456"
          listingId
          asset = shareAsset
          optExchangeFee = Some bobFeeDepositCid
          orderType = Order.Limit with
            price = 100.0
          side = Order.Sell
          timeInForce = Order.GTC
          marketType = Order.Collateralized bobDepositCid bob.mainAccount

  -- Exchange matches the two orders
  Matching.Collateralized settlementInstructionCid <- submit exchange do
    exerciseCmd matchingServiceCid Matching.MatchOrders
      with
        execution = Order.Execution with
          matchId = "789"
          makerOrderId = "123"
          takerOrderId = "456"
          quantity = 200.0
          price = 100.0
          timestamp = ""

  -- Exchange settles the instructed trade
  submit exchange do exerciseCmd settlementInstructionCid Settlement.Settle

  assert =<< depositsQuantityEquals exchange feeAccount 4.0

  pure ()
