module Tests.Trading.Cleared where

import Daml.Script
import Common
import DA.Assert ((===))
import DA.List (head)
import Marketplace.Trading.Service qualified as Trading
import Marketplace.Trading.Model qualified as Order
import Marketplace.Clearing.Role qualified as Clearing
import Marketplace.Clearing.Model qualified as Clearing
import Marketplace.Clearing.Service qualified as Clearing
import Marketplace.Clearing.Market.Service qualified as ClearedMarket
import Marketplace.Trading.Matching.Service qualified as Matching
import Marketplace.Listing.Service qualified as Listing
import Marketplace.Listing.Model qualified as Listing

checkOrderStatusMatches : ContractId Order.T -> Party -> Order.Status -> Script ()
checkOrderStatusMatches orderCid party status = do
  Some order <- queryContractId @Order.T party orderCid
  order.status === status
  pure ()


clearedTradingTest : Script ()
clearedTradingTest = do
  parties@Parties{..} <- allocateParties

  providers@Providers{operator; exchange; matchingServiceCid; clearinghouse, clearingRoleCid, bank, public} <- onboardProviders parties

  alice <- onboardCustomer providers "Alice" alice
  bob <- onboardCustomer providers "Bob" bob
  charlie <- onboardCustomer providers "Charlie" charlie
  issuer <- onboardCustomer providers "Issuer" issuer

  Assets{usd; tsla} <- onboardAssets providers issuer

  let
    shareAsset = tsla with quantity = 200.0
    cashAsset = usd with quantity = 20000.0

  let
    symbol = "TSLAUSD"
    listingType = Listing.ClearedRequest clearinghouse
    calendarId = "**********"
    description = "Tesla Inc."
    tradedAssetId = tsla.id
    quotedAssetId = usd.id
    tradedAssetPrecision = 2
    quotedAssetPrecision = 2
    minimumTradableQuantity = 1.0
    maximumTradableQuantity = 1000000.0
    providerId = "123"
    observers = [public]
    processed = False

  -- List a Security to trade
  (listingServiceCid, _) <- head <$> query @Listing.Service alice.customer

  -- !! Alice cannot create listing without being a customer of the clearinghouse
  alice.customer `submitMustFail` do exerciseCmd listingServiceCid Listing.RequestCreateListing with ..

  -- Setup clearing market service
  marketServiceOfferCid <- submit clearinghouse do exerciseCmd clearingRoleCid Clearing.OfferMarketService with customer = alice.customer, ..
  marketServiceCid <- submit alice.customer do exerciseCmd marketServiceOfferCid ClearedMarket.Accept

  createListingRequestCid <- alice.customer `submit` do exerciseCmd listingServiceCid Listing.RequestCreateListing with ..
  listingCid <- exchange `submit` do exerciseCmd listingServiceCid Listing.CreateListing with ..
  (Some Listing.Listing{listingId}) <- queryContractId @Listing.Listing exchange listingCid

  -- Create orders
  let aliceOrderId = "1"
  aliceOrderCid <- alice.customer `submit` do
    exerciseCmd alice.tradingServiceCid Trading.RequestCreateOrder with
        details = Order.Details with
            id = aliceOrderId
            optExchangeFee = None
            listingId
            asset = tsla with quantity = 200.0
            side = Order.Sell
            timeInForce = Order.GTC
            orderType = Order.Limit with price = 100.0
            marketType = Order.Cleared clearinghouse
  checkOrderStatusMatches aliceOrderCid alice.customer Order.New

  let bobOrderId = "2"
  bobOrderCid <- bob.customer `submit` do
    exerciseCmd bob.tradingServiceCid Trading.RequestCreateOrder with
        details = Order.Details with
            id = bobOrderId
            listingId
            asset = shareAsset
            optExchangeFee = None
            side = Order.Buy
            timeInForce = Order.GTC
            orderType = Order.Limit with price = 100.0
            marketType = Order.Cleared clearinghouse
  checkOrderStatusMatches bobOrderCid bob.customer Order.New

  -- Acknowledge Orders
  [(aliceCreateOrderRequestCid, _)] <- query @Trading.CreateOrderRequest alice.customer
  aliceOrderCid <- exchange `submit` do
    exerciseCmd alice.tradingServiceCid Trading.AcknowledgeOrderRequest with
        createOrderRequestCid = aliceCreateOrderRequestCid
        providerOrderId = "alice's exchange order id"
  checkOrderStatusMatches aliceOrderCid alice.customer Order.PendingExecution

  [(bobCreateOrderRequestCid, _)] <- query @Trading.CreateOrderRequest bob.customer
  bobOrderCid <- exchange `submit` do
    exerciseCmd bob.tradingServiceCid Trading.AcknowledgeOrderRequest with
        createOrderRequestCid = bobCreateOrderRequestCid
        providerOrderId = "bob's exchange order id"
  checkOrderStatusMatches bobOrderCid bob.customer Order.PendingExecution

  -- Confirm order matches
  let execution = Order.Execution with
        matchId = "1"
        makerOrderId = aliceOrderId
        takerOrderId = bobOrderId
        quantity = 200.0
        price = 100.0
        timestamp = "123456"

  Matching.Cleared clearedTradeCid <- exchange `submit` exerciseCmd matchingServiceCid Matching.MatchOrders with execution

  aliceOrderCid <- fst . head <$> query @Order.T alice.customer
  checkOrderStatusMatches aliceOrderCid alice.customer Order.FullyExecuted
  bobOrderCid <- fst . head <$> query @Order.T bob.customer
  checkOrderStatusMatches bobOrderCid bob.customer Order.FullyExecuted

  (aliceClearedTradeSideCid, bobClearedTradeSideCid) <- clearinghouse `submit` exerciseCmd clearedTradeCid Clearing.ClearedTrade_Novate

  -- Failed trades

  -- Have Bob fail a margin call
  calculationCid <- submit clearinghouse $
    exerciseCmd bob.clearingServiceCid Clearing.CreateMarginCalculation with
      currency = usd.id; targetAmount = 50_000.0; calculationId = "34556"

  submit clearinghouse $
    exerciseByKeyCmd @Clearing.Service (operator,clearinghouse,bob.customer) Clearing.PerformMarginFill
      with depositCids = []; marginDepositCids = []; ..

  -- !! Bob is in bad standing, so the new order should fail
  let bobOrderId2 = "3"
  bobOrderCid <- bob.customer `submit` do
    exerciseCmd bob.tradingServiceCid Trading.RequestCreateOrder with
        details = Order.Details with
            id = bobOrderId2
            listingId
            asset = shareAsset
            optExchangeFee = None
            side = Order.Buy
            timeInForce = Order.GTC
            orderType = Order.Limit with price = 100.0
            marketType = Order.Cleared clearinghouse
  Some (Order.Order{status = Order.Rejected _}) <- queryContractId bob.customer bobOrderCid

  -- Terminate Charlie's relationship with the clearing house for further testing
  submit charlie.customer $ exerciseCmd charlie.clearingServiceCid Clearing.Terminate with ctrl = charlie.customer

  -- !! Charlie is not a member of the clearinghouse, so Charlie should not be able to place an order
  let charlieOrderId = "4"
  charlieOrderCid <- charlie.customer `submit` do
    exerciseCmd charlie.tradingServiceCid Trading.RequestCreateOrder with
        details = Order.Details with
            id = charlieOrderId
            listingId
            asset = shareAsset
            optExchangeFee = None
            side = Order.Buy
            timeInForce = Order.GTC
            orderType = Order.Limit with price = 100.0
            marketType = Order.Cleared clearinghouse
  Some (Order.Order{status = Order.Rejected _}) <- queryContractId charlie.customer charlieOrderCid

  return ()
