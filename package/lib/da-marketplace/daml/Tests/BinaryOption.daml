module Tests.BinaryOption where
-- ^ This module tests the full lifecycle of a binary option

import Common (allocateParties, Parties(..), onboardProviders, onboardCustomer, onboardAssets, Assets(..))
import ContingentClaims.Claim (serialize)
import DA.Date (date, Month(Mar, Oct))
import DA.Finance.Types (Id(..))
import DA.Finance.Asset (AssetDeposit, AssetDeposit_Transfer(..))
import Daml.Script
import DA.Set qualified as Set
import DA.Map qualified as Map
import Marketplace.Issuance.Service (RequestOrigination(..), Originate(..), RequestCreateIssuance(..), CreateIssuance(..))
import Marketplace.Custody.Service (RequestLifecycle(..), Lifecycle(..), RequestDeposit(..), Deposit(..))
import Marketplace.Issuance.CFI qualified as CFI
import Marketplace.Settlement.Model (Settle(..))
import Tests.Utils (binaryCallOption)
import DA.Time (time)
import DA.Assert
import DA.Foldable


expiry = date 2022 Oct 22
notExpiry = date 2020 Mar 18
strike : Decimal = 700.0
aboveStrike : Decimal = 920.0
belowStrike : Decimal = 630.0
optionAssetLabel = "TSLA Oct22 700.0"


endToEnd = script do
  requestExercise 920.0 $ date 2020 Mar 18


testNotExercisedWhenFixingDateIsNotExpiry = script do
  (alice, bob, usdLabel) <- requestExercise aboveStrike notExpiry

  [(_, bobsOption)] <- queryFilter @AssetDeposit bob (\c -> c.asset.id.label == optionAssetLabel)
  bobsOption.asset.id.version === 0
  bobsMoneyAssets <- getMoneyAssetsFor bob usdLabel
  bobsMoneyAssets === []
  [(_, alicesMoney)] <- getMoneyAssetsFor alice usdLabel
  alicesMoney.asset.quantity === 3000.0


testNotExercisedWhenPriceIsBelowStrikeAtExpiry = script do
  (alice, bob, usdLabel) <- requestExercise belowStrike expiry

  [(_, bobsOption)] <- queryFilter @AssetDeposit bob (\c -> c.asset.id.label == optionAssetLabel)
  bobsOption.asset.id.version === 1
  bobsMoneyAssets <- getMoneyAssetsFor bob usdLabel
  bobsMoneyAssets === []
  [(_, alicesMoney)] <- getMoneyAssetsFor alice usdLabel
  alicesMoney.asset.quantity === 3000.0

testExercisedOnExpiry = script do
  (alice, bob, usdLabel) <- requestExercise aboveStrike expiry

  [(_, bobsOption)] <- queryFilter @AssetDeposit bob (\c -> c.asset.id.label == optionAssetLabel)
  bobsOption.asset.id.version === 1
  [(_, bobsMoney)] <- getMoneyAssetsFor bob usdLabel
  bobsMoney.asset.quantity === 1000.0
  [(_, alicesMoney)] <- getMoneyAssetsFor alice usdLabel
  alicesMoney.asset.quantity === 2000.0


getMoneyAssetsFor party assetLabel = do
  queryFilter @AssetDeposit party (\c -> c.asset.id.label == assetLabel)


requestExercise: Decimal -> Date -> Script (Party, Party, Text)
requestExercise underlyingPrice observationDate = script do
  parties@Parties{..} <- allocateParties
  providers <- onboardProviders parties

  let underlyingId = Id with
                       signatories = Set.singleton providers.bank
                       label = "TSLA"
                       version = 0

  alice <- onboardCustomer providers "Alice" alice
  bob <- onboardCustomer providers "Bob" bob
  issuer <- onboardCustomer providers "Issuer" issuer

  Assets{usd; tsla} <- onboardAssets providers issuer

  -- Alice originates the option
  let boClaims = binaryCallOption underlyingId usd.id strike expiry
  origReqCid <- submit alice.customer $ exerciseCmd alice.issuanceServiceCid RequestOrigination with
      assetLabel = optionAssetLabel
      description = "Tesla Binary Option"
      cfi = CFI.other
      claims = serialize boClaims
      observers = []
  (optionDescCid, optionDesc) <- submit providers.bank $ exerciseCmd alice.issuanceServiceCid $ Originate origReqCid

  -- Alice issues the option
  createReqCid <- submit alice.customer $ exerciseCmd alice.issuanceServiceCid
    RequestCreateIssuance with
      issuanceId = "Myissuance"
      account = alice.mainAccount
      assetId = optionDesc.assetId
      quantity = 1000.0
  (_issuanceCid, optionDepositCid) <- submit providers.bank $ exerciseCmd alice.issuanceServiceCid $ CreateIssuance createReqCid

  -- Alice gifts the option to Bob
  optionDepositCid <- submitMulti [ alice.customer, bob.customer ] [] $ exerciseCmd optionDepositCid AssetDeposit_Transfer with receiverAccount = bob.mainAccount

  -- The option expires at maturity
  setTime $ time observationDate 16 30 00

  -- The issuer, Alice, must deposit the payout in the safekeeping account
  corpActReqCid <- submit alice.customer $ exerciseCmd alice.custodyServiceCid $ RequestDeposit usd with quantity = 3000.0
  corpActDepositCid <- submit providers.bank $ exerciseCmd alice.custodyServiceCid Deposit with
    depositRequestCid = corpActReqCid

  -- Bob exercises the Option
  lifecycleReqCid <- submit bob.customer $ exerciseCmd bob.custodyServiceCid RequestLifecycle with
    assetDepositCid = optionDepositCid
    choice = optionDesc.claims.claim.lhs -- TODO: How do we get this in practice? From a UI?
  (_, settlementInstructionCids) <-
    submit providers.bank $ exerciseCmd alice.custodyServiceCid $ Lifecycle with
      safekeepingDepositCid = corpActDepositCid -- TODO: how do we get this in practice?
      lifecycleRequestCid = lifecycleReqCid
      fixings = Map.fromList [ ("TSLA", Map.insert observationDate underlyingPrice Map.empty) ]
      uniquePayoutId = "ABC-111"

  forA_ settlementInstructionCids $
    \settlementInstructionCid ->
      submitMulti [alice.customer, bob.customer, providers.bank] [] $
        exerciseCmd settlementInstructionCid Settle

  return (alice.customer, bob.customer, usd.id.label)
