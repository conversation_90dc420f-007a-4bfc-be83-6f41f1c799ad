module Tests.Clearing where

import Daml.Script
import Marketplace.Clearing.Model
import Marketplace.Clearing.Service (DepositWithRemaining(..),CalculationResult(..))
import Marketplace.Clearing.Service qualified as Clearing

import Common
import Tests.Utils
import DA.Action (void)
import DA.Set qualified as Set

testClearingTransfers : Script ()
testClearingTransfers = do
  time <- getTime
  parties@Parties{..} <- allocateParties
  providers@Providers{clearinghouse} <- onboardProviders parties

  alice  <- onboardCustomer providers "Alice" alice
  bob    <- onboardCustomer providers "Bob" bob
  issuer <- onboardCustomer providers "Issuer" issuer

  Assets{usd; tsla} <- onboardAssets providers issuer
  -- Assets
  let
    cashAsset = usd with quantity = 20000.0
    observers = Set.fromList [ clearinghouse ]

  aliceDepositCid <- depositAsset providers alice cashAsset alice.mainAccount.id observers
  bobDepositCid   <- depositAsset providers bob cashAsset bob.mainAccount.id observers

  marginDepositWithRemaining <- submit clearinghouse $
    exerciseCmd alice.clearingServiceCid Clearing.TransferToMargin
      with depositCids = [aliceDepositCid]; amount = 5000.0

  clearingDepositCid <- submit clearinghouse $
    exerciseCmd alice.clearingServiceCid Clearing.TransferFromMargin
      with amount = 1000.0, marginDepositCids = [marginDepositWithRemaining.deposit]
  assert =<< depositsQuantityEqualsByFilter alice.customer (\deposit -> deposit.account == alice.mainAccount && deposit.lockers == Set.fromList [ clearinghouse ]) 4000.0

  (DepositWithRemaining providerDepositCid _) <- submit clearinghouse $
    exerciseCmd bob.clearingServiceCid Clearing.TransferToProvider
      with amount = 2000.0; depositCids = [bobDepositCid]

  submit clearinghouse $ exerciseCmd alice.clearingServiceCid Clearing.TransferFromProvider
      with amount = 1000.0; depositCids = [providerDepositCid]
  assert =<< depositsQuantityEqualsByFilter alice.customer (\deposit -> deposit.account == alice.mainAccount && Set.null deposit.lockers) 17_000.0

  return ()

testMarginCalls : Script ()
testMarginCalls = do
  time <- getTime
  parties@Parties{..} <- allocateParties
  providers@Providers{clearinghouse; operator} <- onboardProviders parties

  alice  <- onboardCustomer providers "Alice" alice
  bob    <- onboardCustomer providers "Bob" bob
  issuer <- onboardCustomer providers "Issuer" issuer

  Assets{usd; tsla} <- onboardAssets providers issuer

  -- Assets
  let cashAsset = usd with quantity = 20000.0
      observers = Set.fromList [ clearinghouse ]

  aliceDepositCid <- depositAsset providers alice cashAsset alice.mainAccount.id observers
  calculationCid <- submit clearinghouse $
    exerciseCmd alice.clearingServiceCid Clearing.CreateMarginCalculation with
      currency = usd.id; targetAmount = 4000.0; calculationId = "123456"; ..

  CalculationSuccess _ (Some marginWithRemaining) <- submit clearinghouse $
    exerciseCmd alice.clearingServiceCid Clearing.PerformMarginFill
      with depositCids = [aliceDepositCid]; marginDepositCids = []; ..
  assert =<< depositsQuantityEqualsByFilter alice.customer (\deposit -> deposit.account == alice.mainAccount && deposit.lockers == Set.fromList [ clearinghouse ]) 4000.0

  calculationCid <- submit clearinghouse $
    exerciseCmd alice.clearingServiceCid Clearing.CreateMarginCalculation with
      currency = usd.id; targetAmount = 1000.0; calculationId = "456789"

  CalculationSuccess _ (Some clearingWithRemaining) <- submit clearinghouse $
    exerciseByKeyCmd @Clearing.Service (operator, clearinghouse, alice.customer) Clearing.PerformMarginFill
      with depositCids = marginWithRemaining.remaining; marginDepositCids = [marginWithRemaining.deposit]; ..
  assert =<< depositsQuantityEqualsByFilter alice.customer (\deposit -> deposit.account == alice.mainAccount && deposit.lockers == Set.fromList [ clearinghouse ]) 1000.0

  -- Fail margin calculation
  calculationCid <- submit clearinghouse $
    exerciseCmd alice.clearingServiceCid Clearing.CreateMarginCalculation with
      currency = usd.id; targetAmount = 50_000.0; calculationId = "34556"

  void $ submit clearinghouse $
    exerciseByKeyCmd @Clearing.Service (operator, clearinghouse, alice.customer) Clearing.PerformMarginFill
      with depositCids = [clearingWithRemaining.deposit]; marginDepositCids = clearingWithRemaining.remaining; ..
  assert =<< depositsQuantityEqualsByFilter alice.customer (\deposit -> deposit.account == alice.mainAccount && deposit.lockers == Set.fromList [ clearinghouse ]) 1000.0

  (Some (_,aliceStanding)) <- queryContractKey @MemberStanding clearinghouse (clearinghouse, alice.customer)
  assert $ not aliceStanding.marginSatisfied

  return ()

testMarkToMarket : Script ()
testMarkToMarket = do
  time <- getTime
  parties@Parties{..} <- allocateParties
  providers@Providers{clearinghouse} <- onboardProviders parties

  alice  <- onboardCustomer providers "Alice" alice
  bob    <- onboardCustomer providers "Bob" bob
  issuer <- onboardCustomer providers "Issuer" issuer

  Assets{usd; tsla} <- onboardAssets providers issuer

  -- Assets
  let cashAsset = usd with quantity = 20000.0
      observers = Set.fromList [ clearinghouse ]

  aliceDepositCid <- depositAsset providers alice cashAsset alice.mainAccount.id observers
  bobDepositCid   <- depositAsset providers bob cashAsset bob.mainAccount.id observers

  calculationCid <- submit clearinghouse $
    exerciseCmd alice.clearingServiceCid Clearing.CreateMarkToMarket with
      currency = usd.id; mtmAmount = -5000.0; calculationId = "34567"

  CalculationSuccess _ (Some depositWithRemaining) <- submit clearinghouse $
    exerciseCmd alice.clearingServiceCid Clearing.PerformMarkToMarket
      with customerDepositCids = [aliceDepositCid]; providerDepositCids = []; ..

  calculationCid2 <- submit clearinghouse $
    exerciseCmd bob.clearingServiceCid Clearing.CreateMarkToMarket with
      currency = usd.id; mtmAmount = 5000.0; calculationId = "34567"

  _ <- submit clearinghouse $
    exerciseCmd bob.clearingServiceCid Clearing.PerformMarkToMarket
      with customerDepositCids = []; providerDepositCids = [depositWithRemaining.deposit]; calculationCid = calculationCid2

  assert =<< depositsQuantityEquals alice.customer alice.mainAccount 15_000.0
  assert =<< depositsQuantityEquals bob.customer bob.mainAccount 25_000.0

  return ()

