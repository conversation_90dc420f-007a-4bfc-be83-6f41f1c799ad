module Tests.Utils where

import Daml.Script

import ContingentClaims.Claim
import ContingentClaims.Observable qualified as O
import ContingentClaims.Observation qualified as O
import ContingentClaims.Observable (at)
import ContingentClaims.Financial (fixed, floating, swap, european, bermudan, forward)
import DA.Finance.Asset
import DA.Finance.Types (Account, Id(..))

type C = Claim O.Observation Date Decimal Id

payment : Id -> Decimal -> C
payment ccy qty =
  Scale (O.pure qty) (One ccy)

delivery : Id -> Decimal -> C
delivery asset qty =
  Give $ Scale (O.pure qty) (One asset)

futurePayment : Id -> Decimal -> Date -> C
futurePayment ccy qty d =
  When (at d) $ payment ccy qty

futureDelivery : Id -> Decimal -> Date -> C
futureDelivery asset qty d =
  When (at d) $ delivery asset qty

dvp : Id -> Decimal -> Id -> Decimal -> C
dvp asset assetQty ccy ccyQty =
  And (delivery asset assetQty) (payment ccy ccyQty)

futureDvp : Id -> Decimal -> Id -> Decimal -> Date -> C
futureDvp asset assetQty ccy ccyQty d =
  When (at d) $ dvp asset assetQty ccy ccyQty

bond : Id -> Decimal -> Decimal -> [Date] -> C
bond ccy notional coupon dates =
  fixed notional (notional * coupon) ccy dates

future : Id -> Decimal -> Date -> C
future underlying mult expiry = forward expiry (O.pure mult) (One underlying)

callOptionPhysical : Id -> Decimal -> Id -> Decimal -> Date -> C
callOptionPhysical asset assetQty ccy ccyQty d =
  european d $ dvp asset assetQty ccy ccyQty

binaryCallOption : Id -> Id -> Decimal -> Date -> C
binaryCallOption underlying ccy strike expiry =
  When (at expiry) $ Or (Cond (O.pure strike O.<= O.observe underlying) (One ccy) Zero) Zero

koPutWarrant : Decimal -> Decimal -> Id -> Id -> C
koPutWarrant barrier strike underlying ccy  =
  Cond (O.observe underlying O.<= O.pure barrier) (Or (Scale (O.pure strike + (O.observe underlying)) (One ccy)) (Zero)) Zero

bermudanSwaption : Id -> Decimal -> Id -> Decimal -> [Date] -> [Date] -> C
bermudanSwaption floatingRateId fixedRate fixedCcyId principal fixingDates exerciseDates =
  let
    floatObs = O.observe floatingRateId
    floatingLeg = floating (O.pure 0.0) floatObs floatingRateId
    fixedLeg = fixed principal fixedRate fixedCcyId
  in bermudan (swap floatingLeg fixedLeg fixingDates) exerciseDates

-- Modeled after https://ledgy.com/blog/examples-of-convertible-notes/
convertibleNote : Id -> Id -> Decimal -> Decimal -> Date -> Decimal -> Decimal -> C
convertibleNote underlying ccy principal discount maturity interest cap =
  let
    conversionCondition = O.observe underlying O.<= O.pure cap
    conversionPayout = Scale (O.pure (principal * (1.0 + interest)) / (O.observe underlying * O.pure (1.0 - discount))) (One underlying)
    principalPayout = Scale (O.pure (principal * (1.0 + interest))) (One ccy)
  in When (at maturity) $ Cond conversionCondition conversionPayout principalPayout

-- |Check if the given account has deposits equaling the given amount
depositsQuantityEquals : Party -> Account -> Decimal -> Script Bool
depositsQuantityEquals party account amount = do
  accountQuantity <- getDepositQuantities party =<< getDepositsForAccount party account
  return $ amount == accountQuantity

getDepositsForAccount : Party -> Account -> Script [ContractId AssetDeposit]
getDepositsForAccount party account = map fst . filter (\(_,d) -> d.account == account) <$> query @AssetDeposit party

depositsQuantityEqualsByFilter : Party -> (AssetDeposit -> Bool) -> Decimal -> Script Bool
depositsQuantityEqualsByFilter party depositFilter amount = do
  accountQuantity <- getDepositQuantities party =<< getDeposits party depositFilter
  return $ amount == accountQuantity

getDeposits : Party -> (AssetDeposit -> Bool) -> Script [ContractId AssetDeposit]
getDeposits party depositFilter = map fst . filter (depositFilter . snd) <$> query @AssetDeposit party

getDepositQuantities : Party -> [ContractId AssetDeposit] -> Script Decimal
getDepositQuantities party depositCids = do
  foldl (+) 0.0 <$> forA depositCids (\dcid -> do
      Some d <- queryContractId party dcid
      return d.asset.quantity)

