module Tests.Bond where
-- ^ This module tests the full lifecycle of a bond

import Common (allocateParties, Parties(..), Customer, Providers, onboardProviders, onboardCustomer, Assets(..), onboardAssets)
import ContingentClaims.Claim (serialize)
import DA.Date (date, Month(Jan, Oct))
import DA.Finance.Asset (AssetDeposit, AssetDeposit_Transfer(..))
import Daml.Script
import DA.Map qualified as Map
import Marketplace.Issuance.Service (RequestOrigination(..), Originate(..), RequestCreateIssuance(..), CreateIssuance(..))
import Marketplace.Custody.Service (Service, RequestLifecycle(..), Lifecycle(..), RequestDeposit(..), Deposit(..))
import Marketplace.Issuance.CFI qualified as CFI
import Marketplace.Issuance.AssetDescription (AssetDescription)
import Marketplace.Settlement.Model (Settle(..))
import Tests.Utils (bond)
import DA.Time (time)
import DA.Assert

data RequestExerciseBondResult = RequestExerciseBondResult
  with
    alice             : Party
    providers         : Providers
    bob               : Customer
    bondCid           : ContractId AssetDeposit
    bobsDesc          : AssetDescription
    custodyServiceCid : ContractId Service
    corpActDepositCid : ContractId AssetDeposit
    usdAssetLabel     : Text


testBond = script do
  let date1 = date 2001 Jan 1
      date2 = date 2002 Oct 31
      notional = 100.0
      coupon = 0.1
      couponPayout = notional * coupon
      usdAmount = 3000.0

  requestExerciseBondResult <-
    requestExerciseBond usdAmount notional coupon [date1, date2]

  -- First lifecycle
  setTime $ time date1 16 30 00
  lifecycleReqCid <- submit requestExerciseBondResult.bob.customer $ exerciseCmd requestExerciseBondResult.bob.custodyServiceCid RequestLifecycle with
    assetDepositCid = requestExerciseBondResult.bondCid
    choice = requestExerciseBondResult.bobsDesc.claims
  (bondCid, [settlementInstructionCid]) <- submit requestExerciseBondResult.providers.bank $ exerciseCmd requestExerciseBondResult.custodyServiceCid $ Lifecycle with
    safekeepingDepositCid = requestExerciseBondResult.corpActDepositCid
    lifecycleRequestCid = lifecycleReqCid
    fixings = Map.fromList []
    uniquePayoutId = show date1

  pure () -- Below fails due to call to SettlementInstruction_Process in the SDk as the role contracts dont exist

  submitMulti [requestExerciseBondResult.alice, requestExerciseBondResult.bob.customer, requestExerciseBondResult.providers.bank] [] $
    exerciseCmd settlementInstructionCid Settle

  [(_, bobsOption)] <- queryFilter @AssetDeposit requestExerciseBondResult.bob.customer (\c -> c.asset.id.label == "BondLabel")
  bobsOption.asset.id.version === 1
  [(_, bobsMoney)] <- getMoneyAssetsFor requestExerciseBondResult.bob.customer requestExerciseBondResult.usdAssetLabel
  bobsMoney.asset.quantity === couponPayout
  [(corpActDepositCid, alicesMoney)] <- getMoneyAssetsFor requestExerciseBondResult.alice requestExerciseBondResult.usdAssetLabel
  alicesMoney.asset.quantity === usdAmount - couponPayout

  -- Second lifecycle
  setTime $ time date2 16 30 00
  lifecycleReqCid <- submit requestExerciseBondResult.bob.customer $ exerciseCmd requestExerciseBondResult.bob.custodyServiceCid RequestLifecycle with
    assetDepositCid = bondCid
    choice = requestExerciseBondResult.bobsDesc.claims
  (_, [settlementInstructionCid1, settlementInstructionCid2]) <-
    submit requestExerciseBondResult.providers.bank $ exerciseCmd requestExerciseBondResult.custodyServiceCid $ Lifecycle with
      safekeepingDepositCid = corpActDepositCid
      lifecycleRequestCid = lifecycleReqCid
      fixings = Map.fromList []
      uniquePayoutId = show date2

  submitMulti [requestExerciseBondResult.alice, requestExerciseBondResult.bob.customer, requestExerciseBondResult.providers.bank] [] $
    exerciseCmd settlementInstructionCid1 Settle
  submitMulti [requestExerciseBondResult.alice, requestExerciseBondResult.bob.customer, requestExerciseBondResult.providers.bank] [] $
    exerciseCmd settlementInstructionCid2 Settle

  [(_, bobsOption)] <- queryFilter @AssetDeposit requestExerciseBondResult.bob.customer (\c -> c.asset.id.label == "BondLabel")
  bobsOption.asset.id.version === 2
  bobsMoneyWithCids <- getMoneyAssetsFor requestExerciseBondResult.bob.customer requestExerciseBondResult.usdAssetLabel
  sum (map (\(_, money) -> money.asset.quantity) bobsMoneyWithCids)
    === couponPayout + notional + couponPayout
  [(_, alicesMoney)] <- getMoneyAssetsFor requestExerciseBondResult.alice requestExerciseBondResult.usdAssetLabel
  alicesMoney.asset.quantity === usdAmount - couponPayout - notional - couponPayout

getMoneyAssetsFor : Party -> Text -> Script [(ContractId AssetDeposit, AssetDeposit)]
getMoneyAssetsFor party usdAssetLabel =
  queryFilter @AssetDeposit party (\c -> c.asset.id.label == usdAssetLabel)

requestExerciseBond: Decimal -> Decimal -> Decimal -> [Date] -> Script (RequestExerciseBondResult)
requestExerciseBond usdAmount notional coupon dates = script do
  parties@Parties{..} <- allocateParties
  providers <- onboardProviders parties

  alice <- onboardCustomer providers "Alice" alice
  bob <- onboardCustomer providers "Bob" bob
  issuer <- onboardCustomer providers "Issuer" issuer

  Assets{usd; tsla} <- onboardAssets providers issuer

  -- Alice originates the bond
  let boClaims = bond usd.id notional coupon dates
  origReqCid <- submit alice.customer $ exerciseCmd alice.issuanceServiceCid RequestOrigination with
      assetLabel = "BondLabel"
      description = "TestBond"
      claims = serialize boClaims
      cfi = CFI.bond
      observers = []
  (optionDescCid, optionDesc) <- submit providers.bank $ exerciseCmd alice.issuanceServiceCid $ Originate origReqCid

  -- Alice issues the bond
  createReqCid <- submit alice.customer $ exerciseCmd alice.issuanceServiceCid
    RequestCreateIssuance with
      issuanceId = "Myissuance"
      account = alice.mainAccount
      assetId = optionDesc.assetId
      quantity = 1.0
  (_, bondCid) <- submit providers.bank $ exerciseCmd alice.issuanceServiceCid $ CreateIssuance createReqCid

  -- Alice gifts the bond to Bob
  bondCid <- submitMulti [ alice.customer, bob.customer ] [] $ exerciseCmd bondCid AssetDeposit_Transfer with receiverAccount = bob.mainAccount

  -- The issuer, Alice, must deposit the payout in the safekeeping account
  corpActReqCid <- submit alice.customer $ exerciseCmd alice.custodyServiceCid RequestDeposit with
    asset = usd with quantity = usdAmount
  corpActDepositCid <- submit providers.bank $ exerciseCmd alice.custodyServiceCid Deposit with
    depositRequestCid = corpActReqCid

  return
    RequestExerciseBondResult with
      alice             = alice.customer
      bobsDesc          = optionDesc
      custodyServiceCid = alice.custodyServiceCid
      usdAssetLabel     = usd.id.label
      ..