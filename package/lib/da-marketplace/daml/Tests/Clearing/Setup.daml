module Tests.Clearing.Setup where

import Daml.Script
import Common
import ContingentClaims.Claim (Claim(Zero))
import DA.Assert ((===))
import DA.Finance.Types
import DA.Date (date, Month(..))
import DA.Set (fromList)
import Marketplace.Trading.Service qualified as Trading
import Marketplace.Trading.Model qualified as Order
import Marketplace.Clearing.Role qualified as Clearing
import Marketplace.Clearing.Model qualified as Clearing
import Marketplace.Clearing.Service qualified as Clearing
import Marketplace.Clearing.Market.Service qualified as ClearedMarket
import Marketplace.Trading.Matching.Service qualified as Matching
import Marketplace.Listing.Service qualified as Listing
import Marketplace.Listing.Model qualified as Listing
import Marketplace.Issuance.AssetDescription as AssetDescription
import Marketplace.Issuance.CFI qualified as CFI
import Tests.Utils (future)

checkOrderStatusMatches : ContractId Order.T -> Party -> Order.Status -> Script ()
checkOrderStatusMatches orderCid party status = do
  Some order <- queryContractId @Order.T party orderCid
  order.status === status
  pure ()

setupClearingCustomer : Providers -> Assets -> Text -> Party -> Script Customer
setupClearingCustomer providers@Providers{clearinghouse} Assets{..} name party = do
  customer <- onboardCustomer providers name party
  let
    cashAsset = usd with quantity = 2000000.0
    observers = fromList [ clearinghouse ]

  depositCid <- depositAsset providers customer cashAsset customer.mainAccount.id observers

  calculationCid <- submit clearinghouse $
    exerciseCmd customer.clearingServiceCid Clearing.CreateMarginCalculation with
      currency = usd.id; targetAmount = 4000.0; calculationId = "123456"

  Clearing.CalculationSuccess _ (Some marginWithRemaining) <- submit clearinghouse $
    exerciseCmd customer.clearingServiceCid Clearing.PerformMarginFill
      with depositCids = [depositCid]; marginDepositCids = []; calculationCid

  return customer

clearedTradingTest : Script ()
clearedTradingTest = allocateParties >>= runClearedTradingTest

runClearedTradingTest : Parties -> Script ()
runClearedTradingTest parties@Parties{..} = do

  providers@Providers{operator; exchange; matchingServiceCid; clearinghouse, clearingRoleCid, bank, public} <- onboardProviders parties

  issuer <- onboardCustomer providers "Issuer" issuer
  assets@Assets{usd} <- onboardAssets providers issuer

  alice   <- setupClearingCustomer providers assets "Alice" alice
  bob     <- setupClearingCustomer providers assets "Bob" bob
  charlie <- setupClearingCustomer providers assets "Charlie" charlie
  dana    <- setupClearingCustomer providers assets "Dana" dana


  -- Setup clearing market service
  marketServiceOfferCid <- submit clearinghouse do exerciseCmd clearingRoleCid Clearing.OfferMarketService with customer = exchange; ..
  marketServiceCid <- submit exchange do exerciseCmd marketServiceOfferCid ClearedMarket.Accept

  (_, AssetDescription{assetId = h2oId}) <- originate providers issuer "H2O" "H2O Spot" CFI.equity Zero [clearinghouse]

  let mkSimpleH2OFuture name description expiry = do
      (descCid, AssetDescription{assetId}) <- originate providers issuer name description CFI.future (future h2oId 1.0 expiry) [clearinghouse]
      return (descCid, assetId)

  (q122Cid,h2oQ122DerId) <- mkSimpleH2OFuture "H2OQ122" "H2O Q1 22" $ date 2022 Jan 1
  (_,h2oQ222DerId)       <- mkSimpleH2OFuture "H2OQ222" "H2O Q2 22" $ date 2022 Apr 1
  (_,h2oQ322DerId)       <- mkSimpleH2OFuture "H2OQ322" "H2O Q3 22" $ date 2022 Jul 1
  (_,h2oQ422DerId)       <- mkSimpleH2OFuture "H2OQ422" "H2O Q4 22" $ date 2022 Oct 1

  (Some expiry)   <- submit issuer.customer $ exerciseCmd q122Cid AssetDescription.Expiry with party = issuer.customer
  (underlying::_) <- submit issuer.customer $ exerciseCmd q122Cid AssetDescription.Underlying with party = issuer.customer
  (mult::_)       <- submit issuer.customer $ exerciseCmd q122Cid AssetDescription.Multipliers with party = issuer.customer

  expiry     === (date 2022 Jan 1)
  underlying === h2oId
  mult       === 1.0

  -- List a Security to trade
  Some (listingServiceCid, _) <- queryContractKey @Listing.Service exchange (operator, exchange, exchange)

  let mkMarket : Id -> Id -> Script Text
      mkMarket quoteId tradedId = do
        let
          symbol = quoteId.label <> tradedId.label
          listingType = Listing.ClearedRequest clearinghouse
          calendarId = "**********"
          description = quoteId.label <> " vs " <> tradedId.label
          tradedAssetId = tradedId
          quotedAssetId = quoteId
          tradedAssetPrecision = 8
          quotedAssetPrecision = 8
          minimumTradableQuantity = 1.0
          maximumTradableQuantity = 1000000.0
          providerId = "123"
          observers = [public]
          processed = True
        createListingRequestCid <- exchange `submit` do exerciseCmd listingServiceCid Listing.RequestCreateListing with ..
        listingCid <- exchange `submit` do exerciseCmd listingServiceCid Listing.CreateListing with ..
        (Some Listing.Listing{listingId}) <- queryContractId @Listing.Listing exchange listingCid
        return listingId

  h2oQ122Id <- mkMarket usd.id h2oQ122DerId
  h2oQ222Id <- mkMarket usd.id h2oQ222DerId
  h2oQ322Id <- mkMarket usd.id h2oQ322DerId
  h2oQ422Id <- mkMarket usd.id h2oQ422DerId

  makeTrade providers h2oQ122Id alice bob 50.0 1000.0 1
  makeTrade providers h2oQ222Id alice charlie 10.0 3000.0 3
  makeTrade providers h2oQ322Id bob dana 200.0 5000.0 5
  makeTrade providers h2oQ422Id dana alice 15.0 2500.0 7
  makeTrade providers h2oQ122Id bob charlie 500.0 1000.0 9

  return ()

makeTrade : Providers -> Text -> Customer -> Customer -> Decimal -> Decimal -> Int -> Script ()
makeTrade Providers{..} listingId buyer seller price qty currentId = do
  (Some (_,listing)) <- queryContractKey @Listing.T exchange (operator, exchange, listingId)
  -- Create orders
  let sellerOrderId = show currentId
  sellerOrderCid <- seller.customer `submit` do
    exerciseCmd seller.tradingServiceCid Trading.RequestCreateOrder with
        details = Order.Details with
            id = sellerOrderId
            optExchangeFee = None
            listingId
            asset = Asset with id = listing.tradedAssetId; quantity = qty
            side = Order.Sell
            timeInForce = Order.GTC
            orderType = Order.Limit with price
            marketType = Order.Cleared clearinghouse

  let buyerOrderId = show (currentId + 1)
  buyerOrderCid <- submit buyer.customer $ do
    exerciseCmd buyer.tradingServiceCid Trading.RequestCreateOrder with
        details = Order.Details with
            id = buyerOrderId
            listingId
            asset = Asset with id = listing.tradedAssetId; quantity = price * qty
            optExchangeFee = None
            side = Order.Buy
            timeInForce = Order.GTC
            orderType = Order.Limit with price
            marketType = Order.Cleared clearinghouse

  -- Acknowledge Orders
  [(sellerCreateOrderRequestCid, _)] <- query @Trading.CreateOrderRequest seller.customer
  sellerOrderCid <- exchange `submit` do
    exerciseCmd seller.tradingServiceCid Trading.AcknowledgeOrderRequest with
        createOrderRequestCid = sellerCreateOrderRequestCid
        providerOrderId = "seller's exchange order id"
  checkOrderStatusMatches sellerOrderCid seller.customer Order.PendingExecution

  [(buyerCreateOrderRequestCid, _)] <- query @Trading.CreateOrderRequest buyer.customer
  buyerOrderCid <- exchange `submit` do
    exerciseCmd buyer.tradingServiceCid Trading.AcknowledgeOrderRequest with
        createOrderRequestCid = buyerCreateOrderRequestCid
        providerOrderId = "buyer's exchange order id"
  checkOrderStatusMatches buyerOrderCid buyer.customer Order.PendingExecution

  -- Confirm order matches
  let execution = Order.Execution with
        matchId = show currentId
        makerOrderId = sellerOrderId
        takerOrderId = buyerOrderId
        quantity = 200.0
        price = 100.0
        timestamp = "123456"

  Matching.Cleared clearedTradeCid <- exchange `submit` exerciseCmd matchingServiceCid Matching.MatchOrders with execution

  (sellerClearedTradeSideCid, buyerClearedTradeSideCid) <- clearinghouse `submit` exerciseCmd clearedTradeCid Clearing.ClearedTrade_Novate
  return ()
