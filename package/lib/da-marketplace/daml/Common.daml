module Common where

import Daml.Script
import DA.Finance.Asset (AssetDeposit)
import DA.Finance.Types (Id(..), Asset(..), Account(..))
import DA.Set
import DA.Optional
import ContingentClaims.Claim (Claim(Zero), serialize)
import ContingentClaims.Observation (Observation)
import qualified DA.Finance.Asset as AssetDeposit
import qualified Marketplace.Operator.Role as Operator
import qualified Marketplace.Clearing.Role as Clearinghouse
import qualified Marketplace.Clearing.Service as Clearing
import qualified Marketplace.Custody.Role as Custodian
import qualified Marketplace.Custody.Service as Custody
import qualified Marketplace.Issuance.Service as Issuance
import qualified Marketplace.Trading.Role as Exchange
import qualified Marketplace.Trading.Matching.Service as Matching
import qualified Marketplace.Trading.Service as Trading
import qualified Marketplace.Listing.Service as Listing
import qualified Marketplace.Settlement.Service as Settlement
import qualified Marketplace.Distribution.Role as Distributor
import qualified Marketplace.Distribution.Bidding.Service as Bidding
import qualified Marketplace.Regulator.Service as RegulatorService
import qualified Marketplace.Regulator.Role as Regulator
import Marketplace.Issuance.AssetDescription
import Marketplace.Issuance.CFI (CFI)
import Marketplace.Issuance.CFI qualified as CFI

data Parties = Parties
  with
    userAdmin : Party
    bank : Party
    exchange : Party
    clearinghouse : Party
    issuer : Party
    alice : Party
    bob : Party
    charlie : Party
    dana : Party
    public : Party

allocateParties : Script Parties
allocateParties = do
  operator      <- allocatePartyWithHint "Operator" $ PartyIdHint "Operator"
  bank          <- allocatePartyWithHint "Bank" $ PartyIdHint "Bank"
  exchange      <- allocatePartyWithHint "Exchange" $ PartyIdHint "Exchange"
  clearinghouse <- allocatePartyWithHint "Ccp" $ PartyIdHint "Ccp"
  issuer        <- allocatePartyWithHint "Issuer" $ PartyIdHint "Issuer"
  alice         <- allocatePartyWithHint "Alice" $ PartyIdHint "Alice"
  bob           <- allocatePartyWithHint "Bob" $ PartyIdHint "Bob"
  charlie       <- allocatePartyWithHint "Charlie" $ PartyIdHint "Charlie"
  dana          <- allocatePartyWithHint "Dana" $ PartyIdHint "Dana"
  public        <- allocatePartyWithHint "Public" $ PartyIdHint "Public"
  pure $ Parties with userAdmin = operator; ..


getExistingParties : Script Parties
getExistingParties = do
  [operator, bank, exchange, clearinghouse, issuer, alice, bob, charlie, dana, public]
    <- retrieve ["Operator", "Bank", "Exchange", "Ccp", "Issuer", "Alice", "Bob", "Charlie", "Dana", "Public"]
  pure $ Parties with userAdmin = operator; ..
  where
    retrieve : [Text] -> Script [Party]
    retrieve parties = pure . fromSome $ mapA partyFromText parties

data Assets = Assets
  with
    usd : Asset
    eur : Asset
    tsla : Asset
    nflx : Asset
    libor : Asset

data Providers = Providers
  with
    operator : Party
    bank : Party
    exchange : Party
    clearinghouse : Party
    public : Party
    operatorRoleCid : ContractId Operator.Role
    clearingRoleCid : ContractId Clearinghouse.Role
    custodianRoleCid : ContractId Custodian.Role
    exchangeRoleCid : ContractId Exchange.Role
    matchingServiceCid : ContractId Matching.Service
    distributorRoleCid : ContractId Distributor.Role

-- TODO: consider whether these services need to be more granular. i.e. does a custody relationship allow execution, tarde-away, safekeeping etc. in one go
data Customer = Customer
  with
    customer : Party
    tradingServiceCid : ContractId Trading.Service
    listingServiceCid : ContractId Listing.Service
    issuanceServiceCid : ContractId Issuance.Service
    clearingServiceCid : ContractId Clearing.Service
    custodyServiceCid : ContractId Custody.Service
    biddingServiceCid : ContractId Bidding.Service
    mainAccount : Account

onboardProviders : Parties -> Script Providers
onboardProviders Parties{userAdmin = operator; ..} = do

  -- Roles
  operatorRoleCid <- submit operator  do createCmd Operator.Role with observers = fromList [public]; ..

  custodianRoleOfferCid  <- submit operator  do exerciseCmd operatorRoleCid Operator.OfferCustodianRole with provider = bank
  (custodianRoleCid, issuanceServiceCid, custodyServiceCid)  <- submit bank do exerciseCmd custodianRoleOfferCid Custodian.Accept

  exchangeRoleOfferCid  <- submit operator  do exerciseCmd operatorRoleCid Operator.OfferExchangeRole with provider = exchange
  (exchangeRoleCid, matchingServiceCid, listingServiceCid, settlementServiceCid) <- submit exchange  do exerciseCmd exchangeRoleOfferCid Exchange.Accept

  distributorRoleOfferCid   <- submit operator  do exerciseCmd operatorRoleCid Operator.OfferDistributorRole with provider = bank
  distributorRoleCid        <- submit bank      do exerciseCmd distributorRoleOfferCid Distributor.Accept

  settlementServiceOfferCid <- submit operator  do exerciseCmd operatorRoleCid Operator.OfferSettlementService with provider = bank
  settlementServiceCid      <- submit bank      do exerciseCmd settlementServiceOfferCid Settlement.Accept

  -- Identities
  regulatorRoleOfferCid     <- submit operator  do exerciseCmd operatorRoleCid Operator.OfferRegulatorRole with provider = operator
  regulatorRoleCid          <- submit operator  do exerciseCmd regulatorRoleOfferCid Regulator.Accept

  regulatorServiceOfferCid        <- submit operator  do exerciseCmd regulatorRoleCid Regulator.OfferRegulatorService with customer = bank
  regulatorServiceCid             <- submit bank      do exerciseCmd regulatorServiceOfferCid RegulatorService.Accept
  identityVerificationRequestCid  <- submit bank      do exerciseCmd regulatorServiceCid RegulatorService.RequestIdentityVerification with legalName = "Bank"; location = "Bank Location"; observers = [public]
  verifiedIdentity                <- submit operator  do exerciseCmd regulatorServiceCid RegulatorService.VerifyIdentity with ..

  regulatorServiceOfferCid        <- submit operator  do exerciseCmd regulatorRoleCid Regulator.OfferRegulatorService with customer = exchange
  regulatorServiceCid             <- submit exchange  do exerciseCmd regulatorServiceOfferCid RegulatorService.Accept
  identityVerificationRequestCid  <- submit exchange  do exerciseCmd regulatorServiceCid RegulatorService.RequestIdentityVerification with legalName = "Exchange"; location = "Exchange Location"; observers = [public]
  verifiedIdentity                <- submit operator  do exerciseCmd regulatorServiceCid RegulatorService.VerifyIdentity with ..

  regulatorServiceOfferCid        <- submit operator      do exerciseCmd regulatorRoleCid Regulator.OfferRegulatorService with customer = clearinghouse
  regulatorServiceCid             <- submit clearinghouse do exerciseCmd regulatorServiceOfferCid RegulatorService.Accept
  identityVerificationRequestCid  <- submit clearinghouse do exerciseCmd regulatorServiceCid RegulatorService.RequestIdentityVerification with legalName = "CCP"; location = "CCP Location"; observers = [public]
  verifiedIdentity                <- submit operator      do exerciseCmd regulatorServiceCid RegulatorService.VerifyIdentity with ..

  -- Clearing
  custodyServiceOfferCid <- submit bank do exerciseCmd custodianRoleCid Custodian.OfferCustodyService with customer = clearinghouse, ..
  custodyServiceCid <- submit clearinghouse do exerciseCmd custodyServiceOfferCid Custody.Accept

  let
    clearinghouseAccountId = Id with signatories = fromList [ bank, clearinghouse ]; label = "CCPAccount-" <> partyToText clearinghouse; version = 0
    clearinghouseAccount   = Account with provider = bank; owner = clearinghouse; id = clearinghouseAccountId

  clearingRoleOfferCid  <- submit operator do exerciseCmd operatorRoleCid Operator.OfferClearingRole with provider = clearinghouse
  (clearingRoleCid, marketServiceCid)       <- submit clearinghouse do exerciseCmd clearingRoleOfferCid Clearinghouse.Accept with ccpAccount = clearinghouseAccount; ..
  pure Providers with ..

onboardCustomer : Providers -> Text -> Party -> Script Customer
onboardCustomer Providers{..} party customer = do
  -- Custody service
  custodyServiceOfferCid <- submit bank do exerciseCmd custodianRoleCid Custodian.OfferCustodyService with ..
  custodyServiceCid <- submit customer do exerciseCmd custodyServiceOfferCid Custody.Accept

  -- Trading and Listing services
  tradingServiceOfferCid <- submit exchange do exerciseCmd exchangeRoleCid Exchange.OfferTradingService with ..
  tradingServiceCid <- submit customer do exerciseCmd tradingServiceOfferCid Trading.Accept
  listingServiceOfferCid <- submit exchange do exerciseCmd exchangeRoleCid Exchange.OfferListingService with ..
  listingServiceCid <- submit customer do exerciseCmd listingServiceOfferCid Listing.Accept
  issuanceServiceOfferCid <- submit bank do exerciseCmd custodianRoleCid Custodian.OfferIssuanceService with ..
  issuanceServiceCid <- submit customer do exerciseCmd issuanceServiceOfferCid Issuance.Accept

  -- Clearing Service
  mainAccount <- (.account) . fromSome <$> queryContractId customer custodyServiceCid
  clearingServiceOfferCid  <- submit clearinghouse do exerciseCmd clearingRoleCid Clearinghouse.OfferClearingService with ..
  clearingServiceCid       <- submit customer do exerciseCmd clearingServiceOfferCid Clearing.Accept with clearingAccount = mainAccount

  -- Identity
  (Some (regulatorRoleCid,_))          <- queryContractKey @Regulator.Role operator (operator, operator)

  regulatorServiceOfferCid        <- submit operator do exerciseCmd regulatorRoleCid Regulator.OfferRegulatorService with customer = customer
  regulatorServiceCid             <- submit customer do exerciseCmd regulatorServiceOfferCid RegulatorService.Accept
  identityVerificationRequestCid  <- submit customer do exerciseCmd regulatorServiceCid RegulatorService.RequestIdentityVerification with legalName = party; location = party <> " Location"; observers = [public]
  verifiedIdentity                <- submit operator do exerciseCmd regulatorServiceCid RegulatorService.VerifyIdentity with ..

  -- Bidding Service
  biddingServiceOfferCid <- submit bank do exerciseCmd distributorRoleCid Distributor.OfferBiddingService with ..
  biddingServiceCid <- submit customer do exerciseCmd biddingServiceOfferCid Bidding.Accept

  pure $ Customer with ..

originate : Providers -> Customer -> Text ->  Text -> CFI -> Claim Observation  Date Decimal Id -> [Party] -> Script (ContractId AssetDescription, AssetDescription)
originate Providers{..} issuer assetLabel description cfi claims extraObs = do
    let observers = public :: extraObs
        safekeepingAccount = issuer.mainAccount
    createOriginationCid <- submit issuer.customer do exerciseCmd issuer.issuanceServiceCid Issuance.RequestOrigination with claims = serialize claims; ..
    submit bank do exerciseCmd (issuer.issuanceServiceCid) Issuance.Originate with ..

onboardAssets : Providers -> Customer -> Script Assets
onboardAssets providers issuer = do
  let mkAsset name description cfi claims = do
      (_, AssetDescription{assetId = id}) <- originate providers issuer name description cfi claims []
      pure $ Asset with quantity = 0.0; ..

  usd   <- mkAsset "USD" "United States Dollar" CFI.currency Zero
  eur   <- mkAsset "EUR" "Euro" CFI.currency Zero
  tsla  <- mkAsset "TSLA" "Tesla, Inc." CFI.equity Zero
  nflx  <- mkAsset "NFLX" "Netflix" CFI.equity Zero
  libor <- mkAsset "LIBOR" "Libor" CFI.equity Zero

  pure Assets with ..

depositAsset : Providers -> Customer -> Asset -> Id -> Set Party -> Script (ContractId AssetDeposit)
depositAsset Providers{..} Customer{..} asset accountId observers = do
  -- Assets
  depositRequestCid <- submit customer do exerciseCmd custodyServiceCid Custody.RequestDeposit with asset
  depositCid <- submit bank do exerciseCmd custodyServiceCid Custody.Deposit with ..
  (Some deposit) <- queryContractId bank depositCid
  submit customer do exerciseCmd depositCid AssetDeposit.AssetDeposit_SetObservers with newObservers = deposit.observers `union` observers
