-- Copyright (c) 2019, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0

module DA.Finance.Asset where

import DA.Action
import DA.Assert
import DA.Set

import DA.Finance.Types
import DA.Finance.Utils

-- | Represents a deposit of an asset in an account. The `account.id` and `asset.id` fields
-- can be used to link the contract to other contracts that provide further information
-- such as the type of the asset or reference data for it. This allows new asset classes
-- to be added without having to modify business processes that operate on generic
-- asset deposits.
template AssetDeposit
  with
    account : Account
      -- ^ A deposit is allocated to an account and backed by the account.id.signatories.
      -- Depending on the desired trust model this might be (i) both the provider and the
      -- owner, (ii) just the provider or (iii) a third party agent.
    asset : Asset
      -- ^ Specifies the id and the amount of assets deposited. The asset.id.signatories
      -- are the parties that publish reference data and hence the details for how to
      -- lifecycle the asset.
    lockers : Set Party
      -- ^ Specifies if this deposit has been locked and for what reason. A party in
      -- lock.releasers has the authority to unlock the deposit.
    observers : Set Party
  where
    signatory account.id.signatories, lockers
    observer observers, account.provider, account.owner
    ensure asset.quantity > 0.0

    let setQty (qty: Decimal) (ad: AssetDeposit) =
          ad with asset = ad.asset with quantity = qty

    choice AssetDeposit_SetObservers : ContractId AssetDeposit
        with
          newObservers : Set Party
        controller account.owner, lockers
        do
          create this with observers = newObservers

    choice AssetDeposit_Split : [ContractId AssetDeposit]
        -- ^ Splits an asset deposit according to the provided list of quantities.
        with
          quantities : [Decimal]
            -- ^ The quantities of the newly created asset deposits. The total quantity
            -- needs to be smaller or equal than the current quantity. If it does not add up,
            -- an asset deposit with the remainder is created.
        controller account.owner, lockers
        do
          let quantitySum = foldl (+) 0.0 quantities
          assertMsg ("Quantity sum (" <> show quantitySum <> ") is greater than quantity (" <> show asset.quantity <> ") for asset (" <> asset.id.label <> ")") $ quantitySum <= asset.quantity

          let quantitiesAll =
                if quantitySum == asset.quantity
                then quantities
                else quantities ++ [asset.quantity - quantitySum]
          mapA (\q -> create $ setQty q this) quantitiesAll

    choice AssetDeposit_Merge : ContractId AssetDeposit
        -- ^ Merges a list of asset deposits with the given one into one.
        with
          depositCids : [ContractId AssetDeposit]
            -- ^ The asset deposits that will be consumed. All fields except for the quantity
            -- need to match.
        controller account.owner, lockers
        do
          deposit <-
            foldlA
              (\acc cid -> do
                c <- fetchAndArchive cid
                c === (setQty c.asset.quantity acc)
                return $ setQty (acc.asset.quantity + c.asset.quantity) acc
              )
            this
            depositCids
          create deposit

    choice AssetDeposit_Unlock : ContractId AssetDeposit
        -- ^ Unlock a locked asset deposit
        controller account.owner, lockers
        do
          create this with lockers = empty

    choice AssetDeposit_Lock : ContractId AssetDeposit
      -- ^ Lock an asset deposit
      with
        newLockers : Set Party
      controller account.owner, newLockers
      do
        create this with lockers = newLockers

    choice AssetDeposit_Transfer : ContractId AssetDeposit
      -- ^ Transfers an asset from one owner to an owner with the same account provider
      with
        receiverAccount : Account
      controller account.owner, receiverAccount.owner, lockers
      do
        create this with account = receiverAccount; lockers = empty

template AssetCategorization
  with
    id : Id
    assetType : Text
    assetClass : Text
    observers : Set Party
  where
    signatory id.signatories
    observer observers

    choice AssetCategorization_SetObservers : ContractId AssetCategorization
      with newObservers : Set Party
      controller id.signatories
      do create this with observers = newObservers

