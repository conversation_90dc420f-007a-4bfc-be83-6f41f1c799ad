-- Copyright (c) 2019, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0

module DA.Finance.Instrument.Equity.Stock where

import DA.Set

import DA.Finance.Types

-- | Reference data describing an equity stock.
template EquityStock
  with
    id : Id
      -- ^ The stock asset id. Depending on the trust model the signatories
      -- might be the issuer or a third party reference data provider
      -- such as Reuters.
    ccy : Id
     -- ^ The currency id of the stock.
    observers : Set Party
  where
    signatory id.signatories
    observer observers

    key id : Id
    maintainer key.signatories

    choice EquityStock_SetObservers : ContractId EquityStock
        with newObservers : Set Party
        controller id.signatories
        do create this with observers = newObservers
