-- Copyright (c) 2019, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0

module DA.Finance.Instrument.FX.Currency where

import DA.Set

import DA.Finance.Types

-- | Reference data describing an equity stock.
template Currency
  with
    id : Id
      -- ^ The currency asset id.
    isoCode : Text
     -- ^ The ISO code of the currency.
    observers : Set Party
  where
    signatory id.signatories
    observer observers

    key id : Id
    maintainer key.signatories

    choice EquityStock_SetObservers : ContractId Currency
        with newObservers : Set Party
        controller id.signatories
        do create this with observers = newObservers
