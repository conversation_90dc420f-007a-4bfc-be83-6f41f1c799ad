-- Copyright (c) 2019, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0

module DA.Finance.RefData.Fixing where

import DA.Set
import DA.Finance.Types

template Fixing
  with
    id : Id
    currency : Id
    date : Date
    value : Decimal
    observers : Set Party
  where
    signatory id.signatories
    observer observers

    key (id, date) : (Id, Date)
    maintainer key._1.signatories
