-- Copyright (c) 2020, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: 0BSD

module DA.Finance.Trade.SettlementInstruction where

import DA.Assert
import DA.Either
import DA.List
import DA.Set hiding (null)
import DA.Optional hiding (fromSomeNote)
import DA.Optional (fromSomeNote)

import DA.Finance.Asset
import DA.Finance.Types

-- | Data describing settlement details.
data SettlementDetails = SettlementDetails
  with
    senderAccount : Account
      -- ^ The sender account.
    receiverAccount : Account
      -- ^ The receiver account.
    depositCid : Optional (ContractId AssetDeposit)
      -- ^ The allocated asset deposit.
  deriving (Eq, Show)

-- | Represents a settlement instruction for a specific trade. It is typically
-- created at the same time than the trade is instructed. Allows to allocate assets
-- and, once done, settle the instruction.
-- If both counterparties have an account with the same provider, a single step,
-- i.e. a direct transfer, suffices. If assets need to be atomically transferred up
-- and down an account hierarchy, then multiple steps are required.
template SettlementInstruction
  with
    masterAgreement : MasterAgreement
      -- ^ The master agreement to which the settlement applies.
    tradeId : Id
      -- ^ The trade under the master agreement to which the settlement applies.
    asset : Asset
      -- ^ The id and amount of the asset to be settled.
    steps : [SettlementDetails]
      -- ^ The steps in the settlement. If both counterparties have an account
      -- with the same provider, a single step, i.e. a direct transfer, suffices.
    observers : Set Party
  where
    signatory union masterAgreement.id.signatories $ fromList sendersDone
    observer insert masterAgreement.party1 $ insert masterAgreement.party2
      $ union observers $ fromList (sendersPending ++ (map (\(_,s2) -> s2.senderAccount.owner) (zip steps $ tail steps)))
    ensure not (null steps) && asset.quantity > 0.0
      && all (\(s1,s2) -> s1.receiverAccount.owner == s2.senderAccount.owner) (zip steps $ tail steps)

    key (masterAgreement.id, tradeId, asset.id) : (Id, Id, Id)
    maintainer key._1.signatories

    let (sendersDone, sendersPending) = partitionEithers $
          map (\SettlementDetails{..} -> if isSome depositCid then Left senderAccount.owner else Right senderAccount.owner) steps

    choice SettlementInstruction_AllocateNext : ContractId SettlementInstruction
      -- ^ Allocates an asset deposit to the next step of the settlement instruction.
      -- In the simple case where both counterparties have an account with the same provider
      -- a single allocation by the sender party is required.
      with
        depositCid : ContractId AssetDeposit
            -- ^ Specifies the asset deposit contract to be allocated.
        ctrl : Party
          -- ^ The next sender.
      controller ctrl
      do
        let sender = fromSomeNote "fully allocated already" $ nextSender this
        assertMsg "expecting controller to be next sender" $ ctrl == sender

        deposit <- fetch depositCid
        let (done, curr :: next) = break (\x -> isNone x.depositCid) steps
        deposit.account === curr.senderAccount
        deposit.asset === asset
        let currNew = curr with depositCid = Some depositCid
        create this with steps = done ++ currNew :: next


    choice SettlementInstruction_Process : [ContractId AssetDeposit]
      -- ^ Processes a settlement instruction by transferring all allocated asset deposits.
      -- This choice is often called from the trade itself to atomically settle all
      -- assets involved.
      controller masterAgreement.id.signatories
      do
        forA steps \step -> do
          let depositCid = fromSome step.depositCid
          exercise depositCid AssetDeposit_Transfer with receiverAccount = step.receiverAccount

    choice SettlementInstruction_Archive : ()
      controller masterAgreement.id.signatories
      do return ()

-- | HIDE
nextSender : SettlementInstruction -> Optional Party
nextSender SettlementInstruction{..} = (.senderAccount.owner) <$> find (\step -> isNone step.depositCid) steps
