-- Copyright (c) 2019, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0

module DA.Finance.Instrument.FixedIncome.FixedRateBond where

import DA.Set
import DA.Finance.Types

template FixedRateBond
  with
    id : Id
    currencyId : Id
    couponDates : [Date]
    couponIdx : Int
    coupon : Decimal
    observers : Set Party
  where
    signatory id.signatories
    observer observers

    key id : Id
    maintainer key.signatories
