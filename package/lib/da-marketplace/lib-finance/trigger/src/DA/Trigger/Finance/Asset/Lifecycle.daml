-- Copyright (c) 2019, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0

module DA.Trigger.Finance.Asset.Lifecycle where

import DA.Action (when)
import DA.Foldable as F (forA_)
import Daml.Trigger

import DA.Finance.Asset
import DA.Finance.Asset.Lifecycle
import DA.Trigger.Finance.Utils

-- | HIDE
ruleImpl : (LifecycleEffects -> Bool) -> Party -> TriggerA () ()
ruleImpl pred ctrl = do
  deposits <- filter (\(_, depositData) -> depositData.account.owner == ctrl) <$> query @AssetDeposit
  les <- filter (\(_, leData) -> pred leData && null leData.consuming) <$> query @LifecycleEffects

  forA_ deposits $ \(depositCid, depositData) -> do
    forA_ les $ \(lifecycleEffectsCid, leData) ->
      when (leData.id == depositData.asset.id) do
        optCmd <- exerciseByKeyIfExistsCmd @AssetLifecycleRule depositData.account.id
                        AssetLifecycle_Process with optAccounts = None, consumingDepositCids = [], ..
        whenSomeNote (show ctrl <> ": `AssetLifecycleRule` contract " <> show depositData.account.id <> " not found.") optCmd $ \cmd -> do
          debug (show ctrl <> ": Lifecycling " <> show leData.id)
          () <$ emitCommands [ cmd ] [toAnyContractId depositCid]

-- | A trigger to lifecycle `AssetDeposit` contracts. It listens to
-- `LifecycleEffects` contracts that do not consume any assets and
-- eagerly applies them.
--
-- Inputs:
--
-- `pred` (LifecycleEffects -> Bool): A predicate to filter `LifecycleEffects`
-- contracts that are processed. The trigger cannot handle cases where
-- the asset effects end up in different accounts.
trigger : (LifecycleEffects -> Bool) -> Trigger ()
trigger pred = Trigger with
                initialize = pure ()
                updateState = \_ -> pure ()
                heartbeat = None
                rule = ruleImpl pred
                registeredTemplates = RegisteredTemplates
                                        [ registeredTemplate @AssetDeposit
                                        , registeredTemplate @LifecycleEffects
                                        , registeredTemplate @AssetLifecycleRule
                                        ]
