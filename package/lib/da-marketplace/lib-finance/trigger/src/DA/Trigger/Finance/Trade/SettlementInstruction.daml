-- Copyright (c) 2019, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0

module DA.Trigger.Finance.Trade.SettlementInstruction where

import DA.Action (when)
import DA.Foldable as F (forA_, foldMap)
import DA.List (head, tail)
import DA.Map as Map (lookup)
import DA.Optional (fromSome, isNone)
import Daml.Trigger

import DA.Finance.Asset
import DA.Finance.Trade.SettlementInstruction
import DA.Finance.Types
import DA.Finance.Utils
import DA.Trigger.Finance.Utils as U

-- | Rule that helps to allocate `AssetDeposit` to `SettlementInstruction`
-- contracts by atomically merging and splitting the deposits.
template AllocationRule
  with
    sig : Party
      -- ^ The signatory.
  where
    signatory sig

    key sig : Party
    maintainer key

    controller sig can
      nonconsuming Allocate : [ContractId SettlementInstruction]
        -- ^ Allows the `sig` to allocate deposits to settlement
        -- instructions. Expects deposits of the same asset in the
        -- same account.
        with
          instructionCids : [ContractId SettlementInstruction]
            -- ^ The settlement instructions that are allocated.
          depositCids : [ContractId AssetDeposit]
            -- ^ The deposits that are used for allocation. Need to
            -- be mergeable.
        do
          assertMsg "expecting at least one deposit" $ notNull depositCids
          depositCid <- exercise (head depositCids) AssetDeposit_Merge with depositCids = tail depositCids

          instructions <- mapA fetch instructionCids
          let quantities = map (.asset.quantity) instructions

          depositCids <- exercise depositCid AssetDeposit_Split with quantities
          zipWithA (\instructionCid depositCid -> exercise instructionCid SettlementInstruction_AllocateNext with ctrl = sig, ..) instructionCids depositCids

-- | HIDE
ruleImpl : Party -> TriggerA () ()
ruleImpl ctrl = do
  instrs <- filter (\(_, instrData) -> nextSender instrData == Some ctrl) <$> query @SettlementInstruction
  let instrsGroupedByIds = groupByFull (\(_, instrData) -> (nextSenderAccountId instrData, instrData.asset.id)) instrs

  when (notNull instrsGroupedByIds) do
    let instrData = map snd instrs
    deposits <- filter (isNotAllocated instrData . fst) <$> query @AssetDeposit
    let ids2deposits = fromListWithKey (\(_, depositData) -> (depositData.account.id, depositData.asset.id)) deposits

    forA_ instrsGroupedByIds $ \(ids, instrs) ->
      whenSomeNote (show ctrl <> ": no deposits available to allocate.") (Map.lookup ids ids2deposits) $ \deposits -> do
        let instructionCids = map fst instrs

        let quantityRequired = foldMap ((.asset.quantity) . snd) instrs
        let quantityAvailable = foldMap ((.asset.quantity) . snd) deposits

        whenNote (show ctrl <> ": not enough deposits available") (quantityRequired <= quantityAvailable) do
          let depositCids = map fst $ U.takeWhile quantityRequired ((.asset.quantity) . snd) deposits
          optCmd <- exerciseByKeyIfExistsCmd @AllocationRule ctrl Allocate with ..

          whenSomeNote ("`AllocationRule` contract for " <> show ctrl <> " not found.") optCmd $ \cmd -> do
            debug (show ctrl <> ": Allocating to " <> show (map (\(_, instrData) -> show instrData.masterAgreement.id <> "; " <> show instrData.tradeId) instrs))
            let pending = map toAnyContractId depositCids ++ map toAnyContractId depositCids ++ map (toAnyContractId . fst) instrs
            () <$ emitCommands [ cmd ] pending

-- | HIDE
isNotAllocated : [SettlementInstruction] -> ContractId AssetDeposit -> Bool
isNotAllocated instrData depositCid = all (\step -> step.depositCid /= Some depositCid) $ concatMap (.steps) instrData

-- | HIDE
nextSenderAccountId : SettlementInstruction -> Id
nextSenderAccountId SettlementInstruction{..} =  (.senderAccount.id) $ fromSome $ find (\step -> isNone step.depositCid) steps

-- | A trigger that eagerly allocates `AssetDeposit` contracts
-- to `SettlementInstruction` contracts.
trigger :  Trigger ()
trigger = Trigger with
            initialize = pure ()
            updateState = \_ -> pure ()
            heartbeat = None
            rule = ruleImpl
            registeredTemplates = RegisteredTemplates
                                    [ registeredTemplate @AssetDeposit
                                    , registeredTemplate @SettlementInstruction
                                    , registeredTemplate @AllocationRule
                                    ]
