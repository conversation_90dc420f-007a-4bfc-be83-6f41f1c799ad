-- Copyright (c) 2019, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0

module DA.Trigger.Finance.Instrument.Equity.Stock where

import DA.Action (filterA)
import DA.Foldable as F (forA_)
import Daml.Trigger

import DA.Finance.Asset.Lifecycle
import DA.Finance.Instrument.Equity.CashDividend
import DA.Finance.Instrument.Equity.StockSplit
import DA.Finance.Instrument.Equity.Stock.Lifecycle
import DA.Finance.Types
import DA.Trigger.Finance.Utils

-- | HIDE
ruleImpl : RelTime -> (Id -> Text) -> Party -> TriggerA () ()
ruleImpl settleOffset toEntitlementIdLabel ctrl = do
  time <- getTime
  dividends <- getContractsForProvider @EquityCashDividend ctrl >>= filterA (\(_, d) -> isNotProcessed ctrl d.id) . filter (\(_, d) -> isDue settleOffset time d)
  stockSplits <- getContractsForProvider @EquityStockSplit ctrl >>= filterA (\(_, d) -> isNotProcessed ctrl d.id) . filter (\(_, d) -> isDue settleOffset time d)

  forA_ dividends $ \(divCid, divData) -> do
    debug (show ctrl <> ": Processing " <> show divData.id)
    optCmd <- exerciseByKeyIfExistsCmd @EquityStockCashDividendRule divData.id.signatories
                    EquityStockCashDividend_Lifecycle with
                      dividendCid = divCid
                      entitlementIdLabel = toEntitlementIdLabel divData.id

    whenSomeNote (show ctrl <> ": `EquityStockCashDividendRule` contract " <> show divData.id <> " not found.") optCmd $ \cmd -> do
      () <$ emitCommands [ cmd ] [ toAnyContractId divCid ]

  forA_ stockSplits $ \(splitCid, splitData) -> do
    debug (show ctrl <> ": Processing " <> show splitData.id)
    optCmd <- exerciseByKeyIfExistsCmd @EquityStockSplitRule splitData.id.signatories
                    EquityStockSplit_Lifecycle with stockSplitCid = splitCid

    whenSomeNote (show ctrl <> ": `EquityStockSplitRule` contract " <> show splitData.id <> " not found.") optCmd $ \cmd -> do
      () <$ emitCommands [ cmd ] [ toAnyContractId splitCid ]

-- | A trigger that eagerly processes equity lifecycle event contracts
-- (`EquityCashDividend`, `EquityStockSplit`) once they are due.
--
-- Inputs:
--
-- `settlementOffset` (RelTime): Offset to the ex date when the lifecycle event
-- is processed.
--
-- `heartbeat` (RelTime): The heartbeat of the trigger.
--
-- `toEntitlementIdLabel` (Id -> Text): A function to derive the entitlement label
-- from the id of the lifecycle event (e.g. for `EquityCashDividend`).
trigger : RelTime -> RelTime -> (Id -> Text) -> Trigger ()
trigger settlementOffset heartbeat toEntitlementIdLabel =
  Trigger with
    initialize = pure ()
    updateState = \_ -> pure ()
    heartbeat = Some heartbeat
    rule = ruleImpl settlementOffset toEntitlementIdLabel
    registeredTemplates = RegisteredTemplates
                            [ registeredTemplate @EquityCashDividend
                            , registeredTemplate @EquityStockCashDividendRule
                            , registeredTemplate @EquityStockSplit
                            , registeredTemplate @EquityStockSplitRule
                            , registeredTemplate @LifecycleEffects
                            ]
