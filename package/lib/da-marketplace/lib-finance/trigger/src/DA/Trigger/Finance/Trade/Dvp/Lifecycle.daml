-- Copyright (c) 2019, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0

module DA.Trigger.Finance.Trade.Dvp.Lifecycle where

import DA.Action (foldlA)
import DA.Foldable as F (forA_)
import Daml.Trigger

import DA.Finance.Asset.Lifecycle
import DA.Finance.Trade.Dvp
import DA.Finance.Trade.Dvp.Lifecycle
import DA.Trigger.Finance.Utils

-- | HIDE
ruleImpl : Party -> TriggerA () ()
ruleImpl ctrl = do
  dvps <- filter (isReadyToLifecycle ctrl . snd) <$> query @Dvp
  les <- query @LifecycleEffects

  forA_ dvps $ \(dvpCid, dvpData) -> do
    let paymentIds = map (.id) dvpData.payments
    let deliveryIds = map (.id) dvpData.deliveries
    foldlA (\notInProcess (lifecycleEffectsCid, leData) ->
          if notInProcess && (leData.id `elem` paymentIds || leData.id `elem` deliveryIds)
          then do
            optCmd <- exerciseByKeyIfExistsCmd @DvpLifecycleRule dvpData.masterAgreement.id DvpLifecycle_Process with ..
            case optCmd of
              Some cmd -> do
                debug (show ctrl <> ": Lifecycling " <> show dvpData.masterAgreement.id <> "; " <> show dvpData.tradeId)
                False <$ emitCommands [ cmd ] [toAnyContractId dvpCid]
              None     -> do
                debug (show ctrl <> ": `DvpLifecycleRule` contract " <> show dvpData.masterAgreement.id <> " not found.")
                return notInProcess
          else return notInProcess
      ) True les

-- | HIDE
isReadyToLifecycle : Party -> Dvp -> Bool
isReadyToLifecycle ctrl Dvp{..} = masterAgreement.party1 == ctrl || masterAgreement.party2 == ctrl

-- | A trigger to lifecycle `Dvp` contracts. It listens to `LifecycleEffects`
-- contracts and eagerly applies them. If multiple LifecycleEffects contracts
-- are affecting a single dvp, they are  applied sequentially.
trigger : Trigger ()
trigger = Trigger with
            initialize = pure ()
            updateState = \_ -> pure ()
            heartbeat = None
            rule = ruleImpl
            registeredTemplates = RegisteredTemplates
                                    [ registeredTemplate @Dvp
                                    , registeredTemplate @LifecycleEffects
                                    , registeredTemplate @DvpLifecycleRule
                                    ]
