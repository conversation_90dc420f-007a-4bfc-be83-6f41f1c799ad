-- Copyright (c) 2019, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0

module DA.Trigger.Finance.Trade.Dvp.Settlement where

import DA.Foldable (forA_)
import DA.Optional (isSome)
import Daml.Trigger

import DA.Finance.Trade.Dvp
import DA.Finance.Trade.Dvp.Settlement
import DA.Finance.Trade.SettlementInstruction
import DA.Trigger.Finance.Utils

-- | HIDE
ruleImpl : RelTime -> Party -> TriggerA () ()
ruleImpl settlementOffset ctrl = do
  time <- getTime
  dvps <- filter (isReadyToSettle ctrl settlementOffset time . snd) <$> query @Dvp
  instrs <- if null dvps then pure [] else filter (isAllocated . snd) <$> query @SettlementInstruction

  forA_ dvps $ \(dvpCid, Dvp{..}) -> do
    let findInstruction asset =
          find
            (\(_, instrData) -> instrData.masterAgreement == masterAgreement
                                && instrData.tradeId == tradeId
                                && instrData.asset == asset
            )
            instrs

    let optPaymentInstructions = mapA findInstruction payments
    let optDeliveryInstructions = mapA findInstruction deliveries

    whenSomeNote (show ctrl <> ": some payment instructions missing") optPaymentInstructions $ \paymentInstructions ->
      whenSomeNote (show ctrl <> ": some delivery instructions missing") optDeliveryInstructions $ \deliveryInstructions -> do
        let paymentInstructionCids = map fst paymentInstructions
        let deliveryInstructionCids = map fst deliveryInstructions
        opdCmd <- exerciseByKeyIfExistsCmd @DvpSettlementRule masterAgreement.id DvpSettlement_Process with ..

        whenSomeNote (show ctrl <> ": `DvpSettlementRule` contract " <> show masterAgreement.id <> " not found.") opdCmd $ \cmd -> do
          debug (show ctrl <> ": Settling " <> show masterAgreement.id <> "; " <> show tradeId)
          () <$ emitCommands [ cmd ] [ toAnyContractId dvpCid ]

-- | HIDE
-- Checks if a Dvp is ready to be settled.
isReadyToSettle : Party -> RelTime -> Time -> Dvp -> Bool
isReadyToSettle ctrl settlementOffset time Dvp{..} =
  let settlementTime = optional time (addRelTimeToDate settlementOffset) settlementDate
  in settlementTime <= time
      && status == SettlementStatus_Instructed
      && (masterAgreement.party1 == ctrl || masterAgreement.party2 == ctrl)

-- | HIDE
-- Checks if a settlement instruction is fully allocated
isAllocated : SettlementInstruction -> Bool
isAllocated  SettlementInstruction{..} = all (\step -> isSome step.depositCid) steps

-- | A trigger to settle `Dvp` contracts once all allocated `SettlementInstruction`
-- contracts are available and the current time is after the settlement date.
--
-- Inputs:
--
-- `settlementOffset` (RelTime): Offset to the settlement date when the settlement happens.
--
-- `heartbeat` (RelTime): The heartbeat of the trigger.
trigger : RelTime -> RelTime -> Trigger ()
trigger settlementOffset heartbeat =
  Trigger with
    initialize = pure ()
    updateState = \_ -> pure ()
    heartbeat = Some heartbeat
    rule = ruleImpl settlementOffset
    registeredTemplates = RegisteredTemplates
                            [ registeredTemplate @Dvp
                            , registeredTemplate @SettlementInstruction
                            , registeredTemplate @DvpSettlementRule
                            ]
