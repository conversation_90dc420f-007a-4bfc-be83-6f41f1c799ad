
-- Copyright (c) 2019, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0

module Test.Trigger.Finance.TestScript where

import DA.Assert
import DA.Date
import DA.List as List
import DA.Set as Set
import DA.Map as Map
import Daml.Script
import Prelude hiding (submit)

import DA.Finance.Asset
import DA.Finance.Instrument.Equity.CashDividend
import DA.Finance.Instrument.Equity.StockSplit
import DA.Finance.Trade.Dvp
import DA.Finance.Types

import Test.Finance.Market.Asset
import Test.Finance.Market.Dvp
import Test.Finance.Market.Instrument
import Test.Finance.Market.Party
import Test.Finance.Utils
import Test.StartupScript

-- | The script that tests the trigger executions.
test : Parties -> Script ()
test p@Parties{..} = do
  debug "Setting up market"
  (InstrumentMarket{..}, AssetMarket{..}, DvpMarket{..}) <- startup p


  debug "Adding corporate actions"
  submit reuters do
    createCmd EquityCashDividend with
      id = initAssetId reuters "DAH" 0
      exDate = date 1970 Jan 1 -- current date not available in scripts yet
      settlementDate = date 1970 Jan 1 -- current date not available in scripts yet
      perShare = 0.4
      observers = Set.fromList $ mapValues $ fmap (.owner) accountMap

  submit reuters do
    createCmd EquityStockSplit with
      id = initAssetId reuters "EUT" 0
      exDate = date 1970 Jan 1 -- current date not available in scripts yet
      rFactor = 0.5
      observers = Set.fromList $ mapValues $ fmap (.owner) accountMap

  _ <- waitForCashDividend [alice, acmeBank, gencoBank]
  _ <- waitForStockSplit [charlie]
  _ <- checkAssetDeposits alice [initAsset reuters "DAH" 1 75.0, initAsset reuters "USD" 0 30.0]
  _ <- checkAssetDeposits charlie [initAsset reuters "USD" 0 1500.0, initAsset reuters "Option" 1 3.0]

  -- debug "Instructing Dvp"
  -- dvps <- query @Dvp alice
  --
  -- submit alice do
  --   forA_ dvps $ \(dvpCid, _) -> do
  --     exerciseByKeyCmd @DvpInstructionRule (maIdMap ! "Charlie&Alice") DvpInstruction_Process with
  --       dvpCid
  --       paymentSteps =
  --         [[ initSettlementDetails accountMap gencoBank charlie gencoBank
  --         , initSettlementDetails accountMap cb gencoBank acmeBank
  --         , initSettlementDetails accountMap acmeBank acmeBank alice
  --         ]]
  --       deliverySteps =
  --         [[ initSettlementDetails accountMap acmeBank alice acmeBank
  --         , initSettlementDetails accountMap csd acmeBank gencoBank
  --         , initSettlementDetails accountMap gencoBank gencoBank charlie
  --         ]]
  --       ctrl = alice
  --
  -- _ <- waitForSettlement alice
  -- _ <- checkAssetDeposits alice [initAsset reuters "DAH" 1 15.0, initAsset reuters "USD" 0 1256.0]
  -- _ <- checkAssetDeposits charlie [initAsset reuters "DAH" 1 60.0, initAsset reuters "USD" 0 274.0, initAsset reuters "Option" 1 3.0]

  debug "TestScript Finished"

waitForCashDividend : [Party] -> Script ()
waitForCashDividend ps = do
  -- All "DAH" deposits are updated (version == 1) and no dividend entitlements left
  let cond party = do
        depositData <- map snd <$> query @AssetDeposit party
        let dahDeposits = List.filter (\d -> d.asset.id.label == "DAH") depositData
        let dividendDeposits = List.filter (\d -> d.asset.id.label == "CashDividend:DAH") depositData

        return $
          all (\d -> d.asset.id.version == 1) dahDeposits
          && List.null dividendDeposits

  checked <- mapA cond ps
  if and checked then debug "CashDividend processed" else waitForCashDividend ps

waitForStockSplit : [Party] -> Script ()
waitForStockSplit ps = do
  -- All "Option" deposits are updated (version == 1)
  let cond party = do
        depositData <- map snd <$> query @AssetDeposit party
        let optionDeposits = List.filter (\d -> d.asset.id.label == "Option") depositData

        return $
          all (\d-> d.asset.id.version == 1) optionDeposits

  checked <- mapA cond ps
  if and checked then debug "StockSplit processed" else waitForStockSplit ps

waitForSettlement : Party -> Script ()
waitForSettlement p = do
  -- All Dvps are settled
  let cond = do
        dvpData <- map snd <$> query @Dvp p
        return $ all (\d -> d.status == SettlementStatus_Settled) dvpData

  checked <- cond
  if checked then debug "Dvps settled" else waitForSettlement p

checkAssetDeposits : Party -> [Asset] -> Script ()
checkAssetDeposits party targetAssets = do
  depositData <- List.filter (\d -> d.account.owner == party) . map snd <$> query @AssetDeposit party
  let assets = map (.asset) depositData
  toMap assets === toMap targetAssets

  where
    toMap assets = Map.fromListWith (+) $ map (\asset -> (asset.id, asset.quantity)) assets

