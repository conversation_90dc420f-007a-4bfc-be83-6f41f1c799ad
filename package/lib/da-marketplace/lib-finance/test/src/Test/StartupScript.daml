-- Copyright (c) 2019, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0

module Test.StartupScript where

import DA.Date
import DA.Foldable
import DA.List
import DA.Set as Set
import Daml.Script

import DA.Finance.Instrument.Equity.Option
import DA.Trigger.Finance.Trade.SettlementInstruction

import Test.Finance.Market.Asset
import Test.Finance.Market.Dvp
import Test.Finance.Market.Instrument
import Test.Finance.Market.Party
import Test.Finance.Types hiding (CASH)

-- | A script that can be used to set up a market.
startup : Parties -> Script (InstrumentMarket, AssetMarket, DvpMarket)
startup p@Parties{..} = do
  let stockInfo =
        [ StockInfo with stockLabel = "DAH", ccyLabel = "USD"
        , StockInfo with stockLabel = "EUT", ccyLabel = "USD"
        ]
  let optionInfo =
        [ OptionInfo with
            optionLabel = "Option"
            undLabel = "EUT"
            optionType = CALL
            exerciseType = EUROPEAN
            strike = 10.0
            contractSize = 1000.0
            maturity = date 1970 Jan 1 -- current date not available in scripts yet
            settlementType = CASH
        ]
  let observers = Set.fromList [alice, charlie, bob, gencoBank, acmeBank, csd]
  im@InstrumentMarket{..} <- instrumentSetup reuters observers stockInfo optionInfo

  let assetInfo =
        [ AssetInfo with provider = gencoBank, owner = charlie, accountType = None, assetData = [("USD", 1500.0), ("Option", 3.0)]
        , AssetInfo with provider = cb, owner = gencoBank, accountType = None, assetData = [("USD", 1500.0)]
        , AssetInfo with provider = cb, owner = acmeBank, accountType = None, assetData = [] -- Required for trade relationship
        , AssetInfo with provider = acmeBank, owner = acmeBank, accountType = None, assetData = [("USD", 1500.0)]
        , AssetInfo with provider = acmeBank, owner = alice, accountType = None, assetData = [("DAH", 75.0)]
        , AssetInfo with provider = csd, owner = acmeBank, accountType = None, assetData = [("DAH", 75.0)]
        , AssetInfo with provider = csd, owner = gencoBank, accountType = None, assetData = [] -- Required for trade relationship
        , AssetInfo with provider = gencoBank, owner = gencoBank, accountType = None, assetData = [("DAH", 75.0)]
        ]
  am@AssetMarket{..} <- assetSetup p TrustModel_Bilateral assetInfo

  let tradeInfo =
        [ TradeInfo with
            buyer = charlie
            seller = alice
            dvpInfo =
              [ DvpInfo with
                  tradeIdLabel = "Dvp:0"
                  settlementDate = None
                  paymentData = [("USD", 1000.0)]
                  deliveryData = [("DAH", 50.0)]
              , DvpInfo with
                  tradeIdLabel = "Dvp:1"
                  settlementDate = None
                  paymentData = [("USD", 250.0)]
                  deliveryData = [("DAH", 10.0)]
              ]

        ]
  dm@DvpMarket{..} <- dvpSetup p TrustModel_Bilateral tradeInfo

  mapA_ (\sig -> submit sig $ createCmd AllocationRule with sig) $ dedup $ map (.owner) assetInfo

  return (im, am, dm)
