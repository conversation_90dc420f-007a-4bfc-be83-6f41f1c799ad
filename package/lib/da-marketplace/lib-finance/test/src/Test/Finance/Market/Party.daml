-- Copyright (c) 2019, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0

module Test.Finance.Market.Party where

import Daml.Script

data Parties = Parties
  with
    acmeBank : Party
    gencoBank : Party
    alice : Party
    bob : Party
    charlie : Party
    agent : Party
    cb : Party
    csd : Party
    reuters : Party

partySetup : Script Parties
partySetup = script do
  acmeBank <- allocateParty "AcmeBank"
  gencoBank <- allocateParty "GencoBank"
  alice <- allocateParty "<PERSON>"
  bob <- allocateParty "<PERSON>"
  charlie <- allocateParty "<PERSON>"
  agent <- allocateParty "Agent"
  cb <- allocateParty "CB"
  csd <- allocateParty "CSD"
  reuters <- allocateParty "Reuters"

  pure $ Parties with ..
