module Test.Finance.Base.HolidayCalendar where

import DA.Assert
import DA.Date
import Daml.Script

import DA.Finance.Base.HolidayCalendar

cal : HolidayCalendarData
cal = HolidayCalendarData with
        id = "USNY"
        weekend = [Saturday, Sunday]
        holidays = [date 2018 Jan 02, date 2018 Jan 31, date 2018 Feb 1]

test_isHoliday = script do
  isHoliday cal (date 2018 Jan 02) === True

test_isBusinessDay = script do
  isBusinessDay cal (date 2018 Jan 02) === False

test_nextBusinessDay = script do
  nextBusinessDay cal (date 2018 Jan 01) === date 2018 Jan 03

test_previousBusinessDay = script do
  previousBusinessDay cal (date 2018 Jan 03) === date 2018 Jan 01

test_nextOrSameBusinessDay = script do
  nextOrSameBusinessDay cal (date 2018 Jan 01) === date 2018 Jan 01

test_previousOrSameBusinessDay = script do
  previousOrSameBusinessDay cal (date 2018 Jan 03) === date 2018 Jan 03

test_nextSameOrLastInMonthBusinessDay = script do
  nextSameOrLastInMonthBusinessDay cal (date 2018 Jan 31) === date 2018 Jan 30
test_addBusinessDays = script do
  addBusinessDays cal 5 (date 2018 Jan 01) === date 2018 Jan 09
  addBusinessDays cal (-5) (date 2018 Feb 05) === date 2018 Jan 25

test_adjustDate = script do
  adjustDate cal NONE (date 2018 Mar 31) === (date 2018 Mar 31)
  adjustDate cal FOLLOWING (date 2018 Mar 31) === (date 2018 Apr 02)
  adjustDate cal MODFOLLOWING (date 2018 Mar 31) === (date 2018 Mar 30)
  adjustDate cal PRECEDING (date 2018 Apr 01) === (date 2018 Mar 30)
  adjustDate cal MODPRECEDING (date 2018 Apr 01) === (date 2018 Apr 02)

