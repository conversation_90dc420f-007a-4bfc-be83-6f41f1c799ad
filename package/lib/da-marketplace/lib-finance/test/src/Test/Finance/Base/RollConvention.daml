module Test.Finance.Base.RollConvention where

import DA.Assert
import DA.Date as D
import Daml.Script

import DA.Finance.Base.RollConvention

test_addPeriod = script do
  addPeriod (D.date 2018 Oct 01) (Period {periodMultiplier = 1, period = D}) === D.date 2018 Oct 02
  addPeriod (D.date 2018 Oct 01) (Period {periodMultiplier = 1, period = W}) === D.date 2018 Oct 08
  addPeriod (D.date 2018 Oct 01) (Period {periodMultiplier = 1, period = M}) === D.date 2018 Nov 01
  addPeriod (D.date 2018 Oct 01) (Period {periodMultiplier = 1, period = Y}) === D.date 2019 Oct 01
  addPeriod (D.date 2018 Oct 31) (Period {periodMultiplier = 1, period = M}) === D.date 2018 Nov 30
  addPeriod (D.date 2018 Nov 30) (Period {periodMultiplier = -1, period = M}) === D.date 2018 Oct 30
  addPeriod (D.date 2018 Jan 30) (Period {periodMultiplier = -1, period = M}) === D.date 2017 Dec 30

test_next = script do
  next (D.date 2018 Oct 31) (Period {periodMultiplier = 1, period = M}) EOM === D.date 2018 Nov 30
  next (D.date 2018 Oct 01) (Period {periodMultiplier = 1, period = M}) (DOM 1) === D.date 2018 Nov 01
  next (D.date 2018 Feb 28) (Period {periodMultiplier = 3, period = M}) (DOM 30) === D.date 2018 May 30
  next (D.date 2018 Feb 28) (Period {periodMultiplier = 3, period = M}) EOM === D.date 2018 May 31
  next (D.date 2018 Feb 28) (Period {periodMultiplier = 2, period = M}) EOM === D.date 2018 Apr 30
  next (D.date 2018 Nov 30) (Period {periodMultiplier = 3, period = M}) (DOM 30) === D.date 2019 Feb 28
