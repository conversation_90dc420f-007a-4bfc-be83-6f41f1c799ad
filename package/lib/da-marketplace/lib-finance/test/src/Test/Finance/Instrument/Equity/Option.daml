-- Copyright (c) 2019, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0

module Test.Finance.Instrument.Equity.Option where

import DA.Assert
import DA.Date
import DA.List
import DA.Set as Set
import DA.Time
import Daml.Script

import DA.Finance.Asset.Lifecycle
import DA.Finance.Instrument.Entitlement
import DA.Finance.Instrument.Equity.Option
import DA.Finance.Instrument.Equity.Option.Lifecycle
import DA.Finance.Instrument.Equity.StockSplit
import DA.Finance.Trade.Dvp.Lifecycle

import Test.Finance.Asset
import Test.Finance.Market.Asset
import Test.Finance.Market.Dvp
import Test.Finance.Market.Instrument
import Test.Finance.Market.Party
import Test.Finance.Types hiding (CASH)
import Test.Finance.Utils

testStockSplit = script do
  setTime (time (date 2020 Jan 1) 0 0 0)

  p@Parties{..} <- partySetup

  let observers = Set.fromList [alice, acmeBank]
  let stockInfo = [ StockInfo with stockLabel = "DAH", ccyLabel = "USD"]
  let optionInfo =
        [ OptionInfo with
            optionLabel = "Option"
            undLabel = "DAH"
            optionType = CALL
            exerciseType = EUROPEAN
            strike = 10.0
            contractSize = 1000.0
            maturity = date 2020 Feb 1
            settlementType = PHYSICAL
        ]
  InstrumentMarket{..} <- instrumentSetup reuters observers stockInfo optionInfo

  let assetInfo =
        [ AssetInfo with provider = acmeBank, owner = alice, accountType = None, assetData = [("Option", 3.0)]
        ]
  am@AssetMarket{..} <- assetSetup p TrustModel_Bilateral assetInfo

  -- Stock split
  stockSplitCid <- submit reuters do
    createCmd EquityStockSplit with
      id = initAssetId reuters "DAH" 0
      exDate = date 2020 Jan 1
      rFactor = 0.5
      observers = Set.fromList $ map (.owner) assetInfo

  (optionCid, lifecycleEffectsCid) <- submit reuters do
    exerciseByKeyCmd @EquityOptionStockSplitRule (Set.singleton reuters)
       EquityOptionStockSplit_Lifecycle with
        optionCid = optionMap ! "Option"
        stockSplitCid

  [depositCid] <- submit alice do
    exerciseByKeyCmd @AssetLifecycleRule (accountMap ! "Alice@AcmeBank").id AssetLifecycle_Process with
      depositCid = depositMap ! "Alice@AcmeBank:Option:0:3.0", lifecycleEffectsCid, optAccounts = None, consumingDepositCids = []

  -- Checks
  matchAssetDeposit depositCid acmeBank alice "Option" 1 3.0
  optDeposit <- queryContractId alice (depositMap ! "Alice@AcmeBank:Option:0:3.0")
  optDeposit === None

  Some option <- queryContractId reuters optionCid
  option.strike === 5.0
  option.contractSize === 2000.0
  option.underlyingId === initAssetId reuters "DAH" 1

testExercise_CASH = script do
  setTime (time (date 2020 Jan 1) 0 0 0)

  p@Parties{..} <- partySetup

  let observers = Set.fromList [alice, charlie, acmeBank]
  let stockInfo = [ StockInfo with stockLabel = "DAH", ccyLabel = "USD"]
  let optionInfo =
        [ OptionInfo with
            optionLabel = "Option"
            undLabel = "DAH"
            optionType = CALL
            exerciseType = EUROPEAN
            strike = 10.0
            contractSize = 1000.0
            maturity = date 2020 Feb 1
            settlementType = CASH
        ]
  InstrumentMarket{..} <- instrumentSetup reuters observers stockInfo optionInfo

  let assetInfo =
        [ AssetInfo with provider = acmeBank, owner = alice, accountType = None, assetData = [("Option", 3.0)]
        , AssetInfo with provider = acmeBank, owner = charlie, accountType = None, assetData = [("Option", 3.0)]
        ]
  am@AssetMarket{..} <- assetSetup p TrustModel_Bilateral assetInfo

  -- Cannot exercise before maturity
  submitMustFail reuters do
    exerciseByKeyCmd @EquityOptionExerciseRule (Set.singleton reuters)
      EquityOptionExercise_Lifecycle with
        optionCid = optionMap ! "Option"
        underlyingPrice = None
        entitlementIdLabel = "Option:Exercise"
        settlementDate = date 2020 Feb 3

  -- Create exercise lifecycle data
  setTime (time (date 2020 Feb 1) 0 0 0)

  (entitlementCid, lifecycleEffectsCid) <- submit reuters do
    exerciseByKeyCmd @EquityOptionExerciseRule (Set.singleton reuters)
      EquityOptionExercise_Lifecycle with
        optionCid = optionMap ! "Option"
        underlyingPrice = Some 15.0
        entitlementIdLabel = "Option:Exercise"
        settlementDate = date 2020 Feb 3

  -- Exercise option by owner
  [exerciseEntitlementCid] <- submit alice do
    exerciseByKeyCmd @AssetLifecycleRule (accountMap ! "Alice@AcmeBank").id AssetLifecycle_Process with
      depositCid = depositMap ! "Alice@AcmeBank:Option:0:3.0", lifecycleEffectsCid, optAccounts = None, consumingDepositCids = []

  matchAssetDeposit exerciseEntitlementCid acmeBank alice "Option:Exercise" 0 3.0
  optDeposit <- queryContractId alice (depositMap ! "Alice@AcmeBank:Option:0:3.0")
  optDeposit === None

  -- Exercise not possible on the next day
  setTime (time (date 2020 Feb 2) 0 0 0)
  submit reuters do
    exerciseCmd lifecycleEffectsCid Archive

  submitMustFail charlie do
    exerciseByKeyCmd @AssetLifecycleRule (accountMap ! "Charlie@AcmeBank").id AssetLifecycle_Process with
      depositCid = depositMap ! "Charlie@AcmeBank:Option:0:3.0", lifecycleEffectsCid, optAccounts = None, consumingDepositCids = []

  -- Process entitlement
  setTime (time (date 2020 Feb 3) 0 0 0)
  lifecycleEffectsCid <- submit reuters do
    exerciseCmd entitlementCid Entitlement_Lifecycle

  [depositCid] <- submit alice do
    exerciseByKeyCmd @AssetLifecycleRule (accountMap ! "Alice@AcmeBank").id AssetLifecycle_Process with
      depositCid = exerciseEntitlementCid
      lifecycleEffectsCid
      optAccounts = None
      consumingDepositCids = []

  -- Checks
  matchAssetDeposit depositCid acmeBank alice "USD" 0 15000.0
  optEntitlement <- queryContractId alice exerciseEntitlementCid
  optEntitlement === None

testExercise_PHYSICAL = script do
  setTime (time (date 2020 Jan 1) 0 0 0)

  p@Parties{..} <- partySetup

  let observers = Set.fromList [alice, acmeBank]
  let stockInfo = [ StockInfo with stockLabel = "DAH", ccyLabel = "USD"]
  let optionInfo =
        [ OptionInfo with
            optionLabel = "Option"
            undLabel = "DAH"
            optionType = CALL
            exerciseType = EUROPEAN
            strike = 10.0
            contractSize = 1000.0
            maturity = date 2020 Feb 1
            settlementType = PHYSICAL
        ]
  InstrumentMarket{..} <- instrumentSetup reuters observers stockInfo optionInfo

  let assetInfo =
        [ AssetInfo with provider = acmeBank, owner = alice, accountType = None, assetData = [("Option", 3.0), ("USD", 30000.0)]
        ]
  am@AssetMarket{..} <- assetSetup p TrustModel_Bilateral assetInfo

  -- Create exercise lifecycle data
  setTime (time (date 2020 Feb 1) 0 0 0)

  (entitlementCid, lifecycleEffectsCid) <- submit reuters do
    exerciseByKeyCmd @EquityOptionExerciseRule (Set.singleton reuters)
      EquityOptionExercise_Lifecycle with
        optionCid = optionMap ! "Option"
        underlyingPrice = None
        entitlementIdLabel = "Option:Exercise"
        settlementDate = date 2020 Feb 3

  -- Exercise option by owner
  [exerciseEntitlementCid] <- submit alice do
    exerciseByKeyCmd @AssetLifecycleRule (accountMap ! "Alice@AcmeBank").id AssetLifecycle_Process with
      depositCid = depositMap ! "Alice@AcmeBank:Option:0:3.0", lifecycleEffectsCid, optAccounts = None, consumingDepositCids = []

  matchAssetDeposit exerciseEntitlementCid acmeBank alice "Option:Exercise" 0 3.0
  optDeposit <- queryContractId alice (depositMap ! "Alice@AcmeBank:Option:0:3.0")
  optDeposit === None

  -- Process entitlement
  setTime (time (date 2020 Feb 3) 0 0 0)
  lifecycleEffectsCid <- submit reuters do
    exerciseCmd entitlementCid Entitlement_Lifecycle

  [depositCid] <- submit alice do
    exerciseByKeyCmd @AssetLifecycleRule (accountMap ! "Alice@AcmeBank").id AssetLifecycle_Process with
      depositCid = exerciseEntitlementCid
      lifecycleEffectsCid
      optAccounts = None
      consumingDepositCids = [depositMap ! "Alice@AcmeBank:USD:0:30000.0"]

  -- Checks
  matchAssetDeposit depositCid acmeBank alice "DAH" 0 3000.0
  optEntitlement <- queryContractId alice exerciseEntitlementCid
  optEntitlement === None

test_Dvp = script do
  setTime (time (date 2020 Jan 1) 0 0 0)

  p@Parties{..} <- partySetup

  let observers = Set.fromList [charlie, alice]
  let stockInfo = [ StockInfo with stockLabel = "DAH", ccyLabel = "USD"]
  let optionInfo =
        [ OptionInfo with
            optionLabel = "Option"
            undLabel = "DAH"
            optionType = CALL
            exerciseType = EUROPEAN
            strike = 10.0
            contractSize = 1000.0
            maturity = date 2020 Feb 1
            settlementType = PHYSICAL
        ]
  InstrumentMarket{..} <- instrumentSetup reuters observers stockInfo optionInfo

  let tradeInfo =
        [ TradeInfo with
            buyer = charlie
            seller = alice
            dvpInfo =
              [ DvpInfo with
                  tradeIdLabel = "Dvp:0"
                  settlementDate = None
                  paymentData = [("Option", 3.0)]
                  deliveryData = []
              ]
        ]
  dm@DvpMarket{..} <- dvpSetup p TrustModel_Bilateral tradeInfo

  -- Create exercise lifecycle data
  setTime (time (date 2020 Feb 1) 0 0 0)

  (entitlementCid, lifecycleEffectsCid) <- submit reuters do
    exerciseByKeyCmd @EquityOptionExerciseRule (Set.singleton reuters)
      EquityOptionExercise_Lifecycle with
        optionCid = optionMap ! "Option"
        underlyingPrice = None
        entitlementIdLabel = "Option:Exercise"
        settlementDate = date 2020 Feb 3

  -- Exercise option
  dvp1Cid <- submit alice do
    exerciseByKeyCmd @DvpLifecycleRule (maIdMap ! "Charlie&Alice") DvpLifecycle_Process with
      dvpCid = dvpMap ! "Charlie&Alice:Dvp:0", lifecycleEffectsCid, ctrl = alice

  -- Checks
  Some dvp1 <- queryContractId alice dvp1Cid
  dvp1.tradeId.version === 1
  dvp1.payments !! 0 === initAsset reuters "Option:Exercise" 0 3.0
  dvp1.deliveries === []

  -- Process entitlement
  setTime (time (date 2020 Feb 3) 0 0 0)
  lifecycleEffectsCid <- submit reuters do
    exerciseCmd entitlementCid Entitlement_Lifecycle

  dvp2Cid <- submit alice do
    exerciseByKeyCmd @DvpLifecycleRule (maIdMap ! "Charlie&Alice") DvpLifecycle_Process with
      dvpCid = dvp1Cid, lifecycleEffectsCid, ctrl = alice

  -- Checks
  Some dvp2 <- queryContractId alice dvp2Cid
  dvp2.tradeId.version === 2
  dvp2.payments !! 0 === initAsset reuters "DAH" 0 3000.0
  dvp2.deliveries !! 0 === initAsset reuters "USD" 0 30000.0
