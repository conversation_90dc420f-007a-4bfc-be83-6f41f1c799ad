-- Copyright (c) 2019, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0

module Test.Finance.Instrument.Equity.Stock where

import DA.Assert
import DA.Date
import DA.List
import DA.Set as Set
import qualified DA.Map as Map
import DA.Time
import Daml.Script

import DA.Finance.Asset.Lifecycle
import DA.Finance.Instrument.Entitlement
import DA.Finance.Instrument.Equity.CashDividend
import DA.Finance.Instrument.Equity.Stock.Lifecycle
import DA.Finance.Instrument.Equity.StockSplit
import DA.Finance.Trade.Dvp.Lifecycle

import Test.Finance.Asset
import Test.Finance.Market.Asset
import Test.Finance.Market.Dvp
import Test.Finance.Market.Instrument
import Test.Finance.Market.Party
import Test.Finance.Types
import Test.Finance.Utils

testEquityCashDividend = script do
  setTime (time (date 2020 Jan 1) 0 0 0)

  p@Parties{..} <- partySetup

  let assetInfo =
        [ AssetInfo with provider = acmeBank, owner = alice, accountType = Some SHARE, assetData = [("DAH", 50.0)]
        , AssetInfo with provider = acmeBank, owner = alice, accountType = Some CASH, assetData = []
        , AssetInfo with provider = csd, owner = acmeBank, accountType = Some SHARE, assetData = [("DAH", 50.0)]
        , AssetInfo with provider = csd, owner = acmeBank, accountType = Some CASH, assetData = []
        ]
  am@AssetMarket{..} <- assetSetup p TrustModel_Bilateral assetInfo

  let stockInfo = [ StockInfo with ccyLabel = "USD", stockLabel = "DAH"]
  let observers = Set.fromList $ map (.owner) assetInfo
  InstrumentMarket{..} <- instrumentSetup reuters observers stockInfo []

  let tradeInfo =
        [ TradeInfo with
            buyer = charlie
            seller = alice
            dvpInfo =
              [ DvpInfo with
                  tradeIdLabel = "Dvp:0"
                  settlementDate = None
                  paymentData = [("USD", 1000.0)]
                  deliveryData = [("DAH", 50.0)]
              ]
        ]
  dm@DvpMarket{..} <- dvpSetup p TrustModel_Bilateral tradeInfo

  -- Dividend
  dividendCid <- submit reuters do
    createCmd EquityCashDividend with
      id = initAssetId reuters "DAH" 0
      exDate = date 2020 Jan 1
      settlementDate = date 2020 Jan 5
      perShare = 0.4
      observers = Set.fromList $ map (.owner) assetInfo

  -- Process equity cash dividend corporate action.
  (_, entitlementCid, lifecycleEffectsCid) <- submit reuters do
    exerciseByKeyCmd @EquityStockCashDividendRule (Set.singleton reuters)
      EquityStockCashDividend_Lifecycle with
        dividendCid
        entitlementIdLabel = "CashDividend:DAH:USD"

  [assetDeposit1Cid, entitlementDeposit1Cid] <- submit alice do
    exerciseByKeyCmd @AssetLifecycleRule (accountMap ! "Alice@AcmeBank:SHARE").id AssetLifecycle_Process with
      depositCid = depositMap ! "Alice@AcmeBank:SHARE:DAH:0:50.0", lifecycleEffectsCid, optAccounts = None, consumingDepositCids = []

  [assetDeposit2Cid, entitlementDeposit2Cid] <- submit acmeBank do
    exerciseByKeyCmd @AssetLifecycleRule (accountMap ! "AcmeBank@CSD:SHARE").id AssetLifecycle_Process with
      depositCid = depositMap ! "AcmeBank@CSD:SHARE:DAH:0:50.0", lifecycleEffectsCid, optAccounts = None, consumingDepositCids = []

  dvp1Cid <- submit alice do
    exerciseByKeyCmd @DvpLifecycleRule (maIdMap ! "Charlie&Alice") DvpLifecycle_Process with
      dvpCid = dvpMap ! "Charlie&Alice:Dvp:0", lifecycleEffectsCid, ctrl = alice

  -- Checks
  submitMustFail reuters do
    exerciseByKeyCmd @EquityStockCashDividendRule (Set.singleton reuters)
      EquityStockCashDividend_Lifecycle with
        dividendCid
        entitlementIdLabel = "CashDividend:DAH:USD"

  matchAssetDeposit assetDeposit1Cid acmeBank alice "DAH" 1 50.0
  matchAssetDeposit assetDeposit2Cid csd acmeBank "DAH" 1 50.0
  matchAssetDeposit entitlementDeposit1Cid acmeBank alice "CashDividend:DAH:USD" 0 50.0
  matchAssetDeposit entitlementDeposit2Cid csd acmeBank "CashDividend:DAH:USD" 0 50.0
  let optDeposit = Map.lookup "Alice@AcmeBank:DAH:0:50" depositMap
  optDeposit === None
  let optDvp = Map.lookup "Alice&Bob:Dvp:0" dvpMap
  optDvp === None

  Some dvp1 <- queryContractId alice dvp1Cid
  dvp1.tradeId.version === 1
  dvp1.deliveries !! 0 === initAsset reuters "DAH" 1 50.0
  dvp1.deliveries !! 1 === initAsset reuters "CashDividend:DAH:USD" 0 50.0

  -- Process entitlement
  setTime (time (date 2020 Jan 5) 0 0 0)
  lifecycleEffectsCid <- submit reuters do
    exerciseCmd entitlementCid Entitlement_Lifecycle

  [cashDeposit1Cid] <- submit alice do
    exerciseByKeyCmd @AssetLifecycleRule (accountMap ! "Alice@AcmeBank:SHARE").id AssetLifecycle_Process with
      depositCid = entitlementDeposit1Cid
      lifecycleEffectsCid
      optAccounts = Some [accountMap ! "Alice@AcmeBank:CASH"]
      consumingDepositCids = []
  [cashDeposit2Cid] <- submit acmeBank do
    exerciseByKeyCmd @AssetLifecycleRule (accountMap ! "AcmeBank@CSD:SHARE").id AssetLifecycle_Process with
      depositCid = entitlementDeposit2Cid
      lifecycleEffectsCid
      optAccounts = Some [accountMap !  "AcmeBank@CSD:CASH"]
      consumingDepositCids = []

  dvp2Cid <- submit alice do
    exerciseByKeyCmd @DvpLifecycleRule (maIdMap ! "Charlie&Alice") DvpLifecycle_Process with
      dvpCid = dvp1Cid, lifecycleEffectsCid, ctrl = alice

  -- Checks
  matchAssetDeposit cashDeposit1Cid acmeBank alice "USD" 0 20.0
  matchAssetDeposit cashDeposit2Cid csd acmeBank "USD" 0 20.0
  optEntitlement <- queryContractId alice entitlementDeposit1Cid
  optEntitlement === None
  optDvp <- queryContractId alice dvp1Cid
  optDvp === None

  Some dvp2 <- queryContractId alice dvp2Cid
  dvp2.tradeId.version === 2
  dvp2.payments !! 0 === initAsset reuters "USD" 0 980.0


testEquityStockSplit = script do
  setTime (time (date 2020 Jan 1) 0 0 0)

  p@Parties{..} <- partySetup

  let assetInfo = [ AssetInfo with provider = acmeBank, owner = alice, accountType = None, assetData = [("DAH", 50.0)] ]
  am@AssetMarket{..} <- assetSetup p TrustModel_Bilateral assetInfo

  let stockInfo = [ StockInfo with ccyLabel = "USD", stockLabel = "DAH"]
  let observers = Set.fromList $ map (.owner) assetInfo
  InstrumentMarket{..} <- instrumentSetup reuters observers stockInfo []

  let tradeInfo =
        [ TradeInfo with
            buyer = charlie
            seller = alice
            dvpInfo =
              [ DvpInfo with
                  tradeIdLabel = "Dvp:0"
                  settlementDate = None
                  paymentData = [("USD", 1000.0)]
                  deliveryData = [("DAH", 50.0)]
              ]
        ]
  dm@DvpMarket{..} <- dvpSetup p TrustModel_Bilateral tradeInfo

  -- Stock split
  stockSplitCid <- submit reuters do
    createCmd EquityStockSplit with
      id = initAssetId reuters "DAH" 0
      exDate = date 2020 Jan 1
      rFactor = 0.5
      observers = Set.fromList $ map (.owner) assetInfo

  (_, lifecycleEffectsCid) <- submit reuters do
    exerciseByKeyCmd @EquityStockSplitRule (Set.singleton reuters)
       EquityStockSplit_Lifecycle with stockSplitCid

  [depositCid] <- submit alice do
    exerciseByKeyCmd @AssetLifecycleRule (accountMap ! "Alice@AcmeBank").id AssetLifecycle_Process with
      depositCid = depositMap ! "Alice@AcmeBank:DAH:0:50.0", lifecycleEffectsCid, optAccounts = None, consumingDepositCids = []

  dvpCid <- submit alice do
    exerciseByKeyCmd @DvpLifecycleRule (maIdMap ! "Charlie&Alice") DvpLifecycle_Process with
      dvpCid = dvpMap ! "Charlie&Alice:Dvp:0", lifecycleEffectsCid, ctrl = alice

  matchAssetDeposit depositCid acmeBank alice "DAH" 1 100.0
  optDeposit <- queryContractId alice (depositMap ! "Alice@AcmeBank:DAH:0:50.0")
  optDeposit === None
  optDvp <- queryContractId alice (dvpMap ! "Charlie&Alice:Dvp:0")
  optDvp === None

  Some dvp <- queryContractId alice dvpCid
  dvp.tradeId.version === 1
  dvp.deliveries !! 0 === initAsset reuters "DAH" 1 100.0
