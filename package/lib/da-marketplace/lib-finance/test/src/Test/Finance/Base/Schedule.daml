module Test.Finance.Base.Schedule where

import DA.Assert
import DA.Date as D
import DA.List
import Daml.Script

import DA.Finance.Base.HolidayCalendar
import DA.Finance.Base.RollConvention
import DA.Finance.Base.Schedule

cals : [HolidayCalendarData]
cals = [ HolidayCalendarData {
            id = "USNY",
            weekend = [Saturday, Sunday],
            holidays = [D.date 2019 Dec 19]
          }
        ]

example : PeriodicSchedule
example = PeriodicSchedule {
          businessDayAdjustment =
            BusinessDayAdjustment {
              calendarIds = ["USNY"],
              convention = MODFOLLOWING
            },
          effectiveDateBusinessDayAdjustment = None,
          terminationDateBusinessDayAdjustment = None,
          frequency =
            Frequency {
              rollConvention = DOM 30,
              period = M,
              periodMultiplier = 3
            },
          effectiveDate = D.date 2018 Nov 15,
          firstRegularPeriodStartDate = Some $ D.date 2018 Nov 30,
          lastRegularPeriodEndDate = Some $ D.date 2019 Nov 30,
          stubPeriodType = None,
          terminationDate = D.date 2019 Dec 15
        }

expectedResultBothStub : Schedule
expectedResultBothStub =
        [ SchedulePeriod {
            unadjustedStartDate = D.date 2018 Nov 15,
            unadjustedEndDate = D.date 2018 Nov 30,
            adjustedStartDate = D.date 2018 Nov 15,
            adjustedEndDate = D.date 2018 Nov 30
          }
        , SchedulePeriod {
            unadjustedStartDate = D.date 2018 Nov 30,
            unadjustedEndDate = D.date 2019 Feb 28,
            adjustedStartDate = D.date 2018 Nov 30,
            adjustedEndDate = D.date 2019 Feb 28
          }
        , SchedulePeriod {
            unadjustedStartDate = D.date 2019 Feb 28,
            unadjustedEndDate = D.date 2019 May 30,
            adjustedStartDate = D.date 2019 Feb 28,
            adjustedEndDate = D.date 2019 May 30
          }
        , SchedulePeriod {
            unadjustedStartDate = D.date 2019 May 30,
            unadjustedEndDate = D.date 2019 Aug 30,
            adjustedStartDate = D.date 2019 May 30,
            adjustedEndDate = D.date 2019 Aug 30
          }
        , SchedulePeriod {
            unadjustedStartDate = D.date 2019 Aug 30,
            unadjustedEndDate = D.date 2019 Nov 30,
            adjustedStartDate = D.date 2019 Aug 30,
            adjustedEndDate = D.date 2019 Nov 29
          }
        , SchedulePeriod {
            unadjustedStartDate = D.date 2019 Nov 30,
            unadjustedEndDate = D.date 2019 Dec 15,
            adjustedStartDate = D.date 2019 Nov 29,
            adjustedEndDate = D.date 2019 Dec 16
          }
        ]

setDates : Date -> Optional Date -> Optional Date -> Date -> PeriodicSchedule -> PeriodicSchedule
setDates effective firstRegularStart lastRegularEnd termination schedule =
  schedule {
    effectiveDate = effective,
    firstRegularPeriodStartDate = firstRegularStart,
    lastRegularPeriodEndDate = lastRegularEnd,
    terminationDate = termination
  }

setStubType : StubPeriodTypeEnum -> PeriodicSchedule -> PeriodicSchedule
setStubType stubType schedule =
  schedule { stubPeriodType = Some stubType }

test_base = script do
  createSchedule cals example === expectedResultBothStub

test_reg_periods_only = script do
  -- Regular periods only
  let testCase = setDates (D.date 2018 Nov 30) None None (D.date 2019 Nov 30) example
  let expectedResult = (tail . init) expectedResultBothStub
  createSchedule cals testCase === expectedResult

test_regStart_equal_effective_smaller_termination = script do
  -- RegStart == Effective < Termination
  let testCase = setDates (D.date 2018 Nov 30) (Some $ D.date 2018 Nov 30) None (D.date 2019 Nov 30) example
  let expectedResult = (tail . init) expectedResultBothStub
  createSchedule cals testCase === expectedResult

test_regStart_smaller_effective_smaller_termination = script do
  -- RegStart < Effective < Termination
  let testCase = setDates (D.date 2018 Nov 15) (Some $ D.date 2018 Nov 30) None (D.date 2019 Nov 30) example
  let expectedResult = init expectedResultBothStub
  createSchedule cals testCase === expectedResult

test_effective_smaller_regStart_equal_termination = script do
  -- Effective < RegStart == Termination
  let testCase = setDates (D.date 2018 Nov 15) (Some $ D.date 2019 Nov 30) None (D.date 2019 Nov 30) example
  let expectedResult =
        [ SchedulePeriod {
            unadjustedStartDate = D.date 2018 Nov 15,
            unadjustedEndDate = D.date 2019 Nov 30,
            adjustedStartDate = D.date 2018 Nov 15,
            adjustedEndDate = D.date 2019 Nov 29
          }
        ]
  createSchedule cals testCase === expectedResult

test_regEnd_equal_effective_smaller_termination = script do
  -- RegEnd == Effective < Termination
  let testCase = setDates (D.date 2018 Nov 30) None (Some $ D.date 2018 Nov 30) (D.date 2019 Nov 30) example
  let expectedResult =
        [ SchedulePeriod {
            unadjustedStartDate = D.date 2018 Nov 30,
            unadjustedEndDate = D.date 2019 Nov 30,
            adjustedStartDate = D.date 2018 Nov 30,
            adjustedEndDate = D.date 2019 Nov 29
          }
        ]
  createSchedule cals testCase === expectedResult

test_effective_smaller_regEnd_smaller_termination = script do
  -- Effective < RegEnd < Termination
  let testCase = setDates (D.date 2018 Nov 30) None (Some $ D.date 2019 Nov 30) (D.date 2019 Dec 15) example
  createSchedule cals testCase === tail expectedResultBothStub

test_effective_smaller_termination_equal_regEnd = script do
  -- Effective < Termination == RegEnd
  let testCase = setDates (D.date 2018 Nov 30) None (Some $ D.date 2019 Nov 30) (D.date 2019 Nov 30) example
  let expectedResult = (tail . init) expectedResultBothStub
  createSchedule cals testCase === expectedResult

test_effective_smaller_firstReg_equal_lastReg_smaller_termination = script do
  -- Effective < FirstRegular == LastRegular < Termination
  let testCase = setDates (D.date 2018 Oct 15) (Some $ D.date 2019 Jan 30) (Some $ D.date 2019 Jan 30) (D.date 2019 Dec 15) example
  let expectedResult =
        [ SchedulePeriod {
            unadjustedStartDate = D.date 2018 Oct 15,
            unadjustedEndDate = D.date 2019 Jan 30,
            adjustedStartDate = D.date 2018 Oct 15,
            adjustedEndDate = D.date 2019 Jan 30
          }
        , SchedulePeriod {
            unadjustedStartDate = D.date 2019 Jan 30,
            unadjustedEndDate = D.date 2019 Dec 15,
            adjustedStartDate = D.date 2019 Jan 30,
            adjustedEndDate = D.date 2019 Dec 16
          }
        ]
  createSchedule cals testCase === expectedResult

test_shortInitial = script do
  -- Implicit Stubs
  -- exact match
  let testCase =
        setDates (D.date 2018 Nov 30) None None (D.date 2019 Nov 30)
        $ setStubType SHORT_INITIAL
        $ example
  let expectedResult = (tail . init) expectedResultBothStub
  createSchedule cals testCase === expectedResult

test_longFinal = script do
  let testCase =
        setDates (D.date 2018 Nov 30) None None (D.date 2019 Nov 30)
        $ setStubType LONG_FINAL
        $ example
  let expectedResult = (tail . init) expectedResultBothStub
  createSchedule cals testCase === expectedResult

test_twoPeriods_shortInitial = script do
  -- Two Periods SHORT_INITIAL
  let testCase =
        setDates (D.date 2018 Nov 15) None None (D.date 2019 Feb 28)
        $ setStubType SHORT_INITIAL
        $ example
  let expectedResult = take 2 expectedResultBothStub
  createSchedule cals testCase === expectedResult

test_twoPeriods_longInitial = script do
  -- Two Periods LONG_INITIAL
  let testCase =
        setDates (D.date 2018 Nov 15) None None (D.date 2019 Feb 28)
        $ setStubType LONG_INITIAL
        $ example
  let expectedResult =
        [ SchedulePeriod {
            unadjustedStartDate = D.date 2018 Nov 15,
            unadjustedEndDate = D.date 2019 Feb 28,
            adjustedStartDate = D.date 2018 Nov 15,
            adjustedEndDate = D.date 2019 Feb 28
          }
        ]
  createSchedule cals testCase === expectedResult

test_singlePeriod_shortInitial = script do
  -- Single Period SHORT_INITIAL
  let testCase =
        setDates (D.date 2018 Nov 15) None None (D.date 2018 Nov 30)
        $ setStubType SHORT_INITIAL
        $ example
  let expectedResult = take 1 expectedResultBothStub
  createSchedule cals testCase === expectedResult

test_singlePeriod_longInitial = script do
  -- Single Period LONG_INITIAL
  let testCase =
        setDates (D.date 2018 Nov 15) None None (D.date 2018 Nov 30)
        $ setStubType LONG_INITIAL
        $ example
  let expectedResult = take 1 expectedResultBothStub
  createSchedule cals testCase === expectedResult

test_twoPeriods_shortFinal = script do
  -- Two Periods SHORT_FINAL
  let testCase =
        setDates (D.date 2019 Aug 30) None None (D.date 2019 Dec 15)
        $ setStubType SHORT_FINAL
        $ example
  let expectedResult = drop 4 expectedResultBothStub
  createSchedule cals testCase === expectedResult

test_twoPeriods_longFinal = script do
  -- Two Periods LONG_FINAL
  let testCase =
        setDates (D.date 2019 Aug 30) None None (D.date 2019 Dec 15)
        $ setStubType LONG_FINAL
        $ example
  let expectedResult =
        [ SchedulePeriod {
            unadjustedStartDate = D.date 2019 Aug 30,
            unadjustedEndDate = D.date 2019 Dec 15,
            adjustedStartDate = D.date 2019 Aug 30,
            adjustedEndDate = D.date 2019 Dec 16
          }
        ]
  createSchedule cals testCase === expectedResult

test_singlePeriod_shortFinal = script do
  -- Single Period SHORT_FINAL
  let testCase =
        setDates (D.date 2019 Nov 30) None None (D.date 2019 Dec 15)
        $ setStubType SHORT_FINAL
        $ example
  let expectedResult =
        [ SchedulePeriod {
            unadjustedStartDate = D.date 2019 Nov 30,
            unadjustedEndDate = D.date 2019 Dec 15,
            adjustedStartDate = D.date 2019 Nov 29,
            adjustedEndDate = D.date 2019 Dec 16
          }
        ]
  createSchedule cals testCase === expectedResult

test_singlePeriod_longFinal = script do
  -- Single Period LONG_FINAL
  let testCase =
        setDates (D.date 2019 Nov 30) None None (D.date 2019 Dec 15)
        $ setStubType LONG_FINAL
        $ example
  let expectedResult =
        [ SchedulePeriod {
            unadjustedStartDate = D.date 2019 Nov 30,
            unadjustedEndDate = D.date 2019 Dec 15,
            adjustedStartDate = D.date 2019 Nov 29,
            adjustedEndDate = D.date 2019 Dec 16
          }
        ]
  createSchedule cals testCase === expectedResult
