-- Copyright (c) 2019, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0

module Test.Finance.Trade.Dvp where

import DA.Assert
import DA.List
import DA.Set as Set
import Daml.Script

import DA.Finance.Trade.Dvp.Settlement
import DA.Finance.Trade.SettlementInstruction

import Test.Finance.Asset
import Test.Finance.Market.Asset
import Test.Finance.Market.Dvp
import Test.Finance.Market.Party
import Test.Finance.Types
import Test.Finance.Utils

-- | Allocate to instruction.
submitAllocate : AssetMarket -> ContractId SettlementInstruction -> Party -> Text -> Script (ContractId SettlementInstruction)
submitAllocate AssetMarket{..} instrCid owner deposit =
  submit owner do
    exerciseCmd instrCid SettlementInstruction_AllocateNext with depositCid = (depositMap ! deposit), ctrl = owner

-- | Test direct (i.e. a single step) Dvp settlement, where both counterparties
-- have an account with the same provider.
testDvpSettlementDirect : Parties -> TrustModel -> TrustModel -> Script DvpSettlement_Process_Result
testDvpSettlementDirect p@Parties{..} assetTrustModel dvpTrustModel = do
  -- Assets & Dvp
  let assetInfo =
        [ AssetInfo with provider = acmeBank, owner = alice, accountType = None, assetData = [("USD", 1000.0)]
        , AssetInfo with provider = acmeBank, owner = bob, accountType = None, assetData = [("DAH", 50.0)]
        ]
  am@AssetMarket{..} <- assetSetup p assetTrustModel assetInfo
  let tradeInfo =
        [ TradeInfo with
            buyer = alice
            seller = bob
            dvpInfo =
              [ DvpInfo with
                  tradeIdLabel = "Dvp:0"
                  settlementDate = None
                  paymentData = [("USD", 1000.0)]
                  deliveryData = [("DAH", 50.0)]
              ]
        ]
  dm@DvpMarket{..} <- dvpSetup p dvpTrustModel tradeInfo

  -- Instruct Dvp
  result <- submit alice do
              exerciseByKeyCmd @DvpInstructionRule (maIdMap ! "Alice&Bob") DvpInstruction_Process with
                dvpCid = dvpMap ! "Alice&Bob:Dvp:0"
                paymentSteps = [[ initSettlementDetails accountMap acmeBank alice bob ]]
                deliverySteps = [[ initSettlementDetails accountMap acmeBank bob alice ]]
                ctrl = alice

  -- Allocate to instructions
  paymentInstructionCid <- submitAllocate am (head result.paymentInstructionCids) alice "Alice@AcmeBank:USD:0:1000.0"
  deliveryInstructionCid <- submitAllocate am (head result.deliveryInstructionCids) bob "Bob@AcmeBank:DAH:0:50.0"

  -- Settle Dvp
  result <- submit bob do
              exerciseByKeyCmd @DvpSettlementRule (maIdMap ! "Alice&Bob") DvpSettlement_Process with
                dvpCid = result.dvpCid
                paymentInstructionCids = [paymentInstructionCid]
                deliveryInstructionCids = [deliveryInstructionCid]
                ctrl = bob

  -- Checks
  matchAssetDeposit (head $ head result.paymentDepositCids) acmeBank bob "USD" 0 1000.0
  matchAssetDeposit (head $ head result.deliveryDepositCids) acmeBank alice "DAH" 0 50.0

  optDeposit <- queryContractId alice (depositMap ! "Alice@AcmeBank:USD:0:1000.0")
  optDeposit === None
  optDeposit <- queryContractId bob (depositMap ! "Bob@AcmeBank:DAH:0:50.0")
  optDeposit === None

  return result

testDvpSettlementDirect_Bilateral = script do
  p@Parties{..} <- partySetup
  result <- testDvpSettlementDirect p TrustModel_Bilateral TrustModel_Bilateral

  Some c <- queryContractId alice result.dvpCid
  c.masterAgreement.id.signatories === Set.fromList [alice, bob]

  Some c <- queryContractId bob (head $ head result.paymentDepositCids)
  c.account.id.signatories === Set.fromList [acmeBank, bob]

  Some c <- queryContractId alice (head $ head result.deliveryDepositCids)
  c.account.id.signatories === Set.fromList [acmeBank, alice]

testDvpSettlementDirect_Unilateral = script do
  p@Parties{..} <- partySetup
  result <- testDvpSettlementDirect p TrustModel_Unilateral TrustModel_Bilateral

  Some c <- queryContractId alice result.dvpCid
  c.masterAgreement.id.signatories === Set.fromList [alice, bob]

  Some c <- queryContractId bob (head $ head result.paymentDepositCids)
  c.account.id.signatories === Set.fromList [acmeBank]

  Some c <- queryContractId alice (head $ head result.deliveryDepositCids)
  c.account.id.signatories === Set.fromList [acmeBank]

-- testDvpSettlementDirect_Agent = script do
--   p@Parties{..} <- partySetup
--   result <- testDvpSettlementDirect p TrustModel_Bilateral TrustModel_Agent

--   Some c <- queryContractId alice result.dvpCid
--   c.masterAgreement.id.signatories === Set.fromList [agent]

--   Some c <- queryContractId bob (head $ head result.paymentDepositCids)
--   c.account.id.signatories === Set.fromList [acmeBank, bob]

--   Some c <- queryContractId alice (head $ head result.deliveryDepositCids)
--   c.account.id.signatories === Set.fromList [acmeBank, alice]


-- | Test Dvp settlement up and down the hierarchy, i.e. the counterparties
-- do not have an account with the same provider.
testDvpSettlementChain : Parties -> TrustModel -> TrustModel -> Script ()
testDvpSettlementChain p@Parties{..} assetTrustModel dvpTrustModel = do
  -- Assets & Dvp
  let assetInfo =
        [ AssetInfo with provider = gencoBank, owner = charlie, accountType = None, assetData = [("USD", 1000.0)]
        , AssetInfo with provider = cb, owner = gencoBank, accountType = None, assetData = [("USD", 1000.0)]
        , AssetInfo with provider = cb, owner = acmeBank, accountType = None, assetData = [] -- Required for trade relationship
        , AssetInfo with provider = acmeBank, owner = acmeBank, accountType = None, assetData = [("USD", 1000.0)]
        , AssetInfo with provider = acmeBank, owner = alice, accountType = None, assetData = [("DAH", 50.0)]
        , AssetInfo with provider = csd, owner = acmeBank, accountType = None, assetData = [("DAH", 50.0)]
        , AssetInfo with provider = csd, owner = gencoBank, accountType = None, assetData = [] -- Required for trade relationship
        , AssetInfo with provider = gencoBank, owner = gencoBank, accountType = None, assetData = [("DAH", 50.0)]
        ]
  am@AssetMarket{..} <- assetSetup p assetTrustModel assetInfo
  let tradeInfo =
        [ TradeInfo with
            buyer = charlie
            seller = alice
            dvpInfo =
              [ DvpInfo with
                  tradeIdLabel = "Dvp:0"
                  settlementDate = None
                  paymentData = [("USD", 1000.0)]
                  deliveryData = [("DAH", 50.0)]
              ]
        ]
  dm@DvpMarket{..} <- dvpSetup p dvpTrustModel tradeInfo

  -- Instruct Dvp
  result <- submit charlie do
              exerciseByKeyCmd @DvpInstructionRule (maIdMap ! "Charlie&Alice") DvpInstruction_Process with
                dvpCid = dvpMap ! "Charlie&Alice:Dvp:0"
                paymentSteps =
                  [[ initSettlementDetails accountMap gencoBank charlie gencoBank
                   , initSettlementDetails accountMap cb gencoBank acmeBank
                   , initSettlementDetails accountMap acmeBank acmeBank alice
                  ]]
                deliverySteps =
                  [[ initSettlementDetails accountMap acmeBank alice acmeBank
                  , initSettlementDetails accountMap csd acmeBank gencoBank
                  , initSettlementDetails accountMap gencoBank gencoBank charlie
                  ]]
                ctrl = charlie

  -- Allocate to instructions
  let paymentInstructionCid = head result.paymentInstructionCids
  paymentInstructionCid <- submitAllocate am paymentInstructionCid charlie "Charlie@GencoBank:USD:0:1000.0"
  paymentInstructionCid <- submitAllocate am paymentInstructionCid gencoBank "GencoBank@CB:USD:0:1000.0"
  paymentInstructionCid <- submitAllocate am paymentInstructionCid acmeBank "AcmeBank@AcmeBank:USD:0:1000.0"

  let deliveryInstructionCid = head result.deliveryInstructionCids
  deliveryInstructionCid <- submitAllocate am deliveryInstructionCid alice "Alice@AcmeBank:DAH:0:50.0"
  deliveryInstructionCid <- submitAllocate am deliveryInstructionCid acmeBank "AcmeBank@CSD:DAH:0:50.0"
  deliveryInstructionCid <- submitAllocate am deliveryInstructionCid gencoBank "GencoBank@GencoBank:DAH:0:50.0"

  -- Settle Dvp
  result <- submit alice do
              exerciseByKeyCmd @DvpSettlementRule (maIdMap ! "Charlie&Alice") DvpSettlement_Process with
                dvpCid = result.dvpCid
                paymentInstructionCids = [paymentInstructionCid]
                deliveryInstructionCids = [deliveryInstructionCid]
                ctrl = alice

  -- Checks
  matchAssetDeposit (result.paymentDepositCids !! 0 !! 0) gencoBank gencoBank "USD" 0 1000.0
  matchAssetDeposit (result.paymentDepositCids !! 0 !! 1) cb acmeBank "USD" 0 1000.0
  matchAssetDeposit (result.paymentDepositCids !! 0 !! 2) acmeBank alice "USD" 0 1000.0
  matchAssetDeposit (result.deliveryDepositCids !! 0 !! 0) acmeBank acmeBank "DAH" 0 50.0
  matchAssetDeposit (result.deliveryDepositCids !! 0 !! 1) csd gencoBank "DAH" 0 50.0
  matchAssetDeposit (result.deliveryDepositCids !! 0 !! 2) gencoBank charlie "DAH" 0 50.0

  optDeposit <- queryContractId charlie (depositMap ! "Charlie@GencoBank:USD:0:1000.0")
  optDeposit === None
  optDeposit <- queryContractId charlie (depositMap ! "GencoBank@CB:USD:0:1000.0")
  optDeposit === None
  optDeposit <- queryContractId charlie (depositMap ! "AcmeBank@AcmeBank:USD:0:1000.0")
  optDeposit === None
  optDeposit <- queryContractId alice (depositMap ! "Alice@AcmeBank:DAH:0:50.0")
  optDeposit === None
  optDeposit <- queryContractId alice (depositMap ! "AcmeBank@CSD:DAH:0:50.0")
  optDeposit === None
  optDeposit <- queryContractId alice (depositMap ! "GencoBank@GencoBank:DAH:0:50.0")
  optDeposit === None

testDvpSettlementChain_Bilateral = script do
  p@Parties{..} <- partySetup
  testDvpSettlementChain p TrustModel_Bilateral TrustModel_Bilateral

testDvpSettlementChain_Unilateral = script do
  p@Parties{..} <- partySetup
  testDvpSettlementChain p TrustModel_Unilateral TrustModel_Bilateral

-- testDvpSettlementChain_Agent = script do
--   p@Parties{..} <- partySetup
--   testDvpSettlementChain p TrustModel_Agent TrustModel_Agent
