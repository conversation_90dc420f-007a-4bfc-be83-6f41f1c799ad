-- Copyright (c) 2019, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0

module Test.Finance.Market.Instrument where

import DA.Map as Map
import DA.Set as Set
import Daml.Script

import DA.Finance.Instrument.Equity.Option
import DA.Finance.Instrument.Equity.Option.Lifecycle
import DA.Finance.Instrument.Equity.Stock
import DA.Finance.Instrument.Equity.Stock.Lifecycle

import Test.Finance.Utils

data InstrumentMarket = InstrumentMarket
  with
    stockMap : Map Text (ContractId EquityStock)
    optionMap : Map Text (ContractId EquityOption)

-- | The stock info used to set up the market.
data StockInfo = StockInfo
  with
    stockLabel : Text
    ccyLabel : Text

-- | The option info used to set up the market.
data OptionInfo = OptionInfo
  with
    optionLabel : Text
    undLabel : Text
    optionType : OptionType
    exerciseType : ExerciseType
    strike : Decimal
    contractSize : Decimal
    maturity : Date
    settlementType : SettlementType

instrumentSetup : Party -> Set Party -> [StockInfo] -> [OptionInfo] -> Script InstrumentMarket
instrumentSetup provider observers stockInfo optionInfo = do
  -- Process a single stock
  let processStock StockInfo{..} = do
        let ccy = initAssetId provider ccyLabel 0
        let id = initAssetId provider stockLabel 0
        (,) stockLabel <$> submit provider (createCmd EquityStock with observers = Set.empty, ..)

  -- Process a single option
  let processOption OptionInfo{..} = do
        let underlyingId = initAssetId provider undLabel 0
        let id = initAssetId provider optionLabel 0
        (,) optionLabel <$> submit provider (createCmd EquityOption with ..)

  let signatories = Set.singleton provider
  submit provider $ createCmd EquityStockCashDividendRule with signatories
  submit provider $ createCmd EquityStockSplitRule with signatories
  stockMap <- Map.fromList <$> mapA processStock stockInfo

  submit provider $ createCmd EquityOptionExerciseRule with signatories
  submit provider $ createCmd EquityOptionStockSplitRule with signatories
  optionMap <- Map.fromList <$> mapA processOption optionInfo

  return InstrumentMarket with ..
