# Copyright (c) 2019, Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
version: 2.1

jobs:
  daml:
    docker:
      - image: digitalasset/daml-sdk:1.17.1
    steps:
      - checkout
      - setup_remote_docker
      - run:
          name: Build Dar
          command: ./build.sh build-dars
      - run:
          name: Test Dar
          command: |
            cd test
            daml test --junit .daml/DarTests.xml
      - run:
          name: Test Trigger
          command: |
            cd test
            ./scripts/test-trigger.sh


workflows:
  Build and test:
    jobs:
      - daml
