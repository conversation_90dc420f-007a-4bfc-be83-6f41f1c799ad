module ClearingTrigger where

import DA.Action
import DA.Optional
import DA.Foldable hiding (elem, null, foldl)
import Daml.Trigger

import DA.Finance.Asset
import DA.Finance.Types

import qualified Marketplace.Listing.Model as Listing
import qualified Marketplace.Clearing.Market.Model as Clearing
import qualified Marketplace.Clearing.Model as Clearing
import qualified Marketplace.Clearing.Role as Clearing
import qualified Marketplace.Clearing.Service as Clearing
import qualified Marketplace.Clearing.Market.Service as MarketClearing
import  Marketplace.Clearing.Service

import Utils
import DA.Set qualified as Set
import DA.List (head)
import DA.List.Total (sortOn)
import DA.Time (seconds, subTime)

data ClearingState = ClearingState {
   operatorParty : Party
 }

handleClearing : Trigger ClearingState
handleClearing = Trigger
  { initialize = return $ ClearingState $ fromSome $ partyFromText "Operator"
  , updateState = \_ -> pure ()
  , rule = handleCCPRule
  , registeredTemplates = RegisteredTemplates  [ registeredTemplate @Clearing.Role
                                               , registeredTemplate @Clearing.Service
                                               , registeredTemplate @MarketClearing.Service
                                               , registeredTemplate @Listing.T
                                               , registeredTemplate @AssetDeposit
                                               , registeredTemplate @Clearing.FairValue
                                               , registeredTemplate @Clearing.FairValueCalculationRequest
                                               , registeredTemplate @Clearing.ClearedTrade
                                               , registeredTemplate @Clearing.ClearedTradeSide
                                               , registeredTemplate @Clearing.MarginCalculation
                                               , registeredTemplate @Clearing.MarkToMarketCalculation ]
  , heartbeat = None
  }

-- |Accept 'DepositTransferRequest' and 'CustodianRelationshipRequest'
handleCCPRule : Party -> TriggerA ClearingState ()
handleCCPRule party = do
  debug "Running ccp rule..."
  state <- get

  -- > Set Operator
  ccpRoles <- query @Clearing.Role
  forA_ ccpRoles
    $ \(_,ccp) -> void $ put (state { operatorParty = ccp.operator })
               >> debug ("Setting operator party to: " <> show ccp.operator)

  -- > Handle MarginCalculation
  marginCalculations <- query @Clearing.MarginCalculation
  forA_ marginCalculations (handleMarginCalculation party)

  -- > Handle MarkToMarketCalculation
  markToMarketCalculations <- query @Clearing.MarkToMarketCalculation
  let incomingMarkToMarkets = filter (\(_,mtm) -> mtm.mtmAmount < 0.0) markToMarketCalculations
      outgoingMarkToMarkets = filter (\(_,mtm) -> mtm.mtmAmount > 0.0) markToMarketCalculations

  ownedAssetDeposits <- filter (\(_,ad) -> ad.account.owner == party) <$> query @AssetDeposit

  forA_ incomingMarkToMarkets (handleIncomingMarkToMarketCalculation party)
  handleOutgoingMTMCalculations party ownedAssetDeposits outgoingMarkToMarkets

  fvRequests <- query @Clearing.FairValueCalculationRequest
  forA_ fvRequests (handleCalculationRequests party)

  clearedTrades <- query @Clearing.ClearedTrade
  mapExercise Clearing.ClearedTrade_Novate (.execution.matchId) clearedTrades


-- |Find all 'AssetDeposit' that a CCP Customer has with the CCP in a specific account
getCustomerDeposits : Party -> Party -> ((ContractId AssetDeposit, AssetDeposit) -> Bool) -> TriggerA ClearingState [ContractId AssetDeposit]
getCustomerDeposits ccp customer depositFilter = do
    state@ClearingState { operatorParty } <- get
    (_,ccpCustomer) <- fromSomeNote "getCustomerDeposits" <$> queryContractKey @Clearing.Service (operatorParty, ccp, customer)
    map fst . filter depositFilter . filter (accountFilter ccpCustomer.clearingAccount) <$> query @AssetDeposit
  where
    accountFilter account = (\(_, deposit) -> deposit.account == account)

-- |Transfer to and from the CCPCustomer's margin deposits from their clearing account.
handleMarginCalculation : Party -> ContractPair Clearing.MarginCalculation -> TriggerA ClearingState ()
handleMarginCalculation ccp (mcCid, <EMAIL>{customer, targetAmount}) = do
  debug ("resolving margin calculation: " <> show mc)
  ClearingState { operatorParty } <- get

  clearingDepositCids <- getCustomerDeposits ccp customer (\(_, deposit) -> Set.null deposit.lockers)
  marginDepositCids   <- getCustomerDeposits ccp customer (\(_, deposit) -> not $ Set.null deposit.lockers)
  emitCommands [exerciseByKeyCmd @Clearing.Service (operatorParty, ccp, customer) Clearing.PerformMarginFill with
                   calculationCid = mcCid, depositCids = clearingDepositCids, marginDepositCids]
               ([toAnyContractId mcCid] <> map toAnyContractId clearingDepositCids <> map toAnyContractId marginDepositCids)

  return ()

  where
    depositIsAccount : Id -> ContractPair AssetDeposit -> Bool
    depositIsAccount accountId (_,deposit) = deposit.account.id == accountId


-- |Transfer from from the CCPCustomer to the CCP for a MarkToMarketCalculation
handleIncomingMarkToMarketCalculation
  : Party
  -> ContractPair Clearing.MarkToMarketCalculation
  -> TriggerA ClearingState ()
handleIncomingMarkToMarketCalculation ccp (mtmCid, <EMAIL> { accountId, customer, mtmAmount }) = do
  debug ("handling incoming mtm calculation: " <> show mtm)
  ClearingState { operatorParty } <- get
  clearingDepositCids <- getCustomerDeposits ccp customer (\(_, deposit) -> Set.null deposit.lockers)
  emitCommands [exerciseByKeyCmd @Clearing.Service (operatorParty, ccp, customer) Clearing.PerformMarkToMarket with
                  customerDepositCids = clearingDepositCids; providerDepositCids = []; calculationCid = mtmCid]
               ([toAnyContractId mtmCid] <> map toAnyContractId clearingDepositCids)
  return ()
--
--
-- |Transfer from the CCP to the CCPCustomer for a MarkToMarketCalculation until it runs out of
-- 'AssetDeposit's.
handleOutgoingMTMCalculations
  : Party
  -> [ContractPair AssetDeposit]
  -> [ContractPair Clearing.MarkToMarketCalculation]
  -> TriggerA ClearingState ()
handleOutgoingMTMCalculations _ [] _  = return ()
handleOutgoingMTMCalculations _ _  [] = return ()

handleOutgoingMTMCalculations party ds ((mtmCid,mtm)::mtms) = case ds' of
    None -> handleOutgoingMTMCalculations party ds mtms
    (Some (toUse,remaining)) -> do
      debug ("resolving outgoing mtm calculation: " <> show mtm)
      ClearingState { operatorParty } <- get
      let providerDepositCids = map fst toUse
      emitCommands [exerciseByKeyCmd @Clearing.Service (operatorParty, party, mtm.customer) Clearing.PerformMarkToMarket with
                      customerDepositCids = []; calculationCid = mtmCid; ..]
                   ([toAnyContractId mtmCid] <> map toAnyContractId providerDepositCids)

      handleOutgoingMTMCalculations party remaining mtms
  where
    amount = mtm.mtmAmount
    ds'    = collectDeposits amount ([], ds)


-- |Recurses through 'AssetDeposit's until the amount of necessary funds is
-- satisfied. Returns (used,remaining), or None if there are not enough AssetDeposits.
collectDeposits
  :  Decimal
  -> ([ContractPair AssetDeposit], [ContractPair AssetDeposit])
  -> Optional ([ContractPair AssetDeposit], [ContractPair AssetDeposit])
collectDeposits restFunds (used, [])
  | restFunds <= 0.0 = Some (used, [])
  | otherwise        = None

collectDeposits restFunds (used, (nextDeposit::remaining))
  | restFunds <= 0.0 = Some (used, remaining)
  | otherwise = collectDeposits (restFunds - nextAmount)
                                ((nextDeposit :: used), remaining)
  where
    nextAmount  = (.asset.quantity) . snd $ nextDeposit


-- |Calculates the FairValue for all Derivatives known to Exchange
handleCalculationRequests : Party -> ContractPair Clearing.FairValueCalculationRequest -> TriggerA ClearingState ()
handleCalculationRequests party (requestCid, <EMAIL>{..}) = do
  debug "handling calculation requests..."
  service <- head . filter (\(_,svc) -> svc.customer == customer) <$> query @MarketClearing.Service
  allListings <- query @Listing.T
  let optListingIdSet = Set.fromList <$> optListingIds
  let listings = case optListingIdSet of
        (Some ids) -> filter (\(_,lst) -> Set.member lst.listingId ids) allListings
        None       -> allListings

  forA_ listings (handleFairValueCalculation party service currency upTo calculationId)
  emitExerciseCmd requestCid Clearing.FairValueCalculationRequest_Ack
  return ()

-- |Calculates a FairValue for a single derivative.
-- Order of precedence:
--    1. The price of the most recent trade before the specified 'upTo'
--    2. If there are no trades, the most recent 'FairValue'
--    3. If there are no 'FairValue', request manual calculation
--
-- TODO: Allow user to specify a 'from' Time interval before which trades and
-- fairvalues will be ignored.
handleFairValueCalculation
  : Party -> ContractPair MarketClearing.Service -> Id -> Time -> Text -> ContractPair Listing.T -> TriggerA ClearingState ()
handleFairValueCalculation party (serviceCid,_) currency upTo calculationId (marketPairCid,l@(Listing.Listing{..})) = do
  debug ("calculating FVR: " <> l.listingId)

  fairValues <- sortOn ((.upTo) . snd)
              . filter (\(_,fv) -> fv.listingId == l.listingId) <$> query @Clearing.FairValue

  trades     <- sortOn (Down . (.timeNovated) . snd)
              . filter (\(_,cts) -> cts.order.details.listingId == l.listingId) <$> query @Clearing.ClearedTradeSide

  let relevantTrades = filter (\(_,ct) -> (subTime ct.timeNovated upTo) < (seconds 0)) trades
  timestamp <- getTime

  case getBestPrice relevantTrades fairValues of
    (Some bp) -> dedupExercise serviceCid MarketClearing.CreateFairValue with price = bp; ..
    None      -> dedupExercise serviceCid $ MarketClearing.CreateManualFairValueRequest with ..

  return ()

  where
    getBestPrice : [ContractPair Clearing.ClearedTradeSide] -> [ContractPair Clearing.FairValue] -> Optional Decimal
    getBestPrice ts fvs
      | not (null ts)  = Some . (.price) . (.execution) . snd . head $ ts
      | not (null fvs) = Some . (.price) . snd . head $ fvs
      | otherwise      = None
