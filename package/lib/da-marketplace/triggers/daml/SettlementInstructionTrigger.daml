-- Copyright (c) 2020 Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0

module SettlementInstructionTrigger where

import Daml.Trigger
import DA.Action (unless)
import DA.Foldable (forA_)
import DA.List (head)
import DA.Record (HasField)
import Marketplace.Settlement.Model (SettlementInstruction)
import Marketplace.Settlement.Service (Service, SettleInstruction(..))

handleSettlementInstruction : Trigger ()
handleSettlementInstruction = Trigger
  { initialize = pure ()
  , updateState = \_ -> pure ()
  , rule = handleSettlementInstructionRule
  , registeredTemplates = RegisteredTemplates [ registeredTemplate @SettlementInstruction
    , registeredTemplate @Service ]
  , heartbeat = None
  }

-- |Accept all 'SettlementInstruction'
handleSettlementInstructionRule : Party -> TriggerA () ()
handleSettlementInstructionRule party = do
  debug "Running Settlement Instruction rule..."

  let providerFilter : forall t. (Template t, HasField "provider" t Party) => [(ContractId t, t)] -> [(ContractId t, t)]
      providerFilter = filter ((== party) . (.provider) . snd)

  -- Accept all 'SettlementInstructions'
  settlementInstructions <- providerFilter <$> query @SettlementInstruction

  unless (null settlementInstructions) do
    settlementServices <- providerFilter <$> query @Service
    unless (null settlementServices) do
      let (ssCid, _) = head settlementServices
      forA_ settlementInstructions \(siCid, si) -> do
        debug $ "Processing Settlement Instruction: " <> show si
        emitCommands [exerciseCmd ssCid (SettleInstruction siCid) ] [toAnyContractId siCid]
      pure ()
    pure ()
