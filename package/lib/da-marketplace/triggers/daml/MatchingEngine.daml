-- Copyright (c) 2020 Digital Asset (Switzerland) GmbH and/or its affiliates. All rights reserved.
-- SPDX-License-Identifier: Apache-2.0

module MatchingEngine where

import DA.Action
import DA.Optional
import DA.Foldable hiding (elem, null, length)
import DA.List
import Daml.Trigger
import DA.Map (Map)
import DA.Map qualified as Map

import qualified Marketplace.Trading.Model as Order
import qualified Marketplace.Trading.Service as Service
import qualified Marketplace.Listing.Service as ListingService
import qualified Marketplace.Trading.Matching.Service as Matching

import Utils
import DA.Finance.Asset (AssetDeposit)

type OrderMap = Map (Party, Text) (ContractPair Order.T)

data MatchingState = MatchingState with
    currentOrderId : Int
    remainingOrders : OrderMap
  deriving (Show, Eq)

handleMatching : Trigger MatchingState
handleMatching = Trigger
  { initialize = return $ MatchingState with currentOrderId = 0; remainingOrders = mempty
  , updateState = \_ -> pure ()
  , rule = handleMatchingRule
  , registeredTemplates = RegisteredTemplates [ registeredTemplate @AssetDeposit
                                              , registeredTemplate @ListingService.CreateListingRequest
                                              , registeredTemplate @Service.CreateOrderRequest
                                              , registeredTemplate @Service.CancelOrderRequest
                                              , registeredTemplate @Order.FeeSchedule
                                              , registeredTemplate @Order.T ]
  , heartbeat = None
  }

modifyRemainingOrders : (OrderMap -> OrderMap) -> TriggerA MatchingState ()
modifyRemainingOrders fn = do
  state <- get
  put $ state with remainingOrders = fn state.remainingOrders

handleMatchingRule : Party -> TriggerA MatchingState ()
handleMatchingRule party = do
  debug "Running matching rule..."

  fees <- query @Order.FeeSchedule

  let currentFee = case fees of
        [(_, feeSchedule):xs] -> feeSchedule.currentFee.amount
        _                     -> 0.0

  -- Acknowledge all 'Order.Request' and update current ID
  orderRequests <- query @Service.CreateOrderRequest
  deposits <- query @AssetDeposit
  forA_ orderRequests \(cid,or) -> do
    exchangeFeeAmount <- case or.details.optExchangeFee of
        Some depositCid -> (.asset.quantity) . fromSomeNote "querying exchange fee" <$> queryContractId depositCid
        None            -> return 0.0
    if exchangeFeeAmount < currentFee
    then void $ emitExerciseCmd cid Service.RejectRequest with errorCode = 790; errorMessage = "Fee requirement not met"
    else do
      state <- get
      emitExerciseCmd cid Service.AcknowledgeRequest with providerOrderId = show $ state.currentOrderId
      debug "Acknowledging order"
      put $ state with currentOrderId = state.currentOrderId + 1

  time <- getTime

  -- Acknowledge all 'ListingService.CreateListingRequest'
  createListingRequests <- query @ListingService.CreateListingRequest

  forA_ createListingRequests \(cid,lr) -> do
    emitExerciseCmd cid ListingService.ListingRequestSuccess with providerId = show time
    debug "Acknowledging listing creation"

  -- Acknowledge all 'Order.CancelRequest'
  orderCancelRequests <- query @Service.CancelOrderRequest
  forA_ orderCancelRequests \(cid,_) -> emitExerciseCmd cid Service.AcknowledgeCancel
                                     >> debug "Acknowledging order cancel"

  -- Check for matches on all 'Order'
  orders <- query @Order.T

  state <- get
  let remainingOrders = Map.fromList $ map (\op -> (key op._2, op))
                                     $ filter (\(_,o) -> shouldProcess o) orders
  put $ state with remainingOrders
  matchOrders party $ sortOn (\(_,o) -> Down o.createdAt) $ Map.values remainingOrders

-- |Match all orders removing orders that are matched or do not have any match
matchOrders : Party -> [ContractPair Order.T] -> TriggerA MatchingState ()
matchOrders party []              = return ()
matchOrders party (order::orders) = do
  matchOrder party orders order
  newOrders <- sortOn (\(_,o) -> o.createdAt) . Map.values . remainingOrders <$> get
  matchOrders party newOrders

-- |Order can be matched
shouldProcess : Order.T -> Bool
shouldProcess o = o.status `elem` [Order.PendingExecution, Order.PartiallyExecuted]

-- |Check for crossing orders. If found, fill both orders. Matches by price.
matchOrder : Party -> [ContractPair Order.T] -> ContractPair Order.T -> TriggerA MatchingState ()
matchOrder party orders op@(orderCid, order) = do
  debug $ "Handling order: " <> show order
  processOrder op
  modifyRemainingOrders (Map.delete $ key order)

  where
    processOrder : ContractPair Order.T -> TriggerA MatchingState ()
    processOrder (orderCid, order) = do
      let oppositelimitOrders = sortOn (\(_,x) -> case x.details.orderType of
            Order.Limit price -> price
            _ -> error "How can I get here?") $ filter islimitOrder $ filter isOppositeOrder orders
          oppositelimitOrders' = case order.details.side of
            Order.Buy  -> oppositelimitOrders
            Order.Sell -> reverse oppositelimitOrders
          oppositeMarketOrders = sortOn (\(_, o) -> Down o.details.asset.quantity) $ filter (\(_, o) -> o.details.orderType == Order.Market) $ filter isOppositeOrder orders
          crossing = takeWhile (isCrossingLimitOrders (orderCid,order)) oppositelimitOrders'

      case order.details.orderType of
        Order.Market -> unless (null oppositelimitOrders') $ fill (orderCid, order) (head oppositelimitOrders')
        Order.Limit price -> if (not $ null oppositeMarketOrders)
          then fill (orderCid, order) (head oppositeMarketOrders)
          else unless (null crossing) $ fill (orderCid, order) (head crossing)

    isOppositeOrder : ContractPair Order.T -> Bool
    isOppositeOrder (opoCid, opo) = opoCid /= orderCid
                                 && opo.customer /= order.customer
                                 && opo.details.side /= order.details.side
                                 && opo.details.asset.id.label == order.details.asset.id.label
                                 && opo.status `elem` [Order.PendingExecution, Order.PartiallyExecuted]


    isCrossingLimitOrders : ContractPair Order.T -> ContractPair Order.T -> Bool
    isCrossingLimitOrders (_, aggressive) (_, passive) = do
      case (aggressive.details.orderType, passive.details.orderType) of
        (Order.Limit aggressivePrice, Order.Limit passivePrice) ->
          if aggressive.details.side == Order.Buy
            then aggressivePrice >= passivePrice
            else aggressivePrice <= passivePrice
        _ -> False

    islimitOrder : ContractPair Order.T -> Bool
    islimitOrder (_, order) = case order.details.orderType of
      Order.Limit _ -> True
      _ -> False

    fill : ContractPair Order.T -> ContractPair Order.T -> TriggerA MatchingState ()
    fill (aggressiveCid, aggressive) (passiveCid, passive) = do
      debug $ "Matching order: Taker=" <> show aggressive.details.id <> " to Maker=" <> show passive.details.id

      time <- getTime
      let price = case (aggressive.details.orderType, passive.details.orderType) of
            (Order.Limit price, Order.Market) -> price
            (Order.Market, Order.Limit price) -> price
            (Order.Limit _, Order.Limit passivePrice) -> passivePrice
            (_, _) -> error "Shouldn't be here - can't match two market orders together"
          makerOrderId = passive.details.id
          takerOrderId = aggressive.details.id
          quantity = min passive.remainingQuantity aggressive.remainingQuantity
          execution = Order.Execution with matchId = "matchId"; makerOrderId; takerOrderId; quantity; price; timestamp = show time

      emitCommands [exerciseByKeyCmd @Matching.Service aggressive.provider Matching.MatchOrders with execution] [toAnyContractId aggressiveCid, toAnyContractId passiveCid]

      modifyRemainingOrders (Map.delete $ key passive)

      return ()
