module Utils where

import Daml.Trigger
import DA.Functor (void)
import DA.Foldable (mapA_)

type ContractPair a = (ContractId a, a)

-- |Helper function to exercise a choice
emitExerciseCmd : (Template t, Choice t c r) => ContractId t -> c -> TriggerA s CommandId
emitExerciseCmd cid c = emitCommands [exerciseCmd cid c] [toAnyContractId cid]

-- |Performs a trigger action if bool is satisied, otherwise defer until next run
doOrDefer : Bool -> Text -> (TriggerA s a) -> TriggerA s ()
doOrDefer False txt fn = void $ debug $ ("Assert failed, deffering: " <> txt)
doOrDefer True txt fn = debug txt >> void $ fn

-- |Exercises a choice on all in list
mapExercise
  : (Choice t c r, Eq c, Show c, Show n) => c -> (t -> n) -> [(ContractId t,t)] -> TriggerA s ()
mapExercise c acc = mapA_ (\(cid,t) -> emitExerciseCmd cid c >> debug (show c <> ": " <> show (acc t)))

-- |Helper to to run a filter on the specified field of a 'query'
filterField : (b -> c) -> (c -> Bool) -> [(a,b)] -> [(a,b)]
filterField accessor pred = filter (\(_,x) -> pred (accessor x))

-- |Run a 'query' and filter on the specified field
filterQuery : forall a b m. (Template a, ActionTriggerAny m, Functor m) => (a -> b) -> (b -> Bool) -> m [(ContractId a, a)]
filterQuery accessor pred = filterField accessor pred <$> query @a
