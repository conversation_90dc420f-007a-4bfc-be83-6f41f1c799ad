module Utils where

import Daml.Script
import DA.Time
import DA.Date

getTimestamp : Script Int
getTimestamp = convertRelTimeToMicroseconds . (flip subTime) (datetime 1970 Jan 1 0 0 0) <$> getTime

getRandom : Int -> Int -> Script Int
getRandom min max = (\ts -> (ts % (max - min + 1) + min)) <$> getTimestamp

-- |Wait until the response is Some x and return X, otherwise fail after timeout
waitQuery : Decimal -> (Script (Optional c)) -> Script c
waitQuery timeout fetchFn = getTime >>= waitQuery' timeout fetchFn

-- |Wait until the response is True, otherwise fail after timeout
waitUntil : Decimal -> (<PERSON>ript Bool) -> Script ()
waitUntil timeout checkFn = getTime >>= waitQuery' timeout
  (do checkFn >>= \case
        True  -> return (Some ())
        False -> return None)

waitQuery' : Decimal -> (<PERSON><PERSON><PERSON> (Optional c)) -> Time -> Script c
waitQuery' timeout fetchFn startTime = do
    newTime <- getTime
    let elapsed = intToDecimal $ convertRelTimeToMicroseconds (subTime newTime startTime)
    assertMsg "Request timed out!" $ elapsed < (timeout * 1000000.0)
    fetchFn >>= \case
      (Some x) -> return x
      None     -> sleep (seconds 1) >> waitQuery' timeout fetchFn startTime

