module Tests.Matching where

import Daml.Script
import Common
import DA.Assert ((===))
import DA.List
import DA.Set (empty)
import DA.Foldable (forA_)
import DA.Finance.Asset
import Marketplace.Trading.Service qualified as Trading
import Marketplace.Trading.Model qualified as Order
import Marketplace.Listing.Service qualified as Listing
import Marketplace.Listing.Model qualified as Listing
import Marketplace.Settlement.Service qualified as Settlement
import Marketplace.Settlement.Model qualified as Settlement

import Utils

checkOrderStatusMatches : Text -> Party -> Order.Status -> Script Bool
checkOrderStatusMatches orderId party status = do
  Some (_, order) <- queryContractKey @Order.T party (party, orderId)
  return $ order.status == status

matchingTest : Script ()
matchingTest = getExistingParties >>= runMatchingTest

runMatchingTest : Parties -> Script ()
runMatchingTest parties@Parties{userAdmin = operator; ..} = do

  debug "Onboarding providers..."
  providers@Providers{exchange; matchingServiceCid; bank} <- onboardProviders parties

  debug "Onboarding customers..."
  alice <- onboardCustomer providers "Alice" alice
  bob <- onboardCustomer providers "Bob" bob
  issuer <- onboardCustomer providers "Issuer" issuer

  debug "Requesting listing..."
  Assets{usd; tsla} <- onboardAssets providers issuer
  let
    shareAsset = tsla with quantity = 200.0
    cashAsset = usd with quantity = 30000.0
  aliceDepositCid <- depositAsset providers alice shareAsset alice.mainAccount.id empty
  bobDepositCid <- depositAsset providers bob cashAsset bob.mainAccount.id empty

  listingTestNumber <- getRandom 1 5000
  let listingId = "TSLAUSDTEST" <> show listingTestNumber
  -- List a Security to trade
  (listingServiceCid, _) <- head <$> query @Listing.Service alice.customer
  let
    symbol = listingId
    listingType = Listing.CollateralizedRequest
    calendarId = "**********"
    description = "Tesla Inc. (Match Test)"
    tradedAssetId = tsla.id
    quotedAssetId = usd.id
    tradedAssetPrecision = 2
    quotedAssetPrecision = 2
    minimumTradableQuantity = 1.0
    maximumTradableQuantity = 1000000.0
    providerId = "123"
    observers = []
  createListingRequestCid <- alice.customer `submit` do exerciseCmd listingServiceCid Listing.RequestCreateListing with ..

  debug "Waiting for listing to be accepted..."
  (listingCid, Listing.Listing{listingId}) <- waitQuery 10.0 $ queryContractKey @Listing.Listing exchange (operator, exchange, listingId)
  debug $ listingId

  -- Create orders
  orderNumber <- getRandom 5000 10000

  debug "Testing resting Sell order"
  let aliceOrderId = show orderNumber
  aliceOrderCid <- alice.customer `submit` do
    exerciseCmd alice.tradingServiceCid Trading.RequestCreateOrder with
        details = Order.Details with
            id = aliceOrderId
            listingId
            asset = tsla with quantity = 200.0
            side = Order.Sell
            timeInForce = Order.GTC
            optExchangeFee = None
            orderType = Order.Limit with price = 100.0
            marketType = Order.Collateralized aliceDepositCid alice.mainAccount

  debug "Waiting for Alice's order to be accepted..."
  waitUntil 10.0 $ checkOrderStatusMatches aliceOrderId exchange Order.PendingExecution

  let bobOrderId = show $ orderNumber + 1
  bobOrderCid <- bob.customer `submit` do
    exerciseCmd bob.tradingServiceCid Trading.RequestCreateOrder with
        details = Order.Details with
            id = bobOrderId
            listingId
            asset = shareAsset
            side = Order.Buy
            optExchangeFee = None
            timeInForce = Order.GTC
            orderType = Order.Limit with price = 150.0
            marketType = Order.Collateralized bobDepositCid bob.mainAccount

  debug "Waiting for orders to be executed..."
  waitUntil 10.0 $ checkOrderStatusMatches aliceOrderId exchange Order.FullyExecuted
  waitUntil 10.0 $ checkOrderStatusMatches bobOrderId exchange Order.FullyExecuted

  debug "Running Settlement..."
  [ (settlementCid, _) ] <- query @Settlement.SettlementInstruction exchange
  exchange `submit` exerciseByKeyCmd @Settlement.Service (operator, exchange) Settlement.SettleInstruction with settlementInstructionCid = settlementCid

  debug "Checking settled positions..."
  [ (aliceDepositCid, aliceCashDeposit) ] <- queryFilter @AssetDeposit alice.customer \a -> a.asset == cashAsset with quantity = 20000.0
  [ ]                                     <- queryFilter @AssetDeposit alice.customer \a -> a.asset /= aliceCashDeposit.asset
  [ (_, bobCashDeposit) ]                 <- queryFilter @AssetDeposit bob.customer   \a -> a.asset == cashAsset with quantity = 10000.0
  [ (bobDepositCid, bobShareDeposit) ]    <- queryFilter @AssetDeposit bob.customer   \a -> a.asset == shareAsset
  [ ]                                     <- queryFilter @AssetDeposit bob.customer   \a -> a.asset `notElem` [bobCashDeposit.asset, bobShareDeposit.asset]

  debug "Testing resting Buy order"
  let aliceOrderId = show $ orderNumber + 2
  aliceOrderCid <- alice.customer `submit` do
    exerciseCmd alice.tradingServiceCid Trading.RequestCreateOrder with
        details = Order.Details with
            id = aliceOrderId
            listingId
            asset = tsla with quantity = 200.0
            side = Order.Buy
            timeInForce = Order.GTC
            optExchangeFee = None
            orderType = Order.Limit with price = 100.0
            marketType = Order.Collateralized aliceDepositCid alice.mainAccount

  debug "Waiting for Alice's order to be accepted..."
  waitUntil 10.0 $ checkOrderStatusMatches aliceOrderId exchange Order.PendingExecution

  let bobOrderId = show $ orderNumber + 3
  bobOrderCid <- bob.customer `submit` do
    exerciseCmd bob.tradingServiceCid Trading.RequestCreateOrder with
        details = Order.Details with
            id = bobOrderId
            listingId
            asset = shareAsset
            side = Order.Sell
            optExchangeFee = None
            timeInForce = Order.GTC
            orderType = Order.Limit with price = 50.0
            marketType = Order.Collateralized bobDepositCid bob.mainAccount

  debug "Waiting for orders to be executed..."
  waitUntil 10.0 $ checkOrderStatusMatches aliceOrderId exchange Order.FullyExecuted
  waitUntil 10.0 $ checkOrderStatusMatches bobOrderId exchange Order.FullyExecuted

  debug "Running Settlement..."
  [ (settlementCid, _) ] <- query @Settlement.SettlementInstruction exchange
  exchange `submit` exerciseByKeyCmd @Settlement.Service (operator, exchange) Settlement.SettleInstruction with settlementInstructionCid = settlementCid

  debug "Checking settled positions..."
  [ (aliceDepositCid, aliceShareDeposit) ] <- queryFilter @AssetDeposit alice.customer \a -> a.asset == shareAsset
  [ ]                                      <- queryFilter @AssetDeposit alice.customer \a -> a.asset /= aliceShareDeposit.asset
  [ (bobDepositCid1, bobCashDeposit1) ]    <- queryFilter @AssetDeposit bob.customer   \a -> a.asset == cashAsset with quantity = 10000.0
  [ (bobDepositCid2, bobCashDeposit2) ]    <- queryFilter @AssetDeposit bob.customer   \a -> a.asset == cashAsset with quantity = 20000.0
  [ ]                                      <- queryFilter @AssetDeposit bob.customer   \a -> a.asset `notElem` [bobCashDeposit1.asset, bobCashDeposit2.asset]

  debug "Testing multiple resting Sell orders"
  [ aliceDepositCid1, aliceDepositCid2, aliceDepositCid3, aliceDepositCid4 ] <- alice.customer `submit` exerciseCmd aliceDepositCid AssetDeposit_Split with quantities = [50.0, 50.0, 50.0]

  let aliceOrderId1 = show $ orderNumber + 4
  aliceOrderCid1 <- alice.customer `submit` do
    exerciseCmd alice.tradingServiceCid Trading.RequestCreateOrder with
        details = Order.Details with
            id = aliceOrderId1
            listingId
            asset = tsla with quantity = 50.0
            side = Order.Sell
            timeInForce = Order.GTC
            optExchangeFee = None
            orderType = Order.Limit with price = 100.0
            marketType = Order.Collateralized aliceDepositCid1 alice.mainAccount

  debug "Waiting for Alice's first order to be accepted..."
  waitUntil 10.0 $ checkOrderStatusMatches aliceOrderId1 exchange Order.PendingExecution

  let aliceOrderId2 = show $ orderNumber + 5
  aliceOrderCid2 <- alice.customer `submit` do
    exerciseCmd alice.tradingServiceCid Trading.RequestCreateOrder with
        details = Order.Details with
            id = aliceOrderId2
            listingId
            asset = tsla with quantity = 50.0
            side = Order.Sell
            timeInForce = Order.GTC
            optExchangeFee = None
            orderType = Order.Limit with price = 110.0
            marketType = Order.Collateralized aliceDepositCid2 alice.mainAccount

  debug "Waiting for Alice's second order to be accepted..."
  waitUntil 10.0 $ checkOrderStatusMatches aliceOrderId2 exchange Order.PendingExecution

  let aliceOrderId3 = show $ orderNumber + 6
  aliceOrderCid3 <- alice.customer `submit` do
    exerciseCmd alice.tradingServiceCid Trading.RequestCreateOrder with
        details = Order.Details with
            id = aliceOrderId3
            listingId
            asset = tsla with quantity = 50.0
            side = Order.Sell
            timeInForce = Order.GTC
            optExchangeFee = None
            orderType = Order.Limit with price = 120.0
            marketType = Order.Collateralized aliceDepositCid3 alice.mainAccount

  debug "Waiting for Alice's third order to be accepted..."
  waitUntil 10.0 $ checkOrderStatusMatches aliceOrderId3 exchange Order.PendingExecution

  let aliceOrderId4 = show $ orderNumber + 7
  aliceOrderCid4 <- alice.customer `submit` do
    exerciseCmd alice.tradingServiceCid Trading.RequestCreateOrder with
        details = Order.Details with
            id = aliceOrderId4
            listingId
            asset = tsla with quantity = 50.0
            side = Order.Sell
            timeInForce = Order.GTC
            optExchangeFee = None
            orderType = Order.Limit with price = 130.0
            marketType = Order.Collateralized aliceDepositCid4 alice.mainAccount

  debug "Waiting for Alice's forth order to be accepted..."
  waitUntil 10.0 $ checkOrderStatusMatches aliceOrderId4 exchange Order.PendingExecution

  let bobOrderId = show $ orderNumber + 8
  bobDepositCid <- bob.customer `submit` exerciseCmd bobDepositCid1 AssetDeposit_Merge with depositCids = [ bobDepositCid2 ]
  bobOrderCid <- bob.customer `submit` do
    exerciseCmd bob.tradingServiceCid Trading.RequestCreateOrder with
        details = Order.Details with
            id = bobOrderId
            listingId
            asset = shareAsset
            side = Order.Buy
            optExchangeFee = None
            timeInForce = Order.GTC
            orderType = Order.Limit with price = 150.0
            marketType = Order.Collateralized bobDepositCid bob.mainAccount

  debug "Waiting for orders to be executed..."
  waitUntil 10.0 $ checkOrderStatusMatches aliceOrderId1 exchange Order.FullyExecuted
  waitUntil 10.0 $ checkOrderStatusMatches aliceOrderId2 exchange Order.FullyExecuted
  waitUntil 10.0 $ checkOrderStatusMatches aliceOrderId3 exchange Order.FullyExecuted
  waitUntil 10.0 $ checkOrderStatusMatches aliceOrderId4 exchange Order.FullyExecuted
  waitUntil 10.0 $ checkOrderStatusMatches bobOrderId exchange Order.FullyExecuted

  debug "Running Settlement..."
  settlementCids <- map fst <$> query @Settlement.SettlementInstruction exchange
  forA_ settlementCids \cid -> exchange `submit` exerciseByKeyCmd @Settlement.Service (operator, exchange) Settlement.SettleInstruction with settlementInstructionCid = cid

  debug "Checking settled positions..."
  [ (_, aliceCashDeposit1) ] <- queryFilter @AssetDeposit alice.customer \a -> a.asset == cashAsset with quantity = 5000.0
  [ (_, aliceCashDeposit2) ] <- queryFilter @AssetDeposit alice.customer \a -> a.asset == cashAsset with quantity = 5500.0
  [ (_, aliceCashDeposit3) ] <- queryFilter @AssetDeposit alice.customer \a -> a.asset == cashAsset with quantity = 6000.0
  [ (_, aliceCashDeposit4) ] <- queryFilter @AssetDeposit alice.customer \a -> a.asset == cashAsset with quantity = 6500.0
  [ ]                        <- queryFilter @AssetDeposit alice.customer \a -> a.asset `notElem` [ aliceCashDeposit1.asset, aliceCashDeposit2.asset, aliceCashDeposit3.asset, aliceCashDeposit4.asset ]

  bobAssetQuantity <- sum . map (.asset.quantity) . (map snd) <$> queryFilter @AssetDeposit bob.customer \a -> a.asset == shareAsset with quantity = 50.0
  bobCashQuantity  <- sum . map (.asset.quantity) . (map snd) <$> queryFilter @AssetDeposit bob.customer \a -> a.asset.id.label == cashAsset.id.label
  bobAssetQuantity === 200.0
  bobCashQuantity  === 7000.0

  debug "Test passed!"
