module Tests.Clearing where

import Daml.Script

import Common
import DA.Set (empty, fromList)

import DA.Finance.Asset
import Marketplace.Clearing.Model
import Marketplace.Clearing.Service qualified as Clearing
import Utils (waitUntil)


marginTest : Script ()
marginTest = getExistingParties >>= runMarginTest

runMarginTest : Parties -> Script ()
runMarginTest parties@Parties{userAdmin = operator; ..} = do
  debug "Running margin test..."

  debug "Onboarding parties and providers..."
  providers@Providers{clearinghouse; operator} <- onboardProviders parties

  alice  <- onboardCustomer providers "Alice" alice
  bob    <- onboardCustomer providers "Bob" bob
  issuer <- onboardCustomer providers "Issuer" issuer

  Assets{usd; tsla} <- onboardAssets providers issuer

  -- Assets
  let cashAsset = usd with quantity = 20000.0

  debug "Performing first margin calculation..."
  aliceDepositCid <- depositAsset providers alice cashAsset alice.mainAccount.id $ fromList [ clearinghouse ]
  calculationCid <- submit clearinghouse $
    exerciseCmd alice.clearingServiceCid Clearing.CreateMarginCalculation with
      currency = usd.id; targetAmount = 4000.0; calculationId = "123456"; ..

  waitUntil 10.0 $ do
    deposits <- queryFilter @AssetDeposit alice.customer \a -> a.lockers == fromList [ clearinghouse ]
    let total = sum $ map ((.asset.quantity) . snd) deposits
    pure $ total == 4000.0

  debug "Performing second margin calculation..."
  calculationCid <- submit clearinghouse $
    exerciseCmd alice.clearingServiceCid Clearing.CreateMarginCalculation with
      currency = usd.id; targetAmount = 1000.0; calculationId = "456789"

  waitUntil 10.0 $ do
    deposits <- queryFilter @AssetDeposit alice.customer \a -> a.lockers == fromList [ clearinghouse ]
    let total = sum $ map ((.asset.quantity) . snd) deposits
    pure $ total == 1000.0

  debug "Performing failed margin calculation..."
  calculationCid <- submit clearinghouse $
    exerciseCmd alice.clearingServiceCid Clearing.CreateMarginCalculation with
      currency = usd.id; targetAmount = 50_000.0; calculationId = "34556"

  waitUntil 10.0 $ do
    (Some (_,aliceStanding)) <- queryContractKey @MemberStanding clearinghouse (clearinghouse, alice.customer)
    return $ not aliceStanding.marginSatisfied

  debug "Test passed!"

markToMarketTest : Script ()
markToMarketTest = getExistingParties >>= runMarkToMarketTest

runMarkToMarketTest : Parties -> Script ()
runMarkToMarketTest parties@Parties{userAdmin = operator; ..} = do
  debug "Running mark to market test..."

  debug "Allocating providers and parties..."
  providers@Providers{clearinghouse} <- onboardProviders parties

  alice  <- onboardCustomer providers "Alice" alice
  bob    <- onboardCustomer providers "Bob" bob
  issuer <- onboardCustomer providers "Issuer" issuer

  Assets{usd; tsla} <- onboardAssets providers issuer

  -- Assets
  let cashAsset = usd with quantity = 20000.0

  aliceDepositCid <- depositAsset providers alice cashAsset alice.mainAccount.id $ fromList [ clearinghouse ]
  bobDepositCid   <- depositAsset providers bob cashAsset bob.mainAccount.id $ fromList [ clearinghouse ]

  debug "Testing Mark to Markets..."
  calculationCid <- submit clearinghouse $
    exerciseCmd alice.clearingServiceCid Clearing.CreateMarkToMarket with
      currency = usd.id; mtmAmount = -5000.0; calculationId = "34569"

  calculationCid2 <- submit clearinghouse $
    exerciseCmd bob.clearingServiceCid Clearing.CreateMarkToMarket with
      currency = usd.id; mtmAmount = 5000.0; calculationId = "34670"

  waitUntil 10.0 $ do
    deposits <- queryFilter @AssetDeposit alice.customer \a -> a.lockers == empty
    let total = sum $ map ((.asset.quantity) . snd) deposits
    pure $ total == 15_000.0
  waitUntil 10.0 $ do
    deposits <- queryFilter @AssetDeposit bob.customer \a -> a.lockers == empty
    let total = sum $ map ((.asset.quantity) . snd) deposits
    pure $ total == 25_000.0

  debug "Test passed!"

