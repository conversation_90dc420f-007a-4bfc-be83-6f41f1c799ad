module MoneyMatrixIntegration.Withdraw where


{- -------------------------------------------TEMPLATES------------------------------------------- -}

template InitialWithdrawRequest
    with
        client              : Party
        integrationParty    : Party
        requestedAmount     : Numeric 2
        currency            : Text
        observers           : [Party]
        transactionId       : Text
        timestamp           : Time
        userInformation     : UserInformation
        language            : Text
    where
        signatory client
        observer integrationParty

        key (integrationParty, client) : (Party, Party)
        maintainer key._2

        choice ProcessWithdrawRequest : ContractId WithdrawRequest
            with
                cashierURL      : Text
                transactionCode : Text
                countryCode     : Text
            controller integrationParty
            do
                create WithdrawRequest with ..


template WithdrawRequest
    with
        client              : Party
        integrationParty    : Party
        cashierURL          : Text
        transactionCode     : Text
        requestedAmount     : Numeric 2
        currency            : Text
        observers           : [Party]
        transactionId       : Text
        userInformation     : UserInformation
        timestamp           : Time
        language            : Text
    where
        signatory integrationParty, client
        observer observers

        key (integrationParty, client) : (<PERSON>, Party)
        maintainer key._2

        choice FinalizeTransaction : ContractId WithdrawTransaction
            with
                confirmedAmount : Numeric 2
            controller integrationParty
                do
                    timestamp <- getTime
                    create WithdrawTransaction with confirmedAmount, ..


        choice FailTransaction : ContractId WithdrawFailure
            with
                reason : Text
            controller integrationParty
                do
                    timestamp <- getTime
                    create WithdrawFailure with reason, ..


template WithdrawTransaction
    with
        client              : Party
        integrationParty    : Party
        confirmedAmount     : Numeric 2
        currency            : Text
        transactionCode     : Text
        observers           : [Party]
        timestamp           : Time
        transactionId       : Text
        userInformation     : UserInformation
    where
        signatory client
        observer observers
        key (integrationParty, client, transactionCode) : (Party, Party, Text)
        maintainer key._2


template WithdrawFailure
    with
        client              : Party
        integrationParty    : Party
        transactionCode     : Text
        observers           : [Party]
        timestamp           : Time
        transactionId       : Text
        reason              : Text
        userInformation     : UserInformation
    where
        signatory client
        observer observers

        key (integrationParty, client, transactionCode) : (Party, Party, Text)
        maintainer key._2

data UserInformation = UserInformation
    with
        firstName         : Text
        lastName          : Text
        emailAddress      : Text
        phoneNumber       : Text
        birthday          : Date
        countryCode       : Text
        city              : Text
        postalCode        : Text
        subDivision       : Text
        addressLine1      : Text
    deriving (Show, Eq)