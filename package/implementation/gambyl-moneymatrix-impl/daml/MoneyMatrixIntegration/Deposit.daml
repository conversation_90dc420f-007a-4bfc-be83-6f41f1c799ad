module MoneyMatrixIntegration.Deposit where

{- -------------------------------------------TEMPLATES------------------------------------------- -}

template InitialDepositRequest
    with
      client            : Party
      integrationParty  : Party
      requestedAmount   : Numeric 2
      currency          : Text
      observers         : [Party]
      transactionId     : Text
      timestamp         : Time
      userInformation   : UserInformation
      language          : Text
    where
        signatory client
        observer observers, integrationParty

        key (integrationParty, client) : (Party, Party)
        maintainer key._2

        choice ProcessDepositRequest : ContractId DepositRequest
            with
                cashierURL      : Text
                transactionCode : Text
                countryCode     : Text
            controller integrationParty
            do
                create DepositRequest with ..



        choice FailInitialDepositRequest : ContractId DepositFailure
            with
                reason : Text
            controller integrationParty
            do
                timestamp <- getTime
                create DepositFailure with reason, transactionCode = "", ..


        choice ArchiveInitRequest : ()
            controller client
                do pure ()




template DepositRequest
    with
      client            : Party
      integrationParty  : Party
      cashierURL        : Text
      transactionCode   : Text
      requestedAmount   : Numeric 2
      currency          : Text
      observers         : [Party]
      transactionId     : Text
      userInformation   : UserInformation
      timestamp         : Time
      language          : Text
    where
        signatory integrationParty, client
        observer observers

        key (integrationParty, client, transactionId) : (Party, Party, Text)
        maintainer key._2

        choice FinalizeTransaction : ContractId DepositTransaction
            with
                confirmedAmount : Numeric 2
            controller integrationParty
            do
                    timestamp <- getTime
                    create DepositTransaction with confirmedAmount, ..


        choice FailTransaction : ContractId DepositFailure
            with
                reason : Text
            controller integrationParty
                do
                    timestamp <- getTime
                    create DepositFailure with reason, ..

        choice ArchiveRequest : ()
            controller client
                do pure ()



template DepositTransaction
    with
        client              : Party
        integrationParty    : Party
        transactionCode     : Text
        confirmedAmount     : Numeric 2
        currency            : Text
        observers           : [Party]
        timestamp           : Time
        transactionId       : Text
        userInformation     : UserInformation
    where
        signatory client
        observer observers

        key (integrationParty, client, transactionCode) : (Party, Party, Text)
        maintainer key._2


template DepositFailure
    with
        client              : Party
        integrationParty    : Party
        transactionCode     : Text
        observers           : [Party]
        timestamp           : Time
        transactionId       : Text
        reason              : Text
        userInformation     : UserInformation
    where
        signatory client
        observer observers

        key (integrationParty, client, transactionCode) : (Party, Party, Text)
        maintainer key._2


data UserInformation = UserInformation
    with
        firstName         : Text
        lastName          : Text
        emailAddress      : Text
        phoneNumber       : Text
        birthday          : Date
        countryCode       : Text
        city              : Text
        postalCode        : Text
        subDivision       : Text
        addressLine1      : Text
    deriving (Show, Eq)
