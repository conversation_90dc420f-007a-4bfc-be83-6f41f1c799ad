module SendgridIntegration.Message where

data BetDetails = BetDetails
    with
        ticketId          : Text
        eventTitle        : Text
        dateTime          : Time
        side              : Text
        stake             : Numeric 2
        oddValue          : Text
    deriving (Show, Eq, Ord)

data ActionType =
    ManualCancellation
    | EventInProgress
    | EventFinished
    | EventCancelled
    | EventPostponed
    | EventArchived
    | EventRevised
    deriving (Show, Eq, Ord)

data Subject =
  KYCApproved
  | KYCRejected
  | KYCPending
  | Deposit {
    date                : Time
    , transactionCode   : Text
    , requestedAmount   : Numeric 2
    , feeCharged        : Decimal
  }
  | Withdraw {
    date                : Time
    , transactionCode   : Text
    , requestedAmount   : Numeric 2
    , feeCharged        : Decimal
  }
  | BetPlacement {
    betDetails          : [BetDetails]
  }
  | BetCancellation {
    ticketId            : Text
    , eventTitle        : Text
    , dateTime          : Time
    , side              : Text
    , stake             : Numeric 2
    , oddValue          : Text
    , actionType        : ActionType
  }
  | BetSettled {
    details          : BetDetails
  }
  | Promotion {
    amount              : Numeric 2
  }
  deriving (Show, Eq, Ord)

template Message
  with
    integrationParty  : Party
    toEmail           : Text
    subject           : Subject
  where
    signatory integrationParty
