sdk-version: 2.9.4
name: gambyl-sendgrid-impl
source: daml
init-script:
version: ${GAMBYL_SENDGRID_IMPL_CURRENT_VERSION}
dependencies:
  - daml-prim
  - daml-stdlib
  - daml-script
sandbox-options:
  - --wall-clock-time
build-options:
  - --ghc-option=-Wall
  - --ghc-option=-Wno-name-shadowing
  - --ghc-option=-Wno-unused-do-bind
  - --ghc-option=-Wmissing-import-lists
  - --ghc-option=-Werror
  - --output=../../../build/implementation/gambyl-sendgrid-impl-${GAMBYL_SENDGRID_IMPL_CURRENT_VERSION}.dar