module QuickbooksIntegration.Account.Model where

import QuickbooksIntegration.Common qualified as Common

template CreateAccountRequest
  with
    integrationParty    : Party
    requestingParty     : Party
    requestingFor       : Party
    observers           : [Party]
    request             : AccountRequestObject
  where
    signatory requestingParty
    observer integrationParty :: requestingFor :: observers
    key (requestingParty, requestingFor, request) : (Party, Party, AccountRequestObject)
    maintainer key._1

    choice CreateAccount : ContractId Account
        with
            account : AccountObject
        controller integrationParty
        do
            create Account with accountHolder = requestingFor, observers = requestingParty :: this.observers, ..


template Account
    with
        integrationParty    : Party
        accountHolder       : Party
        observers           : [Party]
        account             : AccountObject
    where
        signatory integrationParty
        observer accountHolder :: observers

        key (integrationParty, accountHolder, account.name) : (Party, Party, Text)
        maintainer key._1


{- ------------------------------------------DATA TYPES------------------------------------------- -}

data AccountRequestObject = AccountRequestObject
    with
        name            : Text
        accountType     : Text
        accountSubType  : Text
    deriving (Show, Eq)


data AccountObject = AccountObject
    with
        fullyQualifiedName              : Text
        domain                          : Text
        name                            : Text
        classification                  : Optional Text
        accountSubType                  : Text
        currencyRef                     : CurrencyRef
        currentBalanceWithSubAccounts   : Decimal
        sparse                          : Bool
        metadata                        : Optional Common.Metadata
        accountType                     : Text
        currentBalance                  : Decimal
        active                          : Bool
        syncToken                       : Text
        id                              : Text
        subAccount                      : Bool
    deriving (Show, Eq)


data CurrencyRef = CurrencyRef
    with
        name    : Text
        type_   : Text
        value   : Text
    deriving(Show, Eq)
