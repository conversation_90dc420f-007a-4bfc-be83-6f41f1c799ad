module QuickbooksIntegration.Deposit.Model where

import QuickbooksIntegration.Common qualified as Q<PERSON><PERSON><PERSON>mon

{-
{
  "Deposit": {
    "SyncToken": "0",
    "domain": "QBO",
    "DepositToAccountRef": {
      "name": "Checking",
      "value": "35"
    },
    "TxnDate": "2014-12-22",
    "TotalAmt": 1675.52,
    "sparse": false,
    "Line": [
      {
        "Amount": 1675,
        "LinkedTxn": [
          {
            "TxnLineId": "0",
            "TxnId": "120",
            "TxnType": "Payment"
          }
        ]
      }
    ],
    "Id": "148",
    "MetaData": {
      "CreateTime": "2014-12-22T12:46:52-08:00",
      "LastUpdatedTime": "2014-12-22T12:46:52-08:00"
    }
  },
  "time": "2014-12-22T13:39:35.449-08:00"
}
-}

-- data DepositObject = DepositObject
--     with
--         deposit : Deposit
--         time    : Time
--     deriving (Show, Eq)

{-
{
  "Line": [
    {
      "DetailType": "DepositLineDetail",
      "Amount": 20.0,
      "DepositLineDetail": {
        "AccountRef": {
          "name": "Unapplied Cash Payment Income",
          "value": "87"
        }
      }
    }
  ],
  "DepositToAccountRef": {
    "name": "Checking",
    "value": "35"
  }
}
-}

template DepositRequest
  with
    integrationParty    : Party
    requestingParty     : Party
    requestingFor       : Party
    transactionId       : Text
    observers           : [Party]
    request             : RequestDepositObject
  where
    signatory requestingParty
    observer integrationParty :: requestingFor :: observers
    key (requestingParty, requestingParty, transactionId) : (Party, Party, Text)
    maintainer key._1

    choice Archive_DepositRequest : ()
        controller integrationParty
            do return ()

{- ------------------------------------------DATA TYPES------------------------------------------- -}

data RequestDepositObject = RequestDepositObject
    with
        depositToAccountRef : QBICommon.AccountRef
        line : [RequestLine]
    deriving (Show, Eq)


data RequestLine = RequestLine
    with
        detailType          : Text -- Always set to "DepositLineDetail"
        amount              : Decimal
        depositLineDetail   : DepositLineDetail
    deriving (Show, Eq)


data DepositLineDetail = DepositLineDetail
    with
        accountRef : QBICommon.AccountRef
    deriving (Show, Eq)


data DepositObject = DepositObject
    with
        syncToken           : Text
        domain              : Text
        depositToAccountRef : QBICommon.AccountRef
        txnDate             : Date
        totalAmt            : Decimal
        sparse              : Bool
        line                : [Line]
        id                  : Text
        metadata            : QBICommon.Metadata
    deriving (Show, Eq)

data Line = Line
    with
        id                  : Text
        lineNum             : Int
        detailType          : Text
        amount              : Decimal
        depositLineDetail   : DepositLineDetail
        linkedTxn           : [Txn]
    deriving (Show, Eq)

data Txn = Txn
    with
        txnLineId : Text
        txnId     : Text
        txnType   : Text
    deriving (Show, Eq)

