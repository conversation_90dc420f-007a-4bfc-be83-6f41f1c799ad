module QuickbooksIntegration.Integration where

import DA.Optional (fromOptional)
template OauthRequest
    with
        integrationParty        : Party
        ledgerOperator          : Party
        oauthUrl                : Text
    where
        signatory integrationParty
        observer ledgerOperator


template Configuration
    with
        integrationParty        : Party
        observers               : [Party]
    where
        signatory integrationParty
        key integrationParty    : Party
        maintainer key

template Authorization
    with
        integrationParty        : Party
        authorizationCode       : Text
        accessToken             : Text
        accessTokenExpiresAt    : Time
        refreshToken            : Text
        refreshTokenExpiresAt   : Time
    where
        signatory integrationParty
        key integrationParty    : Party
        maintainer key

        choice UpdateAuthorization : ContractId Authorization
            with
                newAuthorizationCode      : Optional Text
                newAccessToken            : Optional Text
                newAccessTokenExpiresAt   : Optional Time
                newRefreshToken           : Optional Text
                newRefreshTokenExpiresAt  : Optional Time
            controller integrationParty
            do
                create Authorization with
                    authorizationCode       = fromOptional authorizationCode newAuthorizationCode
                    accessToken             = fromOptional accessToken newAccessToken
                    accessTokenExpiresAt    = fromOptional accessTokenExpiresAt newAccessTokenExpiresAt
                    refreshToken            = fromOptional refreshToken newRefreshToken
                    refreshTokenExpiresAt   = fromOptional refreshTokenExpiresAt newRefreshTokenExpiresAt
                    ..
