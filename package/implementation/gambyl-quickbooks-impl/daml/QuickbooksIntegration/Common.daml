module QuickbooksIntegration.Common where

{- ------------------------------------------DATA TYPEA------------------------------------------- -}

data Metadata = Metadata
    with
        createTime : Time
        lastUpdatedTime : Time
    deriving (Show, Eq)


data AccountRef = AccountRef
    with
        name : Text     -- Account actual name
        value : Text    -- Account Id
    deriving (Show, Eq)
