module QuickbooksIntegration.Transfer.Model where

import QuickbooksIntegration.Common qualified as Q<PERSON><PERSON>ommon
{-
{
  "Transfer": {
    "SyncToken": "0",
    "domain": "QBO",
    "TxnDate": "2015-02-06",
    "ToAccountRef": {
      "name": "Savings",
      "value": "36"
    },
    "Amount": 120.0,
    "sparse": false,
    "Id": "170",
    "FromAccountRef": {
      "name": "Checking",
      "value": "35"
    },
    "MetaData": {
      "CreateTime": "2015-02-06T11:06:12-08:00",
      "LastUpdatedTime": "2015-02-06T11:06:12-08:00"
    }
  },
  "time": "2015-02-06T11:06:12.017-08:00"
}
-}




template TransferRequest
  with
    integrationParty    : Party
    requestingParty     : Party
    requestingFor       : Party
    transactionId       : Text
    observers           : [Party]
    request             : TransferRequestObject
  where
    signatory requestingParty
    observer integrationParty :: requestingFor :: observers
    key (requestingParty, requestingParty, transactionId) : (Party, Party, Text)
    maintainer key._1

    choice Archive_TransferRequest : ()
        controller integrationParty
            do return ()


{- ------------------------------------------DATA TYPES------------------------------------------- -}

data TransferRequestObject = TransferRequestObject
    with
        amount          : Decimal
        fromAccountRef  : QBICommon.AccountRef
        toAccountRef    : QBICommon.AccountRef
    deriving (Show, Eq)


data Transfer = Transfer
    with
        syncToken : Text
        domain : Text
        txnDate : Date
        toAccountRef : QBICommon.AccountRef
        amount : Decimal
        sparse : Bool
        id : Text
        fromAccountRef : QBICommon.AccountRef
        metadata : QBICommon.Metadata
    deriving (Show, Eq)

