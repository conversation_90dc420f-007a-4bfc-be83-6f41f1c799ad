module QuickbooksIntegration.Payment where

import QuickbooksIntegration.Common qualified as <PERSON><PERSON><PERSON><PERSON><PERSON>

{-
{
  "Payment": {
    "SyncToken": "0",
    "domain": "QBO",
    "DepositToAccountRef": {
      "value": "4"
    },
    "UnappliedAmt": 10.0,
    "TxnDate": "2015-01-16",
    "TotalAmt": 65.0,
    "ProcessPayment": false,
    "sparse": false,
    "Line": [
      {
        "Amount": 55.0,
        "LineEx": {
          "any": [
            {
              "name": "{http://schema.intuit.com/finance/v3}NameValue",
              "nil": false,
              "value": {
                "Name": "txnId",
                "Value": "70"
              },
              "declaredType": "com.intuit.schema.finance.v3.NameValue",
              "scope": "javax.xml.bind.JAXBElement$GlobalScope",
              "globalScope": true,
              "typeSubstituted": false
            },
            {
              "name": "{http://schema.intuit.com/finance/v3}NameValue",
              "nil": false,
              "value": {
                "Name": "txnOpenBalance",
                "Value": "71.00"
              },
              "declaredType": "com.intuit.schema.finance.v3.NameValue",
              "scope": "javax.xml.bind.JAXBElement$GlobalScope",
              "globalScope": true,
              "typeSubstituted": false
            },
            {
              "name": "{http://schema.intuit.com/finance/v3}NameValue",
              "nil": false,
              "value": {
                "Name": "txnReferenceNumber",
                "Value": "1024"
              },
              "declaredType": "com.intuit.schema.finance.v3.NameValue",
              "scope": "javax.xml.bind.JAXBElement$GlobalScope",
              "globalScope": true,
              "typeSubstituted": false
            }
          ]
        },
        "LinkedTxn": [
          {
            "TxnId": "70",
            "TxnType": "Invoice"
          }
        ]
      }
    ],
    "CustomerRef": {
      "name": "Red Rock Diner",
      "value": "20"
    },
    "Id": "163",
    "MetaData": {
      "CreateTime": "2015-01-16T15:08:12-08:00",
      "LastUpdatedTime": "2015-01-16T15:08:12-08:00"
    }
  },
  "time": "2015-07-28T15:16:15.435-07:00"
}
-}

data PaymentObject = PaymentObject
    with
        payment : Payment
        time : Time
    deriving(Show, Eq)

data Payment = Payment
    with
        syncToken           : Text
        domain              : Text
        depositToAccountRef : QBICommon.AccountRef
        unappliedAmt        : Decimal
        txnDate             : Date
        totalAmt            : Decimal
        processPayment      : Bool
        sparse              : Bool
        line                : [Line]
        id                  : Text
        metadata            : QBICommon.Metadata
    deriving(Show, Eq)

data Line = Line
    with
        amount      : Decimal
        lineEx      : LineEx
        linkedTxn   : Txn
    deriving(Show, Eq)

data LineEx = LineEx
    with
        any : [AnyObject]
    deriving(Show, Eq)

data AnyObject = AnyObject
    with
        name            : Text
        nil             : Bool
        value           : ValueObject
        declaredType    : Text
        scope           : Text
        globalScope     : Bool
        typeSubstituted : Bool
    deriving(Show, Eq)

data ValueObject = ValueObject
    with
        name    : Text
        value   : Text
    deriving(Show, Eq)

data Txn = Txn
    with
        txnLineId : Text
        txnId     : Text
        txnType   : Text
    deriving (Show, Eq)

{-
{
  "TotalAmt": 25.0,
  "CustomerRef": {
    "value": "20"
  }
}
-}

-- data PaymentRequest = PaymentRequest
--     with
--         totalAmt : Decimal
--         customerRef : QBICustomer.CustomerRef
--     deriving(Show, Eq)

data PaymentResponse = PaymentResponse
    with
        payment : Payment
        time : Time
    deriving(Show, Eq)