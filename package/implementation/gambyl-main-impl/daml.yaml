# for config file options, refer to
# https://docs.daml.com/tools/assistant.html#project-config-file-daml-yaml
sdk-version: 2.9.4
name: gambyl-main-impl
source: daml
init-script:
parties:
  - Operator
  - Gambyl
  - EnetPulse
  - Alice
  - <PERSON>
  - Charlie
  - Dana
  - Public
version: ${GAMBYL_MAIN_IMPL_CURRENT_VERSION}
dependencies:
  - daml-prim
  - daml-stdlib
  - daml-script
data-dependencies:
  # - ../../../ext-pkg/da-marketplace-${DA_MARKETPLACE_CURRENT_VERSION}.dar
  - ../../../ext-pkg/da-marketplace/${DA_MARKETPLACE_CURRENT_VERSION}/da-marketplace-${DA_MARKETPLACE_CURRENT_VERSION}.dar
  - ../../../build/implementation/gambyl-enetpulse-impl-${GAMBYL_ENETPULSE_IMPL_CURRENT_VERSION}.dar
  - ../../../build/implementation/gambyl-exberry-impl-${GAMBYL_EXBERRY_IMPL_CURRENT_VERSION}.dar
  - ../../../build/implementation/gambyl-jumio-impl-${GAMBYL_JUMIO_IMPL_CURRENT_VERSION}.dar
  - ../../../build/implementation/gambyl-moneymatrix-impl-${GAMBYL_MONEYMATRIX_IMPL_CURRENT_VERSION}.dar
  - ../../../build/implementation/gambyl-quickbooks-impl-${GAMBYL_QUICKBOOKS_IMPL_CURRENT_VERSION}.dar
  - ../../../build/implementation/gambyl-sendgrid-impl-${GAMBYL_SENDGRID_IMPL_CURRENT_VERSION}.dar
  # Interface depedencies
  - ../../../build/interface/gambyl-main-interface-${GAMBYL_MAIN_INTERFACE_CURRENT_VERSION}.dar
module-prefixes:
  gambyl-enetpulse-impl-${GAMBYL_ENETPULSE_IMPL_CURRENT_VERSION}: EnetPulse
  gambyl-exberry-impl-${GAMBYL_EXBERRY_IMPL_CURRENT_VERSION}: Exberry
  gambyl-quickbooks-impl-${GAMBYL_QUICKBOOKS_IMPL_CURRENT_VERSION}: QuickBooks
sandbox-options:
  - --wall-clock-time
build-options:
  - --ghc-option=-Wall
  - --ghc-option=-Wno-name-shadowing
  - --ghc-option=-Wno-unused-do-bind
  - --ghc-option=-fno-warn-missing-signatures
  - --ghc-option=-fno-warn-orphans
  - --ghc-option=-Werror
  - --output=../../../build/implementation/gambyl-main-impl-${GAMBYL_MAIN_IMPL_CURRENT_VERSION}.dar