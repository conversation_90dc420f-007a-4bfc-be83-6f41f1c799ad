module Scripts.Bootstrap.Onboarding.Entities where

import Daml.Script

import DA.Action ((>=>))
import DA.Date (date, Month(..))
import DA.Optional (fromSomeNote)
import DA.Set (fromList)
import DA.Map qualified  as Map
import DA.Stack (HasCallStack)
import Scripts.Common
import Scripts.Bootstrap.Onboarding.Parties (EssentialParties(..), Parties(..))

import Marketplace.Custody.Role qualified as CustodyRole
import Marketplace.Custody.Service qualified as CustodyService
import Marketplace.Issuance.Service qualified as Issuance
import Marketplace.Trading.Role qualified as TradingRole

import JumioIntegration.Identity qualified as IntegrationIdentity

import Gambyl.Gambling.Model qualified as GamblingModel
import Gambyl.Gambling.Identity.Model qualified as IdentityModel
import Gambyl.Gambling.Role qualified as GamblingRole
import Gambyl.Gambling.Service qualified as GamblingService
import Gambyl.Marketing.Service qualified as MarketingService
import Gambyl.Gambling.Event.Service qualified as EventService
import Gambyl.Operator.Role qualified as GambylOperator
import DA.Time (time)
import qualified DA.List as List
import DA.Text (splitOn)



data ApproachType = Offer | Request

customerServices = [CustodyService, GamblingService, MarketingService]


demoOnboarding : Entities -> Script Entities
demoOnboarding entities@Entities{parties = Parties{gambyl, jumio, marketingManager, eventManager, alice, bob, charlie, dana}} = do


    debug "Start Onboarding"
    return entities
    >>= onboardWithOperatorRole
    >>= onboardWithGamblingRole         . (, gambyl, Offer)
    >>= onboardWithgamblingService      . (, gambyl, alice)
    >>= createGamblerUnverifiedIdentity . (, gambyl, alice)
    >>= verifyGamblerIdentity           . (, gambyl, jumio, alice)
    >>= givePermissions                 . (, gambyl, alice, [GamblingModel.Bets])
    >>= onboardWithgamblingService      . (, gambyl, bob)
    >>= createGamblerUnverifiedIdentity . (, gambyl, bob)
    >>= verifyGamblerIdentity           . (, gambyl, jumio, bob)
    >>= onboardWithgamblingService      . (, gambyl, charlie)
    >>= createGamblerUnverifiedIdentity . (, gambyl, charlie)
    >>= verifyGamblerIdentity           . (, gambyl, jumio, charlie)
    >>= onboardWithgamblingService      . (, gambyl, dana)
    >>= createGamblerUnverifiedIdentity . (, gambyl, dana)
    >>= verifyGamblerIdentity           . (, gambyl, jumio, dana)
    >>= onboardWithMarketingService     . (, gambyl, marketingManager)
    >>= onboardWithEventService         . (, gambyl, eventManager)

demoHostedDamlHub : EssentialEntities -> Script EssentialEntities
demoHostedDamlHub entities@EssentialEntities{parties = EssentialParties{gambyl, marketingManager, eventManager}} = do

    debug "Start Essential Onboarding"
    return entities
    >>= onboardWithOperatorRoleToParty .(,gambyl)
    >>= onboardWithGamblingRoleToParty .(,gambyl, gambyl, Offer)
    >>= onboardMarketingService . (, gambyl, marketingManager)
    >>= onboardEventService     . (, gambyl, eventManager)

runDamlHubOnboarding : Script ()
runDamlHubOnboarding  = do
    setTime $ time (date 2022 Apr 13) 0 0 0
    fromAllocatedEssentialParties
    >>= demoHostedDamlHub
    >>= debug

runOnboarding : Script ()
runOnboarding = do
    setTime $ time (date 2022 Apr 13) 0 0 0
    fromAllocatedParties
    >>= demoOnboarding
    >>= debug


onboardWithOperatorRole : HasCallStack => Entities -> Script Entities
onboardWithOperatorRole entities@Entities{parties = Parties{operator, public = publicParty}} = script do

    debug "Onboarding Operator"

    let
      notFoundMsg contract = msgWithFunc $ "Contract not found - " <> show contract

    roleCid <- submit operator do
      createCmd GambylOperator.Role with
                  operator
                  public = fromList [publicParty]
                  optMibOperator = None

    gambylOperatorRoleCid <- submit operator do exerciseCmd roleCid GambylOperator.MarketplaceOperator

    gambylOperatorRole : GambylOperator.Role <- queryContractId operator gambylOperatorRoleCid >>= (return . fromSomeNote (notFoundMsg OperatorRole))

    return $ insertInEntitiesRoleServiceMap @GambylOperator.Role entities operator (Left OperatorRole) (key gambylOperatorRole)

onboardWithOperatorRoleToParty : HasCallStack => (EssentialEntities, Party) -> Script EssentialEntities
onboardWithOperatorRoleToParty (entities@EssentialEntities{parties = EssentialParties{public = publicParty}}, party) = script do

  debug $ "Onboarding Operator" <> show party

  let
    notFoundMsg contract = msgWithFunc $ "Contract not found - " <> show contract

  roleCid <- submit party do
    createCmd GambylOperator.Role with
                operator = party
                public = fromList [publicParty]
                optMibOperator = None

  gambylOperatorRoleCid <- submit party do exerciseCmd roleCid GambylOperator.MarketplaceOperator

  gambylOperatorRole : GambylOperator.Role <- queryContractId party gambylOperatorRoleCid >>= (return . fromSomeNote (notFoundMsg OperatorRole))

  return $ insertInEssentialEntitiesRoleServiceMap @GambylOperator.Role entities party (Left OperatorRole) (key gambylOperatorRole)

onboardWithGamblingRole : HasCallStack => (Entities, Party, ApproachType) -> Script Entities
onboardWithGamblingRole (entities@Entities{parties = Parties{operator, jumio, moneyMatrix, exberry, enetPulse, quickBooks}}, provider, type_) = script do

  debug $ "Onboarding Gambling Role - " <> show provider

  let
      optKey = fromEntitiesRoleServiceMap @GambylOperator.Role entities operator (Left OperatorRole)
      operatorRoleKey : Party  = fromSomeNote (msgWithFunc $ keyNotFound operator OperatorRole) optKey
      integrationParties = Map.fromList [
              ("jumio", jumio),
              ("moneyMatrix", moneyMatrix),
              ("enetpulse", enetPulse),
              ("exberry", exberry),
              ("quickbooks", quickBooks)
          ]

  result <-
    case type_ of
      Offer -> do
        gamblingRoleOfferCid <- submit operator do exerciseByKeyCmd @GambylOperator.Role operatorRoleKey $ GambylOperator.OfferGamblingRole with provider
        submit provider do exerciseCmd gamblingRoleOfferCid GamblingRole.Accept with integrationParties
      Request -> do
        initialRequestCid <- submit provider do createCmd GamblingRole.Request with operator, provider, integrationParties, aggregateFor = None
        gamblingRoleRequestCid <- submit provider do exerciseCmd initialRequestCid $ GamblingRole.RequestAggregate
        submit operator do exerciseByKeyCmd @GambylOperator.Role operatorRoleKey $ GambylOperator.ApproveGamblingRole with gamblingRoleRequestCid, integrationParties

  let
      notFoundMsg contract = msgWithFunc $ "Contract not found - " <> show contract
      queryContract : (HasAgreement a, HasTemplateTypeRep a, HasToAnyTemplate a, HasFromAnyTemplate a, Show b) => ContractId a -> b -> Script a
      queryContract cid a = queryContractId provider cid >>= (return . fromSomeNote (notFoundMsg a))

  gamblingRole    <- queryContract result.gamblingRoleCid GamblingRole
  custodyRole     <- queryContract result.custodyRoleCid CustodyRole
  gamblingService <- queryContract result.gamblingServiceCid GamblingService
  custodyService  <- queryContract result.custodyServiceCid CustodyService
  issuanceService <- queryContract result.issuanceServiceCid IssuanceService
  tradingRole     <- queryContract result.tradingRoleCid ExchangeRole

  let
      entitiesI   = insertInEntitiesRoleServiceMap @GamblingRole.Role         entities    provider    (Left GamblingRole)     (key gamblingRole)
      entitiesII  = insertInEntitiesRoleServiceMap @CustodyRole.Role          entitiesI   provider    (Left CustodyRole)      (key custodyRole)
      entitiesIII = insertInEntitiesRoleServiceMap @GamblingService.Service   entitiesII  provider    (Right GamblingService) (key gamblingService)
      entitiesIV  = insertInEntitiesRoleServiceMap @CustodyService.Service    entitiesIII provider    (Right CustodyService)  (key custodyService)
      entitiesV   = insertInEntitiesRoleServiceMap @Issuance.Service          entitiesIV  provider    (Right IssuanceService) (key issuanceService)
      entitiesVI  = insertInEntitiesRoleServiceMap @TradingRole.Role          entitiesV   provider    (Left ExchangeRole)     (key tradingRole)

  return entitiesVI

onboardWithGamblingRoleToParty : HasCallStack => (EssentialEntities, Party, Party, ApproachType) -> Script EssentialEntities
onboardWithGamblingRoleToParty (entities@EssentialEntities{parties = EssentialParties{jumio, moneyMatrix, enetPulse, exberry, quickBooks}}, party, provider, type_) = script do

  debug $ "Onboarding Gambling Role - " <> show provider

  let
      optKey = fromEssentialEntitiesRoleServiceMap @GambylOperator.Role entities party (Left OperatorRole)
      operatorRoleKey : Party  = fromSomeNote (msgWithFunc $ keyNotFound party OperatorRole) optKey
      integrationParties = Map.fromList [
              ("jumio", jumio),
              ("moneyMatrix", moneyMatrix),
              ("enetpulse", enetPulse),
              ("exberry", exberry),
              ("quickbooks", quickBooks)
          ]

  result <-
    case type_ of
      Offer -> do
          gamblingRoleOfferCid <- submit party do exerciseByKeyCmd @GambylOperator.Role operatorRoleKey $ GambylOperator.OfferGamblingRole with provider
          submit provider do exerciseCmd gamblingRoleOfferCid GamblingRole.Accept with integrationParties
      Request -> do
          initialRequestCid <- submit provider do createCmd GamblingRole.Request with operator = party, provider, integrationParties, aggregateFor = None
          gamblingRoleRequestCid <- submit provider do exerciseCmd initialRequestCid $ GamblingRole.RequestAggregate
          submit party do exerciseByKeyCmd @GambylOperator.Role operatorRoleKey $ GambylOperator.ApproveGamblingRole with gamblingRoleRequestCid, integrationParties

  let
      notFoundMsg contract = msgWithFunc $ "Contract not found - " <> show contract
      queryContract : (HasAgreement a, HasTemplateTypeRep a, HasToAnyTemplate a, HasFromAnyTemplate a, Show b) => ContractId a -> b -> Script a
      queryContract cid a = queryContractId provider cid >>= (return . fromSomeNote (notFoundMsg a))

  gamblingRole        <- queryContract result.gamblingRoleCid GamblingRole
  custodyRole         <- queryContract result.custodyRoleCid CustodyRole
  gamblingService     <- queryContract result.gamblingServiceCid GamblingService
  custodyService      <- queryContract result.custodyServiceCid CustodyService
  issuanceService     <- queryContract result.issuanceServiceCid IssuanceService
  tradingRole         <- queryContract result.tradingRoleCid ExchangeRole
  _                   <- queryContract result.settlementServiceCid SettlementService

  let
      entitiesI   = insertInEssentialEntitiesRoleServiceMap @GamblingRole.Role         entities    provider    (Left GamblingRole)     (key gamblingRole)
      entitiesII  = insertInEssentialEntitiesRoleServiceMap @CustodyRole.Role          entitiesI   provider    (Left CustodyRole)      (key custodyRole)
      entitiesIII = insertInEssentialEntitiesRoleServiceMap @GamblingService.Service   entitiesII  provider    (Right GamblingService) (key gamblingService)
      entitiesIV  = insertInEssentialEntitiesRoleServiceMap @CustodyService.Service    entitiesIII provider    (Right CustodyService)  (key custodyService)
      entitiesV   = insertInEssentialEntitiesRoleServiceMap @Issuance.Service          entitiesIV  provider    (Right IssuanceService) (key issuanceService)
      entitiesVI  = insertInEssentialEntitiesRoleServiceMap @TradingRole.Role          entitiesV   provider    (Left ExchangeRole)     (key tradingRole)
      entitiesVII = insertInEssentialEntitiesRoleServiceMap @TradingRole.Role          entitiesVI  provider    (Left ExchangeRole)     (key tradingRole)

  return entitiesVII

onboardWithgamblingService : HasCallStack => (Entities, Party, Party) -> Script Entities
onboardWithgamblingService (entities, provider, customer) = script do

    debug "Onboarding Gambler"
    giveGamblingService entities provider customer

onboardWithMarketingService : HasCallStack => (Entities, Party, Party) -> Script Entities
onboardWithMarketingService (entities, provider, customer) = script do

    debug "Onboarding MarketingManager"
    giveMarketingService entities provider customer

onboardMarketingService : HasCallStack => (EssentialEntities, Party, Party) -> Script EssentialEntities
onboardMarketingService (entities, provider, customer) = script do

    debug "Onboarding MarketingManager"
    giveEssentialMarketingService entities provider customer

onboardWithEventService: HasCallStack => (Entities, Party, Party) -> Script Entities
onboardWithEventService (entities, provider, customer) = script do

    debug "Onboarding EventManager"
    giveEventService entities provider customer

onboardEventService: HasCallStack => (EssentialEntities, Party, Party) -> Script EssentialEntities
onboardEventService (entities, provider, customer) = script do

    debug "Onboarding EventManager"
    giveEssentialEventService entities provider customer

createGamblerUnverifiedIdentity : HasCallStack => (Entities, Party, Party) -> Script Entities
createGamblerUnverifiedIdentity (entities, provider, customer) = script do

    debug $ "Create Unverified Gambler Identity - " <> show customer
    let
        -- Get Gambling Service from Entities data
        gmblrKey = fromEntitiesRoleServiceMap @GamblingService.Service entities customer (Right GamblingService)
        gamblingServiceKey : (Party, Party, Party) = fromSomeNote (msgWithFunc $ keyNotFound provider GamblingService) gmblrKey

    --TODO: Switch this logic to leverage the listOfKnownParties on a common function on a common library project we have set up.
        partyWithoutNamespace = List.head $ splitOn "::" $ partyToText customer

        firstName    = partyWithoutNamespace
        lastName     = partyWithoutNamespace
        emailAddress = partyWithoutNamespace <> "@test-email.com"
        phoneNumber  = "**********"
        birthday     = date 1996 Jul 10
        city         = "Miami"
        countryCode  = "USA"
        postalCode   = "2345"
        subDivision  = "Longbeach"
        addressLine1 = "Longbeach 123"

    unverifiedIdentityRequestCid <- submit customer do
        exerciseByKeyCmd @GamblingService.Service gamblingServiceKey
                GamblingService.RequestUnverifiedIdentity with ..

    submit provider do exerciseByKeyCmd @GamblingService.Service gamblingServiceKey GamblingService.ApproveUnverifiedIdentity with ..

    debug "3"

    return entities

verifyGamblerIdentity : HasCallStack => (Entities, Party, Party, Party) -> Script Entities
verifyGamblerIdentity (entities, provider, integrationParty, customer) = script do

  debug $ "Verify Gambler Identity - " <> show customer
  let
      -- Get Gambling Service from Entities data
      gmblrKey = fromEntitiesRoleServiceMap @GamblingService.Service entities customer (Right GamblingService)
      gamblingServiceKey : (Party, Party, Party) = fromSomeNote (msgWithFunc $ keyNotFound provider GamblingService) gmblrKey

  eitherIdVerificationRequestOrFail <- submit customer do
    exerciseByKeyCmd @GamblingService.Service gamblingServiceKey
      GamblingService.RequestIdentityVerification with integrationParty, locale = "en-GB"

  initialIdentityVerificationRequestCid <- either
    (queryContractId provider >=> assertFail . msgWithFunc . optional ("ActionFailure contract not found") (.reason))
    (pure)
    eitherIdVerificationRequestOrFail

  identityVerificationRequestCid <- submit integrationParty do
    exerciseCmd initialIdentityVerificationRequestCid IntegrationIdentity.ProcessIdentityRequest with
      redirectUrl = "localhost"
      requestFromHashed = "some_hashed_value"

  Right pendingIdentityCid <- submit customer do
    exerciseByKeyCmd @GamblingService.Service gamblingServiceKey
      GamblingService.StartIdentityVerification with idVerReqCid = identityVerificationRequestCid

  IdentityModel.PendingIdentity{jumioPendingIdKey = pendingIdentityRequestKey} <-
    queryContractId customer pendingIdentityCid
      >>= (return . fromSomeNote (msgWithFunc "Pending Identity contract not found"))

  --TODO: Switch this logic to leverage the listOfKnownParties on a common function on a common library project we have set up.
  let customerName = List.head $ splitOn "::" $ partyToText customer

  verifiedIdentityCid <- submit integrationParty do
    exerciseByKeyCmd @IntegrationIdentity.PendingIdentityRequest pendingIdentityRequestKey
      IntegrationIdentity.VerifyIdentity with
        dataUrl = "localhost"
        userData = IntegrationIdentity.User with
          firstName   = customerName
          lastName    = customerName
          birthday    = date 1996 Jul 10
          city        = "Miami"
          country     = "USA"
          postalCode  = "2345"
          subDivision = "Longbeach"
          addressLine1    = "Longbeach 123"
          addressLine2    = "Longbeach 1234"

  submit provider do
    exerciseByKeyCmd @GamblingService.Service gamblingServiceKey
      GamblingService.VerifyIdentity with verifiedIdCid = verifiedIdentityCid

  return entities


givePermissions : HasCallStack => (Entities , Party , Party , [GamblingModel.Permissions]) -> Script Entities
givePermissions (entities, provider, customer, newPermissions) = script do

    debug $ "Onboard with Gambling Service - " <> show customer
    let
      -- Get Gambling Service from Entities data
      gmblrKey = fromEntitiesRoleServiceMap @GamblingService.Service entities customer (Right GamblingService)
      gamblingServiceKey : (Party, Party, Party) = fromSomeNote (msgWithFunc $ keyNotFound customer GamblingService) gmblrKey

    submit provider do
            exerciseByKeyCmd @GamblingService.Service gamblingServiceKey
                GamblingService.GivePermissions with newPermissions

    return entities

{- ----------------------------------------HELPER FUNCTIONS----------------------------------------- -}

giveGamblingService : HasCallStack => Entities -> Party -> Party -> Script Entities
giveGamblingService entities@Entities{parties = Parties{public}} provider customer = script do

    debug $ "Onboard with Gambling Service - " <> show customer
    let
        -- Get Gambling Role from Entities data
        gmblrKey = fromEntitiesRoleServiceMap @GamblingRole.Role entities provider (Left GamblingRole)
        gamblingRoleKey : (Party, Party)  = fromSomeNote (msgWithFunc $ keyNotFound provider GamblingRole) gmblrKey

    -- Give Gambling Service to Customer party
    gamblingServiceOfferCid <- submit provider do exerciseByKeyCmd @GamblingRole.Role gamblingRoleKey $ GamblingRole.OfferGamblingService with customer
    -- submitMulti is required for the readAs public for visibility on the GlobalGamblingConfiguration where the integration parties are represented
    (gamblingServiceCid, custodyServiceCid, _) <- submitMulti [customer] [public] do exerciseCmd gamblingServiceOfferCid GamblingService.Accept

    -- Attribute Customer with Gambling Service, in key format onto Entities data
    let
        notFoundMsg contract = msgWithFunc $ "Contract not found - " <> show contract
        queryContract : (HasAgreement a, Template a, Show b) => ContractId a -> b -> Script a
        queryContract cid a = queryContractId customer cid >>= (return . fromSomeNote (notFoundMsg a))

    gamblingService  <- queryContract gamblingServiceCid GamblingService
    custodyService   <- queryContract custodyServiceCid CustodyService

    let
        entitiesI   = insertInEntitiesRoleServiceMap @GamblingService.Service entities  customer    (Right GamblingService) (key gamblingService)
        entitiesII  = insertInEntitiesRoleServiceMap @CustodyService.Service  entitiesI customer    (Right CustodyService)  (key custodyService)

    return entitiesII

giveMarketingService : HasCallStack => Entities -> Party -> Party -> Script Entities
giveMarketingService entities provider customer = script do

    debug $ "Onboard with Marketing Service - " <> show customer
    let
        -- Get Gambling Role from Entities data
        gmblrKey = fromEntitiesRoleServiceMap @GamblingRole.Role entities provider (Left GamblingRole)
        gamblingRoleKey : (Party, Party)  = fromSomeNote (msgWithFunc $ keyNotFound provider GamblingRole) gmblrKey

    -- Give Marketing Service to Customer party
    marketingServiceOfferCid <- submit provider do exerciseByKeyCmd @GamblingRole.Role gamblingRoleKey $ GamblingRole.OfferMarketingService with customer
    marketingServiceCid <- submit customer do exerciseCmd marketingServiceOfferCid MarketingService.Accept
    let
        notFoundMsg contract = msgWithFunc $ "Contract not found - " <> show contract
        queryContract : (HasAgreement a, HasTemplateTypeRep a, HasToAnyTemplate a, HasFromAnyTemplate a, Show b) => ContractId a -> b -> Script a
        queryContract cid a = queryContractId customer cid >>= (return . fromSomeNote (notFoundMsg a))
    -- Attribute Customer with Marketing Service, in key format onto Entities data

    marketingService  <- queryContract marketingServiceCid MarketingService

    return $ insertInEntitiesRoleServiceMap @MarketingService.Service entities  customer    (Right MarketingService) (key marketingService)

giveEssentialMarketingService : HasCallStack => EssentialEntities -> Party -> Party -> Script EssentialEntities
giveEssentialMarketingService entities provider customer = script do

    debug $ "Onboard with Marketing Service - " <> show customer
    let
        -- Get Gambling Role from Entities data
        gmblrKey = fromEssentialEntitiesRoleServiceMap @GamblingRole.Role entities provider (Left GamblingRole)
        gamblingRoleKey : (Party, Party)  = fromSomeNote (msgWithFunc $ keyNotFound provider GamblingRole) gmblrKey

    -- Give Marketing Service to Customer party
    marketingServiceOfferCid <- submit provider do exerciseByKeyCmd @GamblingRole.Role gamblingRoleKey $ GamblingRole.OfferMarketingService with customer
    marketingServiceCid <- submit customer do exerciseCmd marketingServiceOfferCid MarketingService.Accept
    let
        notFoundMsg contract = msgWithFunc $ "Contract not found - " <> show contract
        queryContract : (HasAgreement a, HasTemplateTypeRep a, HasToAnyTemplate a, HasFromAnyTemplate a, Show b) => ContractId a -> b -> Script a
        queryContract cid a = queryContractId customer cid >>= (return . fromSomeNote (notFoundMsg a))
    -- Attribute Customer with Marketing Service, in key format onto Entities data

    marketingService  <- queryContract marketingServiceCid MarketingService

    return $ insertInEssentialEntitiesRoleServiceMap @MarketingService.Service entities  customer    (Right MarketingService) (key marketingService)

giveEventService : HasCallStack => Entities -> Party -> Party -> Script Entities
giveEventService entities provider customer = script do

    debug $ "Onboard with Event Service - " <> show customer
    let
        -- Get Gambling Role from Entities data
        gmblrKey = fromEntitiesRoleServiceMap @GamblingRole.Role entities provider (Left GamblingRole)
        gamblingRoleKey : (Party, Party)  = fromSomeNote (msgWithFunc $ keyNotFound provider GamblingRole) gmblrKey

    -- Give Event Service to Customer party
    eventServiceOfferCid <- submit provider do exerciseByKeyCmd @GamblingRole.Role gamblingRoleKey $ GamblingRole.OfferEventService with customer
    eventServiceCid <- submit customer do exerciseCmd eventServiceOfferCid EventService.Accept
    let
        notFoundMsg contract = msgWithFunc $ "Contract not found - " <> show contract
        queryContract : (HasAgreement a, HasTemplateTypeRep a, HasToAnyTemplate a, HasFromAnyTemplate a, Show b) => ContractId a -> b -> Script a
        queryContract cid a = queryContractId customer cid >>= (return . fromSomeNote (notFoundMsg a))
    -- Attribute Customer with Event Service, in key format onto Entities data

    eventService  <- queryContract eventServiceCid EventService
    return $ insertInEntitiesRoleServiceMap @EventService.Service entities  customer    (Right EventService) (key eventService)

giveEssentialEventService : HasCallStack => EssentialEntities -> Party -> Party -> Script EssentialEntities
giveEssentialEventService entities provider customer = script do

    debug $ "Onboard with Event Service - " <> show customer
    let
        -- Get Gambling Role from Entities data
        gmblrKey = fromEssentialEntitiesRoleServiceMap @GamblingRole.Role entities provider (Left GamblingRole)
        gamblingRoleKey : (Party, Party)  = fromSomeNote (msgWithFunc $ keyNotFound provider GamblingRole) gmblrKey

    -- Give Event Service to Customer party
    eventServiceOfferCid <- submit provider do exerciseByKeyCmd @GamblingRole.Role gamblingRoleKey $ GamblingRole.OfferEventService with customer
    eventServiceCid <- submit customer do exerciseCmd eventServiceOfferCid EventService.Accept
    let
        notFoundMsg contract = msgWithFunc $ "Contract not found - " <> show contract
        queryContract : (HasAgreement a, HasTemplateTypeRep a, HasToAnyTemplate a, HasFromAnyTemplate a, Show b) => ContractId a -> b -> Script a
        queryContract cid a = queryContractId customer cid >>= (return . fromSomeNote (notFoundMsg a))
    -- Attribute Customer with Event Service, in key format onto Entities data

    eventService  <- queryContract eventServiceCid EventService
    return $ insertInEssentialEntitiesRoleServiceMap @EventService.Service entities  customer    (Right EventService) (key eventService)


