module Scripts.Bootstrap.Onboarding.Parties where

import Daml.Script

import DA.Optional qualified as Optional


{- ------------------------------------------DATA TYPES------------------------------------------- -}

data Parties = Parties
    with
        alice            : Party
        bob              : Party
        charlie          : Party
        dana             : Party
        enetPulse        : Party
        eventManager     : Party
        exberry          : Party
        gambyl           : Party
        jumio            : Party
        marketingManager : Party
        moneyMatrix      : Party
        operator         : Party
        public           : Party
        quickBooks       : Party
    deriving (Eq, Show)

data EssentialParties = EssentialParties
    with
        enetPulse        : Party
        eventManager     : Party
        exberry          : Party
        gambyl           : Party
        jumio            : Party
        marketingManager : Party
        moneyMatrix      : Party
        public           : Party
        quickBooks       : Party
    deriving (Eq, Show)
{- ---------------------------------------HELPER FUNCTIONS---------------------------------------- -}

allocateParties : Script Parties
allocateParties = do

    alice            <- allocatePartyWithHint "Alice" (PartyIdHint "Alice")
    bob              <- allocatePartyWithHint "Bob" (PartyIdHint "Bob")
    charlie          <- allocatePartyWithHint "Charlie" (PartyIdHint "Charlie")
    dana             <- allocatePartyWithHint "Dana" (PartyIdHint "Dana")
    enetPulse        <- allocatePartyWithHint "EnetPulse" (PartyIdHint "EnetPulse")
    eventManager     <- allocatePartyWithHint "EventManager" (PartyIdHint "EventManager")
    exberry          <- allocatePartyWithHint "Exberry" (PartyIdHint "Exberry")
    gambyl           <- allocatePartyWithHint "Gambyl" (PartyIdHint "Gambyl")
    jumio            <- allocatePartyWithHint "Jumio" (PartyIdHint "Jumio")
    marketingManager <- allocatePartyWithHint "MarketingManager" (PartyIdHint "MarketingManager")
    moneyMatrix      <- allocatePartyWithHint "MoneyMatrix" (PartyIdHint "MoneyMatrix")
    operator         <- allocatePartyWithHint "Operator" (PartyIdHint "Operator")
    public           <- allocatePartyWithHint "Public" (PartyIdHint "Public")
    quickBooks       <- allocatePartyWithHint "QuickBooks" (PartyIdHint "QuickBooks")

    pure $ Parties with ..

allocateEssentialParties : Script EssentialParties
allocateEssentialParties = do

    let
        allocate party = allocatePartyWithHint party $ PartyIdHint party

    enetPulse        <- allocate "enetPulse"
    eventManager     <- allocate "eventManager"
    exberry          <- allocate "exberry"
    gambyl           <- allocate "gambyl"
    jumio            <- allocate "jumio"
    marketingManager <- allocate "marketingManager"
    moneyMatrix      <- allocate "moneyMatrix"
    public           <- allocate "public"
    quickBooks       <- allocate "quickBooks"

    pure $ EssentialParties with ..

getExistingParties : Script Parties
getExistingParties = do
    [operator, gambyl, jumio, enetPulse, exberry, moneyMatrix, alice, bob, charlie, dana, marketingManager, eventManager, public, quickBooks] <-
        retrieve ["Operator", "Gambyl", "Jumio", "EnetPulse", "Exberry", "MoneyMatrix", "Alice", "Bob", "Charlie" ,"Dana", "MarketingManager", "EventManager", "Public", "QuickBooks"]
    return $ Parties with ..
    where
        retrieve = pure . Optional.fromSome . mapA partyFromText

getEssentialParties : Script EssentialParties
getEssentialParties = do
    [ gambyl, jumio, enetPulse, moneyMatrix, marketingManager, eventManager,exberry, quickBooks, public] <-
        retrieve ["gambyl", "jumio", "enetPulse", "moneyMatrix", "marketingManager", "eventManager","exberry", "quickBooks","Public"]
    return $ EssentialParties with ..
    where
        retrieve = pure . Optional.fromSome . mapA partyFromText