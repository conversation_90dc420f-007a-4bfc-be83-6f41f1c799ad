module Scripts.Bootstrap.Gambling.Operations where

import Daml.Script

import DA.Action        (filterA)
import DA.Date          (date, Month(..))
import DA.Either qualified as Either
import DA.Finance.Asset qualified as Asset
import DA.List          (sortBy)
import DA.Map qualified as Map
import DA.Optional      (fromSomeNote)
import DA.Set qualified as Set (union, fromList)
import DA.Stack         (HasCallStack)
import DA.Time          (time)

import ContingentClaims.Claim (
    Claim(Zero),
    serialize
  )

import Marketplace.Issuance.CFI qualified as CFI
import Marketplace.Issuance.Service qualified as Issuance

import EnetPulseIntegration.Events qualified as IntegrationEvents
import MoneyMatrixIntegration.Deposit qualified as Deposit
import MoneyMatrixIntegration.Withdraw qualified as Withdraw

import Scripts.Bootstrap.Onboarding.Entities  (demoOnboarding)
import Scripts.Bootstrap.Onboarding.Parties   (EssentialParties(..), Parties(..))
import Scripts.Common

import Gambyl.Gambling.Bet.Model qualified as BetModel
import Gambyl.Gambling.Bet.Odds.Model qualified as Odds
import Gambyl.Gambling.Event.Model qualified as EventModel
import Gambyl.Gambling.Service qualified as Gambling
import Gambyl.Utils qualified as GambylUtils
import Gambyl.Marketing.Model qualified as MarketingModel

data InputData = InputData with
    customer         : Party
    integrationParty : Party
    currency         : Text
    transactionId    : Text
    requestedAmount  : Numeric 2

requestAsset    assetLabel description cfi claims   = Issuance.RequestOrigination assetLabel cfi description (serialize claims)

-- Assets : tuple (Id, Description)
getAsset    asset   = requestAsset asset._1 asset._2
getEvent    event   = Gambling.RequestEventOrigination event


usdAsset    = ("USD"   ,   "United States Dollar")


n_m_Outcomes@(nWin::nmDraw::mWin::_) = [
        (EventModel.InputOutcomeOdd with outcome = (IntegrationEvents.Outcome (Some "TeamA-id") (1) IntegrationEvents.ThreeWay IntegrationEvents.Win 1), odd = Odds.Decimal 2.0),
        (EventModel.InputOutcomeOdd with outcome = (IntegrationEvents.Outcome None 0 IntegrationEvents.ThreeWay IntegrationEvents.Draw 2), odd =  Odds.Decimal 2.0),
        (EventModel.InputOutcomeOdd with outcome = (IntegrationEvents.Outcome (Some "TeamB-id") (2) IntegrationEvents.ThreeWay IntegrationEvents.Win 3), odd = Odds.Decimal 2.0),
        (EventModel.InputOutcomeOdd with outcome = (IntegrationEvents.Outcome None 0 (IntegrationEvents.OverUnder 1.5) IntegrationEvents.Under 1), odd = Odds.Decimal 2.0),
        (EventModel.InputOutcomeOdd with outcome = (IntegrationEvents.Outcome None 0 (IntegrationEvents.OverUnder 2.5) IntegrationEvents.Over 2), odd = Odds.Decimal 2.0),
        (EventModel.InputOutcomeOdd with outcome = (IntegrationEvents.Outcome None 0 (IntegrationEvents.OverUnder 3.5) IntegrationEvents.Under 3), odd = Odds.Decimal 2.0),
        (EventModel.InputOutcomeOdd with outcome = (IntegrationEvents.Outcome None 0 (IntegrationEvents.OverUnder 4.5) IntegrationEvents.Over 4), odd = Odds.Decimal 2.0),
        (EventModel.InputOutcomeOdd with outcome = (IntegrationEvents.Outcome (Some "TeamA-id") (1) (IntegrationEvents.ThreeWayHandicap 3.0) IntegrationEvents.Win 1), odd = Odds.Decimal 2.0),
        (EventModel.InputOutcomeOdd with outcome = (IntegrationEvents.Outcome (Some "TeamA-id") (2) (IntegrationEvents.ThreeWayHandicap 4.5) IntegrationEvents.Draw 2), odd = Odds.Decimal 2.0),
        (EventModel.InputOutcomeOdd with outcome = (IntegrationEvents.Outcome (Some "TeamB-id") (3) (IntegrationEvents.ThreeWayHandicap (-0.5)) IntegrationEvents.Win 3), odd = Odds.Decimal 2.0)
    ]
cu_oh_Outcomes@[cuWin, cuohDraw, ohWin] = [
        (EventModel.InputOutcomeOdd with outcome = (IntegrationEvents.Outcome (Some "curicounido-id") (1) IntegrationEvents.ThreeWay IntegrationEvents.Win 1), odd = Odds.Decimal 2.0),
        (EventModel.InputOutcomeOdd with outcome = (IntegrationEvents.Outcome None 2 IntegrationEvents.ThreeWay IntegrationEvents.Draw 2), odd = Odds.Decimal 2.0),
        (EventModel.InputOutcomeOdd with outcome = (IntegrationEvents.Outcome (Some "ohiggins-id") (3) IntegrationEvents.ThreeWay IntegrationEvents.Win 3), odd = Odds.Decimal 2.0)
    ]
am_g_Outcomes@[amWin, amgDraw, gWin] = [
        (EventModel.InputOutcomeOdd with outcome = (IntegrationEvents.Outcome (Some "atleticomg-id") (1) IntegrationEvents.ThreeWay IntegrationEvents.Win 1), odd = Odds.Decimal 2.0),
        (EventModel.InputOutcomeOdd with outcome = (IntegrationEvents.Outcome None 0 IntegrationEvents.ThreeWay IntegrationEvents.Draw 2), odd = Odds.Decimal 2.0),
        (EventModel.InputOutcomeOdd with outcome = (IntegrationEvents.Outcome (Some "gremio-id") (2) IntegrationEvents.ThreeWay IntegrationEvents.Win 3), odd = Odds.Decimal 2.0)
    ]
al_et_Outcomes@[alWin, etWin] = [
        (EventModel.InputOutcomeOdd with outcome = IntegrationEvents.Outcome (Some "alexandar-lazarov-id") (1) IntegrationEvents.TwoWay IntegrationEvents.Win 1, odd = Odds.Decimal 2.0),
        (EventModel.InputOutcomeOdd with outcome = IntegrationEvents.Outcome (Some "evgeny-tyurnev-id") (2) IntegrationEvents.TwoWay IntegrationEvents.Win 2, odd = Odds.Decimal 2.0)
    ]
h_ca_Outcomes@[hWin, hcaDraw, caWin] = [
        (EventModel.InputOutcomeOdd with outcome = IntegrationEvents.Outcome (Some "hanam-id") (1) IntegrationEvents.ThreeWay IntegrationEvents.Win 1, odd = Odds.Decimal 2.0),
        (EventModel.InputOutcomeOdd with outcome = IntegrationEvents.Outcome None 0 IntegrationEvents.ThreeWay IntegrationEvents.Draw 2, odd = Odds.Decimal 2.0),
        (EventModel.InputOutcomeOdd with outcome = IntegrationEvents.Outcome (Some "chungnam-athletic-id") (2) IntegrationEvents.ThreeWay IntegrationEvents.Win 3, odd = Odds.Decimal 2.0)
    ]

n_m_Asset   = EventModel.CustomerEvent with
                    assetLabel          = "3759839"
                    description         = "TeamA vs TeamB"
                    market              = EventModel.Sport "1"
                    geography           = "England"
                    submarkets          = [EventModel.Tournament "Tournament"]
                    outcomes            = n_m_Outcomes
                    eventParticipants   = [
                                            IntegrationEvents.Participant "TeamA" "TeamA-id" 1 None,
                                            IntegrationEvents.Participant "TeamB" "TeamB-id" 2 None
                                         ]
                    eventTitle          = Map.fromList [(show GambylUtils.EN_US, "TeamA-TeamB")]
                    eventStatus         = IntegrationEvents.NotStarted
                    eventResults        = []
                    startDate           = getDateTime 2021 Nov 04 0 0 0

cu_oh_Asset = EventModel.CustomerEvent with
                    assetLabel          = "3750696"
                    description         = "CuricoUnido vs O'Higgins"
                    market              = EventModel.Sport "1"
                    geography           = "England"
                    submarkets          = [EventModel.Tournament "PrimeraDivision"]
                    outcomes            = cu_oh_Outcomes
                    eventParticipants   = [
                                            IntegrationEvents.Participant "CuricoUnido" "curicounido-id" 1 None,
                                            IntegrationEvents.Participant "O'Higgins" "ohiggins-id" 2 None
                                         ]
                    eventTitle          = Map.fromList [(show GambylUtils.EN_US, "CuricoUnido-O'Higgins")]
                    eventStatus         = IntegrationEvents.NotStarted
                    eventResults        = []
                    startDate           = getDateTime 2021 Nov 04 0 0 0

am_g_Asset  = EventModel.CustomerEvent with
                    assetLabel          = "3564468"
                    description         = "AtleticoMG vs Gremio"
                    market              = EventModel.Sport "1"
                    geography           = "England"
                    submarkets          = [EventModel.Tournament "SerieA"]
                    outcomes            = am_g_Outcomes
                    eventParticipants   = [
                                            IntegrationEvents.Participant "AtleticoMG" "atleticomg-id" 1 None,
                                            IntegrationEvents.Participant "Gremio" "gremio-id" 2 None
                                         ]
                    eventTitle          = Map.fromList [(show GambylUtils.EN_US, "AtleticoMG-Gremio")]
                    eventStatus         = IntegrationEvents.NotStarted
                    eventResults        = []
                    startDate           = getDateTime 2021 Nov 04 0 0 0

al_et_Asset = EventModel.CustomerEvent with
                    assetLabel          = "3779397"
                    description         = "Alexandar Lazarov-Evgeny Tyurnev"
                    market              = (EventModel.Sport "2")
                    geography           = "England"
                    submarkets          = [EventModel.Tournament "Antalya 4"]
                    outcomes            = al_et_Outcomes
                    eventParticipants   = [
                                            IntegrationEvents.Participant "Alexandar Lazarov" "alexandar-lazarov-id" 1 None,
                                            IntegrationEvents.Participant "Evgeny Tyurnev" "evgeny-tyurnev-id" 2 None
                                         ]
                    eventTitle          = Map.fromList [(show GambylUtils.EN_US, "Alexandar Lazarov-Evgeny Tyurnev")]
                    eventStatus         = IntegrationEvents.NotStarted
                    eventResults        = []
                    startDate           = (getDateTime 2021 Dec 12 7 10 0)

h_ca_Asset  = EventModel.CustomerEvent with
                    assetLabel          = "3764239"
                    description         = "Hanam vs Chungnam Athletic"
                    market              = (EventModel.Sport "20")
                    geography           = "South Korea"
                    submarkets          = [EventModel.Tournament "1st League"]
                    outcomes            = h_ca_Outcomes
                    eventParticipants   = [
                                              IntegrationEvents.Participant "Hanam" "hanam-id" 1 None,
                                              IntegrationEvents.Participant "Chungnam Athletic" "chungnam-athletic-id" 2 None
                                          ]
                    eventTitle          = Map.fromList [(show GambylUtils.EN_US, "Hanam-Chungnam Athletic")]
                    eventStatus         = IntegrationEvents.NotStarted
                    eventResults        = []
                    startDate           = (getDateTime 2021 Dec 10 6 0 0)

usd     = getAsset usdAsset CFI.currency Zero
n_m     = getEvent n_m_Asset
cu_oh   = getEvent cu_oh_Asset
am_g    = getEvent am_g_Asset
al_X_et = getEvent al_et_Asset
h_X_ca  = getEvent h_ca_Asset

creditCustomers : ScriptData -> Script ScriptData
creditCustomers scriptData@ScriptData{entities = Entities{parties = Parties{alice, bob, charlie, moneyMatrix}}} = do
    now <- getTime
    debug $ "now: " <> show now
    debug "Credit Customers"
    return scriptData
    >>= creditCustomerAccount . (, (InputData with customer = alice, integrationParty = moneyMatrix, currency = usdAsset._1, transactionId = "creditTransactionId", requestedAmount = 20000.00))
    >>= creditCustomerAccount . (, (InputData with customer = bob, integrationParty = moneyMatrix, currency = usdAsset._1, transactionId = "creditTransactionId", requestedAmount =20000.00))
    >>= creditCustomerAccount . (, (InputData with customer = charlie, integrationParty = moneyMatrix, currency = usdAsset._1, transactionId = "creditTransactionId", requestedAmount =20000.00))


debitCustomers : ScriptData -> Script ScriptData
debitCustomers scriptData@ScriptData{entities = Entities{parties = Parties{alice, moneyMatrix}}} = do
    debug "Debit Customers"
    return scriptData
    >>= debitCustomerAccount . (, (InputData with customer = alice, integrationParty = moneyMatrix, currency = usdAsset._1, transactionId = "debitTransactionId", requestedAmount = 400.00))


demoGambyl : Entities -> Script ScriptData
demoGambyl entities@Entities{parties = Parties{gambyl, eventManager}} = do
    debug "Start Market"
    return entities
    >>= buildScriptDataMaps
    >>= originateAsset      . (, usd, gambyl)
    >>= issueAsset          . (, gambyl, 10_000_000.00, usdAsset._1)
    >>= originateEvent      . (, n_m, gambyl, [eventManager])
    >>= creditCustomers
    >>= debitCustomers

demoGambylClean : Entities -> Script ScriptData
demoGambylClean entities@Entities{parties = Parties{gambyl}} = do
    debug "Starting Clean Market"
    return entities
    >>= buildScriptDataMaps
    >>= originateAsset      . (, usd, gambyl)
    >>= issueAsset          . (, gambyl, 10_000_000.00, usdAsset._1)


demoHostedGambyl : EssentialEntities -> Script ScriptEssentialData
demoHostedGambyl entities@EssentialEntities{parties = EssentialParties{gambyl}} = do
    debug "Start Market"
    return entities
    >>= buildScriptEssentialDataMaps
    >>= originateEssentialAssets      . (, usd, gambyl)


runGambyl : Script ()
runGambyl = do
    setTime $ time (date 2022 Apr 13) 0 0 0
    fromAllocatedParties
    >>= demoOnboarding
    >>= demoGambyl
    >>= debug

{- ---------------------------------------MARKET OPERATIONS--------------------------------------- -}

originateEvent : HasCallStack => (ScriptData, Gambling.RequestEventOrigination, Party, [Party]) -> Script ScriptData
originateEvent (scriptData@ScriptData{entities = entitiesData, events}, originationRequest, customer, managers) = do

    let
        -- Get Issuance Service from Entities data
        optKey = fromEntitiesRoleServiceMap @Gambling.Service entitiesData customer (Right GamblingService)
        gamblingServiceKey : (Party, Party, Party) = fromSomeNote (msgWithFunc $ keyNotFound customer GamblingService) optKey

    eitherRequestCidOrError <- submit customer do
      exerciseByKeyCmd @Gambling.Service gamblingServiceKey originationRequest

    case eitherRequestCidOrError of
      Right requestCid -> do
        eitherApprovedEventOrFail <- submit gamblingServiceKey._2 do
          exerciseByKeyCmd @Gambling.Service gamblingServiceKey
            Gambling.ApproveEventOrigination with requestCid, managers = Set.fromList managers

        either
          (\ actionFailureCid -> do
            optActionFailure <- queryContractId gamblingServiceKey._2 actionFailureCid
            let failureReason = optional ( "ActionFailure contract not found") (.reason) optActionFailure
            debug $ msgWithFunc failureReason
            pure scriptData)
          (either
            (return . const scriptData)
            (\ eventCid ->
                  fromSomeNote (msgWithFunc "Event Instrument doesn't exist") <$> queryContractId customer eventCid
                    >>= (\ eventInstrument -> return scriptData with events = insertInInstrumentsMap events (key eventInstrument))
              ) . fst)
          eitherApprovedEventOrFail

      Left failureCid -> do
        queryContractId customer failureCid >>= optional
          (assertFail $ msgWithFunc "ActionFailure contract not found")
          (\ failure -> debug $ msgWithFunc failure.reason)
        return scriptData


originateAsset : HasCallStack => (ScriptData, ([Party] -> Issuance.RequestOrigination), Party) -> Script ScriptData
originateAsset (scriptData@ScriptData{entities = entitiesData@Entities{parties = Parties{public}}, assets}, originationRequestWith, issuer) = do

    let
        -- Get Issuance Service from Entities data
        optKey = fromEntitiesRoleServiceMap @Issuance.Service entitiesData issuer (Right IssuanceService)
        issuanceServiceKey : (Party, Party, Party)  = fromSomeNote (msgWithFunc $ keyNotFound issuer IssuanceService) optKey
        provider = issuanceServiceKey._2

    createOriginationCid    <- submit issuer do exerciseByKeyCmd @Issuance.Service issuanceServiceKey $ originationRequestWith [public]
    (_, assetDesc)          <- submit provider do exerciseByKeyCmd @Issuance.Service issuanceServiceKey $ Issuance.Originate with createOriginationCid

    return $ scriptData with
                assets = insertInAssetsMap assets assetDesc.assetId

originateEssentialAssets : HasCallStack => (ScriptEssentialData, ([Party] -> Issuance.RequestOrigination), Party) -> Script ScriptEssentialData
originateEssentialAssets (scriptData@ScriptEssentialData{entities = entitiesData@EssentialEntities{parties = EssentialParties{public}}, assets}, originationRequestWith, issuer) = do
    let
        -- Get Issuance Service from Entities data
        optKey = fromEssentialEntitiesRoleServiceMap @Issuance.Service entitiesData issuer (Right IssuanceService)
        issuanceServiceKey : (Party, Party, Party)  = fromSomeNote (msgWithFunc $ keyNotFound issuer IssuanceService) optKey
        provider = issuanceServiceKey._2

    createOriginationCid    <- submit issuer do exerciseByKeyCmd @Issuance.Service issuanceServiceKey $ originationRequestWith [public]
    (_, assetDesc)          <- submit provider do exerciseByKeyCmd @Issuance.Service issuanceServiceKey $ Issuance.Originate with createOriginationCid

    return $ scriptData with
                assets = insertInAssetsMap assets assetDesc.assetId

issueAsset : HasCallStack => (ScriptData, Party, Decimal, Text) -> Script ScriptData
issueAsset (scriptData@ScriptData{entities = entitiesData, assets}, issuer, quantity, assetLabel) = do

    let
        -- Get Issuance Service from Entities data
        optKey = fromEntitiesRoleServiceMap @Issuance.Service entitiesData issuer (Right IssuanceService)
        issuanceServiceKey : (Party, Party, Party) = fromSomeNote (msgWithFunc $ keyNotFound issuer IssuanceService) optKey
        optAssetId = fromAssetsMap assets assetLabel
        assetId = fromSomeNote (msgWithFunc $ "Asset not found" ) optAssetId
    account                    <- getAccountFromCustodyService entitiesData issuer

    let requestCreateIssuance =
            Issuance.RequestCreateIssuance with
                issuanceId = (partyToText issuer) <> "_" <> assetId.label
                account
                assetId
                quantity

    createIssuanceRequestCid <- submit issuer do exerciseByKeyCmd @Issuance.Service issuanceServiceKey requestCreateIssuance
    (_, depositCid) <- submit issuanceServiceKey._2 do exerciseByKeyCmd @Issuance.Service issuanceServiceKey $ Issuance.CreateIssuance with createIssuanceRequestCid

    return $ scriptData with
                entities    = insertInEntitiesDepositMap entitiesData issuer assetId.label [depositCid]


creditCustomerAccount : HasCallStack => (ScriptData, InputData) -> Script ScriptData
creditCustomerAccount (scriptData@ScriptData{entities, assets}, InputData{..}) = script do

    let
        -- Get Custody Service from Entities data
        optKey = fromEntitiesRoleServiceMap @Gambling.Service entities customer (Right GamblingService)
        gamblingServiceKey : (Party, Party, Party) = fromSomeNote (msgWithFunc $ keyNotFound customer GamblingService) optKey
        provider = gamblingServiceKey._2

        _ = fromSomeNote (msgWithFunc "Asset hasn't been originated") $ fromAssetsMap assets currency

        requestDepositAccount = Gambling.RequestDepositAccount with
          integrationParty
          requestedAmount
          currency
          transactionId
          promotion = None
          language = "EN"
        processDepositRequest= Deposit.ProcessDepositRequest with
          cashierURL      = "gambyl-test/cashier-url"
          transactionCode = "SAD88S9A8D8SA9SA9DSA9"
          countryCode     = "US"

    depositRequestForApprovalCid <- submit customer do exerciseByKeyCmd @Gambling.Service gamblingServiceKey requestDepositAccount
    eitherRequestOrFail <- submit provider do exerciseByKeyCmd @Gambling.Service gamblingServiceKey Gambling.ApproveDepositRequest with depositRequestForApprovalCid

    assertMsg (msgWithFunc "Deposit Request failed: " <> show (eitherRequestOrFail)) $ Either.isRight eitherRequestOrFail
    let Right approveDepositRequestCid = eitherRequestOrFail

    depositRequestCid <- submit integrationParty do exerciseCmd approveDepositRequestCid processDepositRequest
    depositTransactionCid <- submit integrationParty do exerciseCmd depositRequestCid Deposit.FinalizeTransaction with confirmedAmount = requestedAmount
    _  <- submit provider do exerciseByKeyCmd @Gambling.Service gamblingServiceKey Gambling.FinalizeDepositRequest with depositTransactionCid

    return scriptData


debitCustomerAccount : HasCallStack => (ScriptData, InputData) -> Script ScriptData
debitCustomerAccount (scriptData@ScriptData{entities, assets}, InputData{..}) = script do

    let
        -- Get Custody Service from Entities data
        optKey = fromEntitiesRoleServiceMap @Gambling.Service entities customer (Right GamblingService)
        gamblingServiceKey : (Party, Party, Party) = fromSomeNote (msgWithFunc $ keyNotFound customer GamblingService) optKey
        provider = gamblingServiceKey._2

        _ = fromSomeNote (msgWithFunc "Asset hasn't been originated") $ fromAssetsMap assets currency

        requestWithdrawAccount    = Gambling.RequestWithdrawAccount with
                                    integrationParty
                                    requestedAmount
                                    currency
                                    transactionId
                                    promotion = None
                                    language = "EN"
        processWithdrawRequest   = Withdraw.ProcessWithdrawRequest with
                                    cashierURL      = "gambyl-test/cashier-url"
                                    transactionCode = "23ASNEW9523NASLD6"
                                    countryCode     = "US"

    withdrawRequestForApprovalCid   <- submit customer do exerciseByKeyCmd @Gambling.Service gamblingServiceKey requestWithdrawAccount
    eitherRequestOrFail             <- submit provider do exerciseByKeyCmd @Gambling.Service gamblingServiceKey Gambling.ApproveWithdrawRequest with withdrawRequestForApprovalCid

    assertMsg (msgWithFunc "Withdraw Request failed: " <> show (eitherRequestOrFail)) $ Either.isRight eitherRequestOrFail
    let Right approveWithdrawRequestCid = eitherRequestOrFail

    withdrawRequestCid <- submit integrationParty do exerciseCmd approveWithdrawRequestCid processWithdrawRequest
    withdrawTransactionCid <- submit integrationParty do exerciseCmd withdrawRequestCid Withdraw.FinalizeTransaction with confirmedAmount = requestedAmount
    _ <- submit provider do
        exerciseByKeyCmd @Gambling.Service gamblingServiceKey Gambling.FinalizeWithdrawRequest with withdrawTransactionCid

    return scriptData


placeBet : HasCallStack => (ScriptData, Party, Text, Text, BetModel.Side, Optional MarketingModel.PromotionKey) -> Script ScriptData
placeBet (scriptData@ScriptData{entities = entities@Entities{parties = Parties{public}}, events}, customer, eventLabel, betPlacementId, side, promotion) = script do

    let
        -- Get Custody Service from Entities data
        optKey = fromEntitiesRoleServiceMap @Gambling.Service entities customer (Right GamblingService)
        gamblingServiceKey : (Party, Party, Party) = fromSomeNote (msgWithFunc $ keyNotFound customer GamblingService) optKey
        eventKey        = fromSomeNote ("Event doesn't exist") $ fromInstrumentsMap events eventLabel
        betFailed type_ = msgWithFunc $ "Bet Placement " <> type_ <> " failed for event " <> eventKey._3

    (_, eventInstrument) <- fromSomeNote (msgWithFunc "Event Instrument contract not found") <$>
        queryContractKey @EventModel.EventInstrument public eventKey

    _ <- submit customer do
        fromRightNote (betFailed "request") <$>
            exerciseByKeyCmd @Gambling.Service gamblingServiceKey
                Gambling.RequestBetPlacement with
                    promotion
                    inBulk = None
                    betDetails = BetModel.Details with
                        eventTitle      = eventInstrument.details.eventTitle
                        eventKey        = eventKey
                        eventStartDate  = eventInstrument.details.startDate
                        betPlacementId
                        side

    return $ scriptData


placeBetsInBulk : HasCallStack => (ScriptData, Party, [BetModel.Details], Optional MarketingModel.PromotionKey) -> Script ScriptData
placeBetsInBulk (scriptData@ScriptData{entities}, customer, betDetailsList, promotion) = script do

    let
        -- Get Custody Service from Entities data
        optKey = fromEntitiesRoleServiceMap @Gambling.Service entities customer (Right GamblingService)
        gamblingServiceKey : (Party, Party, Party) = fromSomeNote (msgWithFunc $ keyNotFound customer GamblingService) optKey
        operator = gamblingServiceKey._1
        provider = gamblingServiceKey._2

    bulkBetPlacementRequestCid <- submit customer do
        fromRightNote (msgWithFunc $ "Bulk Bet Placement request failed") <$>
            exerciseByKeyCmd @Gambling.Service gamblingServiceKey
                Gambling.RequestBulkBetPlacement with betDetailsList, promotion

    currentUserBetPlacements <- queryFilter @BetModel.BetPlacement customer (\bet -> bet.status == BetModel.Unmatched)
    let (_, userBetPlacement) = unzip currentUserBetPlacements
        userBetPlacementsKeys = map key userBetPlacement
    Right bulkBetPlacementFinReqCid <- submit provider do
        exerciseByKeyCmd @Gambling.Service gamblingServiceKey
            Gambling.ApproveBulkBetPlacement with bulkBetPlacementRequestCid, userBetPlacementsKeys

    betPlacementList <- (submit provider do
        exerciseByKeyCmd @Gambling.Service gamblingServiceKey
            Gambling.FinalizeBulkBetPlacement with bulkBetPlacementFinReqCid) >>=
            either
            (\ betPlacementRejectedCid -> do
                betPlacementRejected <-
                    fromSomeNote "Bet Placement Rejected Contract not found" <$>
                        queryContractId customer betPlacementRejectedCid
                assertFail $ msgWithFunc betPlacementRejected.reason)
            (\ bulkBetPlacementCid -> do
                    BetModel.BulkBetPlacement{betPlacementIdList} <-
                        fromSomeNote "BulkBetPlacement contract not found" <$> queryContractId customer bulkBetPlacementCid

                    mapA (\ betPlacementId -> return (operator, provider, betPlacementId)) betPlacementIdList
                )


    return $ scriptData
        with entities = foldl (`insertInEntitiesBetsMap` customer) entities betPlacementList

{- ----------------------------------------HELPER FUNCTIONS----------------------------------------- -}

getDepositForQuantity : HasCallStack => ScriptData -> Party  -> Text -> Decimal -> [Party] -> Script (ScriptData, ContractId Asset.AssetDeposit)
getDepositForQuantity scriptData@ScriptData{entities = entitiesData} customer  assetLabel quantity observers = script do

    let
        optDeposits = fromEntitiesDepositMap entitiesData customer assetLabel
        noDeposit   = msgWithFunc "Customer " <> (partyToText customer) <> " has no deposits for asset " <> assetLabel

    depositTupleListPre <- mapA (\ oldCid -> do

            deposit <- queryContractId customer oldCid >>= (return . fromSomeNote (msgWithFunc $ "Deposit not found with " <> (show customer) <> " as customer and " <> assetLabel <> " as the asset label and " <> (show quantity)))

            let
                existingObservers   = deposit.observers
                addedObservers      = Set.fromList observers

            newCid <- submit deposit.account.owner do exerciseCmd oldCid $ Asset.AssetDeposit_SetObservers with newObservers = existingObservers `Set.union` addedObservers
            deposit <- queryContractId customer newCid >>= (return . fromSomeNote (msgWithFunc $ "DepositX not found with " <> (show customer) <> " as customer and " <> assetLabel <> " as the asset label and " <> (show quantity)))

            return ((oldCid ,newCid), deposit)
        ) $ fromSomeNote noDeposit optDeposits

    let
        oldCids = map (\ ((old, _), _) -> old) depositTupleListPre
        depositTupleList = map (\ ((_, x), y) -> (x, y)) depositTupleListPre
        list = map(fst) depositTupleList
        scriptDataRmOld = scriptData with entities = removeFromEntitiesDepositMap entitiesData customer assetLabel oldCids
        scriptDataInsNew = scriptDataRmOld with entities = insertInEntitiesDepositMap scriptDataRmOld.entities customer assetLabel list

    let
        sortAsc (x0,_) (x1, _)
            | x0 > x1 = GT
            | x0 < x1 = LT
            | otherwise = EQ

        depositsWithQuantity    = map (\ tuple@(_, deposit) -> (deposit.asset.quantity, tuple)) depositTupleList
        sortedDeposits          = sortBy sortAsc depositsWithQuantity
        depositsGreaterQuantity = filter ((>= quantity) . fst) sortedDeposits

    ((cid, deposit), depositsToRemove) <-
            if (not $ null depositsGreaterQuantity) then do
                debug "Single deposit with sufficient quantity found"
                let ((_, deposit))::_ = depositsGreaterQuantity

                return (deposit, [])
            else do
                debug "Not enough quantity in single deposit. Attemping to merge deposits.."
                assertMsg (msgWithFunc "No more assets to be merged") $ length sortedDeposits > 1
                let
                    (mergedQuantity, mergedCids@(cid::depositCids)) = foldl (\ acc@(value, cidList) (depQuantity, (cid, _)) ->
                            if value < quantity then (value + depQuantity, cid :: cidList)
                            else acc
                        ) (0.0, []) sortedDeposits

                assertMsg ("Still not enough quantity in deposit after merger") $ mergedQuantity >= quantity

                depositCid  <- submit customer do exerciseCmd cid $ Asset.AssetDeposit_Merge with depositCids
                deposit     <- queryContractId customer depositCid >>= (return . fromSomeNote (msgWithFunc noDeposit))

                return ((depositCid, deposit), mergedCids)

    (newEntities, depositWithQuantityCid) <-
        if (deposit.asset.quantity > quantity) then do
            debug "Deposit overfills necessary quantity. Splitting..."

            splitCids@(depositWithQuantityCid::_) <- submit customer do exerciseCmd cid $ Asset.AssetDeposit_Split with quantities = [quantity]

            let
                entitiesI   = insertInEntitiesDepositMap scriptDataInsNew.entities customer assetLabel splitCids
                entitiesII  = removeFromEntitiesDepositMap entitiesI customer assetLabel [cid]
            return (entitiesII, depositWithQuantityCid)
        else do
            debug "Deposit contains exact quantity needed"
            let entitiesI = insertInEntitiesDepositMap scriptDataInsNew.entities customer assetLabel [cid]
            return (entitiesI, cid)

    let data_ = scriptDataInsNew with entities = removeFromEntitiesDepositMap newEntities customer assetLabel depositsToRemove

    return (data_, depositWithQuantityCid)


getDepositsWithAccount : HasCallStack => ScriptData -> Party -> Text -> (Text -> AccountType) -> Script [ContractId Asset.AssetDeposit]
getDepositsWithAccount ScriptData{entities} customer assetLabel accountType = script do
    let
        optDeposits         = fromEntitiesDepositMap entities customer assetLabel
        noDeposit           = "Customer " <> (partyToText customer) <> " has no deposits for asset " <> assetLabel
        depositCids         = fromSomeNote (msgWithFunc noDeposit) optDeposits

    filterA (\ cid -> do
            deposit <- queryContractId customer cid >>= (return . fromSomeNote (msgWithFunc "Deposit not found"))
            let marginLabel = show $ accountType (partyToText customer)
            return $ marginLabel == deposit.account.id.label
        ) depositCids
