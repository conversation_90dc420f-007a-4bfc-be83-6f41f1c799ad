module Scripts.Bootstrap.Integrations.QuickBooks where

import Daml.Script

import DA.Either qualified as Either

import Scripts.Common
import QuickbooksIntegration.Integration qualified as Integration


demoIntegrationEvents : (HasField "entities" a b, HasField "configs" a (Either QuickBooksConfigs (ContractId Integration.Configuration))) => a -> Script b
demoIntegrationEvents integrationData =
    (.entities) <$>
    (script do
            debug "Start Integration Events"
            return integrationData
            >>= configureIntegration
    )


runIntegrationEvents : Script ()
runIntegrationEvents = fromAllocatedParties
    >>= flip mapToIntegrationData QuickBooksConfigs
    >>= demoIntegrationEvents
    >>= debug

runEssentialIntegrationEvents : Script ()
runEssentialIntegrationEvents = fromAllocatedEssentialParties
    >>= flip mapEssentialToIntegrationData QuickBooksConfigs
    >>= demoIntegrationEvents
    >>= debug

{- ------------------------------INTEGRATION QUICKBOOKS OPERATION------------------------------- -}

configureIntegration : (HasField "entities" a b, HasField "configs" a (Either QuickBooksConfigs (ContractId Integration.Configuration))) => a -> Script a
configureIntegration integrationData = script do

    assertMsg (msgWithFunc "Configuration from incorrect source") $ Either.isLeft integrationData.configs

    let Left QuickBooksConfigs{integrationParty} = integrationData.configs

    configurationCid <- submit integrationParty do
        createCmd Integration.Configuration with observers = [], ..

    return integrationData with configs = Right configurationCid

{- ---------------------------------------HELPER FUNCTIONS---------------------------------------- -}

mapToIntegrationData : Entities -> (Party -> QuickBooksConfigs) -> Script IntegrationData
mapToIntegrationData entities configuration = script do
    return IntegrationData with configs = Left (configuration entities.parties.quickBooks), entities


mapEssentialToIntegrationData : EssentialEntities -> (Party -> QuickBooksConfigs) -> Script EssentialIntegrationData
mapEssentialToIntegrationData entities configuration = script do
    return EssentialIntegrationData with configs = Left (configuration entities.parties.quickBooks), entities

{- ------------------------------------------DATA TYPES------------------------------------------- -}

data QuickBooksConfigs = QuickBooksConfigs
    with
        integrationParty    : Party
    deriving (Show, Eq)


data IntegrationData = IntegrationData
    with
        entities            : Entities
        configs             : Either QuickBooksConfigs (ContractId Integration.Configuration)
    deriving (Show)

data EssentialIntegrationData = EssentialIntegrationData
    with
        entities            : EssentialEntities
        configs             : Either QuickBooksConfigs (ContractId Integration.Configuration)
    deriving (Show)