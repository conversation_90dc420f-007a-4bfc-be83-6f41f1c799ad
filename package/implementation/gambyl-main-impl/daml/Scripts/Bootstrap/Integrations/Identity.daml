module Scripts.Bootstrap.Integrations.Identity where

import Daml.Script
import DA.Either qualified as Either (isLeft)

import JumioIntegration.Configuration qualified as Configuration

import Scripts.Common
import Scripts.Bootstrap.Onboarding.Parties (Parties(..),EssentialParties(..))


demoIntegrationIdentity : IntegrationData -> Script Entities
demoIntegrationIdentity integrationData =
    (.entities) <$>
    (script do
            debug "Start Integration Identity"
            return integrationData
        >>= configureIntegration
    )

demoEssentialIntegrationIdentity : EssentialIntegrationData -> Script EssentialEntities
demoEssentialIntegrationIdentity integrationData =
    (.entities) <$>
    (script do
            debug "Start Essential Integration Identity"
            return integrationData
        >>= configureEssentialIntegration
    )

runIntegrationIdentity : Script ()
runIntegrationIdentity = fromAllocatedParties
    >>= flip mapToIntegrationData (JumioConfigs "test-token" "test-secret-token")
    >>= demoIntegrationIdentity
    >>= debug

runEssentialIntegrationIdentity : Script ()
runEssentialIntegrationIdentity = fromAllocatedEssentialParties
    >>= flip mapEssentialToIntegrationData (JumioConfigs "test-token" "test-secret-token")
    >>= demoEssentialIntegrationIdentity
    >>= debug
{- --------------------------------INTEGRATION IDENTITY OPERATIONS-------------------------------- -}

configureIntegration : IntegrationData -> Script IntegrationData
configureIntegration integrationData@IntegrationData{configs} = script do

    assertMsg (msgWithFunc "Configuration from incorrect source") $ Either.isLeft configs

    let Left JumioConfigs{integrationParty, token, secretToken, observers} = configs

    configurationCid <- submit integrationParty do
        createCmd Configuration.Configuration with ..

    return integrationData with configs = Right configurationCid

configureEssentialIntegration : EssentialIntegrationData -> Script EssentialIntegrationData
configureEssentialIntegration integrationData@EssentialIntegrationData{configs} = script do

    assertMsg (msgWithFunc "Configuration from incorrect source") $ Either.isLeft configs

    let Left JumioConfigs{integrationParty, token, secretToken, observers} = configs

    configurationCid <- submit integrationParty do
        createCmd Configuration.Configuration with ..

    return integrationData with configs = Right configurationCid

{- ---------------------------------------HELPER FUNCTIONS---------------------------------------- -}

mapToIntegrationData : Entities -> (Party -> [Party] -> JumioConfigs) -> Script IntegrationData
mapToIntegrationData entities@Entities{parties = Parties{gambyl, jumio}} configuration = script do
    return IntegrationData with configs = Left (configuration jumio [gambyl]), ..

mapEssentialToIntegrationData : EssentialEntities -> (Party -> [Party] -> JumioConfigs) -> Script EssentialIntegrationData
mapEssentialToIntegrationData entities@EssentialEntities{parties = EssentialParties{gambyl, jumio}} configuration = script do
    return EssentialIntegrationData with configs = Left (configuration jumio [gambyl]), ..
{- ------------------------------------------DATA TYPES------------------------------------------- -}

data JumioConfigs = JumioConfigs
    with
        token : Text
        secretToken : Text
        integrationParty : Party
        observers : [Party]
    deriving (Show, Eq)

data JumioCredentials = JumioCredentials
    with
        token       : Text
        secretToken : Text
    deriving (Show, Eq)


data IntegrationData = IntegrationData
    with
        entities    : Entities
        configs     : Either JumioConfigs (ContractId Configuration.Configuration)
    deriving (Show)

data EssentialIntegrationData = EssentialIntegrationData
    with
        entities    : EssentialEntities
        configs     : Either JumioConfigs (ContractId Configuration.Configuration)
    deriving (Show)