module Scripts.Bootstrap.Integrations.Events where

import Daml.Script

import DA.Date      (Month(..))
import DA.Either qualified as Either (isRight, isLeft)
import DA.Optional qualified as Optional (fromSomeNote)
import DA.Stack     (HasCallStack)

import EnetPulseIntegration.Configuration qualified as Configuration
import EnetPulseIntegration.Events qualified as IntegrationEvents

import Scripts.Bootstrap.Onboarding.Parties (EssentialParties(..), Parties(..))
import Scripts.Common

requestEvent eventId eventDetails integrationParty observers integrationTime = IntegrationEvents.EventInstrument with
    integrationParty, observers, eventId, eventDetails, status = IntegrationEvents.NotStarted, results = [], integrationTime, liveEvent = "yes"

getEvent event = requestEvent event._1 event._2

n_m_Event = (
        "123456" ,
        IntegrationEvents.Details with
            sportFK             = "1"
            tournamentStageName = "PrimeraDivisionClausura"
            geography           = "Uruguai"
            eventParticipants   = [
                        IntegrationEvents.Participant "Nacional" "nacional-id" 1 None,
                        IntegrationEvents.Participant "MontevideoCityTorque" "montevideocitytorque-id" 1 None
                    ]
            outcomes            = [
                    (IntegrationEvents.OutcomeOdds with outcome = IntegrationEvents.Outcome (Some "nacional-id") (1) IntegrationEvents.ThreeWay IntegrationEvents.Win 1, odd = 2.0),
                    (IntegrationEvents.OutcomeOdds with outcome = IntegrationEvents.Outcome None 0 IntegrationEvents.ThreeWay IntegrationEvents.Draw 2, odd = 2.0),
                    (IntegrationEvents.OutcomeOdds with outcome = IntegrationEvents.Outcome (Some "montevideocitytorque-id") (2) IntegrationEvents.ThreeWay IntegrationEvents.Win 3, odd = 2.0)
                ]
            eventTitle          = "Nacional-MontevideoCityTorque"
            eventGame           = Some "1"
            startDate           = getDateTime 2021 Nov 04 0 0 0
        )

n_m = getEvent n_m_Event

demoIntegrationEvents : IntegrationData -> Script Entities
demoIntegrationEvents integrationData =
    (.entities) <$>
    (script do
            debug "Start Integration IntegrationEvents"
            return integrationData
        >>= configureIntegration
        -- createEventInstrument   . (, n_m, enetPulse)
    )

demoEssentialIntegrationEvents : EssentialIntegrationData -> Script EssentialEntities
demoEssentialIntegrationEvents integrationData =
    (.entities) <$>
    (script do
            debug "Start Integration IntegrationEvents"
            return integrationData
        >>= configureEssentialIntegration
    )

runIntegrationEvents : Script ()
runIntegrationEvents = fromAllocatedParties
    >>= flip mapToIntegrationData (EnetPulseConfigs "test-user" "test-token")
    >>= demoIntegrationEvents
    >>= debug

runEssentialIntegrationEvents : Script ()
runEssentialIntegrationEvents = fromAllocatedEssentialParties
    >>= flip mapEssentialToIntegrationData (EnetPulseConfigs "test-user" "test-token")
    >>= demoEssentialIntegrationEvents
    >>= debug

{- ---------------------------------INTEGRATION EVENT OPERATIONS---------------------------------- -}



configureIntegration : HasCallStack => IntegrationData -> Script IntegrationData
configureIntegration (~integrationData@IntegrationData{configs}) = script do

    assertMsg (msgWithFunc "Configuration from incorrect source") $ Either.isLeft configs

    let Left EnetPulseConfigs{integrationParty, user, token, observers} = configs

    configurationCid <- submit integrationParty do
        createCmd Configuration.Configuration with ..

    return integrationData with configs = Right configurationCid

configureEssentialIntegration :HasCallStack => EssentialIntegrationData -> Script EssentialIntegrationData
configureEssentialIntegration (~integrationData@EssentialIntegrationData{configs}) = script do

    assertMsg (msgWithFunc "Configuration from incorrect source") $ Either.isLeft configs

    let Left EnetPulseConfigs{integrationParty, user, token, observers} = configs

    configurationCid <- submit integrationParty do
        createCmd Configuration.Configuration with ..

    return integrationData with configs = Right configurationCid

createEventInstrument : HasCallStack => (IntegrationData, (Party -> [Party] -> IntegrationEvents.EventInstrument), Party) -> Script IntegrationData
createEventInstrument (~integrationData@IntegrationData{configs, events}, eventIntrument, integrationParty) = script do


    assertMsg (msgWithFunc "Configuration from incorrect source") $ Either.isRight configs

    let Right configurationCid = configs

    Configuration.Configuration{observers} <- queryContractId integrationParty configurationCid >>= (return . Optional.fromSomeNote (msgWithFunc "Confituration contract not found"))

    eventInstrumentCid <- submit integrationParty do createCmd $ eventIntrument integrationParty observers

    return integrationData with events = events ++ [eventInstrumentCid]

{- ---------------------------------------HELPER FUNCTIONS---------------------------------------- -}

mapToIntegrationData : HasCallStack => Entities -> (Party -> [Party] -> EnetPulseConfigs) -> Script IntegrationData
mapToIntegrationData entities@Entities{parties = Parties{gambyl, enetPulse}} configuration = script do
    return IntegrationData with configs = Left (configuration enetPulse [gambyl]), events = [], ..

mapEssentialToIntegrationData : HasCallStack => EssentialEntities -> (Party -> [Party] -> EnetPulseConfigs) -> Script EssentialIntegrationData
mapEssentialToIntegrationData entities@EssentialEntities{parties = EssentialParties{gambyl, enetPulse}} configuration = script do
    return EssentialIntegrationData with configs = Left (configuration enetPulse [gambyl]), events = [], ..

{- ------------------------------------------DATA TYPES------------------------------------------- -}

data EnetPulseConfigs = EnetPulseConfigs
    with
        user : Text
        token : Text
        integrationParty : Party
        observers : [Party]
    deriving (Show, Eq)


data EnetPulseCredentials = EnetPulseCredentials
    with
        user                : Text
        token               : Text
    deriving (Show, Eq)

data IntegrationData = IntegrationData
    with
        entities            : Entities
        configs             : Either EnetPulseConfigs (ContractId Configuration.Configuration)
        events              : [ContractId IntegrationEvents.EventInstrument]
    deriving (Show)

data EssentialIntegrationData = EssentialIntegrationData
    with
        entities            : EssentialEntities
        configs             : Either EnetPulseConfigs (ContractId Configuration.Configuration)
        events              : [ContractId IntegrationEvents.EventInstrument]
    deriving (Show)