module Scripts.Bootstrap.Integrations.MoneyMatrix where

import Daml.Script
import DA.Either qualified as Either
import DA.Stack (HasCallStack)

import Scripts.Common
import Scripts.Bootstrap.Onboarding.Parties (Parties(..),EssentialParties(..))
import MoneyMatrixIntegration.Configuration qualified as Configuration

demoIntegrationEvents : IntegrationData -> Script Entities
demoIntegrationEvents integrationData =
    (.entities) <$>
    (script do
            debug "Start Integration Events"
            return integrationData
            >>= configureIntegration
    )

demoEssentialIntegrationEvents : EssentialIntegrationData -> Script EssentialEntities
demoEssentialIntegrationEvents integrationData =
    (.entities) <$>
    (script do
            debug "Start Essential Integration Events"
            return integrationData
        >>= configureEssentialIntegration
    )

runIntegrationEvents : Script ()
runIntegrationEvents = fromAllocatedParties
    >>= flip mapToIntegrationData (MoneyMatrixConfigs "test-merchant-id" "test-merchant-key")
    >>= demoIntegrationEvents
    >>= debug

runEssentialIntegrationEvents : Script ()
runEssentialIntegrationEvents = fromAllocatedEssentialParties
    >>= flip mapEssentialToIntegrationData (MoneyMatrixConfigs "test-merchant-id" "test-merchant-key")
    >>= demoEssentialIntegrationEvents
    >>= debug

{- ------------------------------INTEGRATION MONEY MATRIX OPERATION------------------------------- -}

configureIntegration : HasCallStack => IntegrationData -> Script IntegrationData
configureIntegration (~integrationData@IntegrationData{configs}) = script do

    assertMsg (msgWithFunc "Configuration from incorrect source") $ Either.isLeft configs

    let Left MoneyMatrixConfigs{integrationParty, merchantId, merchantKey} = configs

    configurationCid <- submit integrationParty do
        createCmd Configuration.Configuration with ..

    return integrationData with configs = Right configurationCid

configureEssentialIntegration : EssentialIntegrationData -> Script EssentialIntegrationData
configureEssentialIntegration integrationData@EssentialIntegrationData{configs} = script do

    assertMsg (msgWithFunc "Configuration from incorrect source") $ Either.isLeft configs

    let Left MoneyMatrixConfigs{integrationParty, merchantId, merchantKey} = configs

    configurationCid <- submit integrationParty do
        createCmd Configuration.Configuration with ..

    return integrationData with configs = Right configurationCid


{- ---------------------------------------HELPER FUNCTIONS---------------------------------------- -}

mapToIntegrationData : HasCallStack => Entities -> (Party -> MoneyMatrixConfigs) -> Script IntegrationData
mapToIntegrationData entities@Entities{parties = Parties{moneyMatrix}} configuration = script do
    return IntegrationData with configs = Left (configuration moneyMatrix), ..

mapEssentialToIntegrationData : EssentialEntities -> (Party -> MoneyMatrixConfigs) -> Script EssentialIntegrationData
mapEssentialToIntegrationData entities@EssentialEntities{parties = EssentialParties{moneyMatrix}} configuration = script do
    return EssentialIntegrationData with configs = Left (configuration moneyMatrix), ..

{- ------------------------------------------DATA TYPES------------------------------------------- -}

data MoneyMatrixConfigs = MoneyMatrixConfigs
    with
        merchantId          : Text
        merchantKey         : Text
        integrationParty    : Party

    deriving (Show, Eq)

data MoneyMatrixCredentials = MoneyMatrixCredentials
    with
        merchantId          : Text
        merchantKey         : Text
    deriving (Show, Eq)


data IntegrationData = IntegrationData
    with
        entities            : Entities
        configs             : Either MoneyMatrixConfigs (ContractId Configuration.Configuration)
    deriving (Show)

data EssentialIntegrationData = EssentialIntegrationData
    with
        entities            : EssentialEntities
        configs             : Either MoneyMatrixConfigs (ContractId Configuration.Configuration)
    deriving (Show)