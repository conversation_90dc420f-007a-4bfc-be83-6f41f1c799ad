module Scripts.Bootstrap.Demo where

import Daml.Script

import DA.Date
import DA.Time

import Scripts.Common (fromAllocatedEssentialParties, buildEssentialEntitiesData, EssentialEntities(..), buildEntitiesData,
    Entities(..),
    fromAllocatedParties
    )
import Scripts.Bootstrap.Onboarding.Entities        (demoHostedDamlHub, demoOnboarding)
import Scripts.Bootstrap.Onboarding.Parties         (allocateParties, allocateEssentialParties, Parties(..), EssentialParties(..))
import Scripts.Bootstrap.Gambling.Operations        (demoHostedGambyl, demoGambyl)
import Scripts.Bootstrap.Marketing.Operations       (demoHostedMarketing, demoMarketing)
import Scripts.Bootstrap.Integrations.Events qualified as Events
import Scripts.Bootstrap.Integrations.Identity qualified as Identity
import Scripts.Bootstrap.Integrations.MoneyMatrix qualified as MoneyMatrix
import Scripts.Bootstrap.Integrations.QuickBooks qualified as QuickBooks

{- ------------------------------------------DATA TYPES------------------------------------------- -}

data IntegrationCredentials = IntegrationCredentials with
    jumioCredentials        : Identity.JumioCredentials
    enetpulseCredentials    : Events.EnetPulseCredentials
    moneymatrixCredentials  : MoneyMatrix.MoneyMatrixCredentials
        deriving (Show, Eq)


data ScriptInput =
    ScriptInput with
        parties     : Parties
        credentials : IntegrationCredentials
    deriving (Show, Eq)

data ScriptEssentialInput =
    ScriptEssentialInput with
        parties     : EssentialParties
        credentials : IntegrationCredentials
    deriving (Show, Eq)
{- -------------------------------------------FUNCTIONS------------------------------------------- -}

runDemo : Entities -> Script ()
runDemo entities = script do
    demoOnboarding entities
        >>= demoMarketing
        >>= demoGambyl
    return ()

runDemoHosted : EssentialEntities -> Script ()
runDemoHosted entities = script do
    demoHostedDamlHub entities
        >>= demoHostedMarketing
        >>= demoHostedGambyl
    return ()


runDemoHostedWithConfigs : ScriptEssentialInput -> Script ()
runDemoHostedWithConfigs  essentialInput = script do
    demoHostedWithConfigs essentialInput
    return ()


demoWithConfigs : Script ()
demoWithConfigs = do
    setTime $ time (date 2022 Apr 13) 0 0 0
    parties <- allocateEssentialParties
    demoHostedWithConfigs $ ScriptEssentialInput with
        parties
        credentials = IntegrationCredentials
            (Identity.JumioCredentials "jumio-token" "jumio-secret-token")
            (Events.EnetPulseCredentials "enetpulse-user" "enetpulse-token")
            (MoneyMatrix.MoneyMatrixCredentials "moneymatrix-merchantid" "moneymatrix-merchantkey")




demoLocally : Script ()
demoLocally = do
    setTime $ time (date 2022 Apr 13) 0 0 0
    fromAllocatedParties >>= runDemo

demoHosted : Script ()
demoHosted = do
    setTime $ time (date 2022 Apr 13) 0 0 0
    fromAllocatedEssentialParties >>= runDemoHosted


demoHostedWithConfigs : ScriptEssentialInput -> Script ()
demoHostedWithConfigs ScriptEssentialInput{
        parties,
        credentials = IntegrationCredentials{
            jumioCredentials        = Identity.JumioCredentials{token = jToken, secretToken},
            enetpulseCredentials    = Events.EnetPulseCredentials{user, token = eToken},
            moneymatrixCredentials  = MoneyMatrix.MoneyMatrixCredentials{merchantId, merchantKey}
        }
    } = buildEssentialEntitiesData parties
    >>= flip Identity.mapEssentialToIntegrationData (Identity.JumioConfigs jToken secretToken)
    >>= Identity.demoEssentialIntegrationIdentity
    >>= flip Events.mapEssentialToIntegrationData (Events.EnetPulseConfigs user eToken)
    >>= Events.demoEssentialIntegrationEvents
    >>= flip MoneyMatrix.mapEssentialToIntegrationData (MoneyMatrix.MoneyMatrixConfigs merchantId merchantKey)
    >>= MoneyMatrix.demoEssentialIntegrationEvents
    >>= flip QuickBooks.mapEssentialToIntegrationData QuickBooks.QuickBooksConfigs
    >>= QuickBooks.demoIntegrationEvents
    >>= runDemoHosted



demoLocallyWithConfigs : IntegrationCredentials -> Script ()
demoLocallyWithConfigs IntegrationCredentials{
        jumioCredentials        = Identity.JumioCredentials{token = jToken, secretToken},
        enetpulseCredentials    = Events.EnetPulseCredentials{user, token = eToken},
        moneymatrixCredentials  = MoneyMatrix.MoneyMatrixCredentials{merchantId, merchantKey}
    } =
    allocateParties
    >>= buildEntitiesData
    >>= flip Identity.mapToIntegrationData (Identity.JumioConfigs jToken secretToken)
    >>= Identity.demoIntegrationIdentity
    >>= flip Events.mapToIntegrationData (Events.EnetPulseConfigs user eToken)
    >>= Events.demoIntegrationEvents
    >>= flip MoneyMatrix.mapToIntegrationData (MoneyMatrix.MoneyMatrixConfigs merchantId merchantKey)
    >>= MoneyMatrix.demoIntegrationEvents
    >>= flip QuickBooks.mapToIntegrationData QuickBooks.QuickBooksConfigs
    >>= QuickBooks.demoIntegrationEvents
    >>= runDemo

run_demoLocallyWithEvents : Script ()
run_demoLocallyWithEvents = script do
    setTime $ time (date 2022 Apr 13) 0 0 0
    demoLocallyWithConfigs $ IntegrationCredentials
        (Identity.JumioCredentials "72b11396-cce2-4203-a95c-166b804eb264" "h4FHM4i4drUWCBQ82ZwgmLaFL4FQAQn6")
        (Events.EnetPulseCredentials "enetpulse-user" "enetpulse-token")
        (MoneyMatrix.MoneyMatrixCredentials "moneymatrix-merchantid" "moneymatrix-merchantkey")


demoExternally : ScriptInput -> Script ()
demoExternally
    ScriptInput{
        credentials = IntegrationCredentials{
            jumioCredentials        = Identity.JumioCredentials{token = jToken, secretToken},
            enetpulseCredentials    = Events.EnetPulseCredentials{user, token = eToken},
            moneymatrixCredentials  = MoneyMatrix.MoneyMatrixCredentials{merchantId, merchantKey}
        },
        parties
    } = buildEntitiesData parties
    >>= flip Identity.mapToIntegrationData (Identity.JumioConfigs jToken secretToken)
    >>= Identity.demoIntegrationIdentity
    >>= flip Events.mapToIntegrationData (Events.EnetPulseConfigs user eToken)
    >>= Events.demoIntegrationEvents
    >>= flip MoneyMatrix.mapToIntegrationData (MoneyMatrix.MoneyMatrixConfigs merchantId merchantKey)
    >>= MoneyMatrix.demoIntegrationEvents
    >>= runDemo

run_demoExternally : Script ()
run_demoExternally = script do
    setTime $ time (date 2022 Apr 13) 0 0 0
    parties <- allocateParties
    demoExternally $ ScriptInput with
        parties
        credentials = IntegrationCredentials
            (Identity.JumioCredentials "jumio-token" "jumio-secret-token")
            (Events.EnetPulseCredentials "enetpulse-user" "enetpulse-token")
            (MoneyMatrix.MoneyMatrixCredentials "moneymatrix-merchantid" "moneymatrix-merchantkey")
