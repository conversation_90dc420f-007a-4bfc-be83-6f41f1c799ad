{-# LANGUAGE AllowAmbiguousTypes #-}
module Scripts.Common where

import Daml.Script

import DA.Date (date)
import DA.Finance.Asset (AssetDeposit)
import DA.Finance.Types (Id(..), Account)
import DA.List (dedup)
import DA.Time (time)
import DA.Map qualified as Map (
        Map,
        insert, empty, lookup
    )
import DA.Optional (fromSomeNote, fromOptional)
import DA.Stack (SrcLoc,
        HasCallStack,
        callStack, getCallStack
    )

import Marketplace.Custody.Service qualified as CustodyService

import Exberry.Integration ()

import Scripts.Bootstrap.Onboarding.Parties (
        Parties(..),EssentialParties(..),
        allocateParties, getExistingParties,allocateEssentialParties
    )

{- ------------------------------------------DATA TYPES------------------------------------------- -}

data Entities = Entities
    with
        parties         : Parties
        rolesServices   : Map.Map Party (Map.Map (Either Role Service) AnyContractKey)
        deposits        : Map.Map Party (Map.Map Text [ContractId AssetDeposit])
        bets            : Map.Map Party Instruments
    deriving (Show)

data EssentialEntities = EssentialEntities
    with
        parties         : EssentialParties
        rolesServices   : Map.Map Party (Map.Map (Either Role Service) AnyContractKey)
        deposits        : Map.Map Party (Map.Map Text [ContractId AssetDeposit])
        bets            : Map.Map Party Instruments
    deriving (Show)

instance Show AnyContractKey where
    show _ = "Contract Key"


data Role =
    OperatorRole        |
    CustodyRole         |
    ExchangeRole        |
    GamblingRole
    deriving (Show, Ord, Eq)


data Service =
    CustodyService          |
    IssuanceService         |
    ExchangeService         |
    GamblingService         |
    MarketingService        |
    EventService            |
    SettlementService
    deriving (Show, Ord, Eq)


data AccountType =
    MainAccount Text            |
    AllocationAccount Text      |
    ExchangeLockedAccount Text  |
    AuctionLockedAccount Text   |
    ClearingAccount Text        |
    MarginAccount Text          |
    CCPAccount Text
    deriving (Show, Ord, Eq)


data Assets = Assets with
    assetMap : Map.Map Text Id
        deriving (Show)


data Instruments = Instruments with
    instrumentMap : Map.Map Text InstrumentKey
        deriving (Show)


data ScriptData = ScriptData with
    entities    : Entities
    assets      : Assets
    events      : Instruments
        deriving (Show)

data ScriptEssentialData = ScriptEssentialData with
    entities    : EssentialEntities
    assets      : Assets
    events      : Instruments
        deriving (Show)

{- -----------------------------------------CUSTOM TYPES------------------------------------------ -}

type InstrumentKey = (Party, Party, Text)

{- ---------------------------------------HELPER FUNCTIONS---------------------------------------- -}

insertInEntitiesRoleServiceMap : forall t k. (HasTemplateTypeRep t, HasToAnyContractKey t k) => Entities -> Party -> Either Role Service -> k -> Entities
insertInEntitiesRoleServiceMap entities@Entities{rolesServices} party template_ key_ =
    entities with rolesServices = Map.insert party newPartyMap entities.rolesServices
    where
        anyKey = toAnyContractKey @t key_
        partyMap = fromOptional Map.empty (Map.lookup party rolesServices)
        newPartyMap = Map.insert template_ anyKey partyMap

insertInEssentialEntitiesRoleServiceMap : forall t k. (HasTemplateTypeRep t, HasToAnyContractKey t k) => EssentialEntities -> Party -> Either Role Service -> k -> EssentialEntities
insertInEssentialEntitiesRoleServiceMap entities@EssentialEntities{rolesServices} party template_ key_ =
    entities with rolesServices = Map.insert party newPartyMap entities.rolesServices
    where
        anyKey = toAnyContractKey @t key_
        partyMap = fromOptional Map.empty (Map.lookup party rolesServices)
        newPartyMap = Map.insert template_ anyKey partyMap

fromEntitiesRoleServiceMap : forall t k. (TemplateKey t k, Show t) => Entities -> Party -> Either Role Service -> Optional k
fromEntitiesRoleServiceMap Entities{rolesServices} party template_ =
    optAnyKey >>= (\ anyKey -> fromAnyContractKey @t anyKey)
    where
        optMap = Map.lookup party rolesServices
        optAnyKey = optMap >>= (\ map_ -> Map.lookup template_ map_)

fromEssentialEntitiesRoleServiceMap : forall t k. (TemplateKey t k, Show t) => EssentialEntities -> Party -> Either Role Service -> Optional k
fromEssentialEntitiesRoleServiceMap EssentialEntities{rolesServices} party template_ =
    optAnyKey >>= (\ anyKey -> fromAnyContractKey @t anyKey)
    where
        optMap = Map.lookup party rolesServices
        optAnyKey = optMap >>= (\ map_ -> Map.lookup template_ map_)

insertInEntitiesDepositMap : Entities -> Party -> Text -> [ContractId AssetDeposit] -> Entities
insertInEntitiesDepositMap entities@Entities{deposits} party assetLabel depositCids =
    entities with deposits = Map.insert party newPartyMap deposits
    where
        partyMap        = fromOptional Map.empty (Map.lookup party deposits)
        depositList     = fromOptional [] (Map.lookup assetLabel partyMap)
        newDepositList  = dedup $ depositCids ++ depositList
        newPartyMap     = Map.insert assetLabel newDepositList partyMap


removeFromEntitiesDepositMap : Entities -> Party -> Text -> [ContractId AssetDeposit] -> Entities
removeFromEntitiesDepositMap entities@Entities{deposits} party assetLabel depositListCid =
    entities with deposits = Map.insert party newPartyMap deposits
    where
        partyMap        = fromOptional Map.empty (Map.lookup party deposits)
        depositList     = fromOptional [] (Map.lookup assetLabel partyMap)
        newDepositList  = filter (`notElem` depositListCid) depositList
        newPartyMap     = Map.insert assetLabel newDepositList partyMap


fromEntitiesDepositMap : Entities -> Party -> Text -> Optional [ContractId AssetDeposit]
fromEntitiesDepositMap Entities{deposits} party assetLabel =
    Map.lookup party deposits >>= (\ map_ -> Map.lookup assetLabel map_)


insertInEntitiesBetsMap : Entities -> Party -> InstrumentKey -> Entities
insertInEntitiesBetsMap entities@Entities{bets} party key_ =
    entities with bets = Map.insert party newBets bets
    where
        existingBets    = fromOptional (Instruments with instrumentMap = Map.empty) (Map.lookup party bets)
        newBets         = insertInInstrumentsMap existingBets key_


fromEntitiesBetsMap : Entities -> Party -> Text -> Optional InstrumentKey
fromEntitiesBetsMap Entities{bets} party betPlacementId =
    Map.lookup party bets >>= (Map.lookup betPlacementId . (.instrumentMap))


insertInAssetsMap : Assets -> Id -> Assets
insertInAssetsMap assets@Assets{assetMap} assetId@Id{label} = assets with assetMap = Map.insert label assetId assetMap


fromAssetsMap :  Assets -> Text -> Optional Id
fromAssetsMap Assets{assetMap} key_ = Map.lookup key_ assetMap


insertInInstrumentsMap : Instruments -> InstrumentKey -> Instruments
insertInInstrumentsMap Instruments{instrumentMap} key_@(_, _, label) =
  Instruments with instrumentMap = Map.insert label key_ instrumentMap


fromInstrumentsMap :  Instruments -> Text -> Optional (Party, Party, Text)
fromInstrumentsMap Instruments{instrumentMap} key_ = Map.lookup key_ instrumentMap


showAccType : (Text -> AccountType) -> Text
showAccType accountType = getType $ accountType "_"
    where
        getType (MainAccount _)             = "main"
        getType (AllocationAccount _)       = "allocation"
        getType (ExchangeLockedAccount _)   = "exchange locked"
        getType (AuctionLockedAccount _)    = "auction locked"
        getType (ClearingAccount _)         = "clearing"
        getType (MarginAccount _)           = "margin"
        getType (CCPAccount _)              = "ccp"


getAccountFromCustodyService : HasCallStack => Entities -> Party -> Script Account
getAccountFromCustodyService entities customer = do

    let
        notFoundMsg contract = msgWithFunc $ "Contract not found - " <> show contract
        -- Get Custody Service from Entities data
        optKey = fromEntitiesRoleServiceMap @CustodyService.Service entities customer (Right CustodyService)
        custodyServiceKey : (Party, Party, Party) = fromSomeNote (msgWithFunc $ keyNotFound customer CustodyService) optKey

    (_, custodyService) <- queryContractKey @CustodyService.Service customer custodyServiceKey
        >>= (return . fromSomeNote (notFoundMsg CustodyService))

    return custodyService.account


fromAllocatedParties : Script Entities
fromAllocatedParties = allocateParties >>= (buildEntitiesData)

fromAllocatedEssentialParties : Script EssentialEntities
fromAllocatedEssentialParties = allocateEssentialParties >>= (buildEssentialEntitiesData)


fromExistingParties : Script Parties
fromExistingParties = getExistingParties


buildEntitiesData : Parties -> Script Entities
buildEntitiesData parties =
    return $ Entities with
        parties
        rolesServices   = Map.empty
        deposits        = Map.empty
        bets            = Map.empty

buildEssentialEntitiesData : EssentialParties -> Script EssentialEntities
buildEssentialEntitiesData parties =
    return $ EssentialEntities with
        parties
        rolesServices   = Map.empty
        deposits        = Map.empty
        bets            = Map.empty

buildScriptDataMaps : Entities -> Script ScriptData
buildScriptDataMaps entities = return ScriptData with ..
    where
        assets  = Assets with assetMap = Map.empty
        events  = Instruments with instrumentMap = Map.empty

buildScriptEssentialDataMaps : EssentialEntities -> Script ScriptEssentialData
buildScriptEssentialDataMaps entities = return ScriptEssentialData with ..
    where
        assets  = Assets with assetMap = Map.empty
        events  = Instruments with instrumentMap = Map.empty

fromRightNote : Show a => Text -> Either a b -> b
fromRightNote errorMsg (Left msg) = error $ errorMsg <> " " <> show msg
fromRightNote _ (Right val) = val

getDateTime year month day = time (date year month day)

{- -------------------------------------------MESSAGES-------------------------------------------- -}


msgWithFunc : HasCallStack => Text -> Text
msgWithFunc = (<>) (msg <> ": ")
    where
        msg = buildMsg $ reverse $ getCallStack callStack


buildMsg : [(Text, SrcLoc)] -> Text
buildMsg []                 = ""
buildMsg [(here, _), _]     = here
buildMsg ((here, _)::xs)    = here <> " -> " <> buildMsg xs


keyNotFound : Show a => Party -> a -> Text
keyNotFound party roleService =
    "Key for " <> show roleService <> " not present in Entities for party " <> partyToText party


accountNotFound : Party -> (Text -> AccountType) -> Text
accountNotFound customer account = "Customer " <> show customer <> " doesn't have a(n) " <> (showAccType account) <> " account"