{-# LANGUAGE AllowAmbiguousTypes #-}

module Gambyl.Gambling.Service where

import Prelude          hiding (null, foldl)

import DA.Action        ((>=>))
import DA.Action qualified as Action hiding ((>=>))
import DA.Date qualified as Date (toGregorian, toDateUTC, Month)
import DA.Either qualified as Either
import DA.Exception
import DA.Finance.Asset qualified as Asset
import DA.Finance.Types qualified as FinanceTypes
import DA.Finance.Utils qualified as FinanceUtils
import DA.Foldable      (forA_)
import DA.Foldable qualified as Foldable (null, mapA_)
import DA.List qualified as List
import DA.List.Total qualified as ListT (findIndex)
import DA.Map qualified as Map
import DA.Numeric qualified as Numeric (div, mul, castAndRound)
import DA.Optional qualified as Optional (fromSomeNote, isNone, whenSome, optional, fromOptional)
import DA.Set           (Set)
import DA.Set qualified as Set hiding (Set)
import DA.Text qualified as Text (words, isPrefixOf, sha256, intercalate, trim, length, splitOn, isInfixOf, isEmpty)
import DA.Tuple qualified as Tuple (snd3)

import Marketplace.Custody.Service qualified as Custody
import Marketplace.Issuance.Model qualified as IssuanceModel
import Marketplace.Issuance.Service qualified as IssuanceService
import Marketplace.Listing.Model qualified as MarketplaceListingModel
import Marketplace.Listing.Service qualified as MarketplaceListingService
import Marketplace.Settlement.Model qualified as SettlementModel
import Marketplace.Trading.Model qualified as TradingModel
import Marketplace.Trading.Service qualified as TradingService
import Marketplace.Utils

import MoneyMatrixIntegration.Deposit qualified as IntegrationDeposit
import MoneyMatrixIntegration.Withdraw qualified as IntegrationWithdraw

import EnetPulseIntegration.Events qualified as IntegrationEvents

import JumioIntegration.Identity qualified as IntegrationIdentity

import Exberry.Integration qualified as IntegrationExberry

import Gambyl.Interface.Gambling.Bet.Cancellation.Service qualified as IBetCancellationService
import Gambyl.Interface.Gambling.Bet.Settlement.Model qualified as IBetSettlementModel
import Gambyl.Interface.Gambling.Bet.Settlement.Service qualified as IBetSettlementService
import Gambyl.Interface.Gambling.Model qualified as IGamblingModel

import Gambyl.Gambling.Account.Model qualified as GamblingAccount
import Gambyl.Gambling.Bet.Model                (Side(Back, Lay))
import Gambyl.Gambling.Bet.Model qualified as BetModel hiding (Side(..))
import Gambyl.Gambling.Bet.Odds.Model           (getOddFromTo, OddType(..))
import Gambyl.Gambling.Bet.Odds.Model qualified as OddsModel hiding (OddType(..))
import Gambyl.Gambling.Event.Model qualified as EventModel
import Gambyl.Gambling.Identity.Model qualified as IdentityModel
import Gambyl.Gambling.Listing.Model qualified as ListingModel
import Gambyl.Gambling.Model qualified as GamblingModel
import Gambyl.Gambling.Settlement.Model qualified as SettlementModel
import Gambyl.Marketing.Model qualified as MarketingModel
import Gambyl.Utils qualified as Utils
import SendgridIntegration.Message qualified as SendgridIntegration

{- -------------------------------------------TEMPLATES------------------------------------------- -}

template Service
    with
        operator : Party
        provider : Party
        customer : Party
        permissions : Set GamblingModel.Permissions
        createdAt : Time
    where
        signatory operator, provider, customer

        key (operator, provider, customer) : (Party, Party, Party)

        maintainer key._1

        choice BlockService : ContractId BlockedService
            with
                party : Party
                reason: Text
         controller party
            do
                now <- getTime
                assertMsg ("Party " <> show party <> "is not capable of exercising this choice") (party == provider || party == customer)
                create BlockedService with service = this, reason, blockedAt = now


        nonconsuming choice RequestIdentityVerification : Either GamblingModel.ActionFailureCid (ContractId IntegrationIdentity.InitialIdentityVerificationRequest)
          with
            integrationParty    : Party
            locale              : Text
          controller customer
          do
            try do
              Utils.whenContractExists @IdentityModel.PendingIdentity (operator, provider, customer)
                (\ _ -> assertFail "There already is an ongoing identity verification for customer")

              Utils.whenContractExists @IdentityModel.GamblerIdentity (operator, provider, customer)
                (\ _ -> assertFail "There already is a valid GamblerIdentity for customer")

              Right <$> create IntegrationIdentity.InitialIdentityVerificationRequest with
                operator, provider, integrationParty
                requestFrom = customer, observers = [operator, provider]
                locale

            catch AssertionFailed msg ->
                    Left <$> createOrLookup GamblingModel.ActionFailure with
                      operator, provider, customer
                      actionId = show customer, action = GamblingModel.IdentityVerification
                      reason = msg


        nonconsuming choice StartIdentityVerification
          : Either GamblingModel.ActionFailureCid (ContractId IdentityModel.PendingIdentity)
          with
            idVerReqCid : ContractId IntegrationIdentity.IdentityVerificationRequest
          controller customer
          do
            try do
              Utils.whenContractExists @IdentityModel.RejectedIdentity (customerKey this) archive

              jumioPendingIdCid <- exercise idVerReqCid IntegrationIdentity.StartVerification
              IntegrationIdentity.PendingIdentityRequest{
                redirectUrl, integrationParty, requestFrom
              } <- fetch jumioPendingIdCid

              Utils.createSendGridNotification (operator, provider, customer) SendgridIntegration.KYCPending

              Right <$> create IdentityModel.PendingIdentity with
                operator, provider, customer, redirectUrl
                jumioPendingIdKey = (integrationParty, requestFrom)

            catch AssertionFailed msg ->
                    Left <$> createOrLookup GamblingModel.ActionFailure with
                      operator, provider, customer,reason = msg
                      actionId = show customer, action = GamblingModel.IdentityVerification


        nonconsuming choice RequestEventOrigination : Either GamblingModel.ActionFailureCid (ContractId EventModel.EventInstrumentRequest)
            with
                eventOrigin : EventModel.EventOrigin
            controller customer
            do
              try do
                assertMsg "Service doesn't allow for Event origination" $ GamblingModel.Events `Set.member` permissions

                Right <$> EventModel.originateEventFrom eventOrigin operator provider customer
              catch AssertionFailed msg -> do
                      eventId <- EventModel.getEventIdFromOrigin eventOrigin
                      Left <$> createOrLookup GamblingModel.ActionFailure with
                          operator, provider, customer
                          action = GamblingModel.EventInstrumentOrigination
                          actionId = eventId, reason = msg


        nonconsuming choice RequestDepositAccount : (ContractId GamblingAccount.DepositRequestForApproval)
          with
            integrationParty    : Party
            requestedAmount     : Numeric 2
            currency            : Text
            transactionId       : Text
            promotion           : Optional MarketingModel.PromotionKey
            language            : Text
          controller customer
          do
            Utils.whenContractExists @IntegrationDeposit.InitialDepositRequest
              (integrationParty, customer) (`exercise` IntegrationDeposit.ArchiveInitRequest)

            lookupByKey @GamblingAccount.PendingDeposits (customerKey this)
              >>= (`Utils.whenNone` do
                Action.void $ create GamblingAccount.PendingDeposits with
                  operator, provider, customer
                  unverifiedPendingDeposits = Map.empty
                  pendingDeposits = Map.empty
              )

            pendingAmount <- fetchByKey @GamblingAccount.PendingDeposits (customerKey this)
                            >>= (\(_, pd) -> return pd.pendingDeposits)
                            >>= calculatePendingDepositsAmount

            newDepositLimit <- exerciseByKey @GamblingAccount.Account (customerKey this) GamblingAccount.ResetAccruedAmount
            GamblingAccount.atDepositLimit newDepositLimit (requestedAmount + pendingAmount) `Optional.whenSome`
              (\ (limitType, GamblingAccount.AccruedLimit{limit, accrued}) ->
                assertFail $ "Requested amount exceeds " <> show limitType <> " deposit limit of " <> show limit
                  <> " " <> currency <> ". User as already deposited " <> show accrued <> " and still has "
                  <> show pendingAmount <> " in pending deposits counting towards this limit.") --"User can't deposit because of their " <> <> " deposit Limit Config in account")

            dateOfBirth <- Utils.createOrfetchBirthDay (customerKey this) (Date.toDateUTC createdAt)
            create GamblingAccount.DepositRequestForApproval with
              operator, customer, provider, integrationParty, requestedAmount, currency
              transactionId, promotion, dateOfBirth, language


        nonconsuming choice RequestWithdrawAccount : (ContractId GamblingAccount.WithdrawRequestForApproval)
          with
            integrationParty : Party
            requestedAmount : Numeric 2
            currency : Text
            transactionId : Text
            promotion : Optional MarketingModel.PromotionKey
            language : Text
          controller customer
          do
            Utils.whenContractExists @IntegrationWithdraw.InitialWithdrawRequest (integrationParty, customer)
              (\_ -> assertFail $ "Initial Withdraw Request already exists")

            Utils.whenContractExists @IntegrationWithdraw.WithdrawRequest (integrationParty, customer)
              (\_ -> assertFail $ "Withdraw Request already exists")

            gamblingAccount <- snd <$> fetchByKey @GamblingAccount.Account (customerKey this)
            assertMsg "There is already a withdrawal in progress" $ Optional.isNone gamblingAccount.assetDepositWithdraw

            assertMsg "Available balance is not enough to cover requested withdraw amount plus withdraw fee"
              $ gamblingAccount.totalMainBalance >= requestedAmount

            dateOfBirth <- Utils.createOrfetchBirthDay (customerKey this) (Date.toDateUTC createdAt)
            create GamblingAccount.WithdrawRequestForApproval with
              operator, customer, provider, integrationParty, requestedAmount, currency
              transactionId, promotion, dateOfBirth, language


        nonconsuming choice UpdatePreferences : ContractId GamblingAccount.Account
          with
            newPreferences : [(Text, Text)]
          controller customer
          do
            exerciseByKey @GamblingAccount.Account (customerKey this)
              $ GamblingAccount.UpdatePreferences (Map.fromList newPreferences)


        nonconsuming choice AddFavouriteMarkets : ContractId GamblingAccount.Account
            with
                markets   : [EventModel.Market]
            controller customer
            do
                exerciseByKey @GamblingAccount.Account (customerKey this) GamblingAccount.AddFavouriteMarkets with markets


        nonconsuming choice RemoveFavouriteMarkets : ContractId GamblingAccount.Account
            with
                markets    : [EventModel.Market]
            controller customer
            do
                exerciseByKey @GamblingAccount.Account (customerKey this) GamblingAccount.RemoveFavouriteMarkets with markets


        nonconsuming choice UpdateIdentity : ContractId IdentityModel.GamblerIdentity
            with
                city            : Text
                postalCode      : Text
                subDivision     : Text
                addressLine1    : Text
                addressLine2    : Text
            controller customer
            do
                exerciseByKey @IdentityModel.GamblerIdentity (customerKey this)
                    IdentityModel.UpdateInfo with ..


        nonconsuming choice RequestBetPlacement : Either GamblingModel.ActionFailureCid (BetModel.BetPlacementKey)
          with
            betDetails  : BetModel.Details
            promotion   : Optional MarketingModel.PromotionKey
            inBulk      : Optional Text
          controller customer
          do
            try do
              assertMsg "Service doesn't allow for Bet origination"
                $ GamblingModel.Bets `Set.member` permissions

              -- If request comes from single bet placement, check if there is any pending bet placement
              Utils.unlessSome inBulk do
                Utils.whenContractExists @BetModel.BetPlacementRequestFlag (operator, provider, customer)
                  (\ _ -> assertFail $ "There is a pending Bet Placement request still being processed.\
                    \ Please try again in a few moments.")

                Utils.whenContractExists @BetModel.BulkBetPlacementRequestFlag (operator, provider, customer)
                  (\ _ -> assertFail $ "There is a pending Bulk Bet Placement \
                    \request still being processed. Please try again in a few moments.")

              stake <- Utils.rightOrFail (BetModel.getStake betDetails.side)

              GamblingAccount.Account{
                totalMainBalance, totalBonusBalance
              } <- Utils.getContractWithKey @GamblingAccount.Account (customerKey this) "Account"

              let availableFunds = totalMainBalance + totalBonusBalance
              assertMsg ("Account doesn't have enough available funds (" <>
                  show availableFunds <> ") to bet on event (" <>
                  betDetails.eventKey._3 <> ") with stake (" <> show stake <> ")"
                ) $ stake <= availableFunds

              -- Lock Cash Amount for same stake as Binary Option
              (_, cashDeposit, optBonusAmount) <-
                exerciseByKey @GamblingAccount.Account (customerKey this)
                  GamblingAccount.LockBet with
                    betPlacementId = betDetails.betPlacementId
                    requestedAmount = Numeric.castAndRound stake

              -- If single bet, create flag, if bulk flag should be created on RequestBulkBetPlacement
              Utils.unlessSome inBulk do
                Action.void $ create BetModel.BetPlacementRequestFlag with ..

              create BetModel.BetPlacementRequest with
                operator, provider, customer, details = betDetails
                cashDeposit, optBonusAmount, promotion, inBulk

              Right <$> pure (operator, provider, customer, betDetails.betPlacementId)

            catch AssertionFailed msg ->
                    Left <$> createOrLookup GamblingModel.ActionFailure with
                      operator, provider, customer, actionId = betDetails.betPlacementId
                      action = GamblingModel.BetPlacement, reason = msg


        nonconsuming choice RequestBulkBetPlacement : Either GamblingModel.ActionFailureCid (ContractId BetModel.BulkBetPlacementRequest)
          with
            betDetailsList  : [BetModel.Details]
            promotion       : Optional MarketingModel.PromotionKey
          controller customer
          do
            try do
              Utils.whenContractExists @BetModel.BetPlacementRequestFlag (operator, provider, customer)
                  (\ _ -> assertFail $ "There is a pending Bet Placement request still being processed.\
                    \ Please try again in a few moments.")

              Utils.whenContractExists @BetModel.BulkBetPlacementRequestFlag (operator, provider, customer)
                  (\ _ -> assertFail $ "There is a pending Bulk Bet Placement request still being processed.\
                    \ Please try again in a few moments.")

              (totalStake, totalPayout, totalRealStake) <-
                Action.foldrA (\ <EMAIL>{side} (accumStake, accumPay, accRealStake) -> do

                        let totalBulk = (BetModel.fromSide side).stake
                        stake <- Utils.rightOrFail (BetModel.getStake betDetails.side)
                        payout <- Utils.rightOrFail (BetModel.getPayout betDetails.side)
                        return (accumStake + stake, accumPay + payout, accRealStake + totalBulk)
                    ) (0.0, 0.0, 0.0) betDetailsList

              let  bulkBetPlacementId = (List.head betDetailsList).betPlacementId

              GamblingAccount.Account{
                totalMainBalance, totalBonusBalance
              } <- Utils.getContractWithKey @GamblingAccount.Account (customerKey this) "Account"

              let availableFunds = totalMainBalance + totalBonusBalance
              assertMsg ("Bulk Bet total stake (" <> show totalStake <> ") is greater than\
                \ customer's account available balance (" <> show availableFunds <> ")")
                $ totalStake <= availableFunds

              betPlacementReqKeyList <-
                Action.mapA (\ betDetails -> do
                  (exercise self $ RequestBetPlacement with inBulk = Some bulkBetPlacementId, betDetails, promotion) >>=
                      Either.either (\ actionFailureCid -> do
                                  actionFailure <- fetch actionFailureCid
                                  throw $ BulkRequestException actionFailure
                                  ) return
                    ) betDetailsList

              requestList <- mapA (`Utils.getContractWithKey` "BetPlacementRequest") betPlacementReqKeyList

              assertMsg "Bet slip cannot be placed because two or more bets could be matched together."
                $ checkIfBulkCanBePlaced requestList

              create BetModel.BulkBetPlacementRequestFlag with
                operator, provider, customer

              Right <$> create BetModel.BulkBetPlacementRequest with
                operator, provider, customer, totalStake, totalRealStake, totalPayout
                betPlacementReqKeyList, promotion, bulkBetPlacementId

            catch
              BulkRequestException actionFailure ->
                  Left <$> createOrLookup actionFailure

              AssertionFailed msg ->
                  Left <$> createOrLookup GamblingModel.ActionFailure with
                    operator, provider, customer
                    actionId = (List.head betDetailsList).betPlacementId
                    action = GamblingModel.BetPlacement, reason = msg


        nonconsuming choice RequestCancelBetPlacement : Either GamblingModel.ActionFailureCid (ContractId BetModel.BetPlacementCancelRequest)
          with
            betPlacementId : Text
          controller customer
          do
            try do
              Utils.whenContractExists @BetModel.CancelledBetPlacement (operator, provider, customer, betPlacementId)
                (\ _ -> assertFail "Bet has already been cancelled")

              BetModel.BetPlacement{
                status, orderId, details = BetModel.Details{betPlacementId}
              } <- Utils.getContractWithKey @BetModel.BetPlacement (operator, provider, customer, betPlacementId) "BetPlacement"

              let cancelRequest = BetModel.BetPlacementCancelRequest with
                    operator, provider, customer, orderId, betPlacementId

              -- Check if there are any BetPlacementCancelRequest or
              -- FinalizeBetPlacementCancelRequest contracts related to this bet
              checkCancelBetRequests betPlacementId (operator, provider, customer, ) "BetPlacementCancelRequest"

              Right <$> case status of
                BetModel.Unmatched -> createOrLookup cancelRequest
                BetModel.Matched ->  assertFail "Bet is already matched"
                BetModel.Closed -> assertFail "Bet has already been closed"

            catch AssertionFailed msg ->
                    Left <$> createOrLookup GamblingModel.ActionFailure with
                        operator, provider, customer
                        action = GamblingModel.BetCancelation, actionId = betPlacementId
                        reason = msg


        nonconsuming choice RequestEventUpdate : Either GamblingModel.ActionFailureCid (ContractId EventModel.EventInstrumentUpdateRequest)
          with
            eventOrigin : EventModel.EventUpdateOrigin
            oldEventKey : EventModel.EventInstrumentKey
          controller customer
          do
            -- TODO: Move to event manager?
            try do
              assertMsg "Service doesn't allow for Event Update" $ customer == provider
              fmap Right
                $ EventModel.updateEventFrom eventOrigin operator provider customer oldEventKey
                >>= Utils.rightOrFail
            catch AssertionFailed msg ->
                    Left <$> createOrLookup GamblingModel.ActionFailure with
                      operator, provider, customer, action = GamblingModel.EventInstrumentUpdate
                      actionId = oldEventKey._3, reason = msg


        nonconsuming choice ArchiveActionFailureNotification : ()
          with
            actionList : [(GamblingModel.Actionable, Text)]
          controller customer
          do
            Foldable.mapA_
              (\ (action, actionId) -> actionFailureNotificationCheck (operator, provider, customer, action, actionId))
              actionList


        nonconsuming choice ArchiveActionSuccessNotification : ()
            with
              actionList : [(GamblingModel.Actionable, Text)]
            controller customer
            do
              Foldable.mapA_
                (\ (action, actionId) -> actionSuccessNotificationCheck (operator, provider, customer, action, actionId))
                actionList


        nonconsuming choice RequestUnverifiedIdentity : ContractId IdentityModel.GamblerUnverifiedIdentityRequest
            with
                firstName        : Text
                lastName        : Text
                emailAddress    : Text
                phoneNumber     : Text
                birthday        : Date
                city            : Text
                countryCode     : Text
                postalCode      : Text
                subDivision     : Text
                addressLine1    : Text
            controller customer
            do
                -- The trim functions removes white spaces before and after the text to make sure we don't parse white spaces
                let
                    trimmedFirstName = Text.trim firstName
                    trimmedLastName = Text.trim lastName
                    trimmedEmailAddress = Text.trim emailAddress
                    trimmedPhoneNumber = Text.trim phoneNumber
                    trimmedCity = Text.trim city
                    trimmedCountryCode = Text.trim countryCode
                    trimmedPostalCode = Text.trim postalCode
                    trimmedSubDivision = Text.trim subDivision
                    trimmedAddressLine1 = Text.trim addressLine1

                assertMsg "firstName is empty when requesting Unverrified Identity" $ not (Text.isEmpty trimmedFirstName)
                assertMsg "lastName is empty when requesting Unverrified Identity" $ not (Text.isEmpty trimmedLastName)

                checkEmailAddress trimmedEmailAddress

                assertMsg "phoneNumber is empty when requesting Unverrified Identity" $ not (Text.isEmpty trimmedPhoneNumber)
                assertMsg "city is empty when requesting Unverrified Identity" $ not (Text.isEmpty trimmedCity)

                assertMsg "countryCode is empty when requesting Unverrified Identity" $ not (Text.isEmpty trimmedCountryCode)
                assertMsg "countryCode is incorrect" $ (Text.length trimmedCountryCode) <= 3


                assertMsg "postalCode is empty when requesting Unverrified Identity" $ not (Text.isEmpty trimmedPostalCode)
                assertMsg "addressLine1 is empty when requesting Unverrified Identity" $ not (Text.isEmpty trimmedAddressLine1)

                lookupByKey @IdentityModel.GamblerUnverifiedIdentityRequest (operator, provider, customer) >>= Optional.optional
                    (create IdentityModel.GamblerUnverifiedIdentityRequest with
                      operator, provider, customer, birthday
                      firstName = trimmedFirstName
                      lastName = trimmedLastName
                      emailAddress = trimmedEmailAddress
                      phoneNumber = trimmedPhoneNumber
                      city = trimmedCity
                      countryCode = trimmedCountryCode
                      postalCode = trimmedPostalCode
                      subDivision = trimmedSubDivision
                      addressLine1 = trimmedAddressLine1)
                    (\unverifiedIdentityCid -> do
                        oldIdentityRequest <- fetch unverifiedIdentityCid

                        let
                          newIdentityRequest = oldIdentityRequest with
                            birthday
                            firstName = trimmedFirstName
                            lastName = trimmedLastName
                            emailAddress = trimmedEmailAddress
                            phoneNumber = trimmedPhoneNumber
                            city = trimmedCity
                            countryCode = trimmedCountryCode
                            postalCode = trimmedPostalCode
                            subDivision = trimmedSubDivision
                            addressLine1 = trimmedAddressLine1

                        if newIdentityRequest == oldIdentityRequest
                          then return unverifiedIdentityCid
                          else do
                            archive unverifiedIdentityCid
                            create newIdentityRequest
                    )


        nonconsuming choice GivePermissions : ContractId Service
            with
                newPermissions : [GamblingModel.Permissions]
            controller provider
            do
                    let exists = map (\ r -> (r, r `Set.member` permissions)) newPermissions
                        filteredPermissions = filter (not . snd) exists
                        restrictionSet = Set.fromList (map fst filteredPermissions)
                    if (Foldable.null restrictionSet) then do
                        return self
                    else do
                        archive self
                        create this with permissions = permissions `Set.union` restrictionSet


        nonconsuming choice VerifyIdentity
          : Either
            (GamblingModel.ActionFailureCid, Optional BlockedServiceCid)
            (ContractId IdentityModel.GamblerIdentity)
          with
            verifiedIdCid : ContractId IntegrationIdentity.VerifiedIdentity
          controller provider
          do
            try do
              birthday <- (.userData.birthday) <$> fetch verifiedIdCid

              legalAge <- (.legalAge) <$> Utils.getContractWithKey @GamblingModel.GlobalGamblingConfiguration
                (operator, provider)  "GlobalGamblingConfiguration"

              today <- Date.toDateUTC <$> getTime

              let
                (y, m, d) = Date.toGregorian today
                (birthY, birthM, birthD) = Date.toGregorian birthday
                age = calculateAge (d, m, y) (birthD, birthM, birthY)

              verifiedidentity <- exerciseByKey @IdentityModel.PendingIdentity (customerKey this)
                IdentityModel.VerifyIdentity with verifiedIdCid

              if age < legalAge then do
                let reason = "Under Legal Age"
                blockedServiceCid <- exerciseByKey @Service (customerKey this) BlockService with
                  party = customer, reason
                (Left . (, Some blockedServiceCid)) <$>
                  createOrLookup GamblingModel.ActionFailure with
                    operator, provider, customer, reason
                    actionId = show customer, action = GamblingModel.IdentityVerification

              else do
                Utils.createSendGridNotification (operator, provider, customer) SendgridIntegration.KYCApproved
                pure $ Right verifiedidentity

            catch AssertionFailed msg ->
                    (Left . (, None)) <$> createOrLookup GamblingModel.ActionFailure with
                      operator, provider, customer,reason = msg
                      actionId = show customer, action = GamblingModel.IdentityVerification


        nonconsuming choice ApproveUnverifiedIdentity : Either (ContractId BlockedService) (ContractId IdentityModel.GamblerUnverifiedIdentity)
            with
                unverifiedIdentityRequestCid : ContractId IdentityModel.GamblerUnverifiedIdentityRequest
            controller provider
            do
                IdentityModel.GamblerUnverifiedIdentityRequest{
                  firstName, lastName, emailAddress, phoneNumber, birthday, countryCode
                  , city, postalCode, subDivision, addressLine1
                } <- exercise unverifiedIdentityRequestCid IdentityModel.Request_RetrieveArchive
                (_, GamblingModel.GlobalGamblingConfiguration{legalAge}) <- fetchByKey @GamblingModel.GlobalGamblingConfiguration (operator, provider)
                today <- Date.toDateUTC <$> getTime
                let
                  (y, m, d) = Date.toGregorian today
                  (birthY, birthM, birthD) = Date.toGregorian birthday
                  age = calculateAge (d, m, y) (birthD, birthM, birthY)

                  unverifiedId = IdentityModel.GamblerUnverifiedIdentity with
                    operator, provider, customer, firstName, lastName, emailAddress, phoneNumber
                    birthday, countryCode, city, postalCode, subDivision, addressLine1

                lookupByKey @IdentityModel.GamblerUnverifiedIdentity (customerKey this)
                  >>= optional
                    (if age < legalAge
                      then Left <$> exerciseByKey @Service (customerKey this)
                        BlockService with party = customer, reason = "Under Legal Age"
                      else Right <$> create unverifiedId)
                    (\ identity -> do
                        archive identity
                        Right <$> create unverifiedId)

        nonconsuming choice UpdateUnverifiedIndetity : ContractId IdentityModel.GamblerUnverifiedIdentity
            with
                newFirstName       : Optional Text
                newLastName        : Optional Text
                newEmailAddress    : Optional Text
                newPhoneNumber     : Optional Text
                newBirthday        : Optional Date
                newCity            : Optional Text
                newCountryCode     : Optional Text
                newPostalCode      : Optional Text
                newSubDivision     : Optional Text
                newAddressLine1    : Optional Text
            controller provider
            do
                (gamblerUnverifiedIdentityCid
                  , <EMAIL>{
                      firstName, lastName, emailAddress, phoneNumber, birthday
                      , city, countryCode, postalCode, subDivision, addressLine1
                }) <- fetchByKey @IdentityModel.GamblerUnverifiedIdentity (customerKey this)

                archive gamblerUnverifiedIdentityCid
                create identity with
                  firstName = Optional.fromOptional firstName newFirstName
                  lastName = Optional.fromOptional lastName newLastName
                  emailAddress = Optional.fromOptional emailAddress newEmailAddress
                  phoneNumber = Optional.fromOptional phoneNumber newPhoneNumber
                  birthday = Optional.fromOptional birthday newBirthday
                  city = Optional.fromOptional city newCity
                  countryCode = Optional.fromOptional countryCode newCountryCode
                  postalCode = Optional.fromOptional postalCode newPostalCode
                  subDivision = Optional.fromOptional subDivision newSubDivision
                  addressLine1 = Optional.fromOptional addressLine1 newAddressLine1


        nonconsuming choice  RejectIdentity
          : Either
            GamblingModel.ActionFailureCid
            (Either (ContractId IdentityModel.RejectedIdentity) ())
          with
            rejectedIdCid : ContractId IntegrationIdentity.RejectedIdentity
          controller provider
          do
            try do
              Utils.createSendGridNotification (operator, provider, customer) SendgridIntegration.KYCRejected
              Right <$> exerciseByKey @IdentityModel.PendingIdentity (customerKey this)
                  IdentityModel.RejectIdentity with ..
            catch AssertionFailed msg -> do
                  Left <$> createOrLookup GamblingModel.ActionFailure with
                      operator, provider, customer,reason = msg
                      actionId = show customer, action = GamblingModel.IdentityVerification


            -- TODO: add choice to update outcomes of events on EventManager

        -- | Can output a Left with an action failure or a Right with contained values.
        -- In the second case, can output a Left with a no outcome event instrument or a regular event instrument.
        -- For both second type outputs an AssetDescription and MarketMap contracts will also be returned.
        nonconsuming choice ApproveEventOrigination
          : Either
              GamblingModel.ActionFailureCid
              ((Either (ContractId EventModel.EventInstrumentNoOutcomes) (ContractId EventModel.EventInstrument))
                , ContractId EventModel.MarketMap
              )
          with
            requestCid  : ContractId EventModel.EventInstrumentRequest
            managers    : Set Party
          controller provider
          do
            actionId <- (.eventId.label) <$> fetch requestCid

            try do
              (eventCid, marketCid) <- exercise requestCid
                EventModel.Approve_Origination with featured = False, managers

              -- | Tuple contructor where first element is mapped (<$>/fmap) to be either the event without outcomes or
              -- the create event on the previous exercise, composing it with a final Right
              Right . (, marketCid) <$>
                try do Left <$> exercise eventCid EventModel.SignalEventNoOutcome
                catch AssertionFailed _ -> pure $ Right eventCid
            catch AssertionFailed msg -> do
                  Left <$> createOrLookup GamblingModel.ActionFailure with
                    operator, provider, customer
                    action = GamblingModel.EventInstrumentOrigination, actionId, reason = msg


        nonconsuming choice ToggleFeatureEvent : ContractId EventModel.EventInstrument
            with
                label : Text
            controller provider
                do
                    exerciseByKey @EventModel.EventInstrument (operator, provider, label) EventModel.ToggleFeatured


        nonconsuming choice ArchiveEvent : Either GamblingModel.ActionFailureCid ()
          with
            eventCid  : ContractId EventModel.EventInstrument
          controller provider
          do
            eventId <- (.eventId.label) <$> fetch eventCid
            try do
              Right <$> exercise eventCid EventModel.ArchiveEvent
            catch AssertionFailed msg ->
                    Left <$> createOrLookup GamblingModel.ActionFailure with
                        operator, provider, customer
                        action = GamblingModel.EventInstrumentUpdate
                        actionId = eventId
                        reason = msg


        nonconsuming choice ArchiveEventNoOutcomes : Either GamblingModel.ActionFailureCid ()
          with
            eventCid  : ContractId EventModel.EventInstrumentNoOutcomes
          controller provider
          do
            eventId <- (.event.eventId.label) <$> fetch eventCid
            try do
              Right <$> exercise eventCid EventModel.ArchiveEventNoOutcomes
            catch AssertionFailed msg ->
                    Left <$> createOrLookup GamblingModel.ActionFailure with
                        operator, provider, customer
                        action = GamblingModel.EventInstrumentUpdate
                        actionId = eventId
                        reason = msg


        nonconsuming choice UpdateEventStatus : Either GamblingModel.ActionFailureCid (ContractId EventModel.EventInstrument)
          with
            eventCid  : ContractId EventModel.EventInstrument
          controller provider
          do
            eventId <- (.eventId.label) <$> fetch eventCid
            try do
              Right <$> exercise eventCid EventModel.ExpireEvent
            catch AssertionFailed msg ->
                    Left <$> createOrLookup GamblingModel.ActionFailure with
                        operator, provider, customer
                        action = GamblingModel.EventInstrumentUpdate
                        actionId = eventId
                        reason = msg


        nonconsuming choice UpdateFinishEventStatus : (ContractId EventModel.EventInstrument)
            with
                eventCid  : ContractId EventModel.EventInstrument
            controller provider
            do
                exercise eventCid EventModel.FinishEvent


        nonconsuming choice ApproveDepositRequest :  Either GamblingModel.ActionFailureCid (ContractId IntegrationDeposit.InitialDepositRequest)
          with
            depositRequestForApprovalCid : ContractId GamblingAccount.DepositRequestForApproval
          controller provider
          do
            GamblingAccount.DepositRequestForApproval{
              integrationParty, requestedAmount, currency, transactionId, promotion, language
            } <- FinanceUtils.fetchAndArchive depositRequestForApprovalCid

            try do

              -- Using optional instead of fromSomeNote, because try catch AssertionFailed not capturing error resulting from failure
              IdentityModel.GamblerUnverifiedIdentity{
                firstName, lastName, emailAddress, phoneNumber, birthday, countryCode
                , city, postalCode, subDivision, addressLine1
              } <- Utils.getContractWithKey @IdentityModel.GamblerUnverifiedIdentity
                (customerKey this) "GamblerUnverifiedIdentity"

              (_, existsAssetDesc) <- Utils.checkAssetDesc [provider] currency
              assertMsg "The currency used in the deposit request is not supported" existsAssetDesc

              pendingAmount <- calculatePendingDepositsAmount . (.pendingDeposits)
                =<< Utils.getContractWithKey @GamblingAccount.PendingDeposits (customerKey this) "PendingDeposits"

              let totalAmount = pendingAmount + requestedAmount

              newDepositLimit <- exerciseByKey @GamblingAccount.Account (customerKey this) GamblingAccount.ResetAccruedAmount
              Optional.optional
                (return ())
                (\ (limitType, GamblingAccount.AccruedLimit{limit, accrued}) ->
                    assertFail $ "Requested amount exceeds " <> show limitType <> " deposit limit of \
                    \" <> show limit <> " " <> currency <> ". User as already deposited \
                    \" <> show accrued <> " and still has" <> show pendingAmount <> "\
                    \ in pending deposits counting towards this limit.")
                $ GamblingAccount.atDepositLimit newDepositLimit totalAmount

              GamblingModel.GlobalGamblingConfiguration{
                depositFee, unverifiedAccountMaxAmount, minDepositAmount
              } <- Utils.getContractWithKey @GamblingModel.GlobalGamblingConfiguration
                (operator, provider) "GlobalGamblingConfiguration"

              assertMsg ("User can't deposit less than \
                \" <> (show minDepositAmount) <> " " <> (currency) <> " in account")
                $ requestedAmount >= minDepositAmount

              -- if given promotion is applicable: create pending promotion application, calculate new fees, and return total amount billed to the customer, with fee already applied
              -- if the promotion is not applicable, returns exception
              appliedFee <-
                optional
                    (return depositFee)
                    (calculateTransactionAmount (customerKey this) GamblingModel.Deposit
                        depositFee requestedAmount currency transactionId)
                    promotion

              now <- getTime
              let
                totalBilledAmount = requestedAmount `Numeric.mul` (1.0 + appliedFee)
                initialDepositRequest = create IntegrationDeposit.InitialDepositRequest with
                  client = customer, integrationParty, observers = [provider]
                  requestedAmount = totalBilledAmount, currency
                  transactionId, timestamp = now, language
                  userInformation = IntegrationDeposit.UserInformation with
                    firstName, lastName, emailAddress, phoneNumber, birthday
                    countryCode, city, postalCode, subDivision, addressLine1

              create GamblingAccount.PendingTransaction with
                operator, provider, customer
                currency, requestedAmount = totalBilledAmount, appliedFee
                transactionType = GamblingModel.Deposit, transactionId
                startedAt = now, countryCode

              GamblingAccount.Account{
                totalMainBalance, totalWithdrawBalance, totalBetBalance, totalBonusBetBalance
              } <- Utils.getContractWithKey (customerKey this) "Account"

              let accountBalance = totalMainBalance + totalWithdrawBalance + (totalBetBalance - totalBonusBetBalance)

              Right <$>
                (lookupByKey @IdentityModel.GamblerIdentity (customerKey this) >>=
                  Optional.optional
                    (do
                      assertMsg ("Unverified user can't have more than \
                        \" <> (show unverifiedAccountMaxAmount) <> " " <> currency <> " in account")
                        $ (accountBalance + requestedAmount) <= unverifiedAccountMaxAmount

                      unverifiedAccountMaxAmount <- (.unverifiedAccountMaxAmount)
                        <$> Utils.getContractWithKey @GamblingModel.GlobalGamblingConfiguration
                          (operator, provider)  "GlobalGamblingConfiguration"

                      assertMsg ("Taking in consideration pending deposits, an unverified user \
                        \can't have more than " <> (show unverifiedAccountMaxAmount) <> " \
                        \" <> currency <> " in account")
                        $ (totalAmount + accountBalance) <= unverifiedAccountMaxAmount

                      -- Add this deposit to the customer's unverified pending deposits
                      Action.void $ exerciseByKey @GamblingAccount.PendingDeposits (customerKey this)
                        GamblingAccount.AddUnverifiedAccountDeposit with transactionId, requestedAmount

                      initialDepositRequest
                    )
                    (\_ -> do
                      -- Add this deposit to the customer's pending deposits
                      Action.void $ exerciseByKey @GamblingAccount.PendingDeposits (customerKey this)
                        GamblingAccount.AddDeposit with transactionId, requestedAmount

                      initialDepositRequest)
                )

            catch AssertionFailed msg -> do
                  Left <$> createOrLookup GamblingModel.ActionFailure with
                    operator, provider, customer, action = GamblingModel.Deposit
                    actionId = transactionId, reason = msg


        nonconsuming choice FinalizeDepositRequest : Either GamblingModel.ActionFailureCid GamblingModel.ActionSuccessCid
          with
            depositTransactionCid : ContractId IntegrationDeposit.DepositTransaction
          controller provider
          do
            IntegrationDeposit.DepositTransaction{
              confirmedAmount, currency, timestamp, transactionId
            } <- FinanceUtils.fetchAndArchive depositTransactionCid

            try do
              GamblingModel.GlobalGamblingConfiguration{flaggedAmount} <-
                snd <$> fetchByKey @GamblingModel.GlobalGamblingConfiguration (operator, provider)

              appliedFee <- (._2.appliedFee) <$>
                Utils.fetchAndArchiveByKey @GamblingAccount.PendingTransaction
                  (operator, provider, customer, GamblingModel.Deposit, transactionId)

              Utils.whenContractExists @GamblingAccount.PendingDeposits (operator, provider, customer)
                (`exercise` GamblingAccount.RemoveDeposit with transactionId)

              -- Remove applied deposit fee from the total amount to be deposited on the gambling account
              let requestedAmount = confirmedAmount `Numeric.div` (1.0 + appliedFee)

              -- | Check if there is a pending promotion application for this transaction and apply
              -- it if there is, otherwise do nothing applying the promotion means adding the
              -- promotion usage to the promotion map for the user account and add any pending
              -- bonuses if the promotion is of bonus type
              Utils.whenContractExists @PendingPromotionApplication (operator, provider, customer, transactionId)
                (Action.void . (`exercise` ApplyPromotion with transactionId, transactedAmount = requestedAmount))

              createFlag requestedAmount flaggedAmount operator provider customer

              asset <- (\ (assetId, _) ->
                  FinanceTypes.Asset with
                    id = assetId, quantity = Numeric.castAndRound requestedAmount
                ) <$> Utils.checkAssetDesc [provider] currency

              custodyServiceCid <- fst <$> fetchByKey @Custody.Service (customerKey this)
              depositRequestCid <- exercise custodyServiceCid Custody.RequestDeposit with asset
              depositCid <- exercise custodyServiceCid Custody.Deposit with depositRequestCid

              exerciseByKey @GamblingAccount.Account (customerKey this)
                GamblingAccount.Deposit with depositCid, transactionId, appliedFee, transactionTime = timestamp

              Utils.createSendGridNotification (operator, provider, customer)
                SendgridIntegration.Deposit {
                  date = timestamp,
                  transactionCode = transactionId,
                  requestedAmount = requestedAmount,
                  feeCharged = appliedFee `Numeric.mul` requestedAmount
                }

              Right <$> create GamblingModel.ActionSuccess with
                operator, provider, customer
                actionId = transactionId, action = GamblingModel.Deposit
                reason = "Deposit Successful"

            catch AssertionFailed msg ->
                    Left <$> createOrLookup GamblingModel.ActionFailure with
                      operator, provider, customer, action = GamblingModel.Deposit
                      actionId = transactionId, reason = msg


        nonconsuming choice ApproveWithdrawRequest :  Either GamblingModel.ActionFailureCid (ContractId IntegrationWithdraw.InitialWithdrawRequest)
          with
            withdrawRequestForApprovalCid : ContractId GamblingAccount.WithdrawRequestForApproval
          controller provider
          do
            GamblingAccount.WithdrawRequestForApproval{
              integrationParty, requestedAmount, currency, transactionId, promotion, language
            } <- FinanceUtils.fetchAndArchive withdrawRequestForApprovalCid

            try do
              IdentityModel.GamblerUnverifiedIdentity{
                firstName, lastName, emailAddress, phoneNumber, birthday, countryCode
                , city, postalCode, subDivision, addressLine1
              } <- Utils.getContractWithKey @IdentityModel.GamblerUnverifiedIdentity
                (customerKey this) "GamblerUnverifiedIdentity"

              (_, existsAssetDesc) <- Utils.checkAssetDesc [provider] currency
              assertMsg "The currency used in the withdraw request is not supported" existsAssetDesc

              (gamblingAccountCid, gamblingAccount) <- fetchByKey @GamblingAccount.Account (customerKey this)
              assertMsg "There is already a withdrawal in progress"
                $ Optional.isNone gamblingAccount.assetDepositWithdraw

              GamblingModel.GlobalGamblingConfiguration{withdrawFee, minWithdrawAmount} <-
                snd <$> fetchByKey @GamblingModel.GlobalGamblingConfiguration (operator, provider)

              appliedFee <- Optional.optional
                (return withdrawFee)
                (calculateTransactionAmount (customerKey this) GamblingModel.Withdrawal
                  (Numeric.castAndRound withdrawFee) requestedAmount currency transactionId)
                promotion

              assertMsg "Available balance is not enough to cover requested withdraw amount plus \
                \withdraw fee" $ gamblingAccount.totalMainBalance >= requestedAmount

              assertMsg "The requested amount for withdrawl is under than the permitted minimum \
                \withdraw amount" $ minWithdrawAmount <= requestedAmount

              lookupByKey @IdentityModel.GamblerIdentity (customerKey this) >>=
                (`Utils.whenNone` (assertFail "Unverified user can't withdraw from account"))

              exercise gamblingAccountCid GamblingAccount.LockWithdraw with requestedAmount

              now <- getTime
              create GamblingAccount.PendingTransaction with
                operator, provider, customer
                currency, requestedAmount, appliedFee
                transactionType = GamblingModel.Withdrawal, transactionId
                startedAt = now, countryCode

              Right <$> create IntegrationWithdraw.InitialWithdrawRequest with
                client = customer, integrationParty, observers = [provider]
                requestedAmount = requestedAmount `Numeric.mul` (1.0 - appliedFee)
                currency, transactionId, language = language
                timestamp = now
                userInformation = IntegrationWithdraw.UserInformation with
                  firstName, lastName, emailAddress, phoneNumber, birthday
                  countryCode, city, postalCode, subDivision, addressLine1

            catch AssertionFailed msg -> do
                  Left <$> createOrLookup GamblingModel.ActionFailure with
                    operator, provider, customer, action = GamblingModel.Withdrawal
                    actionId = transactionId, reason = msg


        nonconsuming choice FinalizeWithdrawRequest : Either GamblingModel.ActionFailureCid GamblingModel.ActionSuccessCid
          with
            withdrawTransactionCid : ContractId IntegrationWithdraw.WithdrawTransaction
          controller provider
          do
            IntegrationWithdraw.WithdrawTransaction{timestamp, transactionId} <-
              FinanceUtils.fetchAndArchive withdrawTransactionCid

            try do

              GamblingAccount.PendingTransaction{requestedAmount, appliedFee} <- snd
                <$> Utils.fetchAndArchiveByKey @GamblingAccount.PendingTransaction
                  (operator, provider, customer, GamblingModel.Withdrawal, transactionId)

              -- | Check if there is a pending promotion application for this transaction and apply
              -- it  if there is, otherwise do nothing applying the promotion means adding the
              -- promotion usage to the promotion map for the user account and add any pending
              -- bonuses if the promotion is of bonus type
              Utils.whenContractExists @PendingPromotionApplication (operator, provider, customer, transactionId)
                (Action.void . (`exercise` ApplyPromotion with transactionId, transactedAmount = requestedAmount))

              exerciseByKey @GamblingAccount.Account (customerKey this)
                GamblingAccount.Withdraw with transactionId, appliedFee, transactionTime = timestamp

              Utils.createSendGridNotification (operator, provider, customer)
                SendgridIntegration.Withdraw {
                  date = timestamp,
                  transactionCode = transactionId,
                  requestedAmount = requestedAmount,
                  feeCharged = appliedFee `Numeric.mul` requestedAmount
                }

              Right <$> create GamblingModel.ActionSuccess with
                operator, provider, customer, actionId = transactionId
                action = GamblingModel.Withdrawal, reason = "Withdraw Successful"

            catch AssertionFailed msg ->
                    Left <$> createOrLookup GamblingModel.ActionFailure with
                      operator, provider, customer, action = GamblingModel.Withdrawal
                      actionId = transactionId, reason = msg


        nonconsuming choice FinalizeWithdrawFailure : ()
          with
            withdrawFailureCid : ContractId IntegrationWithdraw.WithdrawFailure
          controller provider
          do
              IntegrationWithdraw.WithdrawFailure{transactionId} <- FinanceUtils.fetchAndArchive withdrawFailureCid

              GamblingAccount.PendingTransaction{requestedAmount} <-
                snd <$> Utils.fetchAndArchiveByKey @GamblingAccount.PendingTransaction (operator, provider, customer, GamblingModel.Withdrawal, transactionId)

              createOrLookup GamblingModel.ActionFailure with
                operator, provider, customer, action = GamblingModel.Withdrawal
                actionId = transactionId, reason = "Withdraw Request Failed"

              exerciseByKey @GamblingAccount.Account (customerKey this)
                GamblingAccount.UnlockWithdraw with requestedAmount

              return ()


        nonconsuming choice FinalizeDepositFailure : (ContractId GamblingModel.ActionFailure)
            with
                depositFailureCid : ContractId IntegrationDeposit.DepositFailure
            controller provider
                do
                  IntegrationDeposit.DepositFailure{transactionId, reason} <- fetch depositFailureCid

                  failureMsg <-
                    try do
                      archive depositFailureCid
                      let transactionKey = (operator, provider, customer, GamblingModel.Deposit, transactionId)
                      Utils.fetchAndArchiveByKey @GamblingAccount.PendingTransaction transactionKey

                      lookupByKey @GamblingAccount.PendingDeposits (customerKey this) >>= optional
                        (assertFail "PendingDeposits contract not found")
                        (`exercise` GamblingAccount.RemoveDeposit with transactionId)

                      return reason
                    catch AssertionFailed msg -> pure $ msg

                  createOrLookup GamblingModel.ActionFailure with
                    operator, provider, customer, action = GamblingModel.Deposit
                    actionId = transactionId, reason = failureMsg


        nonconsuming choice ApproveBetPlacement : Either GamblingModel.ActionFailureCid (BetModel.BetPlacementKey)
          with
            betPlacementRequestKey : BetModel.BetPlacementKey
            userBetPlacementsKeys : [BetPlacementKey]
          controller provider
          do
            (betPlacementRequestCid, BetModel.BetPlacementRequest {
              details = <EMAIL> {side, betPlacementId, eventKey},
              cashDeposit, optBonusAmount, promotion, inBulk
            }) <- fetchByKey betPlacementRequestKey

            try do
              let betStake = (BetModel.fromSide side).stake

              GamblingModel.GlobalGamblingConfiguration{
                minOdd, maxOdd, public
              } <- Utils.getContractWithKey @GamblingModel.GlobalGamblingConfiguration
                (operator, provider) "GlobalGamblingConfiguration"

              validateMaxMinOdds details minOdd maxOdd

              now <- getTime

              event <- Utils.getContractWithKey @EventModel.EventInstrument eventKey "EventInstrument"

              assertMsg "Betting outcome(s) is not available for the chosen event"
                $ (BetModel.fromSide side).outcome `elem` (getOutcomes event.details.outcomes)
              assertMsg "Bet can no longer be placed for chosen event"
                $ event.details.eventStatus == IntegrationEvents.NotStarted
              assertMsg "Bet cannot be placed because event may have already started."
                $ event.details.startDate > now

              Optional.whenSome promotion $ \ promotionKey -> do
                payout <- Utils.rightOrFail (BetModel.getPayout side)
                Utils.unlessSome inBulk do
                    checkApplyPromotion promotionKey GamblingModel.BetPlacement betPlacementId
                      (customerKey this) betStake payout

                    (assetId, existsAssetDesc) <- Utils.checkAssetDesc [provider] "USD"
                    assertMsg "The currency used in the deposit request is not supported" existsAssetDesc

                    Action.void $ create PendingPromotionApplication with
                      operator, provider, customer
                      actionId = betPlacementId, promotionKey
                      currency = assetId.label, offeredAmount = 0.0

              -- Fetch Or Create Binary Options and isse correspondent Binary Option to gamblers bet
              Asset.AssetDeposit{asset} <- fetch cashDeposit
              let currency = asset.id.label
              (instruments, betIssuanceKey, betDepositCid) <-
                BetModel.originateAndIsssueBet betPlacementRequestCid currency (customerKey this) public

              -- Fetch or Create Listing Cid
              listingId <- BetModel.listBet betPlacementRequestCid instruments operator provider public

              userBetPlacements <- mapA (\ betKey -> snd <$> fetchByKey @BetModel.BetPlacement betKey) userBetPlacementsKeys
              currentBet <- fetch betPlacementRequestCid
              assertMsg "This bet placement is not allowed because it could be matched with an \
                \unmatched bet you already placed."
                $ checkIfCanPlaceBet currentBet userBetPlacements listingId

              -- Finalize Bet Placement request with all necessary contracts
              archive betPlacementRequestCid
              create BetModel.BetPlacementFinalizeRequest with
                operator, provider, customer, instruments, betIssuanceKey, betDepositCid
                listingId, details, optBonusAmount, promotion, inBulk

              Right <$> pure (operator, provider, customer, details.betPlacementId)

            catch AssertionFailed msg -> do

                  Utils.unlessSome inBulk do
                    Utils.whenContractExists @BetModel.BetPlacementRequestFlag (operator, provider, customer) (`exercise` BetModel.ArchiveBetFlag)

                    archive betPlacementRequestCid
                    -- Unlock cash for bet
                    Action.void $ exerciseByKey @GamblingAccount.Account (customerKey this)
                        GamblingAccount.UnlockBet with
                            betPlacementId
                            optBonusBetAmount = optBonusAmount

                  Left <$> createOrLookup GamblingModel.ActionFailure with
                      operator, provider, customer, action = GamblingModel.BetPlacement
                      actionId = betPlacementId, reason = msg


        nonconsuming choice FinalizeBetPlacement : Either GamblingModel.ActionFailureCid (ContractId BetModel.BetPlacement)
          with
            finalizeBetPlacementKey :  BetModel.BetPlacementKey
          controller provider
          do
            (finalizeBetPlacementCid, BetModel.BetPlacementFinalizeRequest{
                listingId, betDepositCid, details, optBonusAmount, inBulk
            }) <- fetchByKey finalizeBetPlacementKey

            try do
                orderCid <- BetModel.orderBet details.side listingId betDepositCid (customerKey this)

                TradingService.CreateOrderRequest{details = orderDetails} <- fetch orderCid

                Utils.unlessSome inBulk do
                  -- | If bet is not part of bulk, check if there is a pending promotion
                  -- application and apply it, otherwise do nothing.
                  -- Applying the promotion means adding the promotion usage to the promotion map
                  -- for the user account and add any pending bonuses if the promotion is of bonus type

                  Utils.whenContractExists @PendingPromotionApplication (operator, provider, customer, details.betPlacementId)
                    (\ pendingPromotionApplicationCid -> do
                        payout <- Utils.rightOrFail (BetModel.getPayout details.side)
                        Action.void $ exercise pendingPromotionApplicationCid
                          ApplyPromotion with transactionId = "", transactedAmount = payout)

                  now <- getTime
                  stake <- Utils.rightOrFail (BetModel.getStake details.side)
                  eventTitle <- optional
                    (assertFail $ "Event has no title in the default locale (" <> show Utils.EN_US <> ")")
                    pure $ Map.lookup (show Utils.EN_US) details.eventTitle
                  let
                    sendGridBetDetails = SendgridIntegration.BetDetails with
                        ticketId = details.betPlacementId
                        eventTitle
                        dateTime = now
                        side = (BetModel.getSide details.side)
                        stake = stake
                        oddValue = show (BetModel.fromSide details.side).odd

                  Utils.createSendGridNotification (operator, provider, customer)
                    SendgridIntegration.BetPlacement {
                      betDetails = [sendGridBetDetails]
                    }

                  Utils.whenContractExists @BetModel.BetPlacementRequestFlag (operator, provider, customer)
                    (`exercise` BetModel.ArchiveBetFlag)

                  pure ()

                Right <$> exerciseByKey @BetModel.BetPlacementFinalizeRequest finalizeBetPlacementKey
                  BetModel.ApproveBetPlacement with
                    orderId = orderDetails.id
                    bonusBetAmount = optBonusAmount

            catch AssertionFailed msg -> do

                  Utils.unlessSome inBulk do
                    Utils.whenContractExists @BetModel.BetPlacementRequestFlag
                      (operator, provider, customer) (`exercise` BetModel.ArchiveBetFlag)

                    Utils.whenContractExists @PendingPromotionApplication
                      (operator, provider, customer, details.betPlacementId) archive

                    -- Remove pending promotion application
                    archive finalizeBetPlacementCid

                    -- Unlock cash for bet
                    Action.void $ exerciseByKey @GamblingAccount.Account (customerKey this)
                      GamblingAccount.UnlockBet with
                          betPlacementId = details.betPlacementId
                          optBonusBetAmount = optBonusAmount

                  Left <$> createOrLookup GamblingModel.ActionFailure with
                    operator, provider, customer, actionId = details.betPlacementId
                    action = GamblingModel.BetPlacement, reason = msg


            -- Automatic cancelation (ex: when event gets cancelled)

        nonconsuming choice ApproveBulkBetPlacement : Either GamblingModel.ActionFailureCid (ContractId BetModel.BulkBetPlacementFinalizeRequest)
            with
                bulkBetPlacementRequestCid : ContractId BetModel.BulkBetPlacementRequest
                userBetPlacementsKeys : [BetPlacementKey]
            controller provider
                do
                    BetModel.BulkBetPlacementRequest{
                        totalPayout, totalRealStake, betPlacementReqKeyList, promotion, bulkBetPlacementId
                    } <- fetch bulkBetPlacementRequestCid

                    try do

                        mapRequestList <- mapA (fetchByKey @BetModel.BetPlacementRequest) betPlacementReqKeyList
                        let (_, requestList) = unzip mapRequestList
                        assertMsg "Bet slip cannot be approved because multiple bets could be matched together" $ checkIfBulkCanBePlaced requestList

                        Optional.whenSome promotion (\ promotionKey -> do
                            checkApplyPromotion promotionKey GamblingModel.BetPlacement
                              bulkBetPlacementId (customerKey this) totalRealStake totalPayout

                            (assetId, existsAssetDesc) <- Utils.checkAssetDesc [provider] "USD"
                            assertMsg "The currency used in the deposit request is not supported" existsAssetDesc

                            Action.void $ create PendingPromotionApplication with
                              operator, provider, customer
                              actionId = bulkBetPlacementId, promotionKey
                              currency = assetId.label, offeredAmount = 0.0
                          )
                        betPlacementFinReqKeyList <- mapA (\ betPlacementRequestKey -> do
                                exercise self ApproveBetPlacement with betPlacementRequestKey, userBetPlacementsKeys >>=
                                    Either.either (\ actionFailureCid -> do
                                                    actionFailure <- fetch actionFailureCid
                                                    throw $ BulkRequestException actionFailure
                                                    ) return

                            ) betPlacementReqKeyList

                        betPlacementListingIds <- mapA (\ betPlacementFinReqKey -> do
                                (_, bpFinReq) <- fetchByKey @BetModel.BetPlacementFinalizeRequest betPlacementFinReqKey
                                return bpFinReq.listingId
                            ) betPlacementFinReqKeyList

                        Right <$> (exercise bulkBetPlacementRequestCid $ BetModel.Bulk_ApproveBet with betPlacementFinReqKeyList, betPlacementListingIds)

                    catch
                        BulkRequestException{actionFailure} -> do
                            Utils.whenContractExists @BetModel.BulkBetPlacementRequestFlag (operator, provider, customer) (`exercise` BetModel.ArchiveBulkBetFlag)

                            unlockBulkBetsAfterFailure @BetModel.BulkBetPlacementRequest @BetModel.BetPlacementRequest
                                bulkBetPlacementRequestCid betPlacementReqKeyList (customerKey this)

                            Left <$> createOrLookup actionFailure

                        AssertionFailed msg -> do

                            Utils.whenContractExists @BetModel.BetPlacementRequestFlag (operator, provider, customer) (`exercise` BetModel.ArchiveBetFlag)

                            unlockBulkBetsAfterFailure @BetModel.BulkBetPlacementRequest @BetModel.BetPlacementRequest
                                bulkBetPlacementRequestCid betPlacementReqKeyList (customerKey this)

                            Left <$> createOrLookup GamblingModel.ActionFailure with
                                operator, provider, customer, actionId = bulkBetPlacementId
                                action = GamblingModel.BetPlacement, reason = msg


        nonconsuming choice FinalizeBulkBetPlacement : Either GamblingModel.ActionFailureCid (ContractId BetModel.BulkBetPlacement)
          with
            bulkBetPlacementFinReqCid : ContractId BetModel.BulkBetPlacementFinalizeRequest
          controller provider
            do
              BetModel.BulkBetPlacementFinalizeRequest{betPlacementFinReqKeyList, bulkBetPlacementId} <-
                fetch bulkBetPlacementFinReqCid

              try do
                -- check if there is a pending promotion application and apply it, otherwise do nothing
                -- applying the promotion means adding the promotion usage to the promotion map for the user account and add any pending bonuses if the promotion is of bonus type
                Utils.whenContractExists @PendingPromotionApplication (operator, provider, customer, bulkBetPlacementId)
                  (Action.void . (`exercise` ApplyPromotion with transactionId = "", transactedAmount = 0.0))

                betPlacementList <- mapA (\ finalizeBetPlacementKey ->
                    (exercise self $ FinalizeBetPlacement with finalizeBetPlacementKey) >>=
                      Either.either (fetch >=> throw . BulkRequestException) fetch
                  ) betPlacementFinReqKeyList

                now <- getTime

                mappedBetInfo <- mapA (\ betPlacementData -> do
                    stake <- Utils.rightOrFail (BetModel.getStake betPlacementData.details.side)
                    eventTitle <- optional
                      (assertFail $ "Event has no title in the default locale (" <> show Utils.EN_US <> ")")
                      pure $ Map.lookup (show Utils.EN_US) betPlacementData.details.eventTitle
                    let
                      sendGridBetDetails = SendgridIntegration.BetDetails with
                          ticketId = betPlacementData.details.betPlacementId
                          eventTitle
                          dateTime = now
                          side = (BetModel.getSide betPlacementData.details.side)
                          stake = stake
                          oddValue = show (BetModel.fromSide betPlacementData.details.side).odd

                    pure (betPlacementData.details.betPlacementId, sendGridBetDetails)
                  ) betPlacementList

                let (betPlacementIdList, betDetailsForNotification) = unzip mappedBetInfo

                -- | Surrounded with try ... catch clause to throw exception that is caught in
                -- external scope, to reduce code deplication on error handling 
                try do
                  Utils.createSendGridNotification (operator, provider, customer)
                    SendgridIntegration.BetPlacement {
                        betDetails = betDetailsForNotification
                    }
                catch AssertionFailed msg ->
                        throw $ BulkRequestException GamblingModel.ActionFailure with
                            operator, provider, customer, actionId = bulkBetPlacementId
                            action = GamblingModel.BetPlacement, reason = msg


                Utils.whenContractExists @BetModel.BulkBetPlacementRequestFlag (operator, provider, customer) (`exercise` BetModel.ArchiveBulkBetFlag)

                Right <$> (exercise bulkBetPlacementFinReqCid $ BetModel.Bulk_FinalizeBet with betPlacementIdList)

              catch BulkRequestException{actionFailure} -> do
                      Utils.whenContractExists @BetModel.BulkBetPlacementRequestFlag (operator, provider, customer) (`exercise` BetModel.ArchiveBulkBetFlag)

                      -- Remove pending promotion application
                      Utils.whenContractExists @PendingPromotionApplication
                        (operator, provider, customer, bulkBetPlacementId) archive
                      unlockBulkBetsAfterFailure @BetModel.BulkBetPlacementFinalizeRequest @BetModel.BetPlacementFinalizeRequest
                          bulkBetPlacementFinReqCid betPlacementFinReqKeyList (customerKey this)

                      Left <$> createOrLookup actionFailure

        nonconsuming choice CancelBet : Either GamblingModel.ActionFailureCid (ContractId BetModel.FinalizeBetPlacementCancelRequest)
          with
            betPlacementCid : ContractId BetModel.BetPlacement
          controller provider
          do
            BetModel.BetPlacement{
              details = BetModel.Details{betPlacementId, eventKey, side, eventTitle = betEventTitle}
              , status = betStatus
            } <- fetch betPlacementCid

            let BetModel.SideDetails{outcome = betOutcome} = BetModel.getSideDetails side

            try do
              Utils.whenContractExists @BetModel.CancelledBetPlacement (operator, provider, customer, betPlacementId)
                (\ _ -> assertFail "Bet is already cancelled")

              betCancelMsg <- lookupByKey @EventModel.EventInstrument eventKey >>= optional
                (pure "Bet was cancelled because event no longer exists")
                (\ eventCid -> do
                  EventModel.EventInstrument{
                      details = EventModel.Details{eventStatus, outcomes, eventParticipants, eventTitle}
                    } <- fetch eventCid

                  title <- optional
                    (assertFail $ "Event has no title in the default locale (" <> show Utils.EN_US <> ")")
                    pure $ Map.lookup (show Utils.EN_US) betEventTitle

                  let
                    eventTitleMatchesBetEventTitle = betEventTitle == eventTitle
                    betOutcomeExists =
                      any (\ EventModel.OutcomeOdds{outcome} -> outcome == betOutcome) outcomes
                    betSideParticipantExists =
                      any (\ participant ->
                        optional True (participant.id ==)
                          (BetModel.getSideDetails side).outcome.participantId) eventParticipants

                    eventHasNoUpdatesForBetCancel = eventTitleMatchesBetEventTitle && betOutcomeExists && betSideParticipantExists
                  -- | If Bet outcome is present in event's possible outcomes, follow standard cancellation rules
                  -- otherwise create FinalizeBetPlacementCancelRequest contract with respective message
                  if eventHasNoUpdatesForBetCancel
                    then do
                      case betStatus of
                        BetModel.Matched -> assertMsg "Bet cannot be Cancelled because it is Matched\
                          \ and event has not been Cancelled or Postponed"
                          $ (eventStatus == IntegrationEvents.Cancelled || eventStatus == IntegrationEvents.Postponed)
                        BetModel.Unmatched -> assertMsg
                          "Bet can only be Manually Cancelled by User because event has not started yet"
                          $ (eventStatus /= IntegrationEvents.NotStarted)
                        _ -> return ()
                      cancelBetMessageBuilder title eventStatus
                    else
                      pure $ partialBetCancellationMsg title <> " was updated with contradictory details"

                )

              cancelRequestCid <- processCancelBet (customerKey this) betPlacementId betCancelMsg False

              return $ Right cancelRequestCid

            catch AssertionFailed msg -> do
                  Left <$> createOrLookup GamblingModel.ActionFailure with
                    operator, provider, customer
                    actionId = betPlacementId, action = GamblingModel.BetCancelation, reason = msg

            -- Approval on manual cancelation request
        nonconsuming choice ApproveBetPlacementCancelRequest : Either GamblingModel.ActionFailureCid (ContractId BetModel.FinalizeBetPlacementCancelRequest)
            with
                betPlacementCancelRequestCid : ContractId BetModel.BetPlacementCancelRequest
            controller provider
                do

                    BetModel.BetPlacementCancelRequest{betPlacementId, customer} <- FinanceUtils.fetchAndArchive betPlacementCancelRequestCid

                    try do

                        (_, betPlacement) <- fetchByKey @BetModel.BetPlacement (operator, provider, customer, betPlacementId)

                        assertMsg "Bet cannot be canceled because it has already been matched" $ betPlacement.status == BetModel.Unmatched


                        Right <$> processCancelBet (operator, provider, customer) betPlacementId "Bet was cancelled by user" True

                    catch AssertionFailed msg -> do
                            Left <$> createOrLookup GamblingModel.ActionFailure with
                              operator, provider, customer, actionId = betPlacementId
                              action = GamblingModel.BetPlacement, reason = msg

          -- Finalize the cancellation of bets
          -- The finalizeBetPlacementCancelCidList contains all related FinalizeBetPlacementCancelRequest (same orderId)
          -- The errorMsg field comes from either a successful cancel or a failure, sent by Exberry
          -- The eventCancelled field is used to cancel bets even if exberry sent an error due to the Event not being available anymore
        nonconsuming choice FinalizeBetPlacementCancel
          : Either (ContractId GamblingModel.ActionFailure) (ContractId BetModel.CancelledBetPlacement, ContractId GamblingModel.ActionSuccess)
          with
            finalizeBetPlacementCancelCid : ContractId BetModel.FinalizeBetPlacementCancelRequest
            betPlacementCid : Optional (ContractId BetModel.BetPlacement)
            errorMsg : Optional Text
            eventCancelled : Bool
          controller provider
          do
            finalizeBetPlacementCancel <- fetch finalizeBetPlacementCancelCid

            --TODO Refactor me
            try do
              if (Optional.isNone errorMsg || eventCancelled) then do

                betPlacement <- case (betPlacementCid) of
                  Some cid -> do fetch cid
                  None -> do
                    let betPlacementKey = (finalizeBetPlacementCancel.operator
                                          , finalizeBetPlacementCancel.provider
                                          , finalizeBetPlacementCancel.customer
                                          , finalizeBetPlacementCancel.betPlacementId)

                    Utils.getContractWithKey @BetModel.BetPlacement betPlacementKey "BetPlacement"

                now <- getTime
                stake <- Utils.rightOrFail (BetModel.getStake betPlacement.details.side)

                eventTitle <- optional
                  (assertFail $ "Event has no title in the default locale (" <> show Utils.EN_US <> ")")
                  pure $ Map.lookup (show Utils.EN_US) betPlacement.details.eventTitle

                actionType <- getBetCancellationActionTypeForSendGrid finalizeBetPlacementCancel eventTitle

                Utils.createSendGridNotification (operator, provider, customer)
                    SendgridIntegration.BetCancellation with
                      ticketId = betPlacement.details.betPlacementId
                      eventTitle, dateTime = now, stake, actionType
                      side = (BetModel.getSide betPlacement.details.side)
                      oddValue = show (BetModel.fromSide betPlacement.details.side).odd

                Right <$> finalizeBetPlacementCancelSuccess betPlacement finalizeBetPlacementCancel
                  finalizeBetPlacementCancelCid operator provider customer
              else do
                let Some msg = errorMsg
                archive finalizeBetPlacementCancelCid

                lookupByKey @TradingService.CancelOrderRequest (provider, finalizeBetPlacementCancel.orderId)
                  >>= optional
                    (assertFail "CancelOrderRequest doesn't exist")
                    (Action.void . (flip exercise)TradingService.FailureCancel with
                      errorMessage = msg, errorCode = 0)

                Left <$> createOrLookup GamblingModel.ActionFailure with
                  operator, provider, customer, actionId = finalizeBetPlacementCancel.betPlacementId
                  action = GamblingModel.BetPlacement, reason = msg

            catch AssertionFailed msg -> do

                    -- If finalize fails, archive everything
                    archive finalizeBetPlacementCancelCid

                    lookupByKey @TradingService.CancelOrderRequest (provider, finalizeBetPlacementCancel.orderId)
                      >>= optional (debug "Couldn't find CancelOrderRequest") archive

                    Left <$> createOrLookup GamblingModel.ActionFailure with
                      operator, provider, customer
                      actionId = finalizeBetPlacementCancel.betPlacementId
                      action = GamblingModel.BetPlacement
                      reason = msg


        nonconsuming choice RejectBetPlacementCancelRequest : ()
            with
                betPlacementCancelRequestCid : ContractId BetModel.BetPlacementCancelRequest
            controller provider
            do
                    archive betPlacementCancelRequestCid


        nonconsuming choice ApproveEventUpdate : Either (GamblingModel.ActionFailureCid) (ContractId EventModel.EventInstrument)
          with
            updateRequestCid  : ContractId EventModel.EventInstrumentUpdateRequest
          controller provider
          do
            eventUpdate <- fetch updateRequestCid
            try do
              Right <$> exercise updateRequestCid EventModel.Approve_Update
            catch AssertionFailed msg ->
                    Left <$> createOrLookup GamblingModel.ActionFailure with
                      operator, provider, customer, action = GamblingModel.EventInstrumentUpdate
                      actionId = eventUpdate.oldEventKey._3, reason = msg

        nonconsuming choice ApproveGamblingDisableListing : ContractId ListingModel.GamblingListing
            with
                disableListingRequestCid : ContractId MarketplaceListingService.DisableListingRequest
            controller provider
            do
                MarketplaceListingService.DisableListingRequest{listingCid} <- fetch disableListingRequestCid
                listing <- fetch listingCid
                exerciseByKey @ListingModel.GamblingUpdateListingRequest (operator, provider, listing.listingId)
                    ListingModel.ApproveDisableListings with ..


        nonconsuming choice UpdateBetPlacementStatus : Either GamblingModel.ActionFailureCid ()
          with
            betPlacementKey     : BetPlacementKey
            newStatus           : Optional TradingModel.Status
            newSettlementCid    : Optional (ContractId SettlementModel.SettlementInstruction)
            newMatchedOdd       : Optional (Numeric 2)
          controller provider
          do
            -- We assume newMatchedOdd is always a Decimal odd value
            (_,
              BetModel.BetPlacement{
                settlementInstructionCid, status, details, bonusBetAmount, promotion, inBulk, instruments,
                betIssuanceKey, orderId, placedAt, listingId, verified, previousSplitBetPlacementIdList
            }) <- fetchByKey betPlacementKey
            try do

              currentOdd <- OddsModel.getOdd <$> Utils.rightOrFail (getOddFromTo (BetModel.fromSide details.side).odd Decimal)
              let
                --getSideOdd sends the Fractional odd, we need to convert to Decimal odd
                --currentOdd = (Either.fromRight 0.0 (BetModel.getSideOdd details.side)) + 1.0
                newOdd = Optional.fromOptional currentOdd newMatchedOdd
                stake = (BetModel.fromSide details.side).stake

                matchedOddType = Decimal newOdd
                newSideDetails = (BetModel.fromSide details.side) with odd = matchedOddType
                newSide = if (BetModel.getSide details.side) == "Back" then Back newSideDetails else Lay newSideDetails

              payout <- Utils.rightOrFail $ OddsModel.calculatePayout matchedOddType stake

              transformedStatus <- optional (return status) (BetModel.transform) newStatus

              let createBetWithNewInfo newSettlementInstructionCid newBonusBetAmount newMatchedOdd =
                    create BetModel.BetPlacement with
                      operator, provider, customer, verified, promotion, inBulk, instruments
                      betIssuanceKey, listingId, details = details with side = newSide
                      orderId, placedAt, previousSplitBetPlacementIdList, status = transformedStatus
                      settlementInstructionCid = newSettlementInstructionCid
                      bonusBetAmount = newBonusBetAmount
                      matchedOdd = newMatchedOdd

              exerciseByKey @BetModel.BetPlacement betPlacementKey Archive
                <* if ((BetModel.getSide details.side) == "Lay" && newOdd /= 1.0 && newOdd /= currentOdd)
                    then do
                      exerciseByKey @GamblingAccount.Account (customerKey this) GamblingAccount.UnlockBet with betPlacementId = details.betPlacementId, optBonusBetAmount = bonusBetAmount

                      Utils.rightOrFail (BetModel.getStake details.side) >> do
                        Action.void $ exerciseByKey @GamblingAccount.Account (customerKey this)
                          GamblingAccount.LockBet with
                            betPlacementId = details.betPlacementId
                            requestedAmount = payout

                      let
                        settle = optional settlementInstructionCid Some newSettlementCid
                        newBonusBetAmount = if payout <= (Optional.fromOptional 0.0 bonusBetAmount) then Some payout else bonusBetAmount

                      createBetWithNewInfo settle newBonusBetAmount (Some newOdd)
                    else
                      createBetWithNewInfo newSettlementCid bonusBetAmount newMatchedOdd

              let newDetails = details with side = newSide
              Action.when (transformedStatus == BetModel.Matched) $ do
                Action.void $ create GamblingModel.ActionSuccess with
                  operator, provider, customer
                  actionId = newDetails.betPlacementId
                  action = GamblingModel.BetMatched
                  reason = "Bet Matched Successful"
              pure $ Right ()

            catch e@(BetModel.NotMappedException _ _) -> do
                      Left <$> createOrLookup GamblingModel.ActionFailure with
                        operator, provider, customer
                        action = GamblingModel.BetPlacement
                        actionId = details.betPlacementId
                        reason = message e


        nonconsuming choice UpdateDepositLimit : ContractId GamblingAccount.Account
            with
                newDepositLimit : Map.Map GamblingAccount.TimedLimit (Numeric 2)
            controller provider
                do
                    today <- Date.toDateUTC <$> getTime
                    let
                        accruedLimit limit = GamblingAccount.AccruedLimit with limit, accrued = 0.0, date = today
                        depositLimit = List.foldr (\ (key_, limit) -> Map.insert key_ (accruedLimit limit)) Map.empty $ Map.toList newDepositLimit

                    exerciseByKey @GamblingAccount.Account (customerKey this) $ GamblingAccount.UpdateDepositLimit depositLimit


        nonconsuming choice RemoveDepositLimit : ContractId GamblingAccount.Account
            with
                timeFrameList : [GamblingAccount.TimedLimit]
            controller provider
                do
                    exerciseByKey @GamblingAccount.Account (customerKey this) $ GamblingAccount.RemoveDepositLimit timeFrameList

        nonconsuming choice RequestDisableGamblingListing : ContractId MarketplaceListingService.DisableListingRequest
            with
                gamblingListingCid :ContractId ListingModel.GamblingListing
            controller provider
                do
                    gamblingListing <- fetch gamblingListingCid
                    exerciseByKey @ListingModel.GamblingListing (operator, provider, gamblingListing.internalId)
                         ListingModel.RequestDisableListings with ..


        nonconsuming choice ApproveBetSplit : [ContractId BetModel.BetPlacement]
            with
                betPlacementKey             : BetPlacementKey
                settlementInstructionCid    : ContractId SettlementModel.SettlementInstruction
                executedOdd                 : OddType
            controller provider
                do
                    BetModel.BetPlacement{
                        betIssuanceKey, details, instruments, listingId, orderId,
                        placedAt, bonusBetAmount, promotion, inBulk, verified, previousSplitBetPlacementIdList
                    } <- FinanceUtils.fetchAndArchiveByKey betPlacementKey

                    TradingModel.Order{remainingQuantity} <- snd <$> fetchByKey @TradingModel.Order (provider, orderId)

                    -- Unlock Previous Bet
                    exerciseByKey @GamblingAccount.Account (customerKey this) GamblingAccount.UnlockBet with betPlacementId = details.betPlacementId, optBonusBetAmount = bonusBetAmount

                    let
                        updateSide (Back sideDetails) newOdd newStake = Back sideDetails with stake = newStake, odd = newOdd
                        updateSide (Lay sideDetails) newOdd newStake = Lay sideDetails with stake = newStake, odd = newOdd

                        originalOdd = (BetModel.fromSide details.side).odd

                    executedOddWithOriginalOddType <-  Utils.rightOrFail $ OddsModel.convert executedOdd originalOdd

                    let
                        matchedAmount = (BetModel.fromSide details.side).stake - Numeric.castAndRound remainingQuantity

                        matchedBetDetails = details with
                            betPlacementId = details.betPlacementId <> "_1"
                            side = updateSide details.side executedOddWithOriginalOddType matchedAmount

                        unmatchedBetDetails = details with
                            betPlacementId = details.betPlacementId <> "_2"
                            side = updateSide details.side originalOdd (Numeric.castAndRound remainingQuantity)

                    (_matchedFiatAmountWithOriginalOdd, matchedFiatAmountWithNewOdd, unmatchedFiatAmountWithOriginalOdd) <- -- FIXME: The _matchedFiatAmountWithOriginalOdd is not used anywhere
                            case details.side of
                                Back{} -> pure (matchedAmount, matchedAmount, (Numeric.castAndRound remainingQuantity))
                                Lay{} -> Utils.rightOrFail $ (,,)
                                    <$> OddsModel.calculatePayout originalOdd matchedAmount
                                    <*> OddsModel.calculatePayout executedOddWithOriginalOddType matchedAmount
                                    -- FIXME: cancel the unmatched side of the bet in case calculating the payout fails
                                    <*> OddsModel.calculatePayout originalOdd (Numeric.castAndRound remainingQuantity)

                    let
                        -- | Calculate remaining bonus amount for both sides

                        bonusAmount = Optional.fromOptional 0.0 bonusBetAmount
                        remainingMatchedBonusAmount =
                            -- if there is not enough bonus to cover the matched amount then
                            if matchedFiatAmountWithNewOdd >= bonusAmount
                            -- then, you get the full bonus amount
                            then bonusAmount
                            -- otherwise, you get as much bonus to cover the matched amount
                            else matchedFiatAmountWithNewOdd

                        remainingUnmatchedBonusAmount
                            -- if there is not enough bonus to cover the matched amount then
                            | matchedFiatAmountWithNewOdd >= bonusAmount
                            -- then, you get no bonus because all goes to the match side
                            = 0.0
                            -- if there is enough bonus to cover both amounts
                            | bonusAmount > (matchedFiatAmountWithNewOdd + unmatchedFiatAmountWithOriginalOdd)
                            -- then, you get as much bonus to cover the unmatch amount
                            = unmatchedFiatAmountWithOriginalOdd
                            | otherwise
                            -- otherwise, you leave the remaining bonus to the unmatch side
                            = bonusAmount - matchedFiatAmountWithNewOdd

                        toOptional : Numeric 2 -> Optional (Numeric 2)
                        toOptional value = if value == 0.0 then None else Some value

                        (matchedBonusAmount, unmatchedBonusAmount)
                                -- if there is no bonus amount for the bet, then there is nothing todo
                                | (Optional.isNone bonusBetAmount)  = (None, None)
                                -- if there is nothing remaining, then all bonus lies on the match side
                                | (remainingQuantity <= 0.0)        = (bonusBetAmount, None)        -- FIXME: This choice is only exercised by the exberry adapter only if the remainingQuantity is greater than 0.0. In this case it is not necessary to have this check
                                | otherwise                         =
                                    (toOptional remainingMatchedBonusAmount, toOptional remainingUnmatchedBonusAmount)

                        -- TODO add property assertion: no asset quantity is created nor deleted

                        lockBetForAmountAndSide betDetails settlementInstructionCid status bonusBetAmount newMatchedOdd = do
                            Utils.rightOrFail (BetModel.getStake betDetails.side) >>= \ requestedAmount ->
                                exerciseByKey @GamblingAccount.Account (customerKey this)
                                    GamblingAccount.LockBet with
                                        betPlacementId = betDetails.betPlacementId
                                        requestedAmount

                            create BetModel.BetPlacement with
                              operator, provider, customer, verified, promotion, inBulk
                              details = betDetails, settlementInstructionCid, status
                              instruments, betIssuanceKey, listingId, orderId, placedAt
                              matchedOdd = newMatchedOdd, bonusBetAmount
                              previousSplitBetPlacementIdList =
                                details.betPlacementId::previousSplitBetPlacementIdList

                    decimalExecutedOdd <- Utils.rightOrFail $ OddsModel.getOddFromTo executedOdd Decimal

                    matchedBet <- lockBetForAmountAndSide matchedBetDetails
                      (Some settlementInstructionCid) BetModel.Matched matchedBonusAmount
                      (Some (OddsModel.getOdd decimalExecutedOdd))

                    unmatchedBet <- lockBetForAmountAndSide unmatchedBetDetails
                      None BetModel.Unmatched unmatchedBonusAmount None

                    Optional.whenSome inBulk $ \ bulkBetPlacementId ->
                      Action.void $ exerciseByKey @BetModel.BulkBetPlacement (operator, provider, customer, bulkBetPlacementId)
                        BetModel.Bulk_SplitBet with
                          unsplitPlacementId = details.betPlacementId
                          splitPlacementIdList = [matchedBetDetails.betPlacementId, unmatchedBetDetails.betPlacementId]

                    return [matchedBet, unmatchedBet]


        nonconsuming choice ExecuteBetMatching : Either GamblingModel.ActionFailureCid (SettlementModel.ExecutedBetsCid)
          with
            backCustomer : Party
            layCustomer : Party
            backBetPlacementId : Text
            layBetPlacementId : Text
            settlementInstructionCid : ContractId SettlementModel.SettlementInstruction
          controller provider
            do
              let executionId = Text.intercalate "_" [backBetPlacementId, layBetPlacementId]

              try do
                assertMsg "Gambling service customer must be the same as provider" $ customer == provider

                let
                  getBetPlacementWithId betCustomer betPlacementId =
                    snd <$> fetchByKey @BetModel.BetPlacement (operator, provider, betCustomer, betPlacementId)

                BetModel.BetPlacement{
                    details = BetModel.Details{eventKey = backEventKey},
                    matchedOdd = backMatchedOdd
                } <- getBetPlacementWithId backCustomer backBetPlacementId

                BetModel.BetPlacement{
                    details = BetModel.Details{eventKey = layEventKey},
                    matchedOdd = layMatchedOdd
                } <- getBetPlacementWithId layCustomer layBetPlacementId

                let
                    backExecutedOdd = Optional.fromSomeNote "Back bet does not have matched odd" backMatchedOdd
                    _layExecutedOdd = Optional.fromSomeNote "Lay bet does not have matched odd" layMatchedOdd

                assertMsg "Back and Lay matched odds are different" $ backMatchedOdd == layMatchedOdd
                assertMsg "Event keys for back bet placement and lay bet placement do not match" $ backEventKey == layEventKey

                Right <$> create SettlementModel.ExecutedBets with
                  operator, provider, backCustomer, layCustomer, backBetPlacementId, layBetPlacementId
                  settlementInstructionCid, executionId, isCancelled = False, isSettled = False
                  eventKey = backEventKey, signers = Set.empty, executedOdd = Decimal backExecutedOdd

              catch AssertionFailed msg ->
                      Left <$> createOrLookup GamblingModel.ActionFailure with
                        operator, provider, customer
                        action = GamblingModel.Settlement
                        actionId = executionId
                        reason = msg


        nonconsuming choice GiveBonusBounty : ()
            with
                bonusAmount : Numeric 2
                currency    : Text
            controller provider
            do
                    now <- getTime

                    let transactionCode = Text.intercalate "_" [show customer, show now, show bonusAmount]

                    existsAssetDesc <- snd <$> Utils.checkAssetDesc [provider] currency

                    -- TODO: Handle failing currency
                    Action.when existsAssetDesc do
                        depositCid <- createBonusDeposit provider (customerKey this) currency bonusAmount
                        Action.void $ exerciseByKey @GamblingAccount.Account (customerKey this) $ GamblingAccount.BonusDeposit with depositCid, transactionId = Text.sha256 transactionCode


        nonconsuming choice DelegateBetExecutionSign : Either GamblingModel.ActionFailureCid (SettlementModel.ExecutedBetsCid)
          with
            executedBetsCid : SettlementModel.ExecutedBetsCid
          controller provider
          do
            SettlementModel.ExecutedBets{backBetPlacementId, layBetPlacementId} <- fetch executedBetsCid
            let executionId = Text.intercalate "_" [backBetPlacementId, layBetPlacementId]

            try do
              Right <$> exercise executedBetsCid
                SettlementModel.SignExecution with signer = customer
            catch AssertionFailed msg ->
                    Left <$> createOrLookup GamblingModel.ActionFailure with
                      operator, provider, customer, action = GamblingModel.Settlement
                      actionId = executionId, reason = msg


        nonconsuming choice SettleExecutedBets : Either GamblingModel.ActionFailureCid ()
          with
            executedBetsCid : SettlementModel.ExecutedBetsCid
          controller provider
          do
            SettlementModel.ExecutedBets{
                backBetPlacementId, layBetPlacementId, executedOdd
            } <- fetch executedBetsCid
            let executionId = Text.intercalate "_" [backBetPlacementId, layBetPlacementId]

            try do
              settledForParties <- exercise executedBetsCid SettlementModel.SettleBets
              let betPlacementIdList = map Tuple.snd3 settledForParties

              Right <$> Foldable.mapA_ (\ (winningsDepositCidList, betPlacementId, customer) -> do
                  -- | betPlacementIdList will always contain two elements, one for each side of the bets
                  let (counterBetPlacementId::_) = filter (betPlacementId /=) betPlacementIdList
                  exerciseByKey @Service (operator, provider, customer) $
                      CloseBet with betPlacementId, counterBetPlacementId, winningsDepositCidList, executedOdd
                ) settledForParties

            catch AssertionFailed msg ->
                    Left <$> createOrLookup GamblingModel.ActionFailure with
                      operator, provider, customer, action = GamblingModel.Settlement
                      actionId = executionId, reason = msg


        nonconsuming choice DelegateBetExecutionSignCancelled : Either GamblingModel.ActionFailureCid ()
          with
            executedBetsCid : SettlementModel.ExecutedBetsCid
          controller provider
          do
            SettlementModel.ExecutedBets{backBetPlacementId, layBetPlacementId} <-
              fetch executedBetsCid

            let executionId = Text.intercalate "_" [backBetPlacementId, layBetPlacementId]

            try do
              executedBetsCid <- exercise executedBetsCid
                SettlementModel.SignCancelExecution with signer = customer
              SettlementModel.ExecutedBets{isCancelled} <- fetch executedBetsCid

              Right <$> Action.when isCancelled (exercise executedBetsCid SettlementModel.ArchiveExecution)

            catch AssertionFailed msg ->
                    Left <$> createOrLookup GamblingModel.ActionFailure with
                      operator, provider, customer, action = GamblingModel.Settlement
                      actionId = executionId, reason = msg


        nonconsuming choice CloseBet : Either GamblingModel.ActionFailureCid (ContractId BetModel.BetHistory)
          with
            betPlacementId : Text
            counterBetPlacementId : Text
            winningsDepositCidList : [ContractId Asset.AssetDeposit]
            executedOdd : OddType
          controller provider
          do
            let betPlacementKey = (operator, provider, customer, betPlacementId)
            try do

              BetModel.BetPlacement{
                promotion, inBulk, details
              } <- snd <$> fetchByKey @BetModel.BetPlacement betPlacementKey

              globalBetFee <- (.betFee) . snd <$>
                fetchByKey @GamblingModel.GlobalGamblingConfiguration (operator, provider)

              let
                discountedFee = (globalBetFee -) . (globalBetFee *) . MarketingModel.getFeeDiscount . (._4)
                feePercentage = Optional.optional globalBetFee discountedFee promotion
                winningsPercentage = 1.0 - feePercentage

              (netWinningsDepositCidList, totalTransacted, totalOffered) <-
                {- Starts fold with last element and on accumulation appends always to beginning of list
                    (ex: [1,2,3,4,5] => fold start at 5, each iteration appends to beginning of new list
                        1::2::3::4::5::[] = [1,2,3,4,5] again. ) -}
                Action.foldrA (\ depositCid (netWinningsCidList, accumOffered, accumTransacted) -> do
                    Asset.AssetDeposit{asset} <- fetch depositCid

                    let
                      winningWithFee = asset.quantity * winningsPercentage
                      offeredAmount = asset.quantity - winningWithFee

                    netWinningCid::deductedWinningCids <- exercise depositCid
                      Asset.AssetDeposit_Split with quantities = [winningWithFee]
                    Foldable.mapA_ archive deductedWinningCids

                    return (
                        netWinningCid::netWinningsCidList,
                        accumTransacted + asset.quantity,
                        accumOffered + offeredAmount)
                ) ([], 0.0, 0.0) winningsDepositCidList

              bonusBetAmount <- (._2.bonusBetAmount) <$> fetchByKey @BetModel.BetPlacement (operator, provider, customer, betPlacementId)
              exerciseByKey @GamblingAccount.Account (customerKey this)
                GamblingAccount.UpdateBetWinnings with
                    betPlacementId
                    winningsDepositCidList = netWinningsDepositCidList
                    bonusBetAmount
                    executedOdd

              optional
                (return ())
                (\ (_, _, _, action, promotionId) -> do
                  let promotionUsageKey = (operator, provider, customer, action, promotionId)
                  Action.void $ exerciseByKey @MarketingModel.PromotionUsageHistory promotionUsageKey
                    MarketingModel.UpdateUsage with
                      usage = Map.fromList [(betPlacementId, (Numeric.castAndRound totalTransacted, Numeric.castAndRound totalOffered))]
                ) promotion

              optional
                (return ())
                (\ bulkBetPlacementId -> do
                  let bulkBetKey = (operator, provider, customer, bulkBetPlacementId)
                  Action.void $ exerciseByKey @BetModel.BulkBetPlacement bulkBetKey
                    BetModel.Bulk_CloseBet with betPlacementId
                ) inBulk

              eventTitle <- optional
                (assertFail $ "Event has no title in the default locale (" <> show Utils.EN_US <> ")")
                pure $ Map.lookup (show Utils.EN_US) details.eventTitle

              create GamblingModel.ActionSuccess with
                  operator, provider, customer, actionId = betPlacementId
                  action = GamblingModel.Settlement
                  reason = "Your bet on " <> eventTitle <> " has been settled"

              now <- getTime
              stake <- Utils.rightOrFail (BetModel.getStake details.side)

              let
                sendGridBetDetails = SendgridIntegration.BetDetails with
                  ticketId = details.betPlacementId
                  eventTitle
                  dateTime = now
                  side = (BetModel.getSide details.side)
                  stake = stake
                  oddValue = show (BetModel.fromSide details.side).odd

              Utils.createSendGridNotification (operator, provider, customer)
                SendgridIntegration.BetSettled {
                    details = sendGridBetDetails
                  }

              Right <$> exerciseByKey @BetModel.BetPlacement betPlacementKey
                if Foldable.null winningsDepositCidList
                  then BetModel.CloseBet with counterBetPlacementId, won = False, winnings = None
                  else BetModel.CloseBet with
                        counterBetPlacementId
                        won = True
                        winnings = Some BetModel.Winnings with
                            grossAmount = Numeric.castAndRound totalTransacted
                            netAmount = totalTransacted `Numeric.mul` winningsPercentage
                            appliedFee = feePercentage

            catch AssertionFailed msg ->
                    Left <$> createOrLookup GamblingModel.ActionFailure with
                      operator, provider, customer, action = GamblingModel.Settlement
                      actionId = betPlacementId, reason = msg

        nonconsuming choice ReconcileBetPlacement : ()
            with
                betPlacementCid : ContractId (BetModel.BetPlacement)
            controller provider
            do
                -- Choice used to reject bets when we get a order rejected from exberry
                reconcileBetPlacement betPlacementCid


        nonconsuming choice RemoveKYC : ()
            controller provider
            do
                lookupByKey @IdentityModel.GamblerIdentity (customerKey this) >>=
                  (`Utils.whenNone` (assertFail "User is not verified"))

                exerciseByKey @IdentityModel.GamblerIdentity (customerKey this) IdentityModel.ArchiveIdentity

        nonconsuming choice RejectFinalizeBetPlacement : ()
            with
                betPlacementKey : BetPlacementKey
            controller provider
            do
                (_, BetModel.BetPlacementFinalizeRequest{
                        operator, provider, customer, betIssuanceKey, details, betDepositCid, inBulk, optBonusAmount
                    }) <- Utils.fetchAndArchiveByKey betPlacementKey

                -- if bet is single reject promotion if exists
                Utils.unlessSome inBulk do
                  Utils.whenContractExists @PendingPromotionApplication (operator, provider, customer, details.betPlacementId)
                    (Action.void . (`exercise` RejectPromotion))

                Utils.whenContractExists @BetModel.BetPlacementRequestFlag (operator, provider, customer)
                  (Action.void . (`exercise` BetModel.ArchiveBetFlag))

                Asset.AssetDeposit{account} <- fetch betDepositCid

                Action.void $ exerciseByKey @GamblingAccount.Account (customerKey this)
                  GamblingAccount.UnlockBet with
                      betPlacementId = details.betPlacementId
                      optBonusBetAmount = optBonusAmount

                betIssuance <- snd <$> fetchByKey @IssuanceModel.Issuance betIssuanceKey

                Utils.reduceIssuanceAndArchiveIfZeroQuantity (operator, provider, account.owner) betDepositCid
                  betIssuance.issuanceId account.id


        nonconsuming choice RejectFinalizeBulkBetPlacement : ()
            with
                bulkFinRequestCid : ContractId (BetModel.BulkBetPlacementFinalizeRequest)
            controller provider
            do
                BetModel.BulkBetPlacementFinalizeRequest{
                        operator, provider, customer, betPlacementFinReqKeyList, bulkBetPlacementId
                    } <- FinanceUtils.fetchAndArchive bulkFinRequestCid

                Utils.whenContractExists @PendingPromotionApplication (operator, provider, customer, bulkBetPlacementId)
                  (Action.void . (`exercise` RejectPromotion))

                Utils.whenContractExists @BetModel.BulkBetPlacementRequestFlag (operator, provider, customer)
                  (Action.void . (`exercise` BetModel.ArchiveBulkBetFlag))

                Foldable.mapA_ (\ betPlacementKey -> do
                        exercise self RejectFinalizeBetPlacement with betPlacementKey
                    ) betPlacementFinReqKeyList


        nonconsuming choice Archive_NewOrderRequest : ()
          with
            newOrderRequestCid : ContractId IntegrationExberry.NewOrderRequest
          controller provider
          do archive newOrderRequestCid


        nonconsuming choice Archive_NewOrderFailure : ()
          with
            newOrderFailureCid : ContractId IntegrationExberry.NewOrderFailure
          controller provider
          do archive newOrderFailureCid


        nonconsuming choice Archive_NewOrderSuccess : ()
          with
            newOrderSuccessCid : ContractId IntegrationExberry.NewOrderSuccess
          controller provider
          do archive newOrderSuccessCid


        nonconsuming choice Archive_Instrument : ()
          with
            instrumentCid : ContractId IntegrationExberry.Instrument
          controller provider
          do archive instrumentCid


        nonconsuming choice Archive_ExecutionReport : ()
          with
            executionReportCid : ContractId IntegrationExberry.ExecutionReport
          controller provider
          do archive executionReportCid


        nonconsuming choice Archive_CancelOrderSuccess : ()
          with
            cancelOrderSuccessCid : ContractId IntegrationExberry.CancelOrderSuccess
          controller provider
          do archive cancelOrderSuccessCid


        nonconsuming choice Archive_CancelOrderFailure : ()
          with
            cancelOrderFailureCid : ContractId IntegrationExberry.CancelOrderFailure
          controller provider
          do archive cancelOrderFailureCid


        nonconsuming choice Archive_CancelOrderRequest : ()
          with
            cancelOrderRequestCid : ContractId IntegrationExberry.CancelOrderRequest
          controller provider
          do archive cancelOrderRequestCid


        nonconsuming choice Fail_CreateInstrumentRequest : ContractId IntegrationExberry.FailedInstrumentRequest
          with
            createInstrumentRequestCid : ContractId IntegrationExberry.CreateInstrumentRequest
            message_ : Text
            name : Text
            code : Text
          controller provider
          do
            exercise createInstrumentRequestCid
              IntegrationExberry.CreateInstrumentRequest_Failure with message = message_, name, code


        nonconsuming choice Succeed_CreateInstrumentRequest : ContractId IntegrationExberry.Instrument
          with
            createInstrumentRequestCid : ContractId IntegrationExberry.CreateInstrumentRequest
            instrumentId : Text
          controller provider
          do
            exercise createInstrumentRequestCid
              IntegrationExberry.CreateInstrumentRequest_Success with instrumentId


        nonconsuming choice Archive_FailedInstrumentRequest : ()
          with
            failedInstrumentRequestCid : ContractId IntegrationExberry.FailedInstrumentRequest
          controller provider
          do archive failedInstrumentRequestCid


        nonconsuming choice Archive_FailedUpdateInstrumentRequest : ()
          with
            failedUpdateInstrumentRequestCid : ContractId IntegrationExberry.FailedUpdateInstrumentRequest
          controller provider
          do archive failedUpdateInstrumentRequestCid


        nonconsuming choice Archive_MassCancelRequest : ()
          with
            massCancelRequestCid : ContractId IntegrationExberry.MassCancelRequest
          controller provider
          do archive massCancelRequestCid


        nonconsuming choice Fail_UpdateInstrumentRequest : ContractId IntegrationExberry.FailedUpdateInstrumentRequest
          with
            updateInstrumentRequestCid : ContractId IntegrationExberry.UpdateInstrumentRequest
            message_ : Text
            name : Text
            code : Text
          controller provider
          do
            exercise updateInstrumentRequestCid
              IntegrationExberry.UpdateInstrumentRequest_Failure with message = message_, name, code


        nonconsuming choice Succeed_UpdateInstrumentRequest : ContractId IntegrationExberry.Instrument
          with
            updateInstrumentRequestCid : ContractId IntegrationExberry.UpdateInstrumentRequest
            instrumentId : Text
          controller provider
          do
            exercise updateInstrumentRequestCid IntegrationExberry.UpdateInstrumentRequest_Success with instrumentId


        nonconsuming choice Flip_DisableListing : ContractId MarketplaceListingService.DisableListingRequest
          with
            disableListingRequestCid : ContractId MarketplaceListingService.DisableListingRequest
          controller provider
          do
            exercise disableListingRequestCid MarketplaceListingService.FlipDisableListing


        nonconsuming choice Archive_DisableListingRequest : ()
          with
            disableListingRequestCid : ContractId MarketplaceListingService.DisableListingRequest
          controller provider
          do
            archive disableListingRequestCid


        nonconsuming choice Flip_CancelOrder : ContractId TradingService.CancelOrderRequest
          with
            cancelOrderRequestCid : ContractId TradingService.CancelOrderRequest
          controller provider
          do
            exercise cancelOrderRequestCid TradingService.FlipCancelOrder


        nonconsuming choice Reject_CreateOrderRequest : ContractId TradingModel.Order
          with
            createOrderRequestCid : ContractId TradingService.CreateOrderRequest
            errorCode : Int
            errorMessage : Text
          controller provider
          do
            exercise createOrderRequestCid
              TradingService.RejectRequest with errorCode, errorMessage


        nonconsuming choice Flip_CreateOrderRequest : ContractId TradingService.CreateOrderRequest
          with
            createOrderRequestCid : ContractId TradingService.CreateOrderRequest
          controller provider
          do
            exercise createOrderRequestCid TradingService.FlipCreateOrder


        nonconsuming choice Acknowledge_CreateOrderRequest : ContractId TradingModel.Order
          with
            createOrderRequestCid : ContractId TradingService.CreateOrderRequest
            providerOrderId : Text
          controller provider
          do
            exercise createOrderRequestCid TradingService.AcknowledgeRequest with providerOrderId


        nonconsuming choice Flip_CreateListingRequest : ContractId MarketplaceListingService.CreateListingRequest
          with
            createListingRequestCid : ContractId MarketplaceListingService.CreateListingRequest
          controller provider
          do
            exercise createListingRequestCid MarketplaceListingService.FlipCreateListing


        nonconsuming choice Succeed_CreateListingRequest : ContractId MarketplaceListingModel.Listing
          with
            createListingRequestCid : ContractId MarketplaceListingService.CreateListingRequest
            providerId : Text
          controller provider
          do
            exercise createListingRequestCid
              MarketplaceListingService.ListingRequestSuccess with providerId


        nonconsuming choice Fail_CreateListingRequest : ContractId MarketplaceListingService.FailedListingCreation
          with
            createListingRequestCid : ContractId MarketplaceListingService.CreateListingRequest
            message_ : Text
            name : Text
            code : Text
          controller provider
          do
            exercise createListingRequestCid
              MarketplaceListingService.ListingRequestFailure with
                message = message_, name, code


        nonconsuming choice Archive_EventInstrumentUpdateOnHold : ()
          with
            eventInstrumentUpdateOnHoldCid : ContractId IntegrationEvents.EventInstrumentUpdateOnHold
          controller provider
          do
            archive eventInstrumentUpdateOnHoldCid


        nonconsuming choice RetryUpdate_EventInstrumentUpdateOnHold
          : Either IntegrationEvents.EventInstrumentUpdateOnHold (ContractId IntegrationEvents.EventInstrumentUpdateOnHold)
          with
            eventInstrumentUpdateOnHoldCid : ContractId IntegrationEvents.EventInstrumentUpdateOnHold
          controller provider
          do
            exercise eventInstrumentUpdateOnHoldCid IntegrationEvents.RetryUpdate with executingParty = provider


        nonconsuming choice RetrieveArchive_EventInstrument : IntegrationEvents.EventInstrument
          with
            eventInstrumentCid : ContractId IntegrationEvents.EventInstrument
          controller provider
          do
            exercise eventInstrumentCid IntegrationEvents.RetrieveArchive_Original with executingParty = provider


        nonconsuming choice RetrieveArchive_EventInstrumentUpdate : IntegrationEvents.EventInstrumentUpdate
          with
            eventInstrumentUpdateCid : ContractId IntegrationEvents.EventInstrumentUpdate
          controller provider
          do
            exercise eventInstrumentUpdateCid IntegrationEvents.RetrieveArchive_Update with executingParty = provider


        nonconsuming choice FlipProcessedFlag_CancelledBetPlacement : ContractId BetModel.CancelledBetPlacement
          with
            cancelledBetPlacementCid : ContractId BetModel.CancelledBetPlacement
          controller provider
          do
            exercise cancelledBetPlacementCid BetModel.FlipProcessedFlag


        interface instance IBetCancellationService.IBetCancellationService for Service where
          view = IBetCancellationService.VService with
            operator, provider, customer

          cancelBet iService IBetCancellationService.CancelBet{betPlacementCid} =
            let
              gamblingService = fromInterfaceContractId @Service iService
              betPlacementCidFromInterface =
                fromInterfaceContractId @BetModel.BetPlacement betPlacementCid
            in Action.void
              $ exercise gamblingService CancelBet with betPlacementCid = betPlacementCidFromInterface

          finalizeBetPlacementCancel iService arg =
            let
              IBetCancellationService.FinalizeBetPlacementCancel{
                finalizeBetPlacementCancelCid, betPlacementCid, errorMsg, eventCancelled
              } = arg
              gamblingService = fromInterfaceContractId @Service iService
              betPlacementCidFromInterface =
                fromInterfaceContractId @BetModel.BetPlacement <$> betPlacementCid
              finalizeBetPlacementCancelCidFromInterface =
                fromInterfaceContractId @BetModel.FinalizeBetPlacementCancelRequest finalizeBetPlacementCancelCid
            in Action.void
              $ exercise gamblingService FinalizeBetPlacementCancel with
                  finalizeBetPlacementCancelCid = finalizeBetPlacementCancelCidFromInterface
                  betPlacementCid = betPlacementCidFromInterface
                  errorMsg, eventCancelled

        interface instance IBetSettlementService.IBetSettlementService for Service where
          view = IBetSettlementService.VService with
            operator, provider, customer

          delegateBetExecutionSign iService IBetSettlementService.DelegateBetExecutionSign{executedBetsCid} =
            let
              gamblingService = fromInterfaceContractId @Service iService
              executedBetsCidFromInterface =
                fromInterfaceContractId @SettlementModel.ExecutedBets executedBetsCid
            in either
              (Left . toInterfaceContractId @IGamblingModel.IActionFailure)
              (Right . toInterfaceContractId @IBetSettlementModel.IExecutedBets)
              <$> exercise gamblingService DelegateBetExecutionSign with executedBetsCid = executedBetsCidFromInterface

          delegateBetExecutionSignCancelled iService IBetSettlementService.DelegateBetExecutionSignCancelled{executedBetsCid} =
            let
              gamblingService = fromInterfaceContractId @Service iService
              executedBetsCidFromInterface =
                fromInterfaceContractId @SettlementModel.ExecutedBets executedBetsCid
            in either
              (Left . toInterfaceContractId @IGamblingModel.IActionFailure)
              (Right)
              <$> exercise gamblingService DelegateBetExecutionSignCancelled with executedBetsCid = executedBetsCidFromInterface

          settleExecutedBets iService IBetSettlementService.SettleExecutedBets{executedBetsCid} =
            let
              gamblingService = fromInterfaceContractId @Service iService
              executedBetsCidFromInterface =
                fromInterfaceContractId @SettlementModel.ExecutedBets executedBetsCid
            in either
              (Left . toInterfaceContractId @IGamblingModel.IActionFailure)
              (Right)
              <$> exercise gamblingService SettleExecutedBets with executedBetsCid = executedBetsCidFromInterface

          archiveExecutedbets _ executedBetsCid = do
            let
              executedBetsCidFromInterface =
                fromInterfaceContractId @SettlementModel.ExecutedBets executedBetsCid
            SettlementModel.ExecutedBets{executionId} <- fetch executedBetsCidFromInterface
            try do
              Right <$> exercise executedBetsCidFromInterface
                SettlementModel.ArchiveExecution 
            catch AssertionFailed msg -> do
                  actionFailureCid <- createOrLookup GamblingModel.ActionFailure with
                    operator, provider, customer
                    action = GamblingModel.Settlement, actionId = executionId, reason = msg
                  pure (Left (toInterfaceContractId actionFailureCid))


template Offer
    with
        operator    : Party
        provider    : Party
        customer    : Party
        observers   : Set Party
    where
        signatory operator, provider
        observer customer

        key (operator, provider, customer) : (Party, Party, Party)
        maintainer key._1

       -- controller customer can

        postconsuming choice Accept : (ContractId Service, ContractId Custody.Service, ContractId IssuanceService.Service)
            controller customer
            do
                    now <- getTime

                    onboardGambler operator provider customer now >>=
                        (\ (gamblingService, custodyService, issuanceService) ->
                            (exercise gamblingService GivePermissions with
                                newPermissions = [GamblingModel.Bets])
                                >>= (return . (, custodyService, issuanceService)))


        choice Decline : ()
            controller customer
            do pure ()

        --controller provider can

        choice Withdraw : ()
            controller provider
            do pure ()


template Request
    with
        customer : Party
        provider : Party
    where
        signatory customer
        observer provider

        key (customer, provider) : (Party, Party)
        maintainer key._1

        choice Cancel : ()
            controller customer
            do pure ()

        choice Reject : ()
            controller provider
            do pure ()


        choice Approve : (ContractId Service, ContractId Custody.Service, ContractId IssuanceService.Service)
            with
                operator : Party
            controller operator, provider
                do
                    now <- getTime
                    onboardGambler operator provider customer now >>=
                        (\ (gamblingService, custodyService, issuanceService) ->
                            (exercise gamblingService GivePermissions with
                                newPermissions = [GamblingModel.Bets])
                                >>= (return . (, custodyService, issuanceService)))


type BlockedServiceCid = ContractId BlockedService

template BlockedService
  with
    service : Service
    reason  : Text
    blockedAt : Time
  where
    signatory service.operator, service.provider, service.customer

    key (service.operator, service.provider, service.customer) : (Party, Party, Party)
    maintainer key._1

    choice Reinstate : ContractId Service
      controller service.provider
        do
            create service

    interface instance IBetCancellationService.IBetCancellationService for BlockedService where
      view = IBetCancellationService.VService with
        operator = service.operator
        provider = service.provider
        customer = service.customer

      cancelBet _ IBetCancellationService.CancelBet{betPlacementCid} = do
        let
          betPlacementCidFromInterface =
            fromInterfaceContractId @BetModel.BetPlacement betPlacementCid

        tempServiceCid <- create service
        exercise tempServiceCid CancelBet with betPlacementCid = betPlacementCidFromInterface
        archive tempServiceCid

      finalizeBetPlacementCancel _ arg = do
        let
          IBetCancellationService.FinalizeBetPlacementCancel{
            finalizeBetPlacementCancelCid, betPlacementCid, errorMsg, eventCancelled
          } = arg
          betPlacementCidFromInterface =
            fromInterfaceContractId @BetModel.BetPlacement <$> betPlacementCid
          finalizeBetPlacementCancelCidFromInterface =
            fromInterfaceContractId @BetModel.FinalizeBetPlacementCancelRequest finalizeBetPlacementCancelCid

        tempServiceCid <- create service

        exercise tempServiceCid FinalizeBetPlacementCancel with
          finalizeBetPlacementCancelCid = finalizeBetPlacementCancelCidFromInterface
          betPlacementCid = betPlacementCidFromInterface
          errorMsg, eventCancelled

        archive tempServiceCid

    interface instance IBetSettlementService.IBetSettlementService for BlockedService where
      view = IBetSettlementService.VService with
        operator = service.operator
        provider = service.provider
        customer = service.customer

      delegateBetExecutionSign _ IBetSettlementService.DelegateBetExecutionSign{executedBetsCid} = do
        let
          executedBetsCidFromInterface =
            fromInterfaceContractId @SettlementModel.ExecutedBets executedBetsCid
        tempServiceCid <- create service
        res <- exercise tempServiceCid DelegateBetExecutionSign with executedBetsCid = executedBetsCidFromInterface
        archive tempServiceCid
        pure $ either
          (Left . toInterfaceContractId @IGamblingModel.IActionFailure)
          (Right . toInterfaceContractId @IBetSettlementModel.IExecutedBets)
          res

      delegateBetExecutionSignCancelled _ IBetSettlementService.DelegateBetExecutionSignCancelled{executedBetsCid} = do
        let
          executedBetsCidFromInterface =
            fromInterfaceContractId @SettlementModel.ExecutedBets executedBetsCid
        tempServiceCid <- create service
        res <- exercise tempServiceCid DelegateBetExecutionSignCancelled with executedBetsCid = executedBetsCidFromInterface
        archive tempServiceCid
        pure $ either
          (Left . toInterfaceContractId @IGamblingModel.IActionFailure)
          (Right)
          res

      settleExecutedBets _ IBetSettlementService.SettleExecutedBets{executedBetsCid} = do
        let
          executedBetsCidFromInterface =
            fromInterfaceContractId @SettlementModel.ExecutedBets executedBetsCid
        tempServiceCid <- create service
        res <- exercise tempServiceCid SettleExecutedBets with executedBetsCid = executedBetsCidFromInterface
        archive tempServiceCid
        pure $ either
          (Left . toInterfaceContractId @IGamblingModel.IActionFailure)
          (Right)
          res

      archiveExecutedbets _ executedBetsCid = do
        let
          executedBetsCidFromInterface =
            fromInterfaceContractId @SettlementModel.ExecutedBets executedBetsCid
        SettlementModel.ExecutedBets{executionId} <- fetch executedBetsCidFromInterface
        try do
          Right <$> exercise executedBetsCidFromInterface
            SettlementModel.ArchiveExecution 
        catch AssertionFailed msg -> do
              actionFailureCid <- createOrLookup GamblingModel.ActionFailure with
                operator = service.operator, provider = service.provider, customer = service.customer
                action = GamblingModel.Settlement, actionId = executionId, reason = msg
              pure (Left (toInterfaceContractId actionFailureCid))

template PendingPromotionApplication
  with
    operator : Party
    provider : Party
    customer : Party
    -- v: Key of the promotion to be applied
    promotionKey : MarketingModel.PromotionKey
    -- v: ActionId of the transaction that triggered the promotion
    actionId : Text
    -- v: Total bonus or discount amount offered by the promotion (in cash)
    offeredAmount : Numeric 2
    currency : Text
  where
    signatory operator, provider, customer

    key (operator, provider, customer, actionId): (Party, Party, Party, Text)
    maintainer key._1

    choice ApplyPromotion : ContractId MarketingModel.PromotionWallet
      with
        transactionId       : Text
        transactedAmount    : Numeric 2
      controller provider
      do
        (_, promotion) <- fetchByKey @MarketingModel.Promotion promotionKey
        Action.when (MarketingModel.isBonusPromotion promotion) $ do
          -- | This choice is called in the context of error handled choices, no need to surround
          -- with try ... catch clause
          Utils.createSendGridNotification (operator, provider, customer)
            SendgridIntegration.Promotion {
              amount = offeredAmount
            }
          Action.void $ createBonusDeposit provider (customerKey this) currency offeredAmount
            >>= \ depositCid -> exerciseByKey @GamblingAccount.Account (customerKey this)
                GamblingAccount.BonusDeposit with depositCid, transactionId

        exerciseByKey @MarketingModel.PromotionWallet (customerKey this)
          MarketingModel.UsePromotion with promotionKey, actionId, offeredAmount, transactedAmount


    choice RejectPromotion : ()
        controller provider
        do pure ()


{- ------------------------------------------EXCEPTIONS------------------------------------------- -}
type BetPlacementKey    = (Party, Party, Party, Text)

exception BulkRequestException
  with
    actionFailure : GamblingModel.ActionFailure
  where
    message "Bulk request failed"

{- -------------------------------------------FUNCTIONS------------------------------------------- -}

customerKey : forall r. (HasField "operator" r Party, HasField "provider" r Party, HasField "customer" r Party)
  => r -> (Party, Party, Party)
customerKey record = (record.operator, record.provider, record.customer)

onboardGambler : Party -> Party -> Party -> Time -> Update (ContractId Service, ContractId Custody.Service, ContractId IssuanceService.Service)
onboardGambler operator provider customer createdAt = do
  GamblingModel.GlobalGamblingConfiguration{integrationParties, defaultOdds} <-
    snd <$> fetchByKey @GamblingModel.GlobalGamblingConfiguration (operator, provider)

  let account = Custody.createAccount provider customer

  custodyServiceCid <- createOrLookup Custody.Service with  ..
  issuanceServiceCid <- createOrLookup IssuanceService.Service with  ..
  _ <- createOrLookup TradingService.Service with  ..

  -- Default user preference for odd type is Decimal

  let oddsPreference = ("Odd", OddsModel.oddTypeToText defaultOdds)
      languagePreference = ("Language", show Utils.EN_US)
      preferences = Map.fromList [oddsPreference, languagePreference]
      favouriteMarkets = Set.empty
      transactionHistory = Map.empty
  create GamblingAccount.Account with
    assetDepositMain = None, assetDepositWithdraw = None, assetDepositBet = Map.empty, assetDepositBonus = None
    totalMainBalance = 0.0, totalWithdrawBalance = 0.0, totalBetBalance = 0.0, totalBonusBalance = 0.0, totalBonusBetBalance = 0.0, totalFees = 0.0
    preferences, favouriteMarkets, transactionHistory, operator, provider, customer, depositLimit = Map.empty, betHistory = Set.empty

  gamblingCid <- createOrLookup Service with operator, provider, customer, createdAt, permissions = Set.empty
  create MarketingModel.PromotionWallet with operator, provider, customer, promotionMap = Map.fromList []

  let Some quickBooks = Map.lookup "quickbooks" integrationParties

  GamblingModel.requestQuickbooksAccount quickBooks provider customer [] "Off-Gambyl" "Bank" "CashOnHand"
  GamblingModel.requestQuickbooksAccount quickBooks provider customer [] "Gambyl" "Bank" "CashOnHand"
  GamblingModel.requestQuickbooksAccount quickBooks provider customer [] "Gambyl Bonus" "Bank" "CashOnHand"

  return (gamblingCid, custodyServiceCid, issuanceServiceCid)


createBonusDeposit : Party -> (Party, Party, Party) -> Text -> Numeric 2 -> Update (ContractId Asset.AssetDeposit)
createBonusDeposit provider customerKey  currency bonusAmount = do
    (assetId, _) <- Utils.checkAssetDesc [provider] currency
    let
        asset = FinanceTypes.Asset with id = assetId, quantity = Numeric.castAndRound bonusAmount

    custodyServiceCid <- fst <$> fetchByKey @Custody.Service customerKey
    depositRequestCid <- exercise custodyServiceCid Custody.RequestDeposit with asset
    exercise custodyServiceCid Custody.Deposit with depositRequestCid


createFlag : Numeric 2 -> Numeric 2 -> Party -> Party -> Party -> Update ()
createFlag amount flaggedAmount operator provider customer = do
    Action.when (amount > flaggedAmount) do
        created <- getTime
        create GamblingAccount.Flag with amount, created, operator, provider, customer
        return ()

--Function to verify if the type of Promotion Action, and Type of Transaction is the same, in order to Apply the Promotion
isSameAction : GamblingModel.Actionable -> MarketingModel.PromotionAction -> Bool
isSameAction GamblingModel.Deposit      (MarketingModel.Deposit _)      = True
isSameAction GamblingModel.Withdrawal   (MarketingModel.Withdrawal _)   = True
isSameAction GamblingModel.BetPlacement (MarketingModel.Bet _)          = True
isSameAction _ _ = False



calculateAge : (Int,Date.Month,Int) -> (Int,Date.Month,Int) -> Int
calculateAge (d, m, y) (d2, m2, y2)
   | m > m2 = y - y2
   | m == m2 && d >= d2 = y - y2
   | otherwise = y - y2 - 1


getOutcomes : (DA.Internal.Record.HasField "outcome" b IntegrationEvents.Outcome) => [b] -> [IntegrationEvents.Outcome]
getOutcomes outcomeOddsList = map (.outcome) outcomeOddsList

processCancelBet : (Party, Party, Party) -> Text -> Text -> Bool -> Update (ContractId BetModel.FinalizeBetPlacementCancelRequest)
processCancelBet customerKey@(operator, provider, customer) betPlacementId reason isUserStarted = do

    BetModel.BetPlacement{
        orderId, betIssuanceKey, bonusBetAmount, details = BetModel.Details{eventKey}
    } <- snd <$> fetchByKey @BetModel.BetPlacement (operator, provider, customer, betPlacementId)

    (orderCid, TradingModel.Order{details = TradingModel.Details{id}}) <- fetchByKey @TradingModel.Order (provider, orderId)

    tradingServiceCid <- fst <$> fetchByKey @TradingService.Service customerKey

    lookupByKey @TradingService.CancelOrderRequest (provider, id) >>=
      (`Utils.whenNone` (Action.void $ exercise tradingServiceCid TradingService.RequestCancelOrder with orderCid))

    -- Check if there are any BetPlacementCancelRequest or
    -- FinalizeBetPlacementCancelRequest contracts related to this bet
    checkCancelBetRequests betPlacementId (operator, provider, customer, ) "FinalizeBetPlacementCancelRequest"

    createOrLookup BetModel.FinalizeBetPlacementCancelRequest with
        operator, provider, customer, cancelOrderRequestKey = (provider, id), orderId = id
        betIssuanceKey, betPlacementId, bonusBetAmount, reason, eventLabel = eventKey._3, isUserStarted

-- TODO: apply promotions and fees on bet placement
{-
Function to calculate the final amount of a deposit/withdraw action and the corresponding fee or bonus amount, with the given promotion applied.
Returns AssertionFailed exception if the given promotion on the request is not applicable to that transaction,
    in which case the approve transaction choice that calls this function should result in an ActionFailure,
If the function is successful creates a PendingPromotionApplication contract for the transaction,
    that will be consumed on the Finalize choice for the transaction - this created contract will be the contract that allows
    the actual application of the promotion,
    and returns a (Decimal, Decimal) tuple containing the final amount calculated for that transaction on the first side and the applied fee on the second side of the tuple. -}
calculateTransactionAmount : (Party, Party, Party) -> GamblingModel.Actionable -> Decimal -> Numeric 2 -> Text -> Text -> MarketingModel.PromotionKey -> Update Decimal
calculateTransactionAmount customerKey@(operator, provider, customer) actionType baseFee requestedAmount currency actionId promotionKey = do

    (_, promotionWallet) <- fetchByKey @MarketingModel.PromotionWallet customerKey
    <EMAIL>{
        config = MarketingModel.PromotionConfig{
            promoType,
            action,
            endDate,
            limitedPromotion,
            minAmount
        },
        status,
        startDate
    } <- snd <$> fetchByKey @MarketingModel.Promotion promotionKey

    account <- snd <$> fetchByKey @GamblingAccount.Account customerKey
    gamblingService <- snd <$> fetchByKey @Service customerKey
    today <- Date.toDateUTC <$> getTime

    let
        transactionHistory = Optional.fromOptional [] $ Map.lookup actionType account.transactionHistory
        promotionUsage = Optional.optional 0 ((.usageCounter))
                $ Map.lookup (key promotion) promotionWallet.promotionMap
        signUpDate = Date.toDateUTC gamblingService.createdAt
        isFirstTime = MarketingModel.isFirstTimeDeposit promoType

    -- 'checkPromoWithSignupDays' function checks if a user qualify for a chosen promotion accordingly to signUpdate, which must comply with Max number of Days allowed for user to qualify for Promotion
    isInSignupDays <- MarketingModel.checkPromoWithSignupDays promoType signUpDate
    assertMsg "Applied promotion was only valid within a period of the sign up" isInSignupDays

    -- 'isSameAction' function checks if the action for which the promotion applies is equivalent to type of Transaction requested (e.g. PromoAction (Deposit) applicable for a Deposit Transaction)
    assertMsg "Promotion action mismatch with requested transaction type" $ isSameAction actionType action
    assertMsg "Promotion is not Active" $ status == MarketingModel.Active
    -- If minAmount not present continue, if it is the request amount should be greater or equal to it
    assertMsg "Requested Amount for transaction is under promotion minimum" $ optional True ((Numeric.castAndRound requestedAmount) >=) minAmount
    -- If endDate not present continue, if it is current date should be below or equal to it
    assertMsg "Promotion has ended" $ optional True (today <=) endDate
        -- If Starting date smaller than current Date
    assertMsg ("You can only start using this Promotion from " <> show (Date.toDateUTC startDate) ) $ today >= Date.toDateUTC startDate
    -- If limitedPromotion not present continue, if it is it's usage should be under it
    assertMsg ("Promotion has been used too many times (" <> show promotionUsage <> ")") $ optional True (> promotionUsage) limitedPromotion
    -- a first time promotion should only be applied to the first time that transaction type is being used by the customer, meaning the transaction history for the given transaction type should be empty
    assertMsg ("First time promotion not applicable to transaction " <> show actionId) $ not isFirstTime || Foldable.null transactionHistory
    -- a reload promotion means that should not be applied to the first transaction of each type (e.g. If it is the customer's first deposit/withdraw and given a Reload promotion, then it should not be applied)
    assertMsg ("Reload promotion not applicable to transaction " <> show actionId) $ promoType /= MarketingModel.Reload || not (Foldable.null transactionHistory)

    -- all conditions for the promotion application have been met, check whether it is a bonus or discount promotion then do calculations and create pending promotion application contract
    let
        createPendingPromotionApplication offeredAmount =
            create PendingPromotionApplication with operator, provider, customer, actionId, promotionKey, currency, offeredAmount
    if  MarketingModel.isBonusPromotion promotion then do
        -- calculate amount of bonus for the transaction based on the promotion configuration and the requested amount
        let bonusAmount = MarketingModel.getBonusAmount promotion requestedAmount

        createPendingPromotionApplication bonusAmount
        return baseFee
    else do
        -- calculate the new fee to apply to the transaction based on the promotion configuration and the base fee, and then calculate the discount amount
        let
            promotionalFee = baseFee * (1.0 - MarketingModel.getFeeDiscount promotion.config.action)

            offeredAmount calc = abs $
                calculateAmount (`calc` baseFee) - calculateAmount (`calc` promotionalFee)
                where
                    calculateAmount : (Numeric 10 -> Numeric 10) -> Numeric 2
                    calculateAmount calculation = requestedAmount `Numeric.mul` (calculation 1.0)

            discountedAmount = case action of
                    MarketingModel.Deposit _    -> offeredAmount (+)
                    _                           -> offeredAmount (-)

        createPendingPromotionApplication discountedAmount
        return promotionalFee


checkApplyPromotion : MarketingModel.PromotionKey -> GamblingModel.Actionable -> Text -> (Party, Party, Party) -> Numeric 2 -> Numeric 2 -> Update ()
checkApplyPromotion promotionKey actionType  actionId    customerKey amountForMin    _amountForMax    = do

    <EMAIL>{
        config = MarketingModel.PromotionConfig{
            promoType,
            action,
            endDate,
            limitedPromotion,
            maxAmount,
            minAmount
        },
        status,
        startDate
    } <- snd <$> fetchByKey @MarketingModel.Promotion promotionKey

    gamblingService <- snd <$> fetchByKey @Service customerKey

    today  <- Date.toDateUTC <$> getTime

    let
        signUpDate = Date.toDateUTC gamblingService.createdAt
        isLessThanMaxAmount = MarketingModel.checkPromoMaxAmount action amountForMin maxAmount

    assertMsg "Promotion is not Active" $ status == MarketingModel.Active

    -- 'checkPromoWithSignupDays' function checks if a user qualify for a chosen promotion accordingly to signUpdate, which must comply with Max number of Days allowed for user to qualify for Promotion
    isInSignupDays <- MarketingModel.checkPromoWithSignupDays promoType signUpDate
    assertMsg "Applied promotion was only valid within a period of the sign up" isInSignupDays

    -- 'isSameAction' function checks if the action for which the promotion applies is equivalent to type of Transaction requested (e.g. PromoAction (Deposit) applicable for a Deposit Transaction)
    assertMsg "Promotion action mismatch with requested transaction type" $ isSameAction actionType action

    -- If minAmount not present continue, if it is the request amount should be greater or equal to it
    assertMsg ("Requested Amount for " <> show actionType <> " is under promotion minimum") $ optional True (amountForMin >=) minAmount

    -- If endDate not present continue, if it is current date should be below or equal to it
    assertMsg "Promotion has ended" $ optional True (today <=) endDate

    -- If Starting date smaller than current Date
    assertMsg ("You can only start using this Promotion from " <> show (Date.toDateUTC startDate) ) $ today >= Date.toDateUTC startDate

    promotionWallet <- snd <$> fetchByKey @MarketingModel.PromotionWallet customerKey
    let promotionUsage = Optional.optional 0 ((.usageCounter)) $ Map.lookup (key promotion) promotionWallet.promotionMap
    -- If limitedPromotion not present continue, if it is it's usage should be under it
    assertMsg ("Promotion has been used too many times (" <> show promotionUsage <> ")") $ optional True (> promotionUsage) limitedPromotion

    assertMsg ("Requested amount for bet placement exceeds the maximum amount valid for the applied promotion (" <> show maxAmount <> ") check T&C`s ") $ isLessThanMaxAmount

    account <- snd <$> fetchByKey @GamblingAccount.Account customerKey

    case action of
        MarketingModel.Bet _ -> do checkApplyBetPromotion promotion account actionId
        _       -> do assertFail "Not Implemented"


checkApplyBetPromotion : MarketingModel.Promotion -> GamblingAccount.Account -> Text -> Update ()
checkApplyBetPromotion MarketingModel.Promotion{config = MarketingModel.PromotionConfig{promoType}} account actionId = do

    {- a first time promotion should only be applied to the first time that action is being used by the customer,
    meaning the bet history should be empty -}

    let isFirstTime = MarketingModel.isFirstTimeDeposit promoType

    assertMsg ("First time promotion not applicable to bet " <> show actionId) $ not isFirstTime || Set.size account.betHistory == 1
    {- a reload promotion means that should not be applied to the first bet of each type
    (e.g. If it is the customer's first bet and given a Reload promotion, then it should not be applied) -}
    assertMsg ("Reload promotion not applicable to bet " <> show actionId) $ promoType /= MarketingModel.Reload || Set.size account.betHistory > 1

    return ()

finalizeBetCancel : ContractId BetModel.FinalizeBetPlacementCancelRequest -> Update (ContractId BetModel.CancelledBetPlacement)
finalizeBetCancel finalizeBetPlacementCancelCid = do

    BetModel.FinalizeBetPlacementCancelRequest{
        operator, provider, customer, betIssuanceKey, cancelOrderRequestKey,
        betPlacementId, bonusBetAmount, reason
    } <- FinanceUtils.fetchAndArchive finalizeBetPlacementCancelCid

    let customerKey = (operator, provider, customer)

    <EMAIL>{inBulk, status, promotion} <-
      snd <$> fetchByKey @BetModel.BetPlacement (operator, provider, customer, betPlacementId)

    exerciseByKey @GamblingAccount.Account customerKey GamblingAccount.UnlockBet with betPlacementId, optBonusBetAmount = bonusBetAmount

    -- | Matched bets cannot try to create a cancel order and their instrument will be reduced in the execution of
    -- either SettleBets or SignCancelExecution depending of matched bet gets cancelled or settled
    Action.when (status == BetModel.Unmatched) do

        -- Cancel order, retrieve collateral deposit (Binary Option)
        -- Will trigger exberry orde cancel (one time use)
        tradingCid <- fst <$> fetchByKey @TradingService.CancelOrderRequest cancelOrderRequestKey
        depositCid <- snd <$> exercise tradingCid TradingService.AcknowledgeCancel
        Asset.AssetDeposit{account} <- fetch depositCid

        -- Reduce issued bet
        betIssuance <- snd <$> fetchByKey @IssuanceModel.Issuance betIssuanceKey
        Utils.reduceIssuanceAndArchiveIfZeroQuantity customerKey depositCid betIssuance.issuanceId account.id

    Optional.optional
        (Optional.whenSome promotion (\ promotionKey ->
                    Action.void $ exerciseByKey @MarketingModel.PromotionWallet (operator, provider, customer)
                        MarketingModel.ReducePromotionUsage with
                            promotionKey
                            actionId = betPlacementId
                            offeredAmount = 0.0
                            transactedAmount = 0.0
                ))
        (\ bulkBetPlacementId -> do
            Action.void $ exerciseByKey @BetModel.BulkBetPlacement (operator, provider, customer, bulkBetPlacementId)
              BetModel.Bulk_CancelBet with betPlacementId
        ) inBulk

    let betKey = key betPlacement
    exerciseByKey @BetModel.BetPlacement betKey $ BetModel.CancelBet with reason


actionFailureNotificationCheck : (Party, Party, Party, GamblingModel.Actionable, Text) -> Update ()
actionFailureNotificationCheck actionFailureKey = do
  Utils.whenContractExists @GamblingModel.ActionFailure actionFailureKey (`exercise` GamblingModel.AcknowledgeActionFailure)


actionSuccessNotificationCheck : (Party, Party, Party, GamblingModel.Actionable, Text) -> Update ()
actionSuccessNotificationCheck actionSuccessKey = do
  Utils.whenContractExists @GamblingModel.ActionSuccess actionSuccessKey (`exercise` GamblingModel.AcknowledgeActionSuccess)


unlockBulkBetsAfterFailure :  forall t t2.
    (Template t, HasFetch t, HasArchive t, HasFetchByKey t2 BetPlacementKey, HasArchive t2, TemplateKey t2 BetPlacementKey,
    HasField "details" t2 BetModel.Details, HasField "optBonusAmount" t2 (Optional (Numeric 2))) =>
    ContractId t -> [BetPlacementKey] -> (Party, Party, Party) -> Update ()
unlockBulkBetsAfterFailure bulkCid requestKeyList customerKey = do

    Foldable.mapA_ (\ betPlacementReqKey -> do
            accountCid <- fst <$> fetchByKey @GamblingAccount.Account customerKey
            req <- snd <$> Utils.fetchAndArchiveByKey @t2 betPlacementReqKey
            Action.void $ exercise accountCid
                GamblingAccount.UnlockBet with
                    betPlacementId = req.details.betPlacementId
                    optBonusBetAmount = req.optBonusAmount

        ) requestKeyList
    archive bulkCid


validateMaxMinOdds : BetModel.Details -> OddType -> OddType -> Update ()
validateMaxMinOdds betDetails minOdd maxOdd = do

    let oddType = (BetModel.fromSide betDetails.side).odd

    case oddType of
        Fractional odd -> do
            fractionalMax <- Utils.rightOrFail $ getOddFromTo maxOdd Fractional
            fractionalMin <- Utils.rightOrFail $ getOddFromTo minOdd Fractional

            let oddFractionalMax  = OddsModel.getOdd fractionalMax
                oddFractionalMin  = OddsModel.getOdd fractionalMin

            assertMsg ("You have Exceeded Fractional odds Max Value or Min Value " <> show odd) $ odd <= oddFractionalMax && odd >= oddFractionalMin

        Moneyline odd -> do
            moneylineMax <- Utils.rightOrFail $ getOddFromTo maxOdd Moneyline
            moneylineMin <- Utils.rightOrFail $ getOddFromTo minOdd Moneyline

            let oddMoneylinemax  = truncate (OddsModel.getOdd moneylineMax)
                oddMoneylinemin  = truncate (OddsModel.getOdd moneylineMin)

            assertMsg ("You have Exceeded Moneyline odds Max Value or Min Value " <> show odd) $ odd <= oddMoneylinemax && odd >= oddMoneylinemin

        Decimal odd -> do
            let oddDecimalmax  = OddsModel.getOdd maxOdd
                oddDecimalmin  = OddsModel.getOdd minOdd

            assertMsg ("You have Exceeded Decimal odds Max Value or Min Value " <> show odd) $ odd <= oddDecimalmax && odd >= oddDecimalmin


-- If the adapter only sends BetPlacement contracts from the same that are unmatched and from the opposite side of the BetPlacementRequest the following logic is correct.
-- Otherwise we need to add the following logic "currentBetRequest.customer == currentBet.customer && currentBetRequest.details.side /= currentBet.details.side && currentBet.status == BetModel.Unmatched"
checkIfCanPlaceBet : BetModel.BetPlacementRequest -> [BetModel.BetPlacement] -> Text -> Bool
checkIfCanPlaceBet _ [] _ = True
checkIfCanPlaceBet currentBetRequest (currentBet::betPlacements) listingId = do
    if (listingId == currentBet.listingId && (BetModel.getSide currentBetRequest.details.side) /= (BetModel.getSide currentBet.details.side))
    then do
        let side = BetModel.getSide currentBetRequest.details.side
            currentOdd = BetModel.getSideOdd currentBetRequest.details.side
            oldOdd = BetModel.getSideOdd currentBet.details.side
        if side == "Back" then (currentOdd > oldOdd)
        else (currentOdd < oldOdd)
    else checkIfCanPlaceBet currentBetRequest betPlacements listingId


checkIfBulkCanBePlaced : [BetModel.BetPlacementRequest] -> Bool
checkIfBulkCanBePlaced [] = True
checkIfBulkCanBePlaced (currentRequest::betPlacementRequests)
    | isClashingBet = False
    | otherwise = checkIfBulkCanBePlaced betPlacementRequests
    where
        isEventKeyEqual p1 p2 = p1.details.eventKey == p2.details.eventKey
        sideText p = BetModel.getSide p.details.side
        isBetSideNotEqual p1 p2 = sideText p1 /= sideText p2
        outcome p = (BetModel.fromSide p.details.side).outcome
        isBetOutcomeEqual p1 p2 = outcome p1 == outcome p2
        odd p = BetModel.getSideOdd p.details.side
        isBetEqualOrGreaterOddThan p1 p2 = odd p1 >= odd p2
        isClashingBet = any (\ request ->
                request `isEventKeyEqual` currentRequest &&
                request `isBetSideNotEqual` currentRequest &&
                request `isBetOutcomeEqual` currentRequest &&
                if sideText currentRequest == "Lay"
                    then currentRequest `isBetEqualOrGreaterOddThan` request
                    else request `isBetEqualOrGreaterOddThan` currentRequest
            ) betPlacementRequests


calculatePendingDepositsAmount : Map.Map Text (Numeric 2) -> Update (Numeric 2)
calculatePendingDepositsAmount pendingDeposits = return $ sum (Map.values pendingDeposits)


reconcileBetPlacement : ContractId (BetModel.BetPlacement) -> Update ()
reconcileBetPlacement betPlacementCid = do

    BetModel.BetPlacement{..} <- FinanceUtils.fetchAndArchive betPlacementCid

    let customerKey = (operator, provider, customer)
    let betPlacementId = details.betPlacementId

    exerciseByKey @GamblingAccount.Account customerKey GamblingAccount.UnlockBet with
        betPlacementId = betPlacementId, optBonusBetAmount = bonusBetAmount

    -- Verify the order is rejected
    Action.when (status == BetModel.Unmatched) $ do
        let orderKey = (provider, orderId)

        lookupByKey @IssuanceModel.Issuance betIssuanceKey
          >>= (`Utils.whenNone` assertFail "Issuance not found")

        TradingModel.Order{status} <-
          Utils.getContractWithKey @TradingModel.Order orderKey "Order"

        case status of
            (TradingModel.Rejected _) -> pure ()
            _ -> assertFail "Status should be rejected"

    Optional.optional
        (Optional.whenSome promotion (\ promotionKey ->
                    Action.void $ exerciseByKey @MarketingModel.PromotionWallet (operator, provider, customer)
                        MarketingModel.ReducePromotionUsage with
                            promotionKey
                            actionId = betPlacementId
                            offeredAmount = 0.0
                            transactedAmount = 0.0
                ))
        (\ bulkBetPlacementId -> do
            exerciseByKey @BetModel.BulkBetPlacement (operator, provider, customer, bulkBetPlacementId)
                BetModel.Bulk_CancelBet with betPlacementId
            return ()
        ) inBulk

    return ()

finalizeBetPlacementCancelSuccess :
  BetModel.BetPlacement -> BetModel.FinalizeBetPlacementCancelRequest
  -> ContractId BetModel.FinalizeBetPlacementCancelRequest -> Party -> Party -> Party
  -> Update (ContractId BetModel.CancelledBetPlacement, ContractId GamblingModel.ActionSuccess)
finalizeBetPlacementCancelSuccess betPlacement finalizeBetPlacementCancel finalizeBetPlacementCancelCid operator provider customer = do
  Action.when (finalizeBetPlacementCancel.betPlacementId /= betPlacement.details.betPlacementId) do
      Action.void $ archive finalizeBetPlacementCancelCid

  let reason = finalizeBetPlacementCancel.reason
  newfinalizeBetPlacementCancelCid <- createOrLookup finalizeBetPlacementCancel with
    betPlacementId = betPlacement.details.betPlacementId, bonusBetAmount = betPlacement.bonusBetAmount

  cancelledBetCid <- finalizeBetCancel newfinalizeBetPlacementCancelCid

  actionSuccessCid <- create GamblingModel.ActionSuccess with
      operator, provider, customer
      actionId = betPlacement.details.betPlacementId
      action = GamblingModel.BetCancelation
      reason

  pure (cancelledBetCid, actionSuccessCid)


getBetCancellationActionTypeForSendGrid
  : BetModel.FinalizeBetPlacementCancelRequest -> Text -> Update SendgridIntegration.ActionType
getBetCancellationActionTypeForSendGrid BetModel.FinalizeBetPlacementCancelRequest{reason} eventTitle = do

  let
    sendgridActionTypeList = [SendgridIntegration.EventInProgress, SendgridIntegration.EventFinished
                              , SendgridIntegration.EventPostponed, SendgridIntegration.EventCancelled]

  cancelMsgList <- mapA (cancelBetMessageBuilder eventTitle)
    [IntegrationEvents.InProgress, IntegrationEvents.Finished, IntegrationEvents.Postponed, IntegrationEvents.Cancelled]

  case reason of
    x | "was updated with contradictory details" `Text.isInfixOf` x -> pure SendgridIntegration.EventRevised
    x | "Bet was cancelled by user" `Text.isPrefixOf` x -> pure SendgridIntegration.ManualCancellation
    x | "Bet was cancelled because event no longer exists" `Text.isPrefixOf` x -> pure SendgridIntegration.EventArchived
    x | x `elem` cancelMsgList -> do
      let Some index = ListT.findIndex (== reason) cancelMsgList
      pure $ sendgridActionTypeList List.!! index
    x -> assertFail $ "Invalid reason for bet cancellation: (" <> x <> ")"

-- | This function will check if there are any BetPlacementCancelRequest or FinalizeBetPlacementCancelRequest contracts
-- for a given order. This logic is needed to stop the user from requesting multiple cancel requests
-- for the same bet placement.
-- However the previous logic was blocking the user from requesting a cancellation on a unmatched split bet if the bet
-- got split right before the cancellation got approved.
-- With this new logic, if there is a request with the same betPlacementId, it means that the exact same bet is being
-- requested to be cancelled again and it needs to be rejected.
-- The orderId is the same but the betPlacementId is different, it means the previous request is related to the unsplit
-- version of the bet placement and the new request is for the unmatched split bet.
-- Incorrect description above ^^^^^^
checkCancelBetRequests : forall k.
  (TemplateKey BetModel.BetPlacementCancelRequest k, TemplateKey BetModel.FinalizeBetPlacementCancelRequest k)
  => Text -> (Text -> (Party, Party, Party, Text))  -> Text -> Update ()
checkCancelBetRequests cancelRequestBetId prebuiltKey template_ = do

  BetModel.BetPlacement{
    status, previousSplitBetPlacementIdList
  } <- lookupByKey @BetModel.BetPlacement (prebuiltKey cancelRequestBetId)
      >>= optional (assertFail "Previously split bet not found") (fetch)

  Action.when (status == BetModel.Unmatched) do
    forA_ (cancelRequestBetId::previousSplitBetPlacementIdList) \ betId -> do
      lookupByKey @BetModel.BetPlacementCancelRequest (prebuiltKey betId)
        >>= checkRequestBetPlacementId template_
      lookupByKey @BetModel.FinalizeBetPlacementCancelRequest (prebuiltKey betId)
        >>= checkRequestBetPlacementId template_


checkRequestBetPlacementId : Text -> Optional (ContractId t') -> Update ()
checkRequestBetPlacementId template_ = Optional.optional
  (return ())
  (\ _ -> assertFail $ "Duplicate contract found for template: " <> template_)

checkEmailAddress : Text -> Update ()
checkEmailAddress emailAddress = do
    let
        splitEmailAddress = Text.splitOn "@" emailAddress
        seconSplitAddress = Text.splitOn "." (List.last splitEmailAddress)

    Action.void $ assertMsg "Incorrect emailAddress format" $
        -- Checks if the provided emailAddress is not empty and only one word (no white spaces)
        emailAddress /= "" &&
        List.length (Text.words emailAddress) == 1 &&
        -- Checks if the emailAddress has two part separated by a "@" and none of them are empty
        length splitEmailAddress == 2 &&
        List.head splitEmailAddress /= "" &&
        List.last splitEmailAddress /= "" &&
        -- Checks if the second part of the address has at least one "." character
        length seconSplitAddress >= 2 &&
        List.head seconSplitAddress /= "" &&
        List.last seconSplitAddress /= ""


cancelBetMessageBuilder :Text -> IntegrationEvents.Status -> Update Text
cancelBetMessageBuilder eventTitle eventStatus = case eventStatus of
    IntegrationEvents.InProgress -> return $ "Unmatched " <> partialBetCancellationMsg eventTitle <> " is InProgress"
    IntegrationEvents.Finished -> return $ "Unmatched " <> partialBetCancellationMsg eventTitle <> " is Finished"
    IntegrationEvents.Postponed -> return $ partialBetCancellationMsg eventTitle <> " is Postponed"
    IntegrationEvents.Cancelled -> return $ partialBetCancellationMsg eventTitle <> " is Cancelled"
    _ -> assertFail $ "Cannot cancel bet because event status is " <> show eventStatus

partialBetCancellationMsg : Text -> Text
partialBetCancellationMsg eventTitle = "Bet was automatically Cancelled because Event with title " <> show eventTitle