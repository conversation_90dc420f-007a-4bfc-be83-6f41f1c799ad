{-# LANGUAGE MultiWayIf #-}
module Gambyl.Gambling.Settlement.Model where

import DA.Action qualified as Action
import DA.Date qualified as Date
import DA.Finance.Asset (AssetDeposit(..))
import DA.Finance.Types (Asset(..))
import DA.Finance.Utils qualified as FinUtils (fetchAndArchive)
import DA.List (dedup, (\\))
import DA.List qualified as List
import DA.Map qualified as Map
import DA.Set (Set)
import DA.Set qualified as Set

import ContingentClaims.Claim                   (Claim(Cond), deserialize)
import ContingentClaims.Observable              (Inequality(Lte))
import ContingentClaims.Observation qualified as O (Observation(..))

import Marketplace.Custody.Service qualified as CustodyService
import Marketplace.Issuance.AssetDescription    (AssetDescription(..))
import Marketplace.Issuance.Model qualified as IssuanceModel
import Marketplace.Settlement.Model qualified as SettlementModel
import Marketplace.Settlement.Service qualified as SettlementService

import EnetPulseIntegration.Events qualified as IntegrationEvents

import Gambyl.Interface.Gambling.Bet.Settlement.Model as ISettlementModel

import Gambyl.Gambling.Account.Model qualified as GamblingAccount
import Gambyl.Gambling.Bet.Model qualified as BetModel
import Gambyl.Gambling.Bet.Odds.Model qualified as OddsModel
import Gambyl.Gambling.Event.Model qualified as EventModel
import Gambyl.Utils qualified as Utils

{- -------------------------------------------TEMPLATES------------------------------------------- -}

type ExecutedBetsCid = ContractId ExecutedBets

template ExecutedBets
    with
        operator                    : Party
        provider                    : Party
        backCustomer                : Party
        layCustomer                 : Party
        signers                     : Set Party
        backBetPlacementId          : Text
        layBetPlacementId           : Text
        eventKey                    : EventModel.EventInstrumentKey
        settlementInstructionCid    : ContractId SettlementModel.SettlementInstruction
        executedOdd                 : OddsModel.OddType
        isSettled                   : Bool
        isCancelled                 : Bool
        executionId                 : Text
        -- ^ Consists of a string in the format "<backBetPlacementId>_<layBetPlacementId>"
    where
        signatory [operator, provider] <> Set.toList signers
        observer backCustomer, layCustomer

        key (operator, provider, executionId) : (Party, Party, Text)
        maintainer key._1

        nonconsuming choice SignExecution : ContractId ExecutedBets
            with
                signer : Party
            controller signer
            do
                assertMsg "Signer not customer" $ signer `elem` [backCustomer, layCustomer]

                let newSigners = Set.insert signer signers
                if newSigners /= signers then do
                    archive self
                    create this with signers = newSigners
                else
                    return self


        choice SignCancelExecution : ContractId ExecutedBets
            with
                signer : Party
            controller signer
            do
                assertMsg "Signer is not a customer" $ signer `elem` [backCustomer, layCustomer]

                let
                    newSigners = Set.insert signer signers
                    getIssuanceKey customer side betPlacementId =
                      lookupByKey @BetModel.CancelledBetPlacement (operator, provider, customer, betPlacementId) >>=
                        optional (assertFail $ side <> " Bet is not cancelled") (fmap (.betIssuanceKey) . fetch)

                executedBets <-
                    if (Utils.traceMsg "customer set" (Set.fromList [backCustomer, layCustomer]) == Utils.traceMsg "New Signers" newSigners && not isCancelled) then do

                        (backBetIssuanceKey) <- getIssuanceKey backCustomer "Back" backBetPlacementId
                        (layBetIssuanceKey) <- getIssuanceKey layCustomer "Lay" layBetPlacementId


                        details <- (.details) <$> FinUtils.fetchAndArchive settlementInstructionCid

                        reduceSettlementIssuance operator provider [(backCustomer, backBetIssuanceKey), (layCustomer, layBetIssuanceKey)] details 0

                        return $ this with isCancelled = True

                    else return this

                create executedBets with signers = newSigners




        choice SettleBets : [([ContractId AssetDeposit], Text, Party)]
          controller provider
            do
              let signersList = Set.toList signers
              assertMsg "Bet Stakeholders are not signing settlement contract"
                $ null (dedup[backCustomer, layCustomer] \\ signersList)

              -- Initial binary option asset swap
              (settlementCid, _) <- fetchByKey @SettlementService.Service (operator, provider)
              swappedDepositList <- exercise settlementCid
                SettlementService.SettleInstruction with settlementInstructionCid

              today <- Date.toDateUTC <$> getTime
              let
                getPlacemetIdFromCustomer customer
                  | customer == backCustomer = return backBetPlacementId
                  | customer == layCustomer = return layBetPlacementId
                  | otherwise = assertFail "Customer does not have a stake in either bet"

                settlementResult = Action.mapA (\ assetDepositCid -> do
                    AssetDeposit{asset = Asset{id = assetId}, account} <- fetch assetDepositCid
                    AssetDescription{claims} <- snd <$> fetchByKey @AssetDescription assetId

                    let
                      signersList = Set.toList signers
                      counterParty = List.head $ List.delete account.owner signersList
                    betPlacementId <- getPlacemetIdFromCustomer account.owner
                    counterBetPlacementId <- getPlacemetIdFromCustomer counterParty

                    GamblingAccount.Account{assetDepositBet} <- snd
                      <$> fetchByKey @GamblingAccount.Account (operator, provider, counterParty)

                    counterDepositCid <- optional
                      (assertFail $ "Customer (" <> show counterParty <> ") doesn't have cash for bet with id: " <>
                        counterBetPlacementId)
                      return $ Map.lookup counterBetPlacementId assetDepositBet

                    observed <- createObservation (operator, provider, account.owner, betPlacementId)

                    (ownerCustodyCid, _) <- fetchByKey @CustodyService.Service (operator, provider, account.owner)
                    (counterPartyCustodyCid, _) <- fetchByKey @CustodyService.Service (operator, provider, counterParty)

                    lifecycleRequestCid <- exercise ownerCustodyCid
                      CustodyService.RequestLifecycle with
                        assetDepositCid
                        choice = claims

                    underlying <- case deserialize claims of
                      Cond (Lte (_, O.Observe key)) _ _ -> return key.label
                      Cond (Lte (O.Observe key, _)) _ _ -> return key.label
                      _ -> assertFail "Binary Option claims should be Cond"

                    (depositCid, stlInstructionCidList) <-
                      exercise counterPartyCustodyCid
                        CustodyService.Lifecycle with
                          lifecycleRequestCid
                          safekeepingDepositCid = counterDepositCid
                          fixings = Map.fromList [(underlying, Map.insert today observed Map.empty)]
                          uniquePayoutId = betPlacementId

                    BetModel.BetPlacement{betIssuanceKey = (_, _, issuanceId)} <-
                      snd <$> fetchByKey (operator, provider, counterParty, counterBetPlacementId)

                    Utils.reduceIssuanceAndArchiveIfZeroQuantity (operator, provider, account.owner) depositCid issuanceId account.id

                    (settlementCid, _) <- fetchByKey @SettlementService.Service (operator, provider)
                    (, betPlacementId, account.owner) . concat <$> Action.mapA (
                        exercise settlementCid . SettlementService.SettleInstruction
                        ) stlInstructionCidList
                  ) swappedDepositList

              create this with isSettled = True
              settlementResult


        choice ArchiveExecution : ()
            controller provider
            do assertMsg "Bets have not undergone settlement or have not been cancelled" $ isSettled || isCancelled

        interface instance ISettlementModel.IExecutedBets for ExecutedBets where
          view = ISettlementModel.VExecutedBets with
            operator, provider, executionId


{- ---------------------------------------HELPER FUNCTIONS---------------------------------------- -}

createObservation : (Party, Party, Party, Text) -> Update Decimal
createObservation betPlacementKey = do
    BetModel.BetPlacement{
        details = BetModel.Details{
            eventKey,
            side,
            betPlacementId
        }
    } <- snd <$> fetchByKey @BetModel.BetPlacement betPlacementKey

    fetchByKey @EventModel.EventInstrument eventKey >>=
        (\ (_, EventModel.EventInstrument{details = EventModel.Details {eventResults}}) -> do
            let
                BetModel.SideDetails{
                    outcome = <EMAIL>{participantId = sideParticipant, subtype}
                } = BetModel.fromSide side
            let
                results = map (\ result ->
                    let
                        IntegrationEvents.Outcome{
                            participantId = resultParticipant,
                            subtype = resultSubType
                        } = result
                    in
                        case side of
                            BetModel.Back _ ->
                                if  | not (IntegrationEvents.sameOutcome result sideOutcome) -> None
                                    | sideParticipant == resultParticipant && subtype == resultSubType -> Some 0.0
                                    | otherwise -> Some 2.0
                            BetModel.Lay _ ->
                                if  | not (IntegrationEvents.sameOutcome result sideOutcome) -> None
                                    | sideParticipant == resultParticipant && subtype == resultSubType -> Some 2.0
                                    | otherwise -> Some 0.0

                    ) eventResults
            let
                optionalAdd None None = None
                optionalAdd (Some x) (Some y) = Some (x + y)
                optionalAdd None (Some x) = Some x
                optionalAdd (Some x) None = Some x

            optional
                (assertFail $ "No observation could be made for bet with Id: " <> betPlacementId)
                (return)
                $ foldl (optionalAdd) None results
        )


reduceSettlementIssuance : Party -> Party -> [(Party, BetModel.IssuanceKey)] -> [SettlementModel.SettlementDetails] -> Int -> Update ()
reduceSettlementIssuance _ _ ~((customer, _)::_) _ 2      = assertFail $ "Customer" <> show customer <> " not in settlement details"
reduceSettlementIssuance _ _ [] [] _    = return ()
reduceSettlementIssuance _ _ _ [] _     = assertFail "Missing customers for settlement details"
reduceSettlementIssuance _ _ [] _ _     = assertFail "Missing settlement details for customer"
reduceSettlementIssuance operator provider (x@(customer, customerIssuanceKey)::xs) details@(SettlementModel.SettlementDetails{senderAccount, depositCid}::ys) retries = do

    if customer /= senderAccount.owner then
        reduceSettlementIssuance operator provider (xs <> [x]) details (retries + 1)
    else do

        betIssuance <- snd <$> fetchByKey @IssuanceModel.Issuance customerIssuanceKey

        let customerKey = (operator, provider, customer)

        -- Reduce issued bet
        Utils.reduceIssuanceAndArchiveIfZeroQuantity customerKey depositCid betIssuance.issuanceId senderAccount.id

        reduceSettlementIssuance operator provider xs ys 0
