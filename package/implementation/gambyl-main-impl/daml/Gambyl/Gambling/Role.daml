module Gambyl.Gambling.Role where

import DA.Map qualified as Map
import DA.Optional (fromSomeNote, fromOptional)
import DA.Set qualified as Set

import Marketplace.Custody.Role qualified as Custodian
import Marketplace.Custody.Role qualified as Custody
import Marketplace.Custody.Service qualified as Custody
import Marketplace.Issuance.Service qualified as Issuance
import Marketplace.Operator.Role qualified as Operator
import Marketplace.Settlement.Service qualified as Settlement
import Marketplace.Trading.Role qualified as Exchange
import Marketplace.Utils

import Gambyl.Counter.Model qualified as CounterModel
import Gambyl.Gambling.Event.Model qualified as EventModel
import Gambyl.Gambling.Model qualified as GamblingModel
import Gambyl.Gambling.Service qualified as GamblingService
import Gambyl.Gambling.Bet.Odds.Model       (inverse, OddType(..))
import Gambyl.Gambling.Event.Service qualified as EventService
import Gambyl.Marketing.Service qualified as MarketingService
import Gambyl.Utils
import Gambyl.Marketing.Model qualified as MarketingModel


template Role
    with
        operator : Party
        provider : Party
    where
        signatory operator, provider

        key (operator, provider) : (Party, Party)
        maintainer key._2

        nonconsuming choice OfferGamblingService : ContractId GamblingService.Offer
          with
            customer : Party
          controller provider
          do
            create GamblingService.Offer with operator, provider, customer, observers = Set.empty

        nonconsuming choice ProcessGamblingRequest : (ContractId GamblingService.Service, ContractId Custody.Service, ContractId Issuance.Service)
            with
                gamblingRequestCid: ContractId GamblingService.Request
            controller provider
            do
                gamblingRequest <- fetch gamblingRequestCid
                lookupByKey @GamblingService.Service (operator, provider, gamblingRequest.customer) >>= \ case
                    Some gamblingServiceCid -> do
                        exercise gamblingRequestCid GamblingService.Reject

                        custodyServiceCid <- fromSomeNote "Custody Service does not exist for the given customer" <$> lookupByKey @Custody.Service (operator, provider, gamblingRequest.customer)
                        issuanceServiceCid <- fromSomeNote "Issuance Service does not exist for the given customer" <$> lookupByKey @Issuance.Service (operator, provider, gamblingRequest.customer)

                        return (gamblingServiceCid, custodyServiceCid, issuanceServiceCid)
                    None -> exercise gamblingRequestCid GamblingService.Approve with ..

        nonconsuming choice ApproveGamblingRequest : (ContractId GamblingService.Service, ContractId Custody.Service, ContractId Issuance.Service)
            with
                gamblingRequestCid: ContractId GamblingService.Request
            controller provider
            do
                exercise gamblingRequestCid GamblingService.Approve with ..

        nonconsuming choice OfferMarketingService : ContractId MarketingService.Offer
            with
                customer : Party
            controller provider
            do
                create MarketingService.Offer with ..

        nonconsuming choice ProcessMarketingRequest : (ContractId MarketingService.Service)
            with
                marketingRequestCid: ContractId MarketingService.Request
            controller provider
            do
                marketingRequest <- fetch marketingRequestCid
                lookupByKey @MarketingService.Service (operator, provider, marketingRequest.customer) >>= \ case
                    Some marketingServiceCid -> do
                        exercise marketingRequestCid MarketingService.Reject
                        return marketingServiceCid
                    None -> exercise marketingRequestCid MarketingService.Approve with ..

        nonconsuming choice ApproveMarketingRequest : (ContractId MarketingService.Service)
            with
                marketingRequestCid: ContractId MarketingService.Request
            controller provider
            do
                exercise marketingRequestCid MarketingService.Approve with ..

        nonconsuming choice OfferEventService : ContractId EventService.Offer
            with
                customer : Party
            controller provider
            do
                create EventService.Offer with ..

        nonconsuming choice ProcessEventServiceRequest : (ContractId EventService.Service)
            with
                eventRequestCid: ContractId EventService.Request
            controller provider
            do
                eventRequest <- fetch eventRequestCid
                lookupByKey @EventService.Service (operator, provider, eventRequest.customer) >>= \ case
                    Some eventServiceCid -> do
                        exercise eventRequestCid EventService.Reject
                        return eventServiceCid
                    None -> exercise eventRequestCid EventService.Approve with ..

        nonconsuming choice ApproveEventServiceRequest : (ContractId EventService.Service)
            with
                eventRequestCid: ContractId EventService.Request
            controller provider
            do
                exercise eventRequestCid EventService.Approve with ..


        nonconsuming choice ChangeGlobalGamblingConfiguration
          : (ContractId GamblingModel.GlobalGamblingConfiguration)
          with
            newFlaggedAmount              : Optional (Numeric 2)
            newUnverifiedAccountMaxAmount : Optional (Numeric 2)
            newMinDepositAmount           : Optional (Numeric 2)
            newMinWithdrawAmount          : Optional (Numeric 2)
            newIntegrationParties         : Map.Map Text Party
            newBetFee                     : Optional Decimal
            newDepositFee                 : Optional Decimal
            newWithdrawFee                : Optional Decimal
            newArchiveEventDays           : Optional Int
            newLegalAge                   : Optional Int
            newIsOnMaintenance            : Optional Bool
            newDaysPostponedEventExpires  : Optional Int
            newMinOdd                     : Optional OddType
            newMinutesMarketMapExpires    : Optional Int
            newAllowedPeriod              : Optional Int
            newDefaultOdds                : Optional OddType
          controller provider
          do
            (globalGamblingConfigurationCid, <EMAIL>{..})
                <- fetchByKey @GamblingModel.GlobalGamblingConfiguration (operator, provider)

            newMaxOdd <- case newMinOdd of
              Some x -> do
                  return (inverse x)
              _ -> do
                  return (maxOdd)

            archive globalGamblingConfigurationCid
            create config with
              flaggedAmount = fromOptional flaggedAmount newFlaggedAmount
              unverifiedAccountMaxAmount = fromOptional unverifiedAccountMaxAmount newUnverifiedAccountMaxAmount
              minDepositAmount = fromOptional minDepositAmount newMinDepositAmount
              minWithdrawAmount = fromOptional minWithdrawAmount newMinWithdrawAmount
              integrationParties = Map.union newIntegrationParties integrationParties
              betFee = fromOptional betFee newBetFee
              depositFee = fromOptional depositFee newDepositFee
              withdrawFee = fromOptional withdrawFee newWithdrawFee
              archiveEventDays = fromOptional archiveEventDays newArchiveEventDays
              legalAge = fromOptional legalAge newLegalAge
              maxOdd = newMaxOdd
              minOdd = fromOptional minOdd newMinOdd
              isOnMaintenance = fromOptional isOnMaintenance newIsOnMaintenance
              daysPostponedEventExpires = fromOptional daysPostponedEventExpires newDaysPostponedEventExpires
              minutesMarketMapExpires = fromOptional minutesMarketMapExpires newMinutesMarketMapExpires
              allowedPeriod = fromOptional allowedPeriod newAllowedPeriod
              defaultOdds = fromOptional defaultOdds newDefaultOdds


template Offer
  with
    operator : Party
    provider : Party
    aggregateFor : Optional (ContractId Custodian.Offer, ContractId Exchange.Offer)
    public : Set.Set Party
  where
    signatory operator
    observer provider

    choice Withdraw : ()
      controller operator
      do pure ()

    choice Accept: GamblingAggregate
      with
        integrationParties : Map.Map Text Party
      controller provider
      do
        now <- show . fromTimeToUnix <$> getTime
        createdAt <- getTime
        createGlobalConfigurations operator provider integrationParties now public
        acceptGamblingAggregate aggregateFor operator provider createdAt

    choice DeclineRole : ()
      controller provider
      do pure ()


template Request
    with
        operator      : Party
        provider      : Party
        aggregateFor  : Optional (ContractId Custodian.Request, ContractId Exchange.Request)
        integrationParties : Map.Map Text Party
    where
        signatory provider
        observer operator

        choice RequestAggregate : ContractId Request
            controller provider
            do
                custodyRoleRequestCid   <- create Custodian.Request with ..
                exchangeRoleRequestCid  <- create Exchange.Request with ..

                create this with aggregateFor = Some (custodyRoleRequestCid, exchangeRoleRequestCid)


        choice Approve : GamblingAggregate
            with
              public : Set.Set Party
            controller operator
            do
                now <- show . fromTimeToUnix <$> getTime
                createdAt <- getTime
                createGlobalConfigurations operator provider integrationParties now public
                approveGamblingAggregate aggregateFor operator provider createdAt

        choice Reject : ()
            controller operator
            do pure ()


data GamblingAggregate = GamblingAggregate {
    gamblingRoleCid      : ContractId Role,
    custodyRoleCid       : ContractId Custody.Role,
    gamblingServiceCid   : ContractId GamblingService.Service,
    custodyServiceCid    : ContractId Custody.Service,
    issuanceServiceCid   : ContractId Issuance.Service,
    tradingRoleCid       : ContractId Exchange.Role,
    settlementServiceCid : ContractId Settlement.Service
} deriving (Show)



acceptGamblingAggregate : (Optional (ContractId Custodian.Offer, ContractId Exchange.Offer)) -> Party -> Party -> Time -> Update GamblingAggregate
acceptGamblingAggregate None _ _ _ = error "Request for aggregation hasn't been made"
acceptGamblingAggregate (Some (custodianOfferCid, exchangeOfferCid)) operator provider createdAt =
    do
        gamblingRoleCid <- createOrLookup Role with operator, provider

        gamblingServiceCid <- createOrLookup GamblingService.Service with
          operator, provider, customer = provider
          permissions = Set.fromList [GamblingModel.Bets, GamblingModel.Events]
          createdAt

        GamblingModel.GlobalGamblingConfiguration{integrationParties}
            <- snd <$> fetchByKey @GamblingModel.GlobalGamblingConfiguration (operator, provider)

        let Some quickBooks = Map.lookup "quickbooks" integrationParties

        createQuickbooksProviderAccounts quickBooks provider

        (custodyRoleCid, issuanceServiceCid, custodyServiceCid) <- exercise custodianOfferCid $ Custodian.Accept
        (tradingRoleCid, _, _, settlementServiceCid) <- exercise exchangeOfferCid $ Exchange.Accept

        return GamblingAggregate with ..



approveGamblingAggregate : (Optional (ContractId Custodian.Request, ContractId Exchange.Request)) -> Party -> Party -> Time -> Update GamblingAggregate
approveGamblingAggregate None _ _ _ = error "Request for aggregation hasn't been made"
approveGamblingAggregate (Some (custodianRequestCid, exchangeRequestCid)) operator provider createdAt =
    do
      gamblingRoleCid <- createOrLookup Role with operator, provider

      gamblingServiceCid <- createOrLookup GamblingService.Service with
        operator, provider, customer = provider
        permissions = Set.fromList [GamblingModel.Bets, GamblingModel.Events]
        createdAt

      GamblingModel.GlobalGamblingConfiguration{integrationParties}
          <- snd <$> fetchByKey @GamblingModel.GlobalGamblingConfiguration (operator, provider)

      let Some quickBooks = Map.lookup "quickbooks" integrationParties

      createQuickbooksProviderAccounts quickBooks provider

      (operatorCid, _) <- fetchByKey @Operator.Role operator
      (custodyRoleCid, issuanceServiceCid, custodyServiceCid) <- exercise operatorCid $ Operator.ApproveCustodianRequest with custodianRequestCid

      (tradingRoleCid, _, _, settlementServiceCid) <- exercise operatorCid $ Operator.ApproveExchangeRequest with exchangeRequestCid

      return GamblingAggregate with ..
      --(gamblingRoleCid, custodyRoleCid, gamblingServiceCid, custodyServiceCid, issuanceServiceCid, tradingRoleCid, settlementServiceCid)


createGlobalConfigurations : Party -> Party -> Map.Map Text Party -> Text -> Set.Set Party -> Update ()
createGlobalConfigurations operator provider integrationPartiesMap id public = do

    -- TODO Add correct ID
    createOrLookup CounterModel.Counter with operator, provider, count = 0, id
    createOrLookup GamblingModel.GlobalGamblingConfiguration with
      operator, provider, unverifiedAccountMaxAmount = 1000.0, flaggedAmount = 10000.0, archiveEventDays = 1
      integrationParties = integrationPartiesMap, minWithdrawAmount = 50.0, minDepositAmount = 20.0
      betFee = 0.05, withdrawFee = 0.05, depositFee = 0.1, legalAge = 18, public, isOnMaintenance = False
      daysPostponedEventExpires = 2, minOdd = Decimal 1.01, maxOdd = inverse (Decimal 1.01)
      minutesMarketMapExpires = 60, allowedPeriod = 7, defaultOdds = Moneyline 0
    createOrLookup EventModel.MarketMap with operator, provider, map = Map.empty, public
    createOrLookup MarketingModel.GlobalPromotions with operator, provider, promotions = [], public
    return ()


createQuickbooksProviderAccounts : Party -> Party -> Update ()
createQuickbooksProviderAccounts quickBooks provider = do
        GamblingModel.requestQuickbooksAccount quickBooks provider provider [] "Fees" "Bank" "CashOnHand"
        GamblingModel.requestQuickbooksAccount quickBooks provider provider [] "Gambyl Bonus" "Bank" "CashOnHand"
        return ()