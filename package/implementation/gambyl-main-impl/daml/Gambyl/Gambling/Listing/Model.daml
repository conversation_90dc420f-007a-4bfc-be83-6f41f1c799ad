module Gambyl.Gambling.Listing.Model where

import DA.List qualified as List
import DA.Set qualified as Set
import DA.<PERSON>ple qualified as Tuple

import EnetPulseIntegration.Events qualified as IntegrationEvents

import Gambyl.Gambling.Bet.Odds.Model qualified as Odds
import Gambyl.Gambling.Event.Model qualified as EventModel

import Gambyl.Utils qualified as Utils
import Marketplace.Utils (createOrLookup)
import  Marketplace.Listing.Model qualified as MarketplaceListingModel
import  Marketplace.Listing.Service qualified as MarketplaceListingService


template GamblingListing
    with
        operator : Party
        provider : Party
        public : [Party]
        -- v: Id used internally to identify listing on gambyl
        internalId : Text
        -- v: Id used externally to identify listing on exberry
        externalId : Text
        eventLabel : Text
        outcome : IntegrationEvents.Outcome
        -- v: Ordered List for current best odds for lay bet
        layOdds : [(Set.Set Odds.OddType, Numeric 2)]
        -- v: Ordered List for current best odds for back bet
        backOdds : [(Set.Set Odds.OddType, Numeric 2)]
        status : Status
        matchedAmount : Numeric 2
    where
        signatory operator, provider
        observer public

        key (operator, provider, internalId) : (Party, Party, Text)
        maintainer key._1

        nonconsuming choice UpdateGamblingListingOdds : ContractId GamblingListing
          with
            listOdds : [(Odds.OddType, Numeric 2)]
            side : Text
            newObservers : [Party]
          controller provider
          do
            let
              eitherErrorOrIdentity = either error identity
              listOddsDecimal = map (\(odd, stake) -> (either (error) (identity) (Odds.getOddFromTo odd Odds.Decimal), stake)) listOdds
              listOddsGrouped = List.groupBy (\ (o1,_) (o2,_) -> o1 == o2) $ List.sort listOddsDecimal
              accumulateStake = foldl (\ (_, acc) -> Tuple.second (+acc) ) (Odds.Decimal 0.0, 0.0)
              listOddsFolded = map accumulateStake listOddsGrouped
              setOddsFolded = map (Tuple.first (Set.fromList . map eitherErrorOrIdentity . EventModel.mapAllOdds)) listOddsFolded

            archive self
            create $ case side of
              "Back" -> this with backOdds = [List.head (List.sortOn fst setOddsFolded)] , public = newObservers -- sortOn function sorts in ascending fashion, based on field that derives Ord
              "Lay"  -> this with layOdds = [List.head (List.sortOn (Down . fst) setOddsFolded)], public = newObservers -- sortOn function using "Down" sorts in descending fashion, based on field that derives Ord
              _      -> error "Incorrect Side"

        nonconsuming choice RequestDisableListings : ContractId MarketplaceListingService.DisableListingRequest
            with
                gamblingListingCid : ContractId GamblingListing
            controller provider
            do
                createOrLookup GamblingUpdateListingRequest with ..

                (listingCid,_) <- fetchByKey @MarketplaceListingModel.Listing (operator, provider, externalId)
                (serviceCid, _) <- fetchByKey @MarketplaceListingService.Service (operator, provider, provider)
                exercise serviceCid
                    MarketplaceListingService.RequestDisableListing with listingCid

        choice UpdateMatchedAmount : ContractId GamblingListing
            with
                amount : Numeric 2
            controller provider
            do
                create this with
                    matchedAmount = this.matchedAmount + amount


template GamblingUpdateListingRequest
    with
        operator        : Party
        provider        : Party
        -- v: Id used internally to identify listing on gambyl
        internalId      : Text
        -- v: Id used externally to identify listing on exberry
        externalId      : Text
        status          : Status
    where
        signatory operator, provider

        key (operator, provider, externalId) : (Party, Party, Text)
        maintainer key._1

        choice ApproveDisableListings : ContractId GamblingListing
            with
                disableListingRequestCid : ContractId MarketplaceListingService.DisableListingRequest
            controller provider
            do
                (serviceCid, _) <- fetchByKey @MarketplaceListingService.Service (operator, provider, provider)
                exercise serviceCid
                    MarketplaceListingService.DisableListing with ..

                (GamblingListing{..}) <- snd <$> Utils.fetchAndArchiveByKey @GamblingListing (operator, provider, internalId)
                create GamblingListing with status = Disabled,..


template FailedGamblingUpdateListing
    with
        operator        : Party
        provider        : Party
        -- v: Id used internally to identify listing on gambyl
        internalId      : Text
        -- v: Id used externally to identify listing on exberry
        externalId      : Text
        reason          : Text
    where
        signatory operator, provider

        key (operator, provider, externalId) : (Party, Party, Text)
        maintainer key._1


data Status
    = Active
    | Disabled
    | Fail
  deriving (Eq, Show)