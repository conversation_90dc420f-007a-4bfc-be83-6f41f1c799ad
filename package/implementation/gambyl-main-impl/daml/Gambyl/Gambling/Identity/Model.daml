module Gambyl.Gambling.Identity.Model where

import Marketplace.Regulator.Model qualified as Regulator

import JumioIntegration.Identity qualified as IntegrationIdentity
import qualified DA.Set as Set

{- -------------------------------------------TEMPLATES------------------------------------------- -}

template PendingIdentity
  with
    operator : Party
    provider : Party
    customer : Party
    redirectUrl : Text
    jumioPendingIdKey : PendingIdentityKey
  where
  signatory operator, provider, customer

  key (operator, provider, customer) : (Party, Party, Party)
  maintainer key._1

  choice VerifyIdentity : ContractId GamblerIdentity
    with
      verifiedIdCid : ContractId IntegrationIdentity.VerifiedIdentity
    controller provider
    do
      IntegrationIdentity.VerifiedIdentity{
        userData = <EMAIL>{firstName, lastName, country}, dataUrl
      } <- exercise verifiedIdCid IntegrationIdentity.Verified_RetrieveArchive with
              executingParty = provider

      verifiedIdentityCid <- create Regulator.VerifiedIdentity with
        operator, provider, customer, legalName   = firstName <> " " <> lastName
        location = country, observers = Set.empty

      create GamblerIdentity with
        mpVerifiedIdCid = verifiedIdentityCid, operator, provider, customer, jumioUserData,  dataUrl


  choice RejectIdentity : Either (ContractId RejectedIdentity) ()
    with
      rejectedIdCid : ContractId IntegrationIdentity.RejectedIdentity
    controller provider
    do
      IntegrationIdentity.RejectedIdentity{
        dataUrl, rejectReason
      } <- exercise rejectedIdCid IntegrationIdentity.Rejected_RetrieveArchive with
        executingParty = provider

      if rejectReason == "NO_ID_UPLOADED"
        then return $ Right ()
        else  Left <$> create RejectedIdentity with ..

template GamblerUnverifiedIdentityRequest
  with
    operator : Party
    provider : Party
    customer : Party
    firstName : Text
    lastName : Text
    emailAddress : Text
    phoneNumber : Text
    birthday : Date
    city : Text
    countryCode : Text
    postalCode : Text
    subDivision : Text
    addressLine1 : Text
  where
    signatory operator, provider, customer

    key (operator, provider, customer) : (Party, Party, Party)
    maintainer key._1

    choice Request_RetrieveArchive : GamblerUnverifiedIdentityRequest
      controller provider
      do return this

template GamblerUnverifiedIdentity
    with
        operator : Party
        provider : Party
        customer : Party
        firstName : Text
        lastName : Text
        emailAddress : Text
        phoneNumber : Text
        birthday : Date
        city : Text
        countryCode : Text
        postalCode : Text
        subDivision : Text
        addressLine1 : Text
    where
        signatory operator, provider, customer

        key (operator, provider, customer) : (Party, Party, Party)
        maintainer key._1


template GamblerIdentity
    with
        operator : Party
        provider : Party
        customer : Party
        jumioUserData : IntegrationIdentity.User
        dataUrl : Text
        mpVerifiedIdCid : ContractId Regulator.VerifiedIdentity
    where
        signatory operator, provider, customer

        key (operator, provider, customer) : (Party, Party, Party)
        maintainer key._1

        choice UpdateInfo : ContractId GamblerIdentity
            with
                city            : Text
                postalCode      : Text
                subDivision     : Text
                addressLine1    : Text
                addressLine2    : Text
            controller operator, customer
            do
                let
                    IntegrationIdentity.User{firstName, lastName, birthday, country} = jumioUserData
                create this with jumioUserData = IntegrationIdentity.User with ..

        choice ArchiveIdentity : ()
            controller provider
            do
                archive mpVerifiedIdCid

template RejectedIdentity
  with
    operator        : Party
    provider        : Party
    customer        : Party
    dataUrl         : Text
    rejectReason    : Text
  where
    signatory operator, provider, customer

    key (operator, provider, customer) : (Party, Party, Party)
    maintainer key._1

    choice ArchiveRejectedIdentity : RejectedIdentity
      controller provider
      do return this

type PendingIdentityKey = (Party, Party)
