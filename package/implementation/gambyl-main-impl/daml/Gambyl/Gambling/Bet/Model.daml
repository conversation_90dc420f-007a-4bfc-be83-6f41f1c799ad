module Gambyl.Gambling.Bet.Model where

import DA.Action        (void, (>=>))
import DA.Exception     as Exception (ActionThrow(throw))
import DA.Finance.Asset as Asset (AssetDeposit, AssetDeposit_Transfer(..))
import DA.Finance.Types as FinanceTypes (Asset(..), Id(..))
import DA.Foldable      as Foldable (null)
import DA.Map           as Map ( Map, lookup, fromList, lookup)
import DA.Numeric       as Numeric
import DA.Optional      as Optional (fromSome, optional)
import DA.Set           as Set (Set, toList)
import DA.Text          as Text  (intercalate)


import ContingentClaims.Claim (
        Claim(Cond, Zero, One),
        serialize
    )
import ContingentClaims.Observable qualified as O

import Marketplace.Custody.Service qualified as Custody
import Marketplace.Issuance.AssetDescription    (AssetDescription(..))
import Marketplace.Issuance.CFI                 (other)
import Marketplace.Issuance.Service qualified as IssuanceService
import Marketplace.Listing.Model qualified as MarketplaceListingModel
import Marketplace.Listing.Service qualified as ListingService
import Marketplace.Settlement.Model qualified as SettlementModel
import Marketplace.Trading.Model qualified as TradingModel
import Marketplace.Trading.Service qualified as TradingService
import Marketplace.Utils (createOrLookup)

import EnetPulseIntegration.Events qualified as IntegrationEvents

import Gambyl.Interface.Gambling.Bet.Model qualified as IBetModel
import Gambyl.Interface.Gambling.Bet.Cancellation.Model qualified as IBetCancelModel

import Gambyl.Counter.Model qualified as CounterModel
import Gambyl.Gambling.Bet.Odds.Model qualified as Odds
import Gambyl.Gambling.Event.Model qualified as EventModel
import Gambyl.Gambling.Listing.Model qualified as GamblingListingModel
import Gambyl.Marketing.Model qualified as MarketingModel
import Gambyl.Utils qualified as Utils
import qualified DA.List as List



{- -------------------------------------------TEMPLATES------------------------------------------- -}

template BetPlacementRequest
  with
    operator        : Party
    provider        : Party
    customer        : Party
    details         : Details
    cashDeposit     : ContractId Asset.AssetDeposit
    optBonusAmount  : Optional (Numeric 2)
    promotion       : Optional MarketingModel.PromotionKey
    inBulk          : Optional Text -- Signifies whether this bet request is inside a bulk and if so the id of that bulk
  where
    signatory operator, provider, customer

    key (operator, provider, customer, details.betPlacementId) : BetPlacementKey
    maintainer key._1


template BetPlacementRequestFlag
    with
        operator        : Party
        provider        : Party
        customer        : Party

    where
        signatory operator, provider
        observer customer

        key (operator, provider, customer) : (Party, Party, Party)
        maintainer key._1

        -- controller provider can
        choice ArchiveBetFlag: ()
            controller provider
            do pure()


template BulkBetPlacementRequestFlag
    with
        operator        : Party
        provider        : Party
        customer        : Party

    where
        signatory operator, provider
        observer customer

        key (operator, provider, customer) : (Party, Party, Party)
        maintainer key._1

        -- controller provider can
        choice ArchiveBulkBetFlag: ()
            controller provider
                do pure()


template BetPlacementFinalizeRequest
    with
        operator        : Party
        provider        : Party
        customer        : Party
        instruments     : Map Text (ContractId AssetDescription)
        betIssuanceKey  : IssuanceKey
        betDepositCid   : ContractId AssetDeposit
        -- v: Id of listing where bet is placed
        listingId       : Text
        details         : Details
        optBonusAmount  : Optional (Numeric 2)
        promotion       : Optional MarketingModel.PromotionKey
        inBulk          : Optional Text -- Signifies whether this bet finalize request is inside a bulk and if so the id of that bulk
    where
        signatory operator, provider, customer

        key (operator, provider, customer, details.betPlacementId) : BetPlacementKey
        maintainer key._1

        choice ApproveBetPlacement : (ContractId BetPlacement)
          with
            orderId             : Text
            bonusBetAmount      : Optional (Numeric 2)
          controller operator, provider
          do
            now <- getTime
            create BetPlacement with
              operator, provider, customer, details, instruments, betIssuanceKey, listingId, orderId
              status = Unmatched, verified = False, matchedOdd = None, placedAt = now, bonusBetAmount
              settlementInstructionCid = None, promotion, inBulk, previousSplitBetPlacementIdList = []


template BetPlacement
  with
    operator                    : Party
    provider                    : Party
    customer                    : Party
    details                     : Details
    instruments                 : Map Text (ContractId AssetDescription)    -- Origination of instruments
    betIssuanceKey              : IssuanceKey                               -- Issuance of bet instrument
    listingId                   : Text                                      -- Listing of bet instrument against counter bet
    -- v: Must be Numerical, for exberry order creation
    orderId                     : Text                                      -- Creation of order for bet with odd (payout/stake)
    status                      : Status
    verified                     : Bool
    matchedOdd                  : Optional (Numeric 2)
    placedAt                    : Time
    bonusBetAmount              : Optional (Numeric 2)
    settlementInstructionCid    : Optional (ContractId SettlementModel.SettlementInstruction)
    promotion                   : Optional MarketingModel.PromotionKey
    inBulk                      : Optional Text -- Signifies whether this bet is inside a bulk and if so the id of that bulk
    previousSplitBetPlacementIdList : [Text]
    -- ^ Represents the any split that might have occured and the bet placement ids that came before each split
  where
    signatory operator, provider, customer

    ensure not $ Foldable.null instruments

    key (operator, provider, customer, details.betPlacementId) : BetPlacementKey
    maintainer key._1

    choice UpdateBet : ContractId BetPlacement
      with
        newStatus                   : Status
        newSettlementInstructionCid : Optional (ContractId SettlementModel.SettlementInstruction)
        newMatchedOdd               : Optional (Numeric 2)
      controller provider, customer
      do
        let
          settle = optional (settlementInstructionCid) (Some) newSettlementInstructionCid
          matchedOdd = optional (None) (Some) newMatchedOdd
        create this with status = newStatus, settlementInstructionCid = settle, matchedOdd


    choice CloseBet : ContractId BetHistory
      with
        counterBetPlacementId : Text
        won : Bool
        winnings : Optional Winnings
      controller customer, provider
      do
        closedAt <- getTime
        create BetHistory with
          operator, provider, customer, details, placedAt, closedAt
          orderId, listingId, bonusBetAmount, won, winnings
          counterBetPlacementId


    choice CancelBet : ContractId CancelledBetPlacement
      with
        reason : Text
      controller provider, customer
      do
        cancelledAt <- getTime
        create CancelledBetPlacement with
          operator, provider, customer
          details, placedAt, cancelledAt, orderId, betIssuanceKey
          bonusBetAmount, matchedOdd, settlementInstructionCid
          promotion, inBulk, previousSplitBetPlacementIdList, reason
          instruments, listingId, verified, processed = False


    choice ApproveBetPlacementVerification : ContractId BetPlacement
      controller provider
      do
          create this with verified = True

    interface instance IBetModel.IBetPlacement for BetPlacement where
      view = IBetModel.VBetPlacement with
        operator, provider, customer, betId = details.betPlacementId

template BetPlacementSplitRequest
    with
        operator                : Party
        provider                : Party
        customer                : Party
        betPlacementId          : Text
        remainingQuantity       : Decimal
    where
        signatory operator, provider, customer

        key (operator, provider, customer, betPlacementId) : BetPlacementKey
        maintainer key._1


template BetPlacementCancelRequest
    with
        operator                : Party
        provider                : Party
        customer                : Party
        betPlacementId          : Text
        orderId                 : Text
    where
        signatory operator, provider, customer

        key (operator, provider, customer, betPlacementId) : (Party, Party, Party, Text)
        maintainer key._1


template FinalizeBetPlacementCancelRequest
  with
    operator                : Party
    provider                : Party
    customer                : Party
    cancelOrderRequestKey   : CancelOrderKey
    orderId                 : Text
    betIssuanceKey          : IssuanceKey
    betPlacementId          : Text
    bonusBetAmount          : Optional (Numeric 2)
    reason                  : Text
      -- ^ Reason for bet cancellation
    eventLabel              : Text
    isUserStarted           : Bool
  where
    signatory operator, provider, customer

    key (operator, provider, customer, betPlacementId) : (Party, Party, Party, Text)
    maintainer key._1

    choice ArchiveFinalizeBetCancelRequest : ()
      controller provider
        do pure ()

    interface instance IBetCancelModel.IBetPlacementCancelRequest for FinalizeBetPlacementCancelRequest where
      view = IBetCancelModel.VBetPlacementCancelRequest with
        operator, provider, customer, betId = betPlacementId


template CancelledBetPlacement
  with
    operator : Party
    provider : Party
    customer : Party
    details : Details
    placedAt : Time
    cancelledAt : Time
    orderId : Text
    betIssuanceKey : IssuanceKey
    bonusBetAmount : Optional (Numeric 2)
    matchedOdd : Optional (Numeric 2)
    settlementInstructionCid : Optional (ContractId SettlementModel.SettlementInstruction)
    promotion : Optional MarketingModel.PromotionKey
    inBulk : Optional Text
    -- ^ Signifies whether this bet is inside a bulk and if so the id of that bulk
    previousSplitBetPlacementIdList : [Text]
      -- ^ Represents the any split that might have occured and the bet placement ids that came before each split
    reason : Text
    -- ^ Reason for bet cancellation
    instruments : Map Text (ContractId AssetDescription)
    listingId : Text
    verified : Bool
    processed : Bool
    -- ^ Flag to be flipped on gambyl adapter has used this cancellation to update Gambling Listing
  where
    signatory operator, provider, customer

    key (operator, provider, customer, details.betPlacementId) : BetPlacementKey
    maintainer key._1

    choice FlipProcessedFlag : ContractId CancelledBetPlacement
      controller operator, provider
      do
        create this with processed = not processed


template BetHistory
  with
    operator : Party
    provider : Party
    customer : Party
    details : Details
    placedAt : Time
    closedAt : Time
    orderId : Text
    listingId : Text
    bonusBetAmount : Optional (Numeric 2)
    won : Bool
    winnings : Optional Winnings
    counterBetPlacementId : Text
  where
    signatory operator, provider, customer

    key (operator, provider, customer, details.betPlacementId) : BetPlacementKey
    maintainer key._1

    ensure Optional.optional (not won) (const won) winnings


template BulkBetPlacementRequest
    with
        operator                : Party
        provider                : Party
        customer                : Party
        totalStake              : Numeric 2
        totalRealStake          : Numeric 2
        totalPayout             : Numeric 2
        betPlacementReqKeyList  : [BetPlacementKey]
        promotion               : Optional MarketingModel.PromotionKey
        bulkBetPlacementId      : Text
    where
        signatory operator, provider, customer

        ensure not $ Foldable.null betPlacementReqKeyList

        key (operator, provider, customer, bulkBetPlacementId) : (Party, Party, Party, Text)
        maintainer key._1

        choice Bulk_ApproveBet : ContractId BulkBetPlacementFinalizeRequest
            with
                betPlacementFinReqKeyList : [BetPlacementKey]
                betPlacementListingIds    : [Text]
            controller customer, provider
            do
                create BulkBetPlacementFinalizeRequest with ..


template BulkBetPlacementFinalizeRequest
    with
        operator                    : Party
        provider                    : Party
        customer                    : Party
        totalStake                  : Numeric 2
        totalPayout                 : Numeric 2
        betPlacementFinReqKeyList   : [BetPlacementKey]
        promotion                   : Optional MarketingModel.PromotionKey
        betPlacementListingIds      : [Text]
        bulkBetPlacementId          : Text
    where
        signatory operator, provider, customer

        ensure not $ Foldable.null betPlacementFinReqKeyList

        key (operator, provider, customer, bulkBetPlacementId) : (Party, Party, Party, Text)
        maintainer key._1


        choice Bulk_FinalizeBet : ContractId BulkBetPlacement
            with
                betPlacementIdList  : [Text]
            controller customer, provider
            do
                create BulkBetPlacement with ..


template BulkBetPlacement
    with
        operator : Party
        provider : Party
        customer : Party
        totalStake : Numeric 2
        totalPayout : Numeric 2
        betPlacementIdList : [Text]
        promotion : Optional MarketingModel.PromotionKey
        bulkBetPlacementId : Text
    where
        signatory operator, provider, customer

        ensure not $ Foldable.null betPlacementIdList

        key (operator, provider, customer, bulkBetPlacementId) : (Party, Party, Party, Text)
        maintainer key._1


        choice Bulk_SplitBet : ContractId BulkBetPlacement
            with
                unsplitPlacementId      : Text
                splitPlacementIdList    : [Text]
            controller customer, provider
            do
              let newBetPlacementIdList = List.delete unsplitPlacementId betPlacementIdList
              create this with betPlacementIdList = newBetPlacementIdList <> splitPlacementIdList

            -- Returns boolean informing, whether contract was recreate and still exists
        choice Bulk_CancelBet : Bool
            with
                betPlacementId : Text
            controller customer, provider
            do
                let newBetPlacementIdList = List.delete betPlacementId betPlacementIdList

                if Foldable.null newBetPlacementIdList then do

                    Optional.optional
                        (return ())
                        (\ promotionKey ->
                            void $ exerciseByKey @MarketingModel.PromotionWallet (operator, provider, customer)
                                MarketingModel.ReducePromotionUsage with
                                    promotionKey
                                    actionId = bulkBetPlacementId
                                    offeredAmount = 0.0
                                    transactedAmount = 0.0
                        ) promotion

                    return False
                else do
                    create this with betPlacementIdList = newBetPlacementIdList
                    return True

            -- Returns boolean informing, whether contract was recreate and still exists
        choice Bulk_CloseBet : Bool
            with
                betPlacementId : Text
            controller customer, provider
            do
                let newBetPlacementIdList = List.delete betPlacementId betPlacementIdList

                if Foldable.null newBetPlacementIdList then do
                    return False
                else do
                    create this with betPlacementIdList = newBetPlacementIdList
                    return True

{- -----------------------------------------CUSTOM TYPES------------------------------------------ -}

type BetPlacementKey                = (Party, Party, Party, Text)
type IssuanceKey                    = (Party, Party, Text)
type CancelOrderKey                 = (Party, Text)

{- ------------------------------------------DATA TYPES------------------------------------------- -}

data Details = Details
    with
        betPlacementId  : Text
        -- v: Includes the Side of the bet along with all information of odds, stakes and outcomes
        side            : Side
        eventTitle      : Map.Map Text Text
        eventKey        : EventModel.EventInstrumentKey
        eventStartDate  : Time
    deriving (Show, Eq)


data Side =
    Back  SideDetails | -- Order Buyer
    Lay   SideDetails   -- Order Seller
    deriving (Show, Eq)


data SideDetails =  SideDetails
    with
        -- v: Odd for bet in the format with correspondent value
        odd     : Odds.OddType
        -- |: In Back bets corresponds to gamblers liability
        -- v: In Lay bets corresponds to gamblers possible payout
        stake   : Numeric 2
        -- v: Outcome betting on (includes the outcome itself and the participant meant for)
        outcome : IntegrationEvents.Outcome
    deriving (Show, Eq)


data Status =
    Unmatched           |
    Matched             |
    Closed  -- TODO: Analyze if this Closed status is to be used or not
  deriving (Show, Eq)


data Winnings = Winnings
    with
        grossAmount : Numeric 2
        netAmount   : Numeric 2
        appliedFee  : Decimal
        -- ^ Represents the fee (in percentage) applied to this bet
    deriving (Show, Eq)


{- -------------------------------------CLASSES AND INSTANCES------------------------------------- -}

class SideCal a where
    getStake        : a -> Either Text (Numeric 2)
    getPayout       : a -> Either Text (Numeric 2)
    getSideOdd      : a -> Either Text (Numeric 2)
    getSide         : a -> Text
    fromSide        : a -> SideDetails
    oppositeSide    : a -> a


instance SideCal Side where
    getStake (Back SideDetails{stake})     = Right stake
    getStake (Lay SideDetails{odd, stake})      = Odds.calculatePayout odd stake

    getPayout (Back SideDetails{odd, stake})    = Odds.calculatePayout odd stake
    getPayout (Lay SideDetails{stake})     = Right stake

    getSideOdd (Back SideDetails{odd})   = either (Left) (Right . Odds.getOdd) $ Odds.getOddFromTo odd Odds.Fractional
    getSideOdd (Lay SideDetails{odd})    = either (Left) (Right . Odds.getOdd) $ Odds.getOddFromTo odd Odds.Fractional

    getSide (Back _)  = "Back"
    getSide (Lay _)   = "Lay"

    fromSide (Back val) = val
    fromSide (Lay val)  = val

    oppositeSide (Back val) = Lay val
    oppositeSide (Lay val) = Back val


class Compare a b where
    (===) : a -> b -> Bool
    (/==) : a -> b -> Bool

    transform : ActionThrow m => b -> m a


instance Compare Status TradingModel.Status where
    (===) Unmatched TradingModel.New                        = True
    (===) Unmatched TradingModel.PendingExecution           = True
    (===) Matched TradingModel.FullyExecuted                = True
    (===) _ _                                               = False

    (/==) betStatus orderStatus = not $ (===) betStatus orderStatus

    transform TradingModel.New              = return Unmatched
    transform TradingModel.PendingExecution = return Unmatched
    transform TradingModel.FullyExecuted    = return Matched
    transform o                             = throw NotMappedException with from = show o, to = "Bet Status"

{- ------------------------------------------EXCEPTIONS------------------------------------------- -}

exception NotMappedException
    with
        from    : Text
        to      : Text
    where
        message "No mapping exists from " <> from <> " to " <> to

{- -------------------------------------------FUNCTIONS------------------------------------------- -}

originateAndIsssueBet : ContractId BetPlacementRequest -> Text -> (Party, Party, Party) -> Set Party -> Update ((Map Text (ContractId AssetDescription)), (Party, Party, Text), ContractId AssetDeposit)
originateAndIsssueBet betPlacementRequestCid currency customerKey public = do

    BetPlacementRequest{details} <- fetch betPlacementRequestCid

    EventModel.EventInstrument{
        eventId = underlying
    } <- snd <$> fetchByKey @EventModel.EventInstrument details.eventKey

    ccy <- fst <$> Utils.checkAssetDesc [customerKey._2] currency

    let
        SideDetails{outcome} = fromSide details.side
        label side = Text.intercalate "_" [side, IntegrationEvents.short outcome, underlying.label]

    instrumentMap <- originateBack_Lay underlying ccy details customerKey public label

    let Some betInstrument = Map.lookup (getSide details.side) instrumentMap
    (issuanceKey, depositCid) <- issueBetInstrument betInstrument details customerKey label

    return (instrumentMap, issuanceKey, depositCid)


originateBack_Lay : Id -> Id -> Details -> (Party, Party, Party) -> Set Party -> (Text -> Text) -> Update (Map Text (ContractId AssetDescription))
originateBack_Lay underlying ccy details (operator, provider, _) public labelFunction = do

    let
        SideDetails{outcome} = fromSide details.side
        buildDesc side  = "Bet on " <> getSide side <> " with outcome " <> IntegrationEvents.long outcome <> " for event " <> underlying.label
        betLabel        = labelFunction $ getSide details.side
        counterBetLabel = labelFunction $ getSide (oppositeSide details.side)
        betDesc         = buildDesc details.side
        counterBetDesc  = buildDesc (oppositeSide details.side)


        -- If bet exists fetch it, if not originate it
        originateBet _ (assetId, True) = fetchByKey @AssetDescription assetId >>= (return . fst)
        originateBet description (assetId, False)  = do

            (issuanceCid, _) <- fetchByKey @IssuanceService.Service (operator, provider, provider)
            createOriginationCid <- exercise issuanceCid IssuanceService.RequestOrigination with
                    assetLabel  = assetId.label
                    cfi         = other
                    description = description
                    claims      = serialize $ Cond (O.observe underlying O.<= O.pure 1.0) (One ccy) Zero
                    observers   = toList public

            (exercise issuanceCid IssuanceService.Originate with
                createOriginationCid) >>= (return . fst)

    betCid <- Utils.checkAssetDesc [provider] betLabel >>= originateBet betDesc
    counterBetCid <- Utils.checkAssetDesc [provider] counterBetLabel >>= originateBet counterBetDesc

    let instruments = fromList [(getSide details.side, betCid), (getSide (oppositeSide details.side), counterBetCid)]
    return instruments


issueBetInstrument : ContractId AssetDescription ->  Details  -> (Party, Party, Party) -> (Text -> Text) -> Update ((Party, Party, Text), ContractId AssetDeposit)
issueBetInstrument betInstrument details customerKey@(operator, provider, customer) label = do

    betAssetDescription <- fetch betInstrument

    let
        betLabel = label $ getSide details.side
        providerKey = (operator, provider, provider)

    -- Fetch the customers and providers custody service to obtain their accounts
    Custody.Service{account = customerAccount} <- snd <$> fetchByKey @Custody.Service customerKey
    Custody.Service{account = providerAccount} <- snd <$> fetchByKey @Custody.Service providerKey

    -- Issue originated bet
    let issuanceId = Text.intercalate "_" [show customer, betLabel, details.betPlacementId]

    (issuanceCid, _) <- fetchByKey @IssuanceService.Service providerKey
    createIssuanceRequestCid <- exercise issuanceCid
        IssuanceService.RequestCreateIssuance with
            issuanceId
            account     = providerAccount
            assetId     = betAssetDescription.assetId
            quantity    = Numeric.castAndRound (fromSide details.side).stake

    (_, assetDeposit) <- exercise issuanceCid
        IssuanceService.CreateIssuance with createIssuanceRequestCid

    transferedDeposit <- exercise assetDeposit Asset.AssetDeposit_Transfer with receiverAccount = customerAccount

    return ((operator, provider, issuanceId), transferedDeposit)


listBet : ContractId BetPlacementRequest -> Map Text (ContractId AssetDescription) -> Party -> Party -> Set Party -> Update Text
listBet betPlacementRequestCid instruments operator provider public = do

    BetPlacementRequest{
        details = Details{
            side,
            eventKey = eventKey_@(_, _, underlying)
        }
    } <- fetch betPlacementRequestCid

    let
        sideOutcome = (fromSide side).outcome
        description = Text.intercalate "_" [IntegrationEvents.short sideOutcome, underlying]


    lookupByKey @GamblingListingModel.GamblingListing (operator, provider, description) >>=
        (optional
          (createListing operator provider public description instruments sideOutcome eventKey_)
          (fetch >=> (return . (.externalId))))


orderBet : Side -> Text -> (ContractId AssetDeposit) -> (Party, Party, Party) -> Update (ContractId TradingService.CreateOrderRequest)
orderBet side listingId betDepositCid customerKey@(operator, provider, _) = do

    let
        valFromSide b _ (Back _) = b
        valFromSide _ l (Lay _) = l

    lookupByKey @MarketplaceListingModel.Listing (operator, provider, listingId) >>=
        optional (assertFail "No Listing exists!")
        (\ listingCid -> do
            either (assertFail)
                (\ price {- Odd value from bet placement -} -> do
                    MarketplaceListingModel.Listing{tradedAssetId} <- fetch listingCid
                    custodyService <- snd <$> fetchByKey @Custody.Service customerKey
                    id <- (exerciseByKey @CounterModel.Counter (operator, provider) CounterModel.CountAndIncrement)

                    (tradingCid, _) <- fetchByKey @TradingService.Service customerKey
                    newOrderCid <- exercise tradingCid
                        TradingService.RequestCreateOrder with
                            details = TradingModel.Details with
                                        id
                                        listingId       = listingId
                                        asset           = FinanceTypes.Asset with
                                                            id = tradedAssetId
                                                            quantity = Numeric.castAndRound (fromSide side).stake
                                        side            = valFromSide TradingModel.Sell TradingModel.Buy side
                                        orderType       = TradingModel.Limit $ Numeric.castAndRound price
                                        timeInForce     = TradingModel.GTC
                                        marketType      = TradingModel.Collateralized with
                                                            depositCid = betDepositCid
                                                            receivableAccount = custodyService.account
                                        optExchangeFee  = None


                    fetch newOrderCid >>= \ TradingModel.Order{details} ->
                        fst <$> fetchByKey @TradingService.CreateOrderRequest (provider, details.id))

                $ getSideOdd side)

createListing : Party -> Party -> Set Party -> Text -> Map Text (ContractId AssetDescription) -> IntegrationEvents.Outcome -> EventModel.EventInstrumentKey -> Update Text
createListing operator provider observers description instruments sideOutcome eventKey = do

  let fetchBet = fetch . fromSome . (flip Map.lookup) instruments
  backBet <- fetchBet "Back"
  layBet  <- fetchBet "Lay"
  outcomes <- (.details.outcomes) . snd <$> fetchByKey @EventModel.EventInstrument eventKey

  let outcome = List.find ((sideOutcome ==) . (.outcome)) outcomes
      defaultOdds = optional ([]) (\ EventModel.OutcomeOdds{odds} -> [(odds, 0.0 : Numeric 2)]) outcome

  calendarId <- show . Utils.fromTimeToUnix <$> getTime
  symbol <-  (exerciseByKey @CounterModel.Counter (operator, provider) CounterModel.CountAndIncrement)

  GamblingListingModel.GamblingListing{externalId} <- fetch =<<
    createOrLookup GamblingListingModel.GamblingListing with
      operator, provider, externalId = symbol, internalId = description, eventLabel = eventKey._3
      layOdds = defaultOdds, backOdds = defaultOdds, outcome = sideOutcome
      public = Set.toList observers, status = GamblingListingModel.Active, matchedAmount = 0.0

  lookupByKey @ListingService.CreateListingRequest (provider, externalId) >>=
    (optional (do
        (listingCid, _) <- fetchByKey @ListingService.Service (operator, provider, provider)
        exercise listingCid
            ListingService.RequestCreateListing with
                symbol
                calendarId
                listingType             = ListingService.CollateralizedRequest
                description
                tradedAssetId           = backBet.assetId
                quotedAssetId           = layBet.assetId
                tradedAssetPrecision    = 2
                quotedAssetPrecision    = 2
                minimumTradableQuantity = 0.01
                maximumTradableQuantity = 1_000_000_000_000.0
                observers               = toList observers
        return symbol)
      (fetch >=> (return . (.symbol))))


getSideDetails : Side -> SideDetails
getSideDetails (Back val) = val
getSideDetails (Lay val) = val