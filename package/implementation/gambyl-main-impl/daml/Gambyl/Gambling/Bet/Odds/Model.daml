module Gambyl.Gambling.Bet.Odds.Model where

import DA.Numeric (castAndRound)

{- ------------------------------------------DATA TYPES------------------------------------------- -}

data OddType =
    Fractional (Numeric 2) |
    Decimal    (Numeric 2) |
    Moneyline  (Int)
    deriving (Show, Eq, Ord)


{- -------------------------------------CLASSES AND INSTANCES------------------------------------- -}

-- Converts an odd from one type to another
getOddFromTo : Multiplicative a => OddType -> (a -> OddType) -> Either Text OddType
getOddFromTo from to = convert from (to munit)

class Calculations a where
    getOdd          : a -> Numeric 2
    calculatePayout : a -> Numeric 2 -> Either Text (Numeric 2)
    inverse         : a -> a
    convert         : a -> a -> Either Text a
    validateOdds    : a -> Bool

instance Calculations OddType where

    getOdd (Fractional odd) = odd
    getOdd (Decimal odd)    = odd
    getOdd (Moneyline odd)  = castAndRound (intToDecimal $ odd)

    -- Calculates the payout of a bet based on the odd (with type) and stake
    -- Outputs an Either with an error message on the Left side and calculated payout on the Right
    calculatePayout d@(Decimal _)    stake = either Left ((flip calculatePayout) stake) $ getOddFromTo d Fractional
    calculatePayout m@(Moneyline _)  stake = either Left ((flip calculatePayout) stake) $ getOddFromTo m Fractional
    calculatePayout (Fractional odd) stake =
        if odd > 0.0 then
            if payout == 0.0 then
                Left oddTooLowMsg
            else Right payout
        else Left invalidFractionalMsg
        where payout = stake * odd

    validateOdds (Moneyline odd) =  odd >= 100 || odd < -100

    validateOdds (Decimal odd)  = odd > 1.0

    validateOdds (Fractional odd) =  odd > 0.0

    -- Inverts the odd to calculate the liability of Lay bets
    inverse (Fractional odd) = Fractional $ 1.0/odd
    inverse (Decimal odd)    = Decimal $ (1.0/(odd - 1.0)) + 1.0
    inverse (Moneyline odd)  | odd == 100   = Moneyline odd
                             | otherwise    = Moneyline $ negate odd


    -- Converts an odd from one type to another
    -- Disregards the value of the second (to) constructor
    -- Outputs the converted odd type, or an error message
    convert (Decimal val)    (Decimal _)        | val <= 1.0    = Left invalidDecimalMsg
                                                | otherwise     = Right $ Decimal val
    convert (Decimal val)    (Fractional _)     | val <= 1.0    = Left invalidDecimalMsg
                                                | otherwise     = Right $ Fractional (val - 1.0)
    convert (Decimal val)    (Moneyline _)      | val <= 1.0    = Left invalidDecimalMsg
                                                | val < 2.0     = Right $ Moneyline $ negate (truncate (100.0 * (1.0/(val - 1.0))))
                                                | otherwise     = Right $ Moneyline $ truncate (100.0 * (val - 1.0))

    convert (Fractional val) (Fractional _)     | val <= 0.0    = Left invalidFractionalMsg
                                                | otherwise     = Right $ Fractional val
    convert (Fractional val) (Decimal _)        | val <= 0.0    = Left invalidFractionalMsg
                                                | otherwise     = Right $ Decimal (val + 1.0)
    convert (Fractional val) (Moneyline _)      | val <= 0.0    = Left invalidFractionalMsg
                                                | val < 1.0     = Right $ Moneyline $ negate (truncate (100.0 * (1.0/val)))
                                                | otherwise     = Right $ Moneyline $ truncate (100.0 * val)

    convert (Moneyline val)  (Moneyline _)      | val < -100    = Right $ Moneyline val
                                                | val >= 100    = Right $ Moneyline val
                                                | otherwise     = Left invalidMoneylineMsg
    convert (Moneyline val) (Decimal _)         | val < -100    = Right $ Decimal $ castAndRound ((negate (100.0/intToDecimal val)) + 1.0)
                                                | val >= 100    = Right $ Decimal $ castAndRound ((intToDecimal val/100.0 + 1.0))
                                                | otherwise     = Left invalidMoneylineMsg
    convert (Moneyline val) (Fractional _)      | val < -100    = Right $ Fractional $ castAndRound (negate (100.0/intToDecimal val))
                                                | val >= 100    = Right $ Fractional $ castAndRound ((intToDecimal val/100.0))
                                                | otherwise     = Left invalidMoneylineMsg


isInverseOdd : OddType -> OddType -> Bool
isInverseOdd minOdd maxOdd = inverse minOdd == maxOdd


isDecimalOdd : OddType -> OddType -> Bool
isDecimalOdd  (Decimal _) (Decimal _) = True
isDecimalOdd  _ _ = False


invalidFractionalMsg    = "Fractional odd must be over 0.0"
invalidDecimalMsg       = "Decimal odd must be over 1.0"
invalidMoneylineMsg     = "Moneyline odd must be equal/over 100 or under -100"
oddTooLowMsg            = "Odd too low to have payout"


oddTypeToText : OddType -> Text
oddTypeToText (Decimal _) = "Decimal"
oddTypeToText (Fractional _) = "Fractional"
oddTypeToText (Moneyline _) = "Moneyline"


instance Additive OddType where
  (+) odd odd' = either error identity $ convert (Fractional (val + val')) odd
    where
      (Fractional val) = either error identity $ getOddFromTo odd Fractional
      (Fractional val') = either error identity $ getOddFromTo odd' Fractional

  (-) odd odd' = either error identity $ convert (Fractional (val - val')) odd
    where
      (Fractional val) = either error identity $ getOddFromTo odd Fractional
      (Fractional val') = either error identity $ getOddFromTo odd' Fractional

  aunit = (Fractional 0.01)