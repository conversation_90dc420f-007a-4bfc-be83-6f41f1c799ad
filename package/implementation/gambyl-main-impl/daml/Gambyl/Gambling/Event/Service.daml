{-# LANGUAGE MultiWayIf #-}
module Gambyl.Gambling.Event.Service where

import DA.Action ((>=>))
import DA.Exception (GeneralError(..), PreconditionFailed(PreconditionFailed))
import DA.Finance.Utils qualified as FinanceUtils (notNull, fetchAndArchive)
import DA.List ((\\))
import DA.Optional (fromOptional)
import DA.Set (Set)

import Marketplace.Utils

import EnetPulseIntegration.Events qualified as IntegrationEvents

import Gambyl.Gambling.Event.Model qualified as EventModel
import Gambyl.Gambling.Model qualified as GamblingModel
import Gambyl.Marketing.Service qualified as MarketingService


{- -------------------------------------------TEMPLATES------------------------------------------- -}

template Service
    with
      operator : Party
      provider : Party
      customer : Party
    where
        signatory operator, provider, customer

        key (operator, provider, customer) : (Party, Party, Party)
        maintainer key._1

        nonconsuming choice RequestEventOrigination : ContractId EventModel.EventInstrumentRequest
          with
            eventOrigin : EventModel.EventOrigin
          controller customer
          do EventModel.originateEventFrom eventOrigin operator provider customer


        nonconsuming choice RequestUpdateEventOdds : Either GamblingModel.ActionFailureCid (ContractId EventModel.EventInstrumentUpdateOutcomesOddsRequest)
          with
            eventCid      : ContractId EventModel.EventInstrument
            newOutcomes   : [EventModel.InputOutcomeOdd]
          controller customer
          do
            EventModel.EventInstrument{eventId, status, details, liveEvent} <- fetch eventCid
            try do
              -- Check if outcomes are the same as in the event instrument, only odds can be changed
              let
                mapOutcomes : (HasField "outcome" a IntegrationEvents.Outcome) => [a] -> [IntegrationEvents.Outcome]
                mapOutcomes = map (.outcome)

                newOutcomesOdds = EventModel.outcomeWithAllOdds newOutcomes

              if  | null newOutcomes -> assertFail "Input outcomes are empty"
                  | not (null (mapOutcomes newOutcomes \\ mapOutcomes details.outcomes)) ->
                      assertFail "Input outcomes do not match the event outcomes"
                  | null (newOutcomesOdds \\ details.outcomes) ->
                      assertFail "Input outcomes are equal to the event outcomes"
                  | otherwise -> do
                      let finalOutcomes = foldl (\ acc oldOutcomeOdds ->
                              optional (oldOutcomeOdds::acc) (::acc)
                                $ find ((== oldOutcomeOdds.outcome) . (.outcome)) newOutcomesOdds
                            ) [] details.outcomes
                      Right <$> create EventModel.EventInstrumentUpdateOutcomesOddsRequest with
                        operator, provider, customer, eventId, status, details, liveEvent
                        newOutcomes = finalOutcomes

            catch AssertionFailed msg ->
                    Left <$> createOrLookup GamblingModel.ActionFailure with
                      operator, provider, customer, actionId = eventId.label
                      action = GamblingModel.EventInstrumentUpdate, reason = msg


        nonconsuming choice RequestUpdateEventOutcomes : ContractId EventModel.EventInstrumentUpdateOutcomesOddsRequest
            with
                updateEventOutcomes : EventModel.UpdateEventOutcomes
                newOutcomes         : [EventModel.InputOutcomeOdd]
            controller customer
            do
                EventModel.EventInstrument{eventId, status, details, liveEvent} <-
                    case updateEventOutcomes of
                        EventModel.FromEvent eventCid -> fetch eventCid
                        EventModel.FromEventNoOutcomes eventNoOutcomesCid -> (fetch >=> return . (.event)) eventNoOutcomesCid

                let outcomes = EventModel.outcomeWithAllOdds newOutcomes
                create EventModel.EventInstrumentUpdateOutcomesOddsRequest with newOutcomes = outcomes, ..


        nonconsuming choice RequestCancelEvent : ContractId EventModel.EventInstrumentCancelRequest
            with
                eventLabel : Text
            controller customer
            do
                create EventModel.EventInstrumentCancelRequest with ..


        nonconsuming choice RequestReinstateEvent : ContractId EventModel.EventInstrumentReinstateRequest
            with
                eventLabel : Text
            controller customer
            do
                create EventModel.EventInstrumentReinstateRequest with ..


        nonconsuming choice RequestEventUpdate : Either GamblingModel.ActionFailureCid (ContractId EventModel.EventInstrumentUpdateRequest)
          with
            eventOrigin : EventModel.EventUpdateOrigin
            oldEventKey : EventModel.EventInstrumentKey
          controller customer
          do
            try do
              EventModel.EventInstrument{
                  operator, provider, details, integrationTime, liveEvent
              } <- lookupByKey @EventModel.EventInstrument oldEventKey
                >>= optional (assertFail "EventInstrument contract not found") fetch
              case details.origin of
                EventModel.Integration -> assertFail "Customer can't update event instrument"
                EventModel.Customer -> do
                    let newDetails = details with
                          eventStatus = eventOrigin.eventStatus
                          eventResults = eventOrigin.eventResults
                          eventTitle = traceId eventOrigin.eventTitle
                          startDate = eventOrigin.startDate
                          eventParticipants = eventOrigin.eventParticipants
                          market = eventOrigin.market
                          submarkets = eventOrigin.submarkets
                          outcomes = EventModel.outcomeWithAllOdds eventOrigin.outcomes

                    Right <$> create EventModel.EventInstrumentUpdateRequest with
                      operator, provider, customer, oldEventKey, integrationTime
                      details = newDetails, liveEvent
            catch AssertionFailed msg ->
                    Left <$> createOrLookup GamblingModel.ActionFailure with
                      operator, provider, customer, actionId = oldEventKey._3
                      action = GamblingModel.EventInstrumentUpdate, reason = msg

        nonconsuming choice RequestEventStatusUpdate
          : Either GamblingModel.ActionFailureCid (ContractId EventModel.EventInstrumentStatusUpdateRequest)
          with
            newEventStatus : IntegrationEvents.Status
            eventResults : Optional [IntegrationEvents.Result]
            oldEventKey : EventModel.EventInstrumentKey
          controller customer
          do
            let failure reason =
                  GamblingModel.ActionFailure with
                    operator, provider, customer
                    actionId = oldEventKey._3
                    action = GamblingModel.EventInstrumentUpdate
                    reason
            try do
              EventModel.EventInstrument{operator, provider, details} <-
                lookupByKey @EventModel.EventInstrument oldEventKey
                  >>= optional (assertFail "EventInstrument contract not found") fetch

              assertMsg "Finished event cannot have its status changed"
                $ details.eventStatus /= IntegrationEvents.Finished

              case details.origin of
                EventModel.Integration -> assertFail "Customer can't update event instrument"
                EventModel.Customer ->
                  Right <$> create EventModel.EventInstrumentStatusUpdateRequest with
                      operator, provider, customer, oldEventKey, newEventStatus, eventResults
            catch
                PreconditionFailed msg -> Left <$> createOrLookup (failure msg)
                AssertionFailed msg -> Left <$> createOrLookup (failure msg)


        nonconsuming choice RequestToggleFeaturedEvent : ContractId EventModel.EventInstrumentToggleFeaturedRequest
          with
            label : Text
          controller customer
          do
            create EventModel.EventInstrumentToggleFeaturedRequest with ..


        nonconsuming choice ApproveUpdateEventOddsOutcomes
          : Either GamblingModel.ActionFailureCid (ContractId EventModel.EventInstrument)
          with
            requestCid : ContractId EventModel.EventInstrumentUpdateOutcomesOddsRequest
            marketingServiceCid : ContractId MarketingService.Service
            hasBets : Bool
          controller provider
            do
              eventInstrumentUpdate <- fetch requestCid
              let archiveRequestAndFail reason = do
                    archive requestCid
                    createOrLookup GamblingModel.ActionFailure with
                      operator, provider, customer, actionId = eventInstrumentUpdate.eventId.label
                      action = GamblingModel.EventInstrumentUpdate, reason

              try do
                assertMsg "Odds cannot be updated on this event because it already has bets \
                  \approved/placed." $ not hasBets

                Right <$> case eventInstrumentUpdate.details.origin of
                  EventModel.Integration -> exercise requestCid
                    EventModel.ApproveEventUpdateOutcomesOddsRequest with manager = provider
                  EventModel.Customer -> do
                    let fetchPartyName noneFunc =
                          lookupByKey @EventModel.EventInstrumentNoOutcomes
                            (operator, provider, eventInstrumentUpdate.eventId.label) >>=
                              optional noneFunc (((.event.customer) <$>) . fetch)

                    eventCustomer <- getEventCustomer
                      (operator, provider, eventInstrumentUpdate.eventId.label) $ fetchPartyName
                        (fetchPartyName (
                          lookupByKey @EventModel.EventInstrument (operator, provider, eventInstrumentUpdate.eventId.label)
                            >>= optional (assertFail "EventInstrument contract not found") (fmap (.customer) . fetch)))



                    if (eventCustomer == customer)
                      then exercise requestCid
                        EventModel.ApproveEventUpdateOutcomesOddsRequest with manager = customer
                      else exercise marketingServiceCid
                        MarketingService.ApproveEventUpdateOutcomesOdds with requestCid
                  catch
                      PreconditionFailed msg -> Left <$> archiveRequestAndFail msg
                      AssertionFailed msg -> Left <$> archiveRequestAndFail msg


        nonconsuming choice ApproveCancelEventRequest
          : Either GamblingModel.ActionFailureCid (ContractId EventModel.EventInstrument)
          with
            requestCid : ContractId EventModel.EventInstrumentCancelRequest
          controller provider
          do
            eventId <- (.eventLabel) <$> fetch requestCid
            try do
              Right <$> exercise requestCid EventModel.ApproveCancelRequest with manager = customer
            catch AssertionFailed msg ->
                    Left <$> createOrLookup GamblingModel.ActionFailure with
                      operator, provider, customer, actionId = eventId
                      action = GamblingModel.EventInstrumentUpdate, reason = msg


        nonconsuming choice ApproveReinstateEventRequest
          : Either GamblingModel.ActionFailureCid (ContractId EventModel.EventInstrument)
          with
            requestCid : ContractId EventModel.EventInstrumentReinstateRequest
          controller provider
          do
            eventId <- (.eventLabel) <$> fetch requestCid
            try do
              Right <$> exercise requestCid EventModel.ApproveReinstateRequest with manager = customer
            catch AssertionFailed msg ->
                    Left <$> createOrLookup GamblingModel.ActionFailure with
                      operator, provider, customer, actionId = eventId
                      action = GamblingModel.EventInstrumentUpdate, reason = msg


        nonconsuming choice ApproveEventUpdate
          : Either GamblingModel.ActionFailureCid (ContractId EventModel.EventInstrument)
          with
            requestCid : ContractId EventModel.EventInstrumentUpdateRequest
            marketingServiceCid : ContractId MarketingService.Service
          controller provider
          do
            EventModel.EventInstrumentUpdateRequest{oldEventKey} <- fetch requestCid
            try do
              eventCustomer <- getEventCustomer (operator, provider, oldEventKey._3)
                ((._2.customer) <$> fetchByKey @EventModel.EventInstrument (operator, provider, oldEventKey._3))

              if (eventCustomer == customer)
                then Right <$> exercise requestCid EventModel.Approve_CustomerUpdate with manager = customer
                else Right <$> (reduceOrRethrowError =<< exercise marketingServiceCid MarketingService.ApproveEventUpdate with requestCid)
            catch AssertionFailed msg ->
                      Left <$> createOrLookup GamblingModel.ActionFailure with
                          operator, provider, customer, actionId = oldEventKey._3
                          action = GamblingModel.EventInstrumentStatusUpdate, reason = msg


        nonconsuming choice ApproveEventStatusUpdate
          : Either GamblingModel.ActionFailureCid (ContractId EventModel.EventInstrument)
          with
            requestCid : ContractId EventModel.EventInstrumentStatusUpdateRequest
            marketingServiceCid : ContractId MarketingService.Service
          controller provider
          do
            EventModel.EventInstrumentStatusUpdateRequest{newEventStatus, eventResults, oldEventKey} <-
              FinanceUtils.fetchAndArchive requestCid

            try do
              EventModel.EventInstrument{details, integrationTime, liveEvent} <-
                lookupByKey @EventModel.EventInstrument oldEventKey >>=
                  optional (assertFail "Instrument not found") fetch

              eventCustomer <- getEventCustomer
                (operator, provider, oldEventKey._3)
                ((._2.customer) <$> fetchByKey @EventModel.EventInstrument (operator, provider, oldEventKey._3))

              let newDetails = details with
                    eventStatus = newEventStatus
                    eventResults = fromOptional [] eventResults

              updatedRequestCid <- create EventModel.EventInstrumentUpdateRequest with
                                    operator, provider, customer, oldEventKey, integrationTime
                                    liveEvent, details = newDetails

              Right <$> if (eventCustomer == customer)
                then
                  exercise updatedRequestCid EventModel.Approve_CustomerUpdate with manager = customer
                else
                  reduceOrRethrowError =<< exercise marketingServiceCid
                    MarketingService.ApproveEventUpdate with requestCid = updatedRequestCid

            catch GeneralError msg -> do
                    Left <$> createOrLookup GamblingModel.ActionFailure with
                        operator, provider, customer, actionId = oldEventKey._3
                        action = GamblingModel.EventInstrumentStatusUpdate, reason = msg


        nonconsuming choice RejectEventUpdate : GamblingModel.ActionFailureCid
          with
            requestCid : ContractId EventModel.EventInstrumentUpdateRequest
            reason : Text
          controller provider
          do
            EventModel.EventInstrumentUpdateRequest{oldEventKey} <-
              FinanceUtils.fetchAndArchive requestCid

            createOrLookup GamblingModel.ActionFailure with
                operator, provider, customer, actionId = oldEventKey._3
                action = GamblingModel.EventInstrumentStatusUpdate, reason


        nonconsuming choice ApproveEventOrigination
          : Either
              GamblingModel.ActionFailureCid
              ((Either (ContractId EventModel.EventInstrumentNoOutcomes) (ContractId EventModel.EventInstrument))
                , ContractId EventModel.MarketMap
              )
          with
            requestCid  : ContractId EventModel.EventInstrumentRequest
            managers    : Set Party
          controller provider
          do
            EventModel.EventInstrumentRequest{details, eventId} <- fetch requestCid
            let actionId = eventId.label

            try do
              assertMsg "Event has no Participants" $ FinanceUtils.notNull details.eventParticipants
              (eventCid, marketCid) <- exercise requestCid
                EventModel.Approve_Origination with managers, featured = False
              -- Same as gambling service approve event origination
              Right . (, marketCid) <$>
                try do Left <$> exercise eventCid EventModel.SignalEventNoOutcome
                catch AssertionFailed _ -> pure $ Right eventCid

            catch AssertionFailed msg -> do
                  Left <$> createOrLookup GamblingModel.ActionFailure with
                    operator, provider, customer, action = GamblingModel.EventInstrumentOrigination
                    actionId, reason = msg

        nonconsuming choice ApproveToggleFeaturedEvent : ContractId EventModel.EventInstrument
          with
            requestCid : ContractId EventModel.EventInstrumentToggleFeaturedRequest
          controller provider
          do
            exercise requestCid EventModel.ApproveToggleFeatured


template Offer
  with
    operator : Party
    provider : Party
    customer : Party
  where
    signatory operator, provider
    observer customer

    key (operator, provider, customer) : (Party, Party, Party)
    maintainer key._1

    choice Accept : (ContractId Service)
      controller customer
      do createOrLookup Service with operator, provider, customer

    choice Decline : ()
      controller customer
      do pure ()

    choice Withdraw : ()
      controller provider
      do pure ()


template Request
  with
    customer : Party
    provider : Party
  where
    signatory customer
    observer provider

    key (customer, provider) : (Party, Party)
    maintainer key._1

    choice Cancel : ()
      controller customer
      do pure ()

    choice Reject : ()
      controller provider
      do pure ()

    choice Approve : ContractId Service
      with
        operator : Party
      controller operator, provider
      do createOrLookup Service with operator, customer, provider


{- -------------------------------------------FUNCTIONS------------------------------------------- -}

getEventCustomer : (Party, Party, Text) -> (Update Party) -> Update Party
getEventCustomer eventKey fetchCustomerFunc = do
    lookupByKey @EventModel.EventInstrument eventKey >>=
        optional
            (fetchCustomerFunc)
            (((.customer) <$>) . fetch)


reduceOrRethrowError : Either GamblingModel.ActionFailureCid a -> Update a
reduceOrRethrowError (Right val) = pure val
reduceOrRethrowError (Left failureCid) = assertFail . (.reason) =<< FinanceUtils.fetchAndArchive failureCid