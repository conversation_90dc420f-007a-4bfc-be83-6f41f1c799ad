{-# LANGUAGE MultiWayIf #-}
module Gambyl.Gambling.Event.Model where

import DA.Date qualified as Date
import DA.Either qualified as Either (rights)
import DA.Finance.Types (Id(..))
import DA.Finance.Utils (fetchAndArchive)
import DA.Foldable qualified as Foldable
import DA.List qualified as List
import DA.Map qualified as Map
import DA.Numeric qualified as Numeric (castAndRound)
import DA.Optional (whenSome)
import DA.Set (Set)
import DA.Set qualified as Set
import DA.Text qualified as Text
import DA.Time (time)
import DA.Time qualified as Time (days, addRelTime)

import EnetPulseIntegration.Events qualified as IntegrationEvents

import Gambyl.Gambling.Bet.Odds.Model qualified as Odds
import Gambyl.Gambling.Model qualified as GamblingModel
import Gambyl.Utils qualified as Utils

{- -------------------------------------------TEMPLATES------------------------------------------- -}

template EventInstrumentRequest
  with
    operator : Party
    provider : Party
    customer : Party
    eventId : Id
    details : Details
    integrationTime : Optional Time
    liveEvent : Text
  where
    signatory operator, provider, customer

    key (operator, provider, eventId.label) : EventInstrumentKey
    maintainer key._1

    ensure
      IntegrationEvents.validOutcomesResults this (.details.outcomes) (.details.eventResults)
      && validOdds this.details.outcomes

    choice Approve_Origination : (ContractId EventInstrument, ContractId MarketMap)
      with
          featured : Bool
          managers : Set Party
      controller operator, provider
      do
        GamblingModel.GlobalGamblingConfiguration{
          minOdd, maxOdd, public
        } <- lookupByKey @GamblingModel.GlobalGamblingConfiguration (operator, provider)
          >>= optional (assertFail "GlobalGamblingConfiguration contract not found") fetch

        let Details{market, submarkets, geography, outcomes} = details

        Foldable.forA_ outcomes $ \ OutcomeOdds{outcome, odds} -> do
          let
            isDecimalOdd (Odds.Decimal _) = True
            isDecimalOdd _ = False
            outcomeDesc = IntegrationEvents.long outcome
            partialMsg = "Odd for outcome " <> outcomeDesc

          decimalOdd <- optional
            (assertFail $ "Decimal Odd not found for outcome " <> outcomeDesc)
            pure
            $ find isDecimalOdd (Set.toList odds)

          assertMsg (partialMsg <> " is lower than min odd of " <> show minOdd) $ decimalOdd >= minOdd
          assertMsg (partialMsg <> " is greater than max odd of " <> show maxOdd) $ decimalOdd <= maxOdd

        now <- getTime

        eventInstrument <- create EventInstrument with
          operator, provider, customer, public, managers
          eventId, details, integrationTime, liveEvent
          featured    = False
          status      = Active
          createdAt   = now
          lastUpdate  = now

        marketMapCid <- lookupByKey @MarketMap (operator, provider) >>= optional
          (assertFail "Market map contract not found")
          (`exercise` AddToMap with
              market, geo = geography, submarketList = submarkets)

        return (eventInstrument, marketMapCid)


template EventInstrumentUpdateRequest
  with
    operator            : Party
    provider            : Party
    customer            : Party
    oldEventKey         : EventInstrumentKey
    details             : Details
    integrationTime     : Optional Time
    liveEvent           : Text
  where
    signatory operator, provider, customer

    ensure IntegrationEvents.validOutcomesResults this (.details.outcomes) (.details.eventResults)

    choice Approve_Update : ContractId EventInstrument
      controller operator, provider
      do
        Utils.rightOrFail =<< Utils.eitherContractExists @EventInstrument oldEventKey "Event Instrument contract not found"
          (\ eventInstrumentCid -> do

              now <- getTime
              oldEvent@EventInstrument{status, details = oldDetails} <- fetch eventInstrumentCid
              (_, GamblingModel.GlobalGamblingConfiguration{daysPostponedEventExpires}) <- fetchByKey @GamblingModel.GlobalGamblingConfiguration (operator, provider)
              let
                -- Checks if events is postponed to a date later than the set number of days found on the GlobalGamblingConfiguration
                newTime = time (Date.toDateUTC this.details.startDate) 0 0 0
                oldTime = time (Date.toDateUTC oldEvent.details.startDate) 0 0 0
                isPostponeToBeCanceled = newTime >= (Time.addRelTime oldTime (Time.days daysPostponedEventExpires))
                -- ^

                newStatus
                  | this.details.eventStatus `elem` [IntegrationEvents.Finished, IntegrationEvents.Cancelled, IntegrationEvents.Postponed]
                    || isPostponeToBeCanceled = Expired
                  | status == Expired
                    && this.details.eventStatus == IntegrationEvents.NotStarted
                    && now < this.details.startDate = Active
                  | otherwise = status

                newEventStatus
                  | isPostponeToBeCanceled = IntegrationEvents.Cancelled
                  | otherwise = this.details.eventStatus

                outcomeTypeNOrderMapFromEventDetails eventDetails =
                  let outcomeToTypeAndOrder IntegrationEvents.Outcome{type_, order} = (type_, order)
                  in Map.fromList [(outcomeToTypeAndOrder outcomeOdd.outcome, outcomeOdd) | outcomeOdd <- eventDetails.outcomes]

                oldOutcomesMap = outcomeTypeNOrderMapFromEventDetails oldDetails
                newOutcomesMap = outcomeTypeNOrderMapFromEventDetails this.details

                -- Function to run when outcome is present in oldOutcomesMap
                -- If the outcome is already present in the event, we keep it
                onlyInOldOutcomes _ outcome = Some outcome
                -- Function to run when outcome is present in newOutcomesMap
                -- If the outcome is only present in the update, we are ignoring all Over/Under and
                -- ThreeWayHandicap outcomes, but take all others unless there are non Over/Under or
                -- ThreeWayHandicap already present
                onlyInNewOutcomes ((IntegrationEvents.OverUnder _), _) val = do
                    if doesOutcomeExist (IntegrationEvents.OverUnder 1.0) oldDetails.outcomes
                      then None
                      else Some val
                onlyInNewOutcomes ((IntegrationEvents.ThreeWayHandicap _), _) val = do
                    if doesOutcomeExist (IntegrationEvents.ThreeWayHandicap 1.0) oldDetails.outcomes
                      then None
                      else Some val
                -- | It is possible that the order field is different on the outcome which could be
                -- considered a new outcome and so it might occur that we might end up with two equal
                -- outcomes with different orders.
                -- If in new outcomes the counterpart of twoway or threeway is present ignore it
                onlyInNewOutcomes (IntegrationEvents.ThreeWay, _) val = do
                  if doesOutcomeExist IntegrationEvents.TwoWay oldDetails.outcomes
                    then None
                    else Some val
                onlyInNewOutcomes (IntegrationEvents.TwoWay, _) val = do
                  if doesOutcomeExist IntegrationEvents.ThreeWay oldDetails.outcomes
                    then None
                    else Some val
                onlyInNewOutcomes _ val = Some val
                -- Function to run when outcome is present in both oldOutcomesMap and newOutcomesMap
                -- If the same outcome is already present in the event and is coming in the update,
                -- we keep the most up-to-date outcome (the one found in the update)
                inBothOutcomes _ _ outcome = Some outcome
                -- TODO: Make outcome map keys unique only on Type and Order to account for participant changes
                mergedOutcomes = Map.merge onlyInOldOutcomes onlyInNewOutcomes inBothOutcomes
                                  oldOutcomesMap newOutcomesMap

                _ = mergedOutcomes

                outcomeListFromBuiltOutcomes : Map.Map (IntegrationEvents.OutcomeType, Int) OutcomeOdds -> OutcomeTupleL
                outcomeListFromBuiltOutcomes outcomeTypeNOrderMap =
                  [(outcomeOdd.outcome, outcomeOdd.odds) | (_, outcomeOdd) <- Map.toList outcomeTypeNOrderMap]

                outcomesToAdd = joinTwoOrThreeway
                  (outcomeListFromBuiltOutcomes mergedOutcomes)
                  (outcomeListFromBuiltOutcomes newOutcomesMap)

              assertMsg "Non Existent Partipicipant Details Received" $ checkIfResultParticipantExists this.details

              let
                -- | New details are constructed from update details except status and outcomes are modified
                -- and market, submarkets are kept as the original
                newDetails = this.details with
                  eventStatus = newEventStatus
                  outcomes = List.sortOn (.outcome.type_) $ Prelude.map (uncurry OutcomeOdds) $ outcomesToAdd
                  market = oldDetails.market
                  submarkets = oldDetails.submarkets
                  origin = oldDetails.origin

              archive eventInstrumentCid
              Right <$> create oldEvent with
                status = newStatus
                details = newDetails
          )


    nonconsuming choice Approve_CustomerUpdate : ContractId EventInstrument
      with
        manager : Party
      controller manager
      do
        case details.origin of
          Integration -> assertFail "Customer can't update event instrument"
          Customer ->
            Utils.rightOrFail =<< Utils.eitherContractExists @EventInstrument oldEventKey "Event Instrument contract not found"
              (\ eventInstrumentCid -> do
                  oldEvent@EventInstrument{status} <- fetchAndArchive eventInstrumentCid

                  let newStatus = if details.eventStatus `elem` [IntegrationEvents.Finished, IntegrationEvents.Cancelled, IntegrationEvents.Postponed]
                        then Expired
                        else status

                  archive self
                  Right <$> create oldEvent with
                    status                    = newStatus
                    details.eventStatus       = details.eventStatus
                    details.eventResults      = details.eventResults
                    details.eventTitle        = details.eventTitle
                    details.startDate         = details.startDate
                    details.market            = details.market
                    details.submarkets        = details.submarkets
                    details.outcomes          = details.outcomes
                    details.eventParticipants = details.eventParticipants
              )


template EventInstrumentStatusUpdateRequest
    with
        operator            : Party
        provider            : Party
        customer            : Party
        oldEventKey         : EventInstrumentKey
        newEventStatus      : IntegrationEvents.Status
        eventResults        : Optional [IntegrationEvents.Result]
    where
        signatory operator, provider, customer

        ensure validateStatusUpdate this


template EventInstrument
  with
    operator : Party
    provider : Party
    customer : Party
    public : Set Party
    managers : Set Party
    eventId : Id
    featured : Bool
    details : Details
    status : Status
    createdAt : Time
    lastUpdate : Time
    integrationTime : Optional Time
    liveEvent : Text
  where
    signatory operator, provider, customer
    observer public, managers

    key (operator, provider, eventId.label) : EventInstrumentKey
    maintainer key._1

    ensure IntegrationEvents.validOutcomesResults this (.details.outcomes) (.details.eventResults)
            && validOdds this.details.outcomes && Text.length (eventId.label) <= 9


    choice ToggleFeatured : ContractId EventInstrument
      controller operator, provider
      do
        lastUpdate <- getTime
        create this with featured = not featured, lastUpdate


    choice UpdateOddsAndOutcomes : ContractId EventInstrument
      with
        newOutcomes : [OutcomeOdds]
      controller operator, provider
      do
        lastUpdate <- getTime
        create this with details = details {outcomes = newOutcomes}, lastUpdate


    choice ArchiveEvent : ()
      controller operator, provider
      do
        gamblingConfig <- snd <$> fetchByKey @GamblingModel.GlobalGamblingConfiguration (operator, provider)
        today <- Date.toDateUTC <$> getTime

        let archivalDate = Date.toDateUTC $ Time.addRelTime lastUpdate (Time.days gamblingConfig.archiveEventDays)
        debug $ "Archive Event Model: " <> show today <> " " <> show archivalDate

        assertMsg ("Event is neither Expired or Cancelled") $ status == Expired
        assertMsg ("It has not passed " <> show (gamblingConfig.archiveEventDays) <> " days required for the Event to be archived") $ today >= archivalDate

        pure ()


    choice ExpireEvent : ContractId EventInstrument
      controller operator, provider
      do
        now <- getTime
        assertMsg "Event is not Active" $ status == Active
        assertMsg "Current date is before the Event start date" $ now >= details.startDate
        create this with status = Expired, lastUpdate = now


    choice CancelEvent : ContractId EventInstrument
        controller operator, provider
        do
          assertMsg "Inactive Event cannot be cancelled" $ status == Active
          lastUpdate <- getTime
          create this with details.eventStatus = IntegrationEvents.Cancelled, lastUpdate


    -- This is to restore the event to a previous effective state (NotStarted)
    choice ReinstateEvent : ContractId EventInstrument
      controller operator, provider
      do
        currentTime <- getTime
        assertMsg "Inactive Event cannot be reinstated" $ status == Active
        assertMsg "Event cannot be reinstantiated because its start date has already passed" $ details.startDate > currentTime
        create this with details.eventStatus = IntegrationEvents.NotStarted, lastUpdate = currentTime


    choice FinishEvent : ContractId EventInstrument
        controller operator, provider
        do
            create this with details.eventStatus = IntegrationEvents.Finished


    choice SignalEventNoOutcome : ContractId EventInstrumentNoOutcomes
      controller operator, provider
      do
        assertMsg "Event has outcomes, can't be hidden" $ null details.outcomes
        create EventInstrumentNoOutcomes with event = this


template EventInstrumentToggleFeaturedRequest
    with
        operator            : Party
        provider            : Party
        customer            : Party
        label               : Text
    where
        signatory operator, provider, customer

        choice ApproveToggleFeatured: ContractId EventInstrument
            controller provider
            do
                exerciseByKey @EventInstrument (operator, provider, label) ToggleFeatured


template EventInstrumentCancelRequest
  with
    operator            : Party
    provider            : Party
    customer            : Party
    eventLabel          : Text
  where
    signatory operator, provider, customer

    choice ApproveCancelRequest : ContractId EventInstrument
      with
        manager : Party
      controller manager
      do
        exerciseByKey @EventInstrument (operator, provider, eventLabel) CancelEvent


template EventInstrumentReinstateRequest
  with
    operator            : Party
    provider            : Party
    customer            : Party
    eventLabel          : Text
  where
    signatory operator, provider, customer

    choice ApproveReinstateRequest : ContractId EventInstrument
      with
        manager : Party
      controller manager
      do
        exerciseByKey @EventInstrument (operator, provider, eventLabel) ReinstateEvent


template EventInstrumentUpdateOutcomesOddsRequest
    with
        operator            : Party
        provider            : Party
        customer            : Party
        eventId             : Id
        status              : Status
        details             : Details
        newOutcomes         : [OutcomeOdds]
        liveEvent           : Text
    where
        signatory operator, provider, customer

        ensure IntegrationEvents.validOutcomesResults this (.details.outcomes) (.details.eventResults) && validOdds this.details.outcomes


        choice ApproveEventUpdateOutcomesOddsRequest : ContractId EventInstrument
            with
                manager : Party
            controller manager
            do
                lookupByKey @EventInstrumentNoOutcomes (operator, provider, eventId.label) >>=
                    optional
                        (exerciseByKey @EventInstrument (operator, provider, eventId.label) UpdateOddsAndOutcomes with newOutcomes)
                        (\ eventInstrumentNoOutcomesCid ->
                            lookupByKey @EventInstrument (operator, provider, eventId.label) >>=
                                optional
                                (exercise eventInstrumentNoOutcomesCid AddOutcomes with newOutcomes)
                                (\ _ -> do
                                    archive eventInstrumentNoOutcomesCid
                                    exerciseByKey @EventInstrument (operator, provider, eventId.label) UpdateOddsAndOutcomes with newOutcomes
                                )
                        )


template EventInstrumentNoOutcomes
    with
        event : EventInstrument
    where

        signatory event.operator, event.provider, event.customer
        observer event.managers

        key (event.operator, event.provider, event.eventId.label) : EventInstrumentKey
        maintainer key._1

        ensure IntegrationEvents.validOutcomesResults event (.details.outcomes) (.details.eventResults)

        choice AddOutcomes : ContractId EventInstrument
            with
                newOutcomes : [OutcomeOdds]
            controller event.provider
            do
                let EventInstrument{..} = event
                create EventInstrument with details = details {outcomes = newOutcomes}, ..

        choice ArchiveEventNoOutcomes : ()
            controller event.provider, event.operator
            do
                gamblingConfig <- snd <$> fetchByKey @GamblingModel.GlobalGamblingConfiguration (event.operator, event.provider)
                today <- Date.toDateUTC <$> getTime
                let archivalDate = Date.toDateUTC $ Time.addRelTime event.lastUpdate (Time.days gamblingConfig.archiveEventDays)

                assertMsg ("Event is not Expired") $ event.status == Expired
                assertMsg ("It has not passed " <> show (gamblingConfig.archiveEventDays) <> " days required for the Event to be archived") $ today >= archivalDate

                archive self


template MarketMap
    with
        operator : Party
        provider : Party
        map : Map.Map Market (Map.Map IntegrationEvents.Geography [Submarket])
        public : Set Party
    where
        signatory operator, provider
        observer public

        key (operator, provider) : (Party, Party)
        maintainer key._1, key._2

        nonconsuming choice UpdateMap : ContractId MarketMap
            with
                entries : Map.Map Market (Map.Map IntegrationEvents.Geography [Submarket])
            controller provider
            do
                if map == entries then return self
                else do
                    archive self
                    create MarketMap with map = entries, ..

        nonconsuming choice AddToMap : ContractId MarketMap
            with
                market : Market
                geo : IntegrationEvents.Geography
                submarketList : [Submarket]

            controller provider
            do
                let
                    submap = Map.fromList [(geo, submarketList)]
                    map' = addToMap map (market, submap)

                if map == map' then return self
                else do
                    archive self
                    create MarketMap with map = map', ..

{- ------------------------------------------DATA TYPES------------------------------------------- -}

data Market =
    Sport Text          |
    Politics            |
    Entertainment       |
    Other_Market Text
    deriving (Show, Eq, Ord)


data Submarket =
    Tournament Text         |
    Other_Submarket Text
    deriving (Show, Eq, Ord)


data Status =
    Expired     |
    Active
    deriving (Show, Eq)


data OutcomeOdds = OutcomeOdds with
        outcome : IntegrationEvents.Outcome
        odds    : Set Odds.OddType
    deriving (Show, Eq)


data Details = Details
    with
        eventTitle          : Map.Map Text Text
        -- ^ Title of the event in a map of different languages where at least "en_us" must be included
        market              : Market
        submarkets          : [Submarket]
        geography           : IntegrationEvents.Geography
        outcomes            : [OutcomeOdds]
        -- ^ List of all outcomes available for an event
        -- Each element includes the odd for that outcome, in all supported formats
        eventParticipants   : [IntegrationEvents.Participant]
        eventGame           : Optional Text
        eventStatus         : IntegrationEvents.Status
        eventResults        : [IntegrationEvents.Result]
        startDate           : Time
        description         : Text
        origin              : OriginType
    deriving (Show, Eq)


data OriginType =
    Customer        |
    Integration
    deriving (Show, Eq)


data InputOutcomeOdd = InputOutcomeOdd with
        outcome : IntegrationEvents.Outcome
        odd     : Odds.OddType
    deriving (Show, Eq)


data EventOrigin =
    CustomerEvent with
        assetLabel          : Text
        -- ^ Unique Identifier for event
        eventTitle          : Map.Map Text Text
        -- ^ Title of the event in a map of different languages where at least "en_us" must be included
        market              : Market
        submarkets          : [Submarket]
        geography           : IntegrationEvents.Geography
        outcomes            : [InputOutcomeOdd]
        -- ^ List of all outcomes available for an event
        -- Each element includes the odd for that outcome in one of the supported format
        eventParticipants   : [IntegrationEvents.Participant]
        eventStatus         : IntegrationEvents.Status
        eventResults        : [IntegrationEvents.Result]
        startDate           : Time
        description         : Text
    | IntegrationEvent with
        eventCid    : ContractId IntegrationEvents.EventInstrument
    deriving (Show, Eq)


data EventUpdateOrigin =
    CustomerUpdate with
        assetLabel          : Text
        -- ^ Unique Identifier for event
        eventTitle          : Map.Map Text Text
        -- ^ Title of the event in a map of different languages where at least "en_us" must be included
        market              : Market
        submarkets          : [Submarket]
        outcomes            : [InputOutcomeOdd]
        -- ^ List of all outcomes available for an event
        -- Each element includes the odd for that outcome in one of the supported format
        eventParticipants   : [IntegrationEvents.Participant]
        eventStatus         : IntegrationEvents.Status
        eventResults        : [IntegrationEvents.Result]
        startDate           : Time
    | IntegrationUpdate with
        eventCid    : ContractId IntegrationEvents.EventInstrumentUpdate
    deriving (Show, Eq)


data UpdateEventOutcomes =
        FromEvent (ContractId EventInstrument) |
        FromEventNoOutcomes (ContractId EventInstrumentNoOutcomes)
    deriving (Eq, Show)

{- -----------------------------------------CUSTOM TYPES------------------------------------------ -}

type EventInstrumentKey = (Party, Party, Text)

type OutcomeTuple = (IntegrationEvents.Outcome, Set Odds.OddType)
type OutcomeTupleL = [OutcomeTuple]
type OutcomeTriple = (OutcomeTupleL, OutcomeTupleL, OutcomeTupleL)
-- ^ Type intended to use in partition with format (TwoWay, ThreeWay, Remaining)

{- ---------------------------------------HELPER FUNCTIONS---------------------------------------- -}

originateEventFrom : EventOrigin -> Party -> Party -> Party -> Update (ContractId EventInstrumentRequest)
originateEventFrom
  CustomerEvent{
    assetLabel = label, description, market, submarkets, geography, outcomes
    , eventParticipants, eventTitle, eventStatus, eventResults, startDate
  } operator provider customer = do

      let tournaments = filter (\case
                                  Tournament _ -> True
                                  Other_Submarket _ -> False) submarkets

      assertMsg "Error: No Tournament given" $ (length tournaments) > 0
      assertMsg "Error: More than one Tournament given" $ (length tournaments) == 1

      let [Tournament txt] = tournaments
      assertMsg "Error: Given Tournament is empty" $ txt /= ""

      let eventId = Id with signatories = Set.fromList [provider, customer]; label; version = 0

      lookupByKey @EventInstrument (operator, provider, eventId.label) >>=
        (`whenSome` \ _ -> assertFail "EventInstrument with Same Keys Already Exists" )

      create EventInstrumentRequest with
        operator, provider, customer, eventId
        integrationTime = None, liveEvent = "no"
        details = Details with
          geography, market, submarkets, eventParticipants
          eventGame = None, eventTitle, eventStatus, eventResults
          startDate, origin = Customer, description
          outcomes = outcomeWithAllOdds outcomes


originateEventFrom IntegrationEvent{eventCid} operator provider customer = do

  IntegrationEvents.EventInstrument{
    eventId = label, status, results, integrationTime, liveEvent,
    eventDetails = IntegrationEvents.Details{
      sportFK, tournamentStageName, geography, eventParticipants, startDate, outcomes, eventGame,
      eventTitle = integrationTitle
    }
  } <- exercise eventCid IntegrationEvents.RetrieveArchive_Original with executingParty = provider

  let
    eventId = Id with signatories = Set.fromList [provider, customer]; label; version = 0
    filteredOutcomes = IntegrationEvents.filterOverUnderAndThreeWayHandicap outcomes
    description = "Sport " <> sportFK <> " event for " <> tournamentStageName <> " ("
                    <> geography <> "), between "
                    <> (GamblingModel.parseParticipants eventParticipants) <> ", at "
                    <> show startDate

  lookupByKey @EventInstrument (operator, provider, eventId.label) >>=
    (`whenSome` \ _ -> assertFail "EventInstrument with Same Keys Already Exists" )

  create EventInstrumentRequest with
    details = Details with
      geography, eventParticipants, eventGame, startDate
      market          = Sport sportFK
      submarkets      = [Tournament tournamentStageName]
      outcomes        = outcomeWithAllOddsFromNumeric filteredOutcomes
      eventStatus     = status
      eventResults    = results
      origin          = Integration
      eventTitle      = Map.fromList [(show Utils.EN_US, integrationTitle)]
      description
    integrationTime = Some integrationTime
    operator, provider, customer, eventId, liveEvent


updateEventFrom : EventUpdateOrigin -> Party -> Party -> Party -> EventInstrumentKey -> Update (Either Text (ContractId EventInstrumentUpdateRequest))
updateEventFrom CustomerUpdate{assetLabel, market, submarkets, outcomes, eventParticipants, eventTitle, eventStatus, eventResults, startDate} operator provider customer oldEventKey = do

    Utils.eitherContractExists @EventInstrument oldEventKey "Event Instrument Contract not found"
        (\ oldEventInstrumentCid -> do

            EventInstrument{eventId = Id{label}, details = oldDetails} <- fetch oldEventInstrumentCid

            if label /= assetLabel
              then return $ Left "Event update request doesn't match existing event"
              else
                Right <$> create EventInstrumentUpdateRequest with
                  operator, provider, customer, oldEventKey, liveEvent = "no", integrationTime = None
                  details = oldDetails with
                    market, submarkets, eventParticipants, eventTitle, eventStatus, eventResults, startDate
                    outcomes = outcomeWithAllOdds outcomes
                    origin = Customer
                    eventGame = None
        )

updateEventFrom IntegrationUpdate{eventCid} operator provider customer oldEventKey = do

    Utils.eitherContractExists @EventInstrument oldEventKey "Event Instrument Contract not found"
        (\ oldEventInstrumentCid -> do

            EventInstrument{
              eventId = Id{label}, integrationTime = oldIntegrationTime, details = oldDetails
            } <- fetch oldEventInstrumentCid

            IntegrationEvents.EventInstrumentUpdate{
              eventId, status, results, liveEvent, integrationTime,
              eventDetails = IntegrationEvents.Details{
                sportFK, tournamentStageName, geography, eventParticipants, eventGame, startDate,
                eventTitle = integrationTitle, outcomes = newOutcomes
              }
            } <- exercise eventCid IntegrationEvents.RetrieveArchive_Update with executingParty = provider

            if
              | label /= eventId ->
                return $ Left "Event update request doesn't match existing event"

              | optional False (integrationTime <) oldIntegrationTime ->
                return $ Left "Event Instrument can not be updated due to integration Time"

              | otherwise -> do
                let filteredOutcomes = IntegrationEvents.filterOverUnderAndThreeWayHandicap newOutcomes

                Right <$> create EventInstrumentUpdateRequest with
                  operator, provider, customer, oldEventKey, liveEvent
                  integrationTime = Some integrationTime
                  details = oldDetails with
                    geography, eventParticipants, eventGame, startDate
                    market          = Sport sportFK
                    submarkets      = [Tournament tournamentStageName]
                    outcomes        = outcomeWithAllOddsFromNumeric filteredOutcomes
                    eventStatus     = status
                    eventResults    = results
                    origin          = Integration
                    eventTitle      = Map.fromList [(show Utils.EN_US, integrationTitle)]
        )


mapOutcomeWithAllOdds : (HasField "outcome" a IntegrationEvents.Outcome, HasField "odd" a b) => (b -> [Either Text Odds.OddType]) -> [a] -> [OutcomeOdds]
mapOutcomeWithAllOdds mapFunction = Prelude.map fromGeneric
    where fromGeneric genericOutcome = OutcomeOdds with
                                            outcome = genericOutcome.outcome
                                            odds    = Set.fromList (Either.rights ((mapFunction genericOutcome.odd)))


reverseMappedOutcomeWithAllOdds : (HasField "outcome" a IntegrationEvents.Outcome, HasField "odds" a (Set Odds.OddType)) => [a] -> [InputOutcomeOdd]
reverseMappedOutcomeWithAllOdds list = Prelude.map (\oddOutcome ->
    InputOutcomeOdd with
        outcome = oddOutcome.outcome
        odd = List.head $ Set.toList oddOutcome.odds) list

mapAllOdds odd = [Odds.getOddFromTo odd Odds.Decimal, Odds.getOddFromTo odd Odds.Fractional, Odds.getOddFromTo odd Odds.Moneyline]

outcomeWithAllOdds              = mapOutcomeWithAllOdds mapAllOdds
outcomeWithAllOddsFromNumeric   = mapOutcomeWithAllOdds (mapAllOdds . Odds.Decimal . Numeric.castAndRound)

validOdds : [OutcomeOdds] -> Bool
validOdds =  Foldable.all (Foldable.all Odds.validateOdds . (.odds))

-- Function to check if certain outcome type already exists in the event
doesOutcomeExist : IntegrationEvents.OutcomeType -> [OutcomeOdds] -> Bool
doesOutcomeExist _  [] = False
doesOutcomeExist outcomeType (outcome1::rest) =
    (IntegrationEvents.sameOutcomeType outcomeType outcome1.outcome.type_)
    || (doesOutcomeExist outcomeType rest)


-- check If EnetPulse Integration did not send incorrect Partipicipant Details
checkIfResultParticipantExists : Details -> Bool
checkIfResultParticipantExists Details{eventResults = []} = True
checkIfResultParticipantExists Details{eventResults, eventParticipants}  = any (`validateParticipantOrder` participantOrderList) eventResults
    where
        participantOrderList = Prelude.map (.order) eventParticipants


-- check If overUnder Type Results have participantOrder set to 0, and if other Results types have existent participantOrder
-- OverUnder and 3Way Draw Outcome Results have no Participants, therefore participantOrder = 0 by default
validateParticipantOrder : IntegrationEvents.Result -> [Int] -> Bool
validateParticipantOrder IntegrationEvents.Outcome{type_ = IntegrationEvents.OverUnder _, participantOrder = 0} _ = True
validateParticipantOrder IntegrationEvents.Outcome{type_ = IntegrationEvents.ThreeWay, subtype = IntegrationEvents.Draw, participantOrder = 0} _ = True
validateParticipantOrder IntegrationEvents.Outcome{participantOrder} participantsOrderList = participantOrder `elem` participantsOrderList


addToMap : Map.Map Market (Map.Map IntegrationEvents.Geography [Submarket]) -> (Market, (Map.Map IntegrationEvents.Geography [Submarket])) -> Map.Map Market (Map.Map IntegrationEvents.Geography [Submarket])
addToMap map (market, submap) =
    -- Retrieve the current submarkets for the market in the map
    case Map.lookup market map  of
        Some (currentSubmarketMap) ->
            -- Join the input submarket list with the current submarket list and remove possible duplicates
            let newSubmarketMap = Utils.unionWith (\ (current) (submap) -> List.dedup (current ++ submap)) currentSubmarketMap submap
            in
                if (currentSubmarketMap == newSubmarketMap) then map
                else Map.insert market (newSubmarketMap) map
        None ->
            -- If market does not exist in the MarketMap
            Map.insert market submap map


validateStatusUpdate : EventInstrumentStatusUpdateRequest -> Bool
validateStatusUpdate EventInstrumentStatusUpdateRequest{newEventStatus = IntegrationEvents.Finished, eventResults = None} = False
validateStatusUpdate EventInstrumentStatusUpdateRequest{newEventStatus = IntegrationEvents.Finished, eventResults = Some []} = False
validateStatusUpdate EventInstrumentStatusUpdateRequest{newEventStatus = IntegrationEvents.Finished, eventResults = Some _} = True
validateStatusUpdate EventInstrumentStatusUpdateRequest{eventResults = None} = True
validateStatusUpdate EventInstrumentStatusUpdateRequest{eventResults = Some _} = False


-- | Joining either TwoWay or ThreeWay outcomes to final update.
joinTwoOrThreeway : OutcomeTupleL -> OutcomeTupleL -> OutcomeTupleL
joinTwoOrThreeway mergedOutcomes newOutcomes = keepCorrectOutcome mergedPartition newPartition
  where
    partitionTwoThreeWay = IntegrationEvents.partition3
                            (\ (outcome, _) -> outcome.type_ == IntegrationEvents.TwoWay)
                            (\ (outcome, _) -> outcome.type_ == IntegrationEvents.ThreeWay)
    -- | Both merged and new outcomes are partitioned into TwoWay, ThreeWay and remainder lists
    mergedPartition = partitionTwoThreeWay mergedOutcomes
    newPartition = partitionTwoThreeWay newOutcomes
    -- | In this function only the intended outcome is kept
    keepCorrectOutcome : OutcomeTriple -> OutcomeTriple -> OutcomeTupleL
    keepCorrectOutcome (merged2Way@(__x::_xs), [], mergedRemaining) ([], [], _) = merged2Way <> mergedRemaining
    -- ^ If TwoWay is present only in merged, keep merged TwoWay and Remainder
    keepCorrectOutcome (merged2Way@(_x::_xs), [], mergedRemaining) ((_y::_ys), [], _) = merged2Way <> mergedRemaining
    -- ^ If TwoWay is present in merged and new, keep merged TwoWay and Remainder
    keepCorrectOutcome ([], merged3Way@(_x::_xs), mergedRemaining) ([], (_y::_ys), _) = merged3Way <> mergedRemaining
    -- ^ If ThreeWay is present in merged and new, keep merged ThreeWay and Remainder
    keepCorrectOutcome ([], merged3Way@(_x::_xs), mergedRemaining) ([], [], _) = merged3Way <> mergedRemaining
    -- ^ If ThreeWay is present only in merged, keep merged ThreeWay and Remainder
    keepCorrectOutcome ([], (_x::_xs), mergedRemaining) (new2Way@(_y::_ys), [], _) = new2Way <> mergedRemaining
    -- ^ If ThreeWay is present in merged but TwoWay is in new, keep new TwoWay and merged Remainder
    keepCorrectOutcome ((_x::_xs), [], mergedRemaining) ([], new3Way@(_y::_ys), _) = new3Way <> mergedRemaining
    -- ^ If TwoWay is present in merged but ThreeWay is in new, keep new ThreeWay and merged Remainder
    keepCorrectOutcome (_, _, mergedRemaining) _ = mergedRemaining
    -- ^ If no combination output merged remaining (which is this case it's all)


getEventIdFromOrigin : EventOrigin -> Update Text
getEventIdFromOrigin CustomerEvent{assetLabel} = pure assetLabel
getEventIdFromOrigin IntegrationEvent{eventCid} = (.eventId) <$> fetch eventCid