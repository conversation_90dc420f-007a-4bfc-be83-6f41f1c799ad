{-# LANGUAGE MultiWayIf #-}
module Gambyl.Gambling.Account.Model where

import DA.Finance.Asset qualified as Asset

import DA.Action qualified as Action
import DA.Date qualified as Date (toDateUTC, subDate, toGregorian)
import DA.Map       (Map)
import DA.Map qualified as Map
import DA.Numeric qualified as Numeric (mul, castAndRound)
import DA.Optional qualified as Optional (fromOptional, listToOptional, fromSomeNote, optional)
import DA.Set       (Set)
import DA.Set qualified as Set (fromList, union, difference, insert, delete)
import DA.Text qualified as Text
import DA.Tuple qualified as Tuple (second)

import Gambyl.Gambling.Bet.Model qualified as BetModel
import Gambyl.Gambling.Bet.Odds.Model qualified as OddsModel
import Gambyl.Gambling.Event.Model qualified as EventModel
import Gambyl.Gambling.Model qualified as GamblingModel
import Gambyl.Marketing.Model qualified as MarketingModel
import Gambyl.Utils qualified as Utils


{- -------------------------------------------TEMPLATES------------------------------------------- -}

template Account
    with
        operator                : Party
        provider                : Party
        customer                : Party
        assetDepositMain        : Optional (ContractId Asset.AssetDeposit)
        totalMainBalance        : Numeric 2
        assetDepositWithdraw    : Optional (ContractId Asset.AssetDeposit)
        totalWithdrawBalance    : Numeric 2
        assetDepositBet         : Map Text (ContractId Asset.AssetDeposit)
        totalBetBalance         : Numeric 2
        assetDepositBonus       : Optional (ContractId Asset.AssetDeposit)
        totalBonusBalance       : Numeric 2
        totalBonusBetBalance    : Numeric 2
        preferences             : Map Text Text
        favouriteMarkets        : Set EventModel.Market
        -- v: Map containing all transactions keys made from this account (Deposits, Withdrawals, etc)
        transactionHistory      : Map GamblingModel.Actionable [TransactionKey]
        -- v: Set containing all successfull bets keys made from this account
        betHistory              : Set BetModel.BetPlacementKey
        -- v: tuple value corresponds to (limit, accrued)
        depositLimit            : Map TimedLimit AccruedLimit
        -- v: represents total Fees Paid (Deposit fee + Withdrawal fee + Bet placement fee)
        totalFees               : Numeric 2
    where
        signatory operator, customer, provider
        key (operator, provider, customer) : AccountKey
        maintainer key._1

        choice BonusDeposit : ContractId Account
            with
                depositCid      : ContractId Asset.AssetDeposit
                transactionId   : Text
            controller customer, provider
            do
                Asset.AssetDeposit{asset} <- fetch depositCid

                assetDepositBonus <- mergeOptionalDeposit assetDepositBonus [depositCid] [provider]

                now <- getTime

                let depositedBonusAmount = Numeric.castAndRound asset.quantity
                    transactionCode = Text.intercalate "_" [show customer, show now, show depositedBonusAmount]

                create TransactionHistory with
                    operator, provider, customer
                    appliedFee = 0.0
                    currency = asset.id.label
                    startedAt = now
                    terminatedAt = now
                    transactionId = Text.sha256 transactionCode
                    confirmedAmount = depositedBonusAmount
                    transactionType = GamblingModel.Bonus
                    hasReconciled = False

                create this with assetDepositBonus, totalBonusBalance = this.totalBonusBalance + depositedBonusAmount


        choice Deposit : ContractId Account
            with
                depositCid      : ContractId Asset.AssetDeposit
                transactionId   : Text
                appliedFee      : Decimal
                transactionTime : Time
            controller customer, provider
            do
                Asset.AssetDeposit{asset} <- fetch depositCid

                assetDepositMain <- mergeOptionalDeposit assetDepositMain [depositCid] [provider]

                let
                    depositedAmount = Numeric.castAndRound asset.quantity
                    feeAmount = appliedFee `Numeric.mul` depositedAmount
                    depositTransactions = Optional.fromOptional [] (Map.lookup GamblingModel.Deposit transactionHistory)
                    newTransactionHistory = Map.insert GamblingModel.Deposit ((operator, provider, customer, GamblingModel.Deposit, transactionId)::depositTransactions) transactionHistory

                now <- getTime
                create TransactionHistory with
                    operator, provider, customer
                    appliedFee
                    currency = asset.id.label
                    startedAt = transactionTime
                    terminatedAt = now
                    transactionId
                    confirmedAmount = depositedAmount
                    transactionType = GamblingModel.Deposit
                    hasReconciled = False

                create this with
                    assetDepositMain
                    totalMainBalance = totalMainBalance + depositedAmount
                    transactionHistory = newTransactionHistory
                    depositLimit = accruedLimit depositLimit depositedAmount
                    totalFees = totalFees + feeAmount


        choice Withdraw : ContractId Account
            with
                transactionId   : Text
                appliedFee      : Decimal
                transactionTime : Time
            controller customer, provider
            do
                let Some accountAssetDeposit = assetDepositWithdraw
                asset <- (.asset) <$> fetch accountAssetDeposit
                assetDepositWithdrawCid <- exercise accountAssetDeposit Asset.AssetDeposit_Unlock

                let
                    withdrawnAmount = Numeric.castAndRound asset.quantity
                    feeAmount = appliedFee `Numeric.mul` withdrawnAmount
                    newTotalWithdrawBalance = Numeric.castAndRound totalWithdrawBalance - withdrawnAmount
                    withdrawTransactions = Optional.fromOptional [] (Map.lookup GamblingModel.Withdrawal transactionHistory)
                    newTransactionHistory = Map.insert GamblingModel.Withdrawal ((operator, provider, customer, GamblingModel.Withdrawal, transactionId)::withdrawTransactions) transactionHistory

                now <- getTime
                create TransactionHistory with
                    operator, provider, customer
                    appliedFee
                    currency = asset.id.label
                    startedAt = transactionTime
                    terminatedAt = now
                    transactionId
                    confirmedAmount = withdrawnAmount
                    transactionType = GamblingModel.Withdrawal
                    hasReconciled = False

                archive assetDepositWithdrawCid
                create this with
                    assetDepositWithdraw = None
                    totalWithdrawBalance = newTotalWithdrawBalance
                    transactionHistory = newTransactionHistory
                    totalFees = totalFees + feeAmount

        choice LockWithdraw : ContractId Account
            with
                requestedAmount  : Numeric 2
            controller customer, provider
            do
                let Some lockedAssetDepositMain = assetDepositMain
                    newTotalMainBalance = totalMainBalance - requestedAmount
                    newTotalWithdrawBalance = totalWithdrawBalance + requestedAmount

                (lockedWithdrawnAssetDeposit, remainingAssetDeposit) <- lockForQuantity provider lockedAssetDepositMain requestedAmount

                create this with assetDepositMain = remainingAssetDeposit, totalMainBalance = newTotalMainBalance,
                                    assetDepositWithdraw = Some lockedWithdrawnAssetDeposit, totalWithdrawBalance = newTotalWithdrawBalance


        choice UnlockWithdraw : ContractId Account
            with
                requestedAmount  : Numeric 2
            controller customer, provider
            do
                let Some lockedAssetDepositWithdraw = assetDepositWithdraw
                    newTotalMainBalance = totalMainBalance + requestedAmount
                    newTotalWithdrawBalance = totalWithdrawBalance - requestedAmount

                (assetDepositMain, assetDepositWithdraw) <- case (assetDepositMain) of
                    Some assetDeposit -> do
                        (mergedAssetDeposit, remainingAssetDeposit) <- lockAndMergeForQuantity provider lockedAssetDepositWithdraw assetDeposit requestedAmount
                        return $ (Some mergedAssetDeposit, remainingAssetDeposit)
                    None -> do
                        (lockedWithdrawnAssetDeposit, remainingAssetDeposit) <- lockForQuantity provider lockedAssetDepositWithdraw requestedAmount
                        return $ (Some lockedWithdrawnAssetDeposit, remainingAssetDeposit)

                create this with assetDepositMain, totalMainBalance = newTotalMainBalance
                                 assetDepositWithdraw, totalWithdrawBalance = newTotalWithdrawBalance


        choice LockBet : (ContractId Account, ContractId Asset.AssetDeposit, Optional (Numeric 2))
            with
                betPlacementId  : Text
                requestedAmount : Numeric 2
            controller customer, provider
            do
                let
                    betFromMainDeposit requestedAmount =
                        optional
                            (assertFail "Account doesn't have a main balance")
                            (\ mainDeposit -> do
                                (lockedBetAssetDeposit, remainingMainDeposit) <- lockForQuantity provider mainDeposit requestedAmount
                                return $ (, lockedBetAssetDeposit, None) $ this with
                                    assetDepositMain = remainingMainDeposit
                                    totalMainBalance = totalMainBalance - requestedAmount
                            ) assetDepositMain
                    betFromBonusDeposit requestedAmount =
                        optional
                            (assertFail "Account doesn't have a bonus balance")
                            (\ bonusDeposit -> do

                                (lockedBetAssetDeposit, remainingBonusDeposit) <- lockForQuantity provider bonusDeposit requestedAmount

                                return $ (, lockedBetAssetDeposit, Some requestedAmount) $ this with
                                    assetDepositBonus = remainingBonusDeposit
                                    totalBonusBalance = totalBonusBalance - requestedAmount
                                ) assetDepositBonus

                (newAccount, betDeposit, usedBonus) <-
                    if  | totalBonusBalance == 0.0 -> do betFromMainDeposit requestedAmount
                        | totalBonusBalance >= requestedAmount -> do betFromBonusDeposit requestedAmount

                        | otherwise -> do
                            optional
                                (betFromMainDeposit requestedAmount)
                                (\ bonusDeposit -> do
                                    Asset.AssetDeposit{asset} <- fetch bonusDeposit

                                    let bonusAssetAmount = (Numeric.castAndRound asset.quantity)
                                        remainingRequestAmount = (requestedAmount -) bonusAssetAmount

                                    (accountUsedBonus, lockedBetBonusDeposit, usedBonus) <- betFromBonusDeposit bonusAssetAmount
                                    (accountUsedMain, lockedBetMainDeposit, _) <- betFromMainDeposit remainingRequestAmount

                                    unlockedBonusDeposit    <- exercise lockedBetBonusDeposit Asset.AssetDeposit_Unlock
                                    unlockedMainDeposit     <- exercise lockedBetMainDeposit Asset.AssetDeposit_Unlock

                                    unlockedBetDeposit      <- exercise unlockedBonusDeposit Asset.AssetDeposit_Merge with depositCids = [unlockedMainDeposit]
                                    lockedBetDeposit        <- exercise unlockedBetDeposit Asset.AssetDeposit_Lock with newLockers = Set.fromList [provider]


                                    let newAccount = accountUsedMain with
                                            assetDepositBonus = accountUsedBonus.assetDepositBonus
                                            totalBonusBalance = accountUsedBonus.totalBonusBalance

                                    return (newAccount, lockedBetDeposit, usedBonus)

                                ) assetDepositBonus

                (, betDeposit, usedBonus) <$>
                    create newAccount with
                        assetDepositBet = Map.insert betPlacementId betDeposit newAccount.assetDepositBet
                        totalBetBalance = newAccount.totalBetBalance + requestedAmount
                        totalBonusBetBalance = newAccount.totalBonusBetBalance + Optional.fromOptional 0.0 usedBonus
                        betHistory = Set.insert (operator, provider, customer, betPlacementId) this.betHistory


        choice UnlockBet : ContractId Account
            with
                betPlacementId      : Text
                optBonusBetAmount   : Optional (Numeric 2)
            controller customer, provider
            do
                betAssetDepositCid <- Optional.optional
                    (assertFail $ "No cash is locked for bet with id: " <> betPlacementId)
                    return
                    $ Map.lookup betPlacementId assetDepositBet

                Asset.AssetDeposit{asset = betAsset} <- fetch betAssetDepositCid
                unlockedAssetDepositBet <- exercise betAssetDepositCid Asset.AssetDeposit_Unlock

                -- Split Bonus and Main

                (bonusBetAmount, optBetAssetDeposit, optBonusAssetDeposit) <- optional (return (0.0, Some unlockedAssetDepositBet, None))
                    (\ bonusBetAmount -> do
                        (betAssetDeposit, bonusAssetDeposit) <-
                            if Numeric.castAndRound betAsset.quantity == bonusBetAmount then do
                                bonusAssetDeposit::[] <- exercise unlockedAssetDepositBet Asset.AssetDeposit_Split with quantities = [Numeric.castAndRound bonusBetAmount]
                                return (None, Some bonusAssetDeposit)
                            else do
                                bonusAssetDeposit::betAssetDeposit::[] <-
                                    exercise unlockedAssetDepositBet Asset.AssetDeposit_Split with quantities = [Numeric.castAndRound bonusBetAmount]
                                return (Some betAssetDeposit, Some bonusAssetDeposit)
                        return (bonusBetAmount, betAssetDeposit, bonusAssetDeposit))
                    optBonusBetAmount

                -- Merge any bonus stake with account total bonus
                newBonusAssetDepositBonus <- mergeOptionalDeposits provider optBonusAssetDeposit assetDepositBonus

                totalBonusBalance <- optional
                    (return 0.0)
                    (fmap (Numeric.castAndRound . (.asset.quantity)) . fetch)
                    newBonusAssetDepositBonus

                -- Merge existing non bonus stake with total account amount
                newAssetDepositMain <- mergeOptionalDeposits provider optBetAssetDeposit assetDepositMain

                newOptAssetDepositMain <- Optional.optional
                    (return None)
                    (\ newAssetDepositMain ->
                        Some <$> exercise newAssetDepositMain Asset.AssetDeposit_Lock with newLockers = Set.fromList [provider])
                    newAssetDepositMain

                let
                    numericAssetQuantity = Numeric.castAndRound betAsset.quantity
                    newTotalMainBalance = totalMainBalance + numericAssetQuantity - bonusBetAmount
                    newTotalBetBalance = totalBetBalance - numericAssetQuantity
                    newTotalBonusBetBalance = totalBonusBetBalance - bonusBetAmount

                create this with
                    assetDepositMain = newOptAssetDepositMain
                    assetDepositBet = Map.delete betPlacementId assetDepositBet
                    totalMainBalance = newTotalMainBalance
                    totalBetBalance = newTotalBetBalance
                    totalBonusBetBalance = newTotalBonusBetBalance
                    assetDepositBonus = newBonusAssetDepositBonus
                    totalBonusBalance
                    betHistory = Set.delete (operator, provider, customer, betPlacementId) this.betHistory

            -- Returns account after reconciliation (if any), and remaining amount to use on asset lifecycle
        nonconsuming choice ReconcileBetPayout : (ContractId Account, ContractId Asset.AssetDeposit)
            with
                betPlacementId    : Text
                amountToReconcile : Decimal
            controller customer, provider
            do
                lockedBetDepositCid <- Optional.optional
                        (assertFail ("Bet placement does not exist for customer (" <> show customer <> ") account"))
                        return $ Map.lookup betPlacementId assetDepositBet

                someBonusBetAmount <- lookupByKey @BetModel.BetPlacement (operator, provider, customer, betPlacementId) >>= Optional.optional
                    (assertFail "Bet Placement could not be found")
                    (fetch Action.>=> (return . (.bonusBetAmount)))

                let bonusBetAmount = Numeric.castAndRound $ Optional.fromOptional 0.0 someBonusBetAmount

                betAmount <- (.asset.quantity) <$> fetch lockedBetDepositCid

                let realMoneyUsed = betAmount - bonusBetAmount

                if amountToReconcile > 0.0 && betAmount > amountToReconcile then
                    if
                        | betAmount == bonusBetAmount -> do
                            -- Will reconcile only bonus money
                            unlockedToReconcileCid  <- exercise lockedBetDepositCid Asset.AssetDeposit_Unlock
                            [reconciledDepositBonusCid, remainingBetDepositBonusCid] <- exercise unlockedToReconcileCid Asset.AssetDeposit_Split with quantities = [amountToReconcile]
                            assetDepositBonus        <- mergeOptionalDeposit assetDepositBonus [reconciledDepositBonusCid] [provider]
                            newTotalBonusBalance     <- Optional.optional (return 0.0) (fmap (.asset.quantity) . fetch) assetDepositBonus
                            lockedRemBetDepositBonusCid  <- exercise remainingBetDepositBonusCid Asset.AssetDeposit_Lock with newLockers = Set.fromList [provider]
                            let newBetDepositMap = Map.insert betPlacementId lockedRemBetDepositBonusCid assetDepositBet

                            updatedBetBalance <- Action.foldlA (\ acc (_, v) ->
                                    (acc +) . (.asset.quantity) <$> fetch v
                                ) 0.0 $ Map.toList newBetDepositMap
                            archive self
                            (, lockedRemBetDepositBonusCid) <$> create this with
                                assetDepositBonus
                                assetDepositBet = newBetDepositMap
                                totalBetBalance = Numeric.castAndRound updatedBetBalance
                                totalBonusBalance = Numeric.castAndRound newTotalBonusBalance
                                totalBonusBetBalance = this.totalBonusBetBalance - (Numeric.castAndRound amountToReconcile)

                        | realMoneyUsed > 0.0 && amountToReconcile <= realMoneyUsed -> do
                            -- Will reconcile only real money
                            unlockedToReconcileCid  <- exercise lockedBetDepositCid Asset.AssetDeposit_Unlock
                            [reconciledDepositCid, remainingBetDepositCid] <- exercise unlockedToReconcileCid Asset.AssetDeposit_Split with quantities = [amountToReconcile]
                            assetDepositMain        <- mergeOptionalDeposit assetDepositMain [reconciledDepositCid] [provider]
                            newMainTotalbalance     <- Optional.optional (return 0.0) (fmap (.asset.quantity) . fetch) assetDepositMain
                            lockedRemBetDepositCid  <- exercise remainingBetDepositCid Asset.AssetDeposit_Lock with newLockers = Set.fromList [provider]
                            let newBetDepositMap = Map.insert betPlacementId lockedRemBetDepositCid assetDepositBet
                            updatedBetBalance <- Action.foldlA (\ acc (_, v) ->
                                    (acc +) . (.asset.quantity) <$> fetch v
                                ) 0.0 $ Map.toList newBetDepositMap
                            archive self
                            (, lockedRemBetDepositCid) <$> create this with
                                assetDepositMain
                                totalMainBalance = Numeric.castAndRound newMainTotalbalance
                                assetDepositBet = newBetDepositMap
                                totalBetBalance = Numeric.castAndRound updatedBetBalance

                        | realMoneyUsed > 0.0 && amountToReconcile > realMoneyUsed -> do
                            -- Will reconcile both real money and bonus money
                            let
                                realMoneyToReconcile = betAmount - bonusBetAmount
                                bonusMoneyToReconcile = amountToReconcile - realMoneyToReconcile
                            unlockedToReconcileCid  <- exercise lockedBetDepositCid Asset.AssetDeposit_Unlock
                            [reconciledDepositCid, reconciledDepositBonusCid, remainingBetDepositCid] <- exercise unlockedToReconcileCid Asset.AssetDeposit_Split with quantities = [realMoneyToReconcile, bonusMoneyToReconcile]
                            assetDepositMain        <- mergeOptionalDeposit assetDepositMain [reconciledDepositCid] [provider]
                            newMainTotalbalance     <- Optional.optional (return 0.0) (fmap (.asset.quantity) . fetch) assetDepositMain
                            assetDepositBonus        <- mergeOptionalDeposit assetDepositBonus [reconciledDepositBonusCid] [provider]
                            newTotalBonusBalance     <- Optional.optional (return 0.0) (fmap (.asset.quantity) . fetch) assetDepositBonus
                            lockedRemBetDepositCid  <- exercise remainingBetDepositCid Asset.AssetDeposit_Lock with newLockers = Set.fromList [provider]
                            let newBetDepositMap = Map.insert betPlacementId lockedRemBetDepositCid assetDepositBet
                            updatedBetBalance <- Action.foldlA (\ acc (_, v) ->
                                    (acc +) . (.asset.quantity) <$> fetch v
                                ) 0.0 $ Map.toList newBetDepositMap
                            archive self
                            (, lockedRemBetDepositCid) <$> create this with
                                assetDepositMain
                                assetDepositBonus
                                totalMainBalance = Numeric.castAndRound newMainTotalbalance
                                assetDepositBet = newBetDepositMap
                                totalBetBalance = Numeric.castAndRound updatedBetBalance
                                totalBonusBalance = Numeric.castAndRound newTotalBonusBalance
                                totalBonusBetBalance = this.totalBonusBetBalance - (Numeric.castAndRound bonusMoneyToReconcile)

                        | otherwise -> return (self, lockedBetDepositCid)

                else return (self, lockedBetDepositCid)


        choice UpdateBetWinnings : ContractId Account
                with
                    betPlacementId          : Text
                    winningsDepositCidList  : [ContractId Asset.AssetDeposit]
                    bonusBetAmount          : Optional (Numeric 2)
                    executedOdd             : OddsModel.OddType
                controller customer, provider
                do

                    updatedAccount <-
                        if null winningsDepositCidList then
                            return this
                        else do

                            betCashDepositCid <- Optional.fromSomeNote "Deposit for bet not found" $
                                (`exercise` Asset.AssetDeposit_Unlock) <$> Map.lookup betPlacementId this.assetDepositBet

                            (newAccount, optBetMainDepositCid) <- Optional.optional
                                (return (this, Some betCashDepositCid))
                                (\ bonusAmount -> do
                                    betBonusDepositCid::betRemainderDepositCid <-
                                        exercise betCashDepositCid Asset.AssetDeposit_Split with quantities = [Numeric.castAndRound bonusAmount]

                                    updatedBonusDeposit <- mergeOptionalDeposit assetDepositBonus [betBonusDepositCid] [provider]

                                    return $ (, Optional.listToOptional betRemainderDepositCid)
                                        this with
                                            assetDepositBonus = updatedBonusDeposit
                                            totalBonusBalance = this.totalBonusBalance + bonusAmount
                                )
                                bonusBetAmount

                            totalWinningsDepositCid <- (\ (headWinning::tailWinnings) ->
                                    if null tailWinnings
                                        then return headWinning
                                        else Optional.fromSomeNote "Total Winnings not merged" <$>
                                            mergeOptionalDeposit (Some headWinning) tailWinnings [provider]
                                ) winningsDepositCidList

                            winningTotal <- (.asset.quantity) <$> fetch totalWinningsDepositCid

                            BetModel.BetPlacement{
                                details = BetModel.Details{side}
                            } <- snd <$> fetchByKey @BetModel.BetPlacement (operator, provider, customer, betPlacementId)

                            payout  <- Utils.rightOrFail $ BetModel.getPayout side

                            let
                                feeAmount = payout - (Numeric.castAndRound winningTotal)

                            totalBetDepositCid <- Optional.fromSomeNote "Winnings not merged with bet remainder" <$>
                                mergeOptionalDeposit optBetMainDepositCid [totalWinningsDepositCid] [provider]

                            -- Unlock totalBetDeposit
                            totalBetDepositCid <- exercise totalBetDepositCid Asset.AssetDeposit_Unlock

                            assetDepositMain <- mergeOptionalDeposit assetDepositMain [totalBetDepositCid] [provider]

                            newMainTotalbalance <- Optional.optional (return 0.0) (fmap (.asset.quantity) . fetch) assetDepositMain

                            return $ newAccount with
                                assetDepositMain
                                totalMainBalance = Numeric.castAndRound newMainTotalbalance
                                totalFees = totalFees + feeAmount

                    BetModel.BetPlacement{
                        details = BetModel.Details{side}
                    } <- snd <$> fetchByKey @BetModel.BetPlacement (operator, provider, customer, betPlacementId)
                    stake <- case side of
                        BetModel.Back sideDetails -> return sideDetails.stake
                        BetModel.Lay sideDetails ->
                            Utils.rightOrFail $ OddsModel.calculatePayout executedOdd sideDetails.stake

                    create updatedAccount with
                        assetDepositBet = Map.delete betPlacementId this.assetDepositBet
                        totalBetBalance = this.totalBetBalance - stake
                        totalBonusBetBalance = (this.totalBonusBetBalance -) $ Optional.optional 0.0 (\bonus -> if bonus > stake then stake else bonus)  bonusBetAmount

        choice UpdatePreferences : ContractId Account
            with
                newPreferences : Map Text Text
            controller customer, provider
            do
                create this with preferences = Map.union newPreferences preferences


        choice AddFavouriteMarkets : ContractId Account
            with
                markets : [EventModel.Market]
            controller customer, provider
            do
                create this with favouriteMarkets = Set.union (Set.fromList markets) favouriteMarkets


        choice RemoveFavouriteMarkets : ContractId Account
            with
                markets : [EventModel.Market]
            controller customer, provider
            do
                create this with favouriteMarkets = Set.difference favouriteMarkets (Set.fromList markets)


        choice UpdateDepositLimit : ContractId Account
            with
                newDepositLimit : Map TimedLimit AccruedLimit
            controller provider
            do
                create this with
                    depositLimit = Map.merge (\ _ -> Some) (\ _ -> Some) (\ _ a b -> Some a with limit = b.limit) depositLimit newDepositLimit


        choice RemoveDepositLimit : ContractId Account
            with
                timeFrameList : [TimedLimit]
            controller provider
            do
                create this with
                    depositLimit = foldr Map.delete depositLimit timeFrameList


        nonconsuming choice ResetAccruedAmount : Map TimedLimit AccruedLimit
            controller provider
            do
                today <- Date.toDateUTC <$> getTime

                let
                    month date = let (_, m, _) = Date.toGregorian date in m
                    year date = let (y, _, _) = Date.toGregorian date in y

                    checkResetAccrued elem@(key_, limit) predicate
                        | predicate = (key_, limit with accrued = 0.00, date = today)
                        | otherwise = elem

                    resetLimit = Map.fromList $ map (\ elem@(key_, AccruedLimit{date}) ->
                        checkResetAccrued elem $
                            case key_ of
                                Daily -> date /= today
                                Weekly -> Date.subDate today date >= 7
                                Monthly -> month today /= month date
                                Yearly -> year today /= year date
                        ) $ Map.toList depositLimit

                if  resetLimit == depositLimit then
                    return depositLimit
                else do
                    archive self
                    create this with depositLimit = resetLimit
                    return resetLimit


template WithdrawRequestForApproval
    with
        integrationParty : Party
        operator : Party
        customer : Party
        provider : Party
        requestedAmount : Numeric 2
        currency : Text
        transactionId : Text
        promotion : Optional MarketingModel.PromotionKey
        dateOfBirth : Date
        language : Text
    where
        signatory operator, provider, customer


template DepositRequestForApproval
    with
        integrationParty    : Party
        operator            : Party
        customer            : Party
        provider            : Party
        requestedAmount     : Numeric 2
        currency            : Text
        transactionId       : Text
        promotion           : Optional MarketingModel.PromotionKey
        dateOfBirth         : Date
        language            : Text
    where
        signatory operator, provider, customer


template TransactionHistory
  with
    operator            : Party
    provider            : Party
    customer            : Party
    -- v: Transaction code comes from integration
    confirmedAmount     : Numeric 2
    -- v: Represents the fee (in percentage) applied to this transaction
    appliedFee          : Decimal
    currency            : Text
    startedAt           : Time
    terminatedAt        : Time
    transactionId       : Text
    transactionType     : GamblingModel.Actionable
    -- v: Indicator that transaction has been reconciled with accounting agent
    hasReconciled       : Bool
  where
    signatory operator, provider, customer

    key (operator, provider, customer, transactionType, transactionId) : TransactionKey
    maintainer key._1

    choice Reconcile : ContractId TransactionHistory
        controller provider
        do
            assertMsg "Transaction History has already been reconciled with accounting agent" $ not hasReconciled
            create this with hasReconciled = True



template PendingTransaction
    with
        operator : Party
        provider : Party
        customer : Party
        transactionId : Text
        transactionType : GamblingModel.Actionable
        requestedAmount : Numeric 2
        currency : Text
        startedAt : Time
        countryCode         : Text
        -- v: Represents the fee (in percentage) applied to this transaction; 0<=fee<=1
        appliedFee : Decimal
    where
        signatory operator, provider, customer

        key (operator, provider, customer, transactionType, transactionId) : TransactionKey
        maintainer key._1

template PendingDeposits
    with
        operator : Party
        provider : Party
        customer : Party
        pendingDeposits : Map Text (Numeric 2)
        unverifiedPendingDeposits : Map Text (Numeric 2)
    where
        signatory operator, provider, customer

        key (operator, provider, customer) : (Party, Party, Party)
        maintainer key._1

        choice AddDeposit : ContractId PendingDeposits
            with
                transactionId : Text
                requestedAmount : Numeric 2
            controller provider
            do
                create this with
                    pendingDeposits = Map.insert transactionId requestedAmount pendingDeposits

        choice AddUnverifiedAccountDeposit : ContractId PendingDeposits
            with
                transactionId : Text
                requestedAmount : Numeric 2
            controller provider
            do
               create this with
                    unverifiedPendingDeposits = Map.insert transactionId requestedAmount unverifiedPendingDeposits
                    pendingDeposits = Map.insert transactionId requestedAmount pendingDeposits

        choice RemoveDeposit : ()
            with
                transactionId : Text
            controller provider
            do
                let
                    updatedUnverifiedPendingDeposits = Map.delete transactionId unverifiedPendingDeposits
                    updatedPendingDeposits = Map.delete transactionId pendingDeposits

                Action.unless (Map.null unverifiedPendingDeposits && Map.null updatedPendingDeposits) do
                    Action.void $ create this with
                                        unverifiedPendingDeposits = updatedUnverifiedPendingDeposits
                                        pendingDeposits = updatedPendingDeposits


template Flag
    with
        operator    : Party
        provider    : Party
        customer    : Party
        amount      : Numeric 2
        created     : Time
    where
        signatory operator, provider

{- -----------------------------------------CUSTOM TYPES------------------------------------------ -}

type AccountKey = (Party, Party, Party)
type TransactionKey = (Party, Party, Party, GamblingModel.Actionable, Text)

{- ------------------------------------------DATA TYPES------------------------------------------- -}

data TimedLimit =
    Daily   |
    Weekly  |
    Monthly |
    Yearly
    deriving (Eq, Show, Ord)

data AccruedLimit =
    AccruedLimit with
        limit : Numeric 2
        accrued : Numeric 2
        date : Date
    deriving (Eq, Show)

{- -------------------------------------------FUNCTIONS------------------------------------------- -}

getAssetQuantity : Optional (ContractId Asset.AssetDeposit) -> Update Decimal
getAssetQuantity assetDepositCid = do
    case assetDepositCid of
        Some cid -> do
            assetDeposit <- fetch cid
            return assetDeposit.asset.quantity
        None -> do
            return 0.0


getAssetQuantityFromBets : [(Text, (ContractId Asset.AssetDeposit))] -> Update Decimal
getAssetQuantityFromBets [] = return 0.0
getAssetQuantityFromBets ((_, cid)::bets) = do
    assetDeposit <- fetch cid
    (getAssetQuantityFromBets bets) >>= (return . (+ assetDeposit.asset.quantity))


lockForQuantity : Party -> ContractId Asset.AssetDeposit -> Numeric 2 -> Update (ContractId Asset.AssetDeposit, Optional (ContractId Asset.AssetDeposit))
lockForQuantity provider assetDepositMain requestedAmount =
    do
        -- (lockedBetAssetDeposit, remainingBonusDeposit) <- lockForQuantity provider bonusDeposit requestedAmount
        unlockedAssetDepositMain <- exercise assetDepositMain Asset.AssetDeposit_Unlock

        unlockedSplitAssetDeposit::optRemainingAssetDeposit
            <- exercise unlockedAssetDepositMain Asset.AssetDeposit_Split with quantities = [Numeric.castAndRound requestedAmount]

        lockedSplitAssetDeposit <- exercise unlockedSplitAssetDeposit Asset.AssetDeposit_Lock with newLockers = Set.fromList [provider]

        remainingAssetDeposit <- case (Optional.listToOptional optRemainingAssetDeposit) of
            Some assetDeposit -> do
                lockedAssetDeposit <- exercise assetDeposit Asset.AssetDeposit_Lock with newLockers = Set.fromList [provider]
                return $ Some lockedAssetDeposit
            None -> return None

        return (lockedSplitAssetDeposit, remainingAssetDeposit)

lockAndMergeForQuantity : Party -> ContractId Asset.AssetDeposit -> ContractId Asset.AssetDeposit -> Numeric 2 -> Update (ContractId Asset.AssetDeposit, Optional (ContractId Asset.AssetDeposit))
lockAndMergeForQuantity provider assetDepositWithdraw assetDepositMain requestedAmount =
    do

        unlockedAssetDepositWithdraw <- exercise assetDepositWithdraw Asset.AssetDeposit_Unlock
        unlockedAssetDepositMain <- exercise assetDepositMain Asset.AssetDeposit_Unlock

        unlockedSplitAssetDeposit::optRemainingAssetDeposit
            <- exercise unlockedAssetDepositWithdraw Asset.AssetDeposit_Split with quantities = [Numeric.castAndRound requestedAmount]

        mergedAssetDeposit <- exercise unlockedSplitAssetDeposit Asset.AssetDeposit_Merge with depositCids = [unlockedAssetDepositMain]

        lockedSplitAssetDeposit <- exercise mergedAssetDeposit Asset.AssetDeposit_Lock with newLockers = Set.fromList [provider]

        remainingAssetDeposit <- case (Optional.listToOptional optRemainingAssetDeposit) of
            Some assetDeposit -> do
                lockedAssetDeposit <-  exercise assetDeposit Asset.AssetDeposit_Lock with newLockers = Set.fromList [provider]
                return $ Some lockedAssetDeposit
            None -> do
                return None

        return (lockedSplitAssetDeposit, remainingAssetDeposit)


mergeOptionalDeposit : Optional (ContractId Asset.AssetDeposit) -> [ContractId Asset.AssetDeposit] -> [Party] ->  Update (Optional (ContractId Asset.AssetDeposit))
mergeOptionalDeposit assetDepositOpt ~depositCidList@(deposit::toMerge) newLockers = do
    --If accountAssetDeposit exists, merge with new assetDeposit, else, create with new one
    newAssetDeposit <-
        optional
            (exercise deposit Asset.AssetDeposit_Merge with depositCids = toMerge)
            (\ currAssetDeposit -> do
                unlockedAssetDeposit <- exercise currAssetDeposit Asset.AssetDeposit_Unlock
                exercise unlockedAssetDeposit Asset.AssetDeposit_Merge with depositCids = depositCidList)
            assetDepositOpt
    Some <$> exercise newAssetDeposit Asset.AssetDeposit_Lock with newLockers = (Set.fromList newLockers)

accruedLimit : Map TimedLimit AccruedLimit -> Numeric 2  -> Map TimedLimit AccruedLimit
accruedLimit depositLimit requestedAmount  = Map.fromList accruedLimitList
    where
        limitList = Map.toList depositLimit
        accruedLimitList = map (Tuple.second (\ limit -> limit with accrued = limit.accrued + requestedAmount)) limitList


atDepositLimit : Map TimedLimit AccruedLimit -> Numeric 2 -> Optional (TimedLimit, AccruedLimit)
atDepositLimit depositLimit requestedAmount = find ((\ (_, AccruedLimit{limit, accrued}) -> limit < accrued + requestedAmount)) limitList
    where limitList = Map.toList depositLimit


mergeOptionalDeposits : Party -> Optional (ContractId Asset.AssetDeposit) -> Optional (ContractId Asset.AssetDeposit) -> Update (Optional (ContractId Asset.AssetDeposit))
mergeOptionalDeposits _ None optAssetDepositToMerge = return optAssetDepositToMerge
mergeOptionalDeposits _ optAssetDepositToMerged None = return optAssetDepositToMerged
mergeOptionalDeposits provider (Some assetDepositToBeMerged) (Some assetDepositToMerge) = do
    unlockedAssetDeposit <- exercise assetDepositToMerge Asset.AssetDeposit_Unlock
    newAssetDeposit <- exercise unlockedAssetDeposit Asset.AssetDeposit_Merge with depositCids = [assetDepositToBeMerged]
    Some <$> exercise newAssetDeposit Asset.AssetDeposit_Lock with newLockers = Set.fromList [provider]