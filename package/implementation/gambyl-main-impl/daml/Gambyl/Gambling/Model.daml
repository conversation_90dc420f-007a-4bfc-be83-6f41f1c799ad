module Gambyl.Gambling.Model where

import DA.Set (Set)
import DA.Map qualified  as Map


import EnetPulseIntegration.Events qualified as IntegrationEvents
import Gambyl.Interface.Gambling.Model as IGamblingModel
import Gambyl.Gambling.Bet.Odds.Model       (isDecimalOdd, isInverseOdd, OddType(..))
import QuickbooksIntegration.Account.Model qualified as IntegrationAccounting

{- ------------------------------------------DATA TYPES------------------------------------------- -}

data Permissions =
    Events |
    Bets
    deriving (Show, Eq, Ord)

template GlobalGamblingConfiguration
  with
    operator : Party
    provider : Party
    flaggedAmount : Numeric 2
    unverifiedAccountMaxAmount : Numeric 2
    minDepositAmount : Numeric 2
    minWithdrawAmount : Numeric 2
    maxOdd : OddType
    minOdd : OddType
    integrationParties : Map.Map Text Party
    -- v: Represents the fee (in percentage) applied bets. (0<=fee<=1)
    betFee : Decimal
    -- v: Represents the fee (in percentage) applied deposits. (0<=fee<=1)
    depositFee : Decimal
    -- v: Represents the fee (in percentage) applied to withdrawals. (0<=fee<=1)
    withdrawFee : Decimal
    archiveEventDays : Int
    -- v: Represents the Mininum Age allowed in the Platform
    legalAge : Int
    public : Set Party
    isOnMaintenance : Bool
    daysPostponedEventExpires : Int
    minutesMarketMapExpires : Int
    -- allowed period of time in days for pending transactions
    allowedPeriod : Int
    defaultOdds : OddType
    -- ^ Used as the default for presenting in FE and to onboard new customers (Will hold the Oddtype with a mock 0 value)
  where
    signatory operator, provider
    observer public
    ensure isInverseOdd minOdd maxOdd && isDecimalOdd minOdd maxOdd && minOdd < maxOdd

    key (operator, provider) : (Party, Party)
    maintainer key._2

type ActionFailureCid = ContractId ActionFailure

template ActionFailure
    with
        operator : Party
        provider : Party
        customer : Party
        actionId : Text
        action : Actionable
        reason : Text
    where
        signatory operator, provider, customer

        key (operator, provider, customer, action, actionId) : (Party, Party, Party, Actionable, Text)
        maintainer key._2

        choice AcknowledgeActionFailure : ()
            controller customer
            do pure ()

        interface instance IGamblingModel.IActionFailure for ActionFailure where
            view = IGamblingModel.VActionFailure with
                operator, provider, customer, actionId

type ActionSuccessCid = ContractId ActionSuccess

template ActionSuccess
    with
        operator : Party
        provider : Party
        customer : Party
        actionId : Text
        action : Actionable
        reason : Text
    where
        signatory operator, provider, customer

        key (operator, provider, customer, action, actionId) : (Party, Party, Party, Actionable, Text)
        maintainer key._2

        choice AcknowledgeActionSuccess : ()
            controller customer
                do pure ()

data Actionable =
    Deposit         |
    Withdrawal      |
    BetPlacement    |
    BetCancelation  |
    BetMatched      |
    Promotion       |
    Bonus           |
    Settlement      |
    EventInstrumentOrigination |
    EventInstrumentUpdate |
    EventInstrumentStatusUpdate |
    IdentityVerification
    deriving (Show, Eq, Ord)

{- -------------------------------------------FUNCTIONS------------------------------------------- -}



parseParticipants : [IntegrationEvents.Participant] -> Text
parseParticipants [] = ""
parseParticipants [IntegrationEvents.Participant{name}] = name
parseParticipants [IntegrationEvents.Participant{name = name'}, IntegrationEvents.Participant{name = name''}] = name' <> " and " <> name''
parseParticipants (IntegrationEvents.Participant{name}::xs) = name <> ", " <> parseParticipants xs


requestQuickbooksAccount : Party -> Party -> Party -> [Party] -> Text -> Text -> Text -> Update (ContractId IntegrationAccounting.CreateAccountRequest)
requestQuickbooksAccount quickBooks provider customer observers purpose type_ subtype = do

    create IntegrationAccounting.CreateAccountRequest with
                integrationParty = quickBooks
                requestingParty = provider
                requestingFor = customer
                observers = observers
                request = IntegrationAccounting.AccountRequestObject with
                    name = "Customer " <> partyToText customer <> " " <> purpose <> " Account"
                    accountType = type_
                    accountSubType = subtype