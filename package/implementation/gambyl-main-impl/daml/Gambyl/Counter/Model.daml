module Gambyl.Counter.Model where

import DA.Text qualified as Text

template Counter
    with
        operator    : Party
        provider    : Party
        id          : Text
        count       : Int
    where
        signatory operator, provider
        key (operator, provider) : (Party, Party)
        maintainer key._1

        ensure counterValidations this

        choice CountAndIncrement : Text
            controller provider
            do
                let
                  idList = Text.explode id
                  idLength = length idList
                  countLength = Text.length $ show count
                  totalLength = idLength + countLength
                  newCount = Text.implode $ (padListingId idList (16 - totalLength)) <> [show count]
                create this with count = count + 1
                return newCount


counterValidations : Counter -> Bool
counterValidations Counter{id, count} = idLength < 12 && totalLength <= 16
  where
    idList = Text.explode id
    idLength = length idList
    countLength = Text.length $ show count
    totalLength = idLength + countLength

padListingId : [Text] -> Int -> [Text]
padListingId [] 0 = []
padListingId [] spacesLeft = "0" :: padListingId [] (spacesLeft - 1)
padListingId (x::xs) spacesLeft = x :: padListingId xs spacesLeft