{-# LANGUAGE MultiWayIf #-}
module Gambyl.Marketing.Model where

import DA.Action qualified as Action
import DA.Date qualified as Date (toDateUTC, subDate)
import DA.List ((\\))
import DA.Map (Map)
import DA.Map qualified as Map hiding (Map)
import DA.Numeric qualified as Numeric
import DA.Optional qualified as Optional (fromSome, fromOptional, optional)
import DA.Set (Set)

import Gambyl.Gambling.Model qualified as GamblingModel
import Gambyl.Utils qualified as Utils

{- -------------------------------------------TEMPLATES------------------------------------------- -}

type PromotionKey = (Party, Party, Party, PromotionAction, Text)

template GlobalPromotions
  with
    operator : Party
    provider : Party
    promotions : [PromotionKey]
    public : Set Party
  where
    signatory operator, provider
    observer public

    key (operator, provider) : (Party, Party)
    maintainer key._1


    choice AddGlobalPromotion : ContractId GlobalPromotions
      with
        promotionKey : PromotionKey
      controller operator, provider
      do create this with promotions = promotions <> [promotionKey]


    choice RemoveGlobalPromotion : ContractId GlobalPromotions
      with
        promotionKey : PromotionKey
      controller operator, provider
      do create this with promotions = promotions \\ [promotionKey]


    nonconsuming choice UpdateGlobalPromotion : ContractId GlobalPromotions
      with
        oldPromotionKey : PromotionKey
        newPromotionKey : PromotionKey
      controller operator, provider
      do
        if oldPromotionKey == newPromotionKey
          then pure self
          else
            lookupByKey @Promotion newPromotionKey >>= optional
              (assertFail "New Promotion does not exist")
              (\ _ -> do
                  archive self
                  create this with
                    promotions = ([newPromotionKey] <> promotions) \\ [oldPromotionKey]
                )

-- | Text fields are map because of translations
template Promotion
    with
        operator : Party
        provider : Party
        customer : Party
        title : Map Text Text
        shortDescription : Map Text Text
        longDescription : Map Text Text
        promotionId : Text
        config : PromotionConfig
        status : Status
        startDate : Time
        thumbnailUrl : Map Text Text
        bannerUrl : Map Text Text
        baseUrl : Map Text Text
        public : Set Party
    where
        signatory operator, provider, customer
        observer public

        key (operator, provider, customer, config.action, promotionId): PromotionKey
        maintainer key._1

        ensure isValid config

        choice UpdatePromotion: ContractId Promotion
            with
                newConfig              : Optional PromotionConfig
                newStartDate           : Optional Time
                newThumbnailUrl        : Map Text Text
                newBannerUrl           : Map Text Text
                newShortDescription    : Map Text Text
                newLongDescription     : Map Text Text
                newTitle               : Map Text Text
                newBaseUrl             : Map Text Text
            controller customer, provider
            do
                create this with
                    config = Optional.fromOptional config newConfig
                    startDate = Optional.fromOptional startDate newStartDate
                    thumbnailUrl =  Map.union newThumbnailUrl thumbnailUrl
                    bannerUrl = Map.union newBannerUrl bannerUrl
                    shortDescription = Map.union newShortDescription shortDescription
                    longDescription = Map.union newLongDescription longDescription
                    title = Map.union newTitle title
                    baseUrl = Map.union newBaseUrl baseUrl


        choice ExpirePromotion: ContractId Promotion
            controller operator, provider
            do
                assertMsg "Promotion already expired" $ status /= Expired
                today <- Date.toDateUTC <$> getTime
                create this with
                        status          = Expired
                        config.endDate  = Some today


        choice ArchivePromotion : ()
          controller operator, provider
          do
            assertMsg "Promotion has not expired" $ status == Expired
            pure ()


template PromotionRequest
    with
        operator : Party
        provider : Party
        customer : Party -- This customer is the Marketing Manager
        promotionId : Text
        title : Map Text Text
        config : PromotionConfig
        status : Status
        startDate : Time
        shortDescription : Map Text Text
        longDescription : Map Text Text
        thumbnailUrl : Map Text Text
        bannerUrl : Map Text Text
        baseUrl : Map Text Text
    where
        signatory operator, provider, customer

        key (operator, provider, customer, config.action, promotionId): PromotionKey
        maintainer key._1

        ensure isValid config

        choice Accept: ContractId Promotion
          controller provider, customer
          do
            GamblingModel.GlobalGamblingConfiguration{public} <-
              lookupByKey @GamblingModel.GlobalGamblingConfiguration (operator, provider)
                >>= optional (assertFail "GlobalGamblingConfiguration contract not found") fetch

            create Promotion with ..


        choice Reject: ()
            controller customer
            do pure ()


template PromotionUpdateRequest
    with
        operator : Party
        provider : Party
        customer : Party
        promotionKey : PromotionKey
        newConfig : Optional PromotionConfig
        newStartDate : Optional Time
        newThumbnailUrl : Map Text Text
        newBannerUrl : Map Text Text
        newShortDescription : Map Text Text
        newLongDescription : Map Text Text
        newTitle : Map Text Text
        newBaseUrl : Map Text Text
    where
        signatory operator, provider, customer

        ensure Optional.optional True isValid newConfig

        choice Approve: ContractId Promotion
            controller provider
            do
                exerciseByKey @Promotion promotionKey $ UpdatePromotion with ..


-- this template is per customer
template PromotionWallet
    with
        operator : Party
        provider : Party
        customer : Party
        -- v: this map indicates the number of times a promotion has been used by this customer
        promotionMap    : Map PromotionKey PromotionUsage -- changeit on data totalDiscountedValue ????
    where
        signatory operator, provider, customer

        key (operator, provider, customer) : (Party, Party, Party)
        maintainer key._1


        choice UsePromotion : ContractId PromotionWallet
            with
                promotionKey        : PromotionKey
                actionId            : Text
                offeredAmount       : Numeric 2
                transactedAmount    : Numeric 2
            controller operator, provider
            do
                now <- getTime

                let
                    promotionUsageOpt = Map.lookup promotionKey promotionMap
                    -- customer already used the promotion, just increment the counter for it
                    promoUsage = PromotionUsage with
                        usageCounter = (+ 1) $ optional 0 ((.usageCounter)) promotionUsageOpt
                        lastUsageDate = now

                    promoAction = promotionKey._4
                    promotionId = promotionKey._5

                lookupByKey @PromotionUsageHistory (operator, provider, customer, promoAction, promotionId) >>=
                    Optional.optional
                        (do
                            Action.void $ create PromotionUsageHistory with
                                operator, provider, customer
                                offerType = Utils.toText $ getPromotionActionDetails promoAction
                                action = promoAction, promotionId
                                usageDate = now
                                totalUsedOn = Map.fromList [(actionId, (transactedAmount, offeredAmount))]

                        )
                        (\ promotionUsageHistoryCid -> do
                            Action.void $ exercise promotionUsageHistoryCid UpdateUsage with
                                usage = Map.fromList [(actionId, (transactedAmount, offeredAmount))]
                        )

                create this with promotionMap = Map.insert promotionKey promoUsage promotionMap


        choice ReducePromotionUsage : ContractId PromotionWallet
            with
                promotionKey        : PromotionKey
                actionId            : Text
                offeredAmount       : Numeric 2
                transactedAmount    : Numeric 2
            controller operator, provider
            do
                promotionUsage <- Optional.optional (assertFail "Promotion was not used") return $ Map.lookup promotionKey promotionMap

                let
                    promoAction = promotionKey._4
                    promotionId = promotionKey._5

                exerciseByKey @PromotionUsageHistory (operator, provider, customer, promoAction, promotionId)
                    UpdateUsage with usage = Map.fromList [(actionId, (negate transactedAmount, negate offeredAmount))]

                create this with
                    promotionMap = Map.insert promotionKey (promotionUsage with usageCounter = promotionUsage.usageCounter - 1) this.promotionMap



-- this template is per customer
template PromotionUsageHistory
    with
        operator : Party
        provider : Party
        customer : Party
        offerType : Text
        action : PromotionAction
        promotionId : Text
        usageDate : Time
        {- v : Map containing as key the action id that led to usage of promotion and value of tuple,
                where first elem is the amount transacted and the second is the amount that was offered per the promotion -}
        totalUsedOn : Map Text (Numeric 2, Numeric 2)
    where
        signatory operator, provider, customer

        key (operator, provider, customer, action, promotionId) : PromotionKey
        maintainer key._1

        ensure offerType == Utils.toText (getPromotionActionDetails action)

        choice UpdateUsage : ContractId PromotionUsageHistory
            with
                usage : Map Text (Numeric 2, Numeric 2)
            controller provider, customer
            do
                now <- getTime
                let
                    newTotalUsedOn = Utils.unionWith
                        (\ (totalTx, totalOffer) (tx, offer) -> (totalTx + tx, totalOffer + offer))
                        totalUsedOn usage

                create this with
                    totalUsedOn = newTotalUsedOn
                    usageDate = now


{- -------------------------------------------DATA TYPES------------------------------------------- -}

data Status =
    Active  |
    Expired
    deriving (Show, Eq, Ord)

data PromoUpdate =
        PromoConfig            PromotionConfig    |
        PromoStartDate         Time               |
        PromoThumbnailUrl      (Map Text Text)    |
        PromoBannerUrl         (Map Text Text)    |
        PromoShortDescription  (Map Text Text)    |
        PromoLongDescription   (Map Text Text)    |
        PromoTitle             (Map Text Text)    |
        PromoBaseUrl           (Map Text Text)

    deriving (Show, Eq)


data PromotionConfig = PromotionConfig
    with
        promoType        : PromotionType
        action           : PromotionAction  -- action on which to apply this promotion on
        endDate          : Optional Date
        limitedPromotion : Optional Int     -- Number of times this promotion can be used per customer
        maxAmount        : Optional (Numeric 2) -- maximum amount of money that can be earned/Redeemed per customer on this promotion
        minAmount        : Optional (Numeric 2) -- mininum amount of money that can be used per customer on this promotion
    deriving (Eq, Show)


data PromotionType =
    FirstTime  (Optional Int) |
    Reload                    |
    Other
    deriving (Show, Eq)


{-
Represents the discount or bonus to be applied and the action (Deposit, Withdrawal or Bet) on which the promotion should be applied
    if promotionDetails is Bonus and Redemption is Cash, the details value would be a decimal representing the bonus' amount (in currency)
        if Redemption is Percentage, the details value would be a decimal representing the percentage of the discount to be applied to the requestedAmount
    if promotionDetails is Discount and Redemption is Percentage, the details value would be a decimal representing the discount's percentage relative to the gambyl comission (in %)
        promotionDetails as Discount and Redemption as Cash is not a valid config - this is ensured by the use of the isValidConfigAction function
        e.g. if promotion details is Discount 1.0, the discount would be 100% of the gambyl comission (so it would be comission free)
    Default Currency for all Promotions is $USD
-}
data PromotionAction =
    Deposit PromotionDetails    |
    Withdrawal PromotionDetails |
    Bet PromotionDetails
    deriving (Eq, Show, Ord)

getPromotionActionDetails : PromotionAction -> PromotionDetails
getPromotionActionDetails (Deposit details) = details
getPromotionActionDetails (Withdrawal details) = details
getPromotionActionDetails (Bet details) = details

data PromotionDetails =
    Bonus Redemption   |
    Discount Redemption
    deriving (Eq, Show, Ord)


{-
Data Type Redemption to specify which method a promotion is Redeemed .
    As Percentage of the Deposit/Withdrawal amount
        Percentage Value varies from 0.0 to 1, where 1 -> 100% therefore Format (value/100)
    As a cash amount set by Gambyl
-}
data Redemption =
    Percentage Decimal  |
    Cash (Numeric 2)
    deriving (Eq, Show, Ord)

data PromotionUsage = PromotionUsage
    with
        usageCounter    : Int
        lastUsageDate   : Time
    deriving (Eq, Show, Ord)



{- -------------------------------------CLASSES AND INSTANCES------------------------------------- -}

-- Class used to validate whether or not a given value is valid, in the context of a PromotionConfig
class ValidConfig a where
    isValid : a -> Bool

instance ValidConfig PromotionConfig where
    isValid PromotionConfig{action = (Deposit (Discount _)),        maxAmount = (Some _)}   = False
    isValid PromotionConfig{action = (Withdrawal (Discount _)),     maxAmount = (Some _)}   = False
    isValid PromotionConfig{action = (Deposit (Bonus (Cash _))),    maxAmount = (Some _)}   = False
    isValid PromotionConfig{action = (Withdrawal (Bonus _)),        maxAmount = (Some _)}   = False
    isValid PromotionConfig{promoType, action, limitedPromotion, maxAmount, minAmount} =
        validationType promoType &&
        isValid action &&
        isValid limitedPromotion &&
        isValid maxAmount &&
        isValid minAmount
        where
            validationType (FirstTime _)    = Optional.optional True (1 ==) limitedPromotion
            validationType _             = True


instance ValidConfig PromotionAction where
    isValid (Deposit promotionDetails) = isValid promotionDetails
    isValid (Withdrawal (Bonus _)) = False
    isValid (Bet (Bonus _)) = False
    isValid (Withdrawal promotionDetails) = isValid promotionDetails
    isValid (Bet promotionDetails) = isValid promotionDetails

-- A discount promotion can only be in Percentage and not Cash;
-- A bonus promotion can be of Percentage or straight Cash
instance ValidConfig PromotionDetails where
    isValid (Bonus redemption) = isValid redemption
    isValid (Discount (Cash _)) = False
    isValid (Discount redemption) = isValid redemption


instance ValidConfig Redemption where
    isValid (Percentage value) = value >= 0.0 && value <= 1.0
    isValid (Cash value) = value >= 0.0

-- LimitedPromotion field must be greater than 0, as a promotion must be able to be used at least once
instance ValidConfig (Optional Int) where
    isValid (Some value) = value > 0
    isValid None = True


instance ValidConfig (Optional (Numeric 2)) where
    isValid (Some value) = value >= 0.0
    isValid None = True


instance Utils.VariantConstructor PromotionDetails where

    toText (Bonus _) = "Bonus"
    toText (Discount _) = "Discount"

{- -------------------------------------------FUNCTIONS------------------------------------------- -}

{- returns the bonus amount that a given promotion corresponds to, given the requested amount as well
    the bonus amount is calculated as a percentage of the requested amount or straight cash
    if the requested amount is greater than the max amount defined for the given promotion,
        the amount used in the calculations is the max amount instead of the requested amount -}
getBonusAmount : Promotion -> Numeric 2 -> Numeric 2
getBonusAmount (Promotion{config = PromotionConfig{action, maxAmount}}) requestedAmount
    | isCashBonus action = getCashBonusAmount action
    | optional False (potentialBonus >) maxAmount = Optional.fromSome maxAmount
    | otherwise = potentialBonus
    where potentialBonus = requestedAmount `Numeric.mul` (getPercentageBonusValue action)


getFeeDiscount : PromotionAction -> Decimal
getFeeDiscount (Deposit (Discount (Percentage value)))    = value
getFeeDiscount (Withdrawal (Discount (Percentage value))) = value
getFeeDiscount ~(Bet (Discount (Percentage value)))       = value

checkPromoWithSignupDays : PromotionType -> Date -> Update Bool
checkPromoWithSignupDays (FirstTime (Some nDays)) signUpDate =
    let compDays date = Date.subDate date signUpDate <= nDays
    in compDays . Date.toDateUTC <$> getTime

checkPromoWithSignupDays _ _  = return True

checkPromoMaxAmount : PromotionAction -> Numeric 2 -> (Optional (Numeric 2)) -> Bool
checkPromoMaxAmount (Bet (Discount (_))) reqAmount maxAmount =
    Optional.optional True (reqAmount <=) maxAmount

checkPromoMaxAmount _ _ _  = False

isFirstTimeDeposit : PromotionType -> Bool
isFirstTimeDeposit (FirstTime (Some _))  = True
isFirstTimeDeposit (FirstTime None)      = True
isFirstTimeDeposit _ = False

isBonusPromotion : Promotion -> Bool
isBonusPromotion (Promotion{config = PromotionConfig{action}}) =
    case action of
        Deposit (Bonus _)    -> True
        Withdrawal (Bonus _) -> True
        Bet (Bonus _)        -> True
        _                    -> False


isCashBonus : PromotionAction -> Bool
isCashBonus action =
    case action of
        Deposit (Bonus (Cash _))    -> True
        Withdrawal (Bonus (Cash _)) -> True
        Bet (Bonus (Cash _))        -> True
        _                           -> False


getCashBonusAmount : PromotionAction -> Numeric 2
getCashBonusAmount action =
    case action of
        Deposit (Bonus (Cash value))    -> value
        Withdrawal (Bonus (Cash value)) -> value
        Bet (Bonus (Cash value))        -> value
        _                               -> 0.0


getPercentageBonusValue : PromotionAction -> Decimal
getPercentageBonusValue action =
    case action of
        Deposit (Bonus (Percentage value))    -> Numeric.castAndRound value
        Withdrawal (Bonus (Percentage value)) -> Numeric.castAndRound value
        Bet (Bonus (Percentage value))        -> Numeric.castAndRound value
        _                                     -> 0.0