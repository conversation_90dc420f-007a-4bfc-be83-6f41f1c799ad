module Gambyl.Marketing.Service where

import DA.Finance.Utils (fetchAndArchive)
import DA.List qualified as List
import DA.Map (Map)
import DA.Map qualified as Map
import DA.Optional qualified as Optional
import DA.Set (Set)
import DA.Text qualified as Text

import Marketplace.Utils (createOrLookup)

import Gambyl.Gambling.Event.Model qualified as EventModel
import Gambyl.Gambling.Model qualified as GamblingModel
import Gambyl.Marketing.Model qualified as MarketingModel
import Gambyl.Utils qualified as Utils

{- -------------------------------------------TEMPLATES------------------------------------------- -}

template Service
    with
      operator : Party
      provider : Party
      customer : Party
    where
        signatory operator, provider, customer

        key (operator, provider, customer) : (Party, Party, Party)
        maintainer key._1

        nonconsuming choice CreatePromotionRequest: ContractId MarketingModel.PromotionRequest
          with
            title : Map Text Text
            config : MarketingModel.PromotionConfig
            promotionId : Text
            status : MarketingModel.Status
            startDate : Time
            shortDescription : Map Text Text
            longDescription : Map Text Text
            thumbnailUrl : Map Text Text
            bannerUrl : Map Text Text
            baseUrl : Map Text Text
          controller customer
          do
            -- Check the title is set for all 3 languages
            assertMsg "Title does not have the correct format" $ checkPromotionTitle title
            create MarketingModel.PromotionRequest with
              operator, provider, customer, promotionId, title, config, status, startDate
              shortDescription, longDescription, thumbnailUrl, bannerUrl, baseUrl


        nonconsuming choice UpdatePromotionRequest : ContractId MarketingModel.PromotionUpdateRequest
          with
            promotionCid : ContractId MarketingModel.Promotion
            newConfig : Optional MarketingModel.PromotionConfig
            newStartDate : Optional Time
            newThumbnailUrl : Map Text Text
            newBannerUrl : Map Text Text
            newShortDescription : Map Text Text
            newLongDescription : Map Text Text
            newTitle : Map Text Text
            newBaseUrl : Map Text Text
          controller customer
          do
            promotion <- fetch promotionCid
            create MarketingModel.PromotionUpdateRequest with
              operator, provider, customer, newThumbnailUrl, newBannerUrl, newShortDescription
              newLongDescription, newTitle, newBaseUrl, newConfig, newStartDate
              promotionKey = (key promotion)

        nonconsuming choice RequestPromotionPartialUpdate : ContractId MarketingModel.PromotionUpdateRequest
          with
            promotionCid : ContractId MarketingModel.Promotion
            promoFields : [MarketingModel.PromoUpdate]
          controller customer
          do
              promotion <- fetch promotionCid

              let promo = UpdatePromotionRequest with
                      promotionCid
                      newConfig = Optional.fromOptional (Some promotion.config) None
                      newStartDate = Optional.fromOptional (Some promotion.startDate) None
                      newThumbnailUrl = promotion.thumbnailUrl
                      newBannerUrl = promotion.bannerUrl
                      newShortDescription = promotion.shortDescription
                      newLongDescription = promotion.longDescription
                      newTitle = promotion.title
                      newBaseUrl = promotion.baseUrl

                  promoUpdatedFields = List.foldl (\ accumulator elem ->
                      case elem of
                        MarketingModel.PromoStartDate val -> accumulator with newStartDate = Some val
                        MarketingModel.PromoConfig val -> accumulator with newConfig = Some val
                        MarketingModel.PromoThumbnailUrl val -> accumulator with newThumbnailUrl = val
                        MarketingModel.PromoBannerUrl val -> accumulator with newBannerUrl = val
                        MarketingModel.PromoShortDescription val -> accumulator with newShortDescription = val
                        MarketingModel.PromoLongDescription val -> accumulator with newShortDescription = val
                        MarketingModel.PromoTitle val -> accumulator with newTitle = val
                        MarketingModel.PromoBaseUrl val -> accumulator with newBaseUrl = val
                    ) promo promoFields

              exercise self promoUpdatedFields


        nonconsuming choice RequestEventOrigination : ContractId EventModel.EventInstrumentRequest
          with
            eventOrigin : EventModel.EventOrigin
          controller customer
          do
            EventModel.originateEventFrom eventOrigin operator provider customer


        nonconsuming choice RequestToggleFeaturedEvent : ContractId EventModel.EventInstrumentToggleFeaturedRequest
            with
                label : Text
            controller customer
            do
                create EventModel.EventInstrumentToggleFeaturedRequest with
                  operator, provider, customer, label


        nonconsuming choice ApprovePromotionRequest: ContractId MarketingModel.Promotion
            with
                promotionReqCid : ContractId MarketingModel.PromotionRequest
            controller provider
            do
                promotionCid <- exercise promotionReqCid MarketingModel.Accept
                fetch promotionCid >>=
                    exerciseByKey @MarketingModel.GlobalPromotions (operator, provider) . MarketingModel.AddGlobalPromotion . key

                return promotionCid


        nonconsuming choice RejectPromotion: ()
            with
                promotionOfferCid : ContractId MarketingModel.PromotionRequest
            controller provider
            do
                exercise promotionOfferCid MarketingModel.Reject


        nonconsuming choice ApprovePromotionUpdateRequest
          : Either GamblingModel.ActionFailureCid (ContractId MarketingModel.Promotion)
          with
            promotionUpdateReqCid : ContractId MarketingModel.PromotionUpdateRequest
          controller provider
          do
            MarketingModel.PromotionUpdateRequest{
              newConfig, promotionKey = (operator, provider, customer, action, promotionId)
            } <- fetch promotionUpdateReqCid
            try do
              promotion <- exercise promotionUpdateReqCid MarketingModel.Approve

              let newAction = if Optional.isSome newConfig 
                                then (Optional.fromSome newConfig).action
                                else action

              exerciseByKey @MarketingModel.GlobalPromotions (operator, provider)
                MarketingModel.UpdateGlobalPromotion with
                  oldPromotionKey = (operator, provider, customer, action, promotionId)
                  newPromotionKey = (operator, provider, customer, newAction , promotionId)
              pure $ Right promotion

            catch AssertionFailed msg ->
                    Left <$> createOrLookup GamblingModel.ActionFailure with
                      operator, provider, customer, actionId = promotionId
                      action = GamblingModel.Promotion, reason = msg


        nonconsuming choice ExpirePromotion : Either GamblingModel.ActionFailureCid (ContractId MarketingModel.Promotion)
          with
            promotionCid: ContractId MarketingModel.Promotion
          controller provider
          do
            <EMAIL>{promotionId} <- fetch promotionCid
            exerciseByKey @MarketingModel.GlobalPromotions (operator, provider) $
              MarketingModel.RemoveGlobalPromotion with promotionKey = key promotion
            try do
              Right <$> exercise promotionCid MarketingModel.ExpirePromotion
            catch AssertionFailed msg ->
                    Left <$> createOrLookup GamblingModel.ActionFailure with
                      operator, provider, customer, actionId = promotionId
                      action = GamblingModel.Promotion, reason = msg


        nonconsuming choice CancelPromotion : ()
            with
                promotionCid: ContractId MarketingModel.Promotion
            controller provider
            do
                fetchAndArchive promotionCid >>= exerciseByKey @MarketingModel.GlobalPromotions (operator, provider) .
                    MarketingModel.RemoveGlobalPromotion . key
                return ()


        nonconsuming choice ArchivePromotion : Either GamblingModel.ActionFailureCid ()
          with
            promotionCid : ContractId MarketingModel.Promotion
          controller provider
          do
            promotionId <- (.promotionId) <$> fetch promotionCid
            try do
              Right <$> exercise promotionCid MarketingModel.ArchivePromotion
            catch AssertionFailed msg ->
                    Left <$> createOrLookup GamblingModel.ActionFailure with
                      operator, provider, customer, action = GamblingModel.Promotion
                      actionId = promotionId, reason = msg


        nonconsuming choice ApproveEventOrigination
          : Either
              GamblingModel.ActionFailureCid
              ((Either (ContractId EventModel.EventInstrumentNoOutcomes) (ContractId EventModel.EventInstrument))
                , ContractId EventModel.MarketMap
              )
          with
            requestCid  : ContractId EventModel.EventInstrumentRequest
            managers    : Set Party
          controller provider
          do
            actionId <- (.eventId.label) <$> fetch requestCid

            try do
              (eventCid, marketCid) <- exercise requestCid
                EventModel.Approve_Origination with managers, featured = False
              -- Same as gambling service approve event origination
              Right . (, marketCid) <$>
                try do Left <$> exercise eventCid EventModel.SignalEventNoOutcome
                catch AssertionFailed _ -> pure $ Right eventCid

            catch AssertionFailed msg -> do
                  Left <$> createOrLookup GamblingModel.ActionFailure with
                    operator, provider, customer, action = GamblingModel.EventInstrumentOrigination
                    actionId, reason = msg


        nonconsuming choice ApproveEventUpdate : Either GamblingModel.ActionFailureCid (ContractId EventModel.EventInstrument)
          with
            requestCid  : ContractId EventModel.EventInstrumentUpdateRequest
          controller provider
          do
            eventId <- (.oldEventKey._3) <$> fetch requestCid
            try do
              Right <$> exercise requestCid EventModel.Approve_CustomerUpdate with manager = customer
            catch AssertionFailed msg ->
                    Left <$> createOrLookup GamblingModel.ActionFailure with
                        operator, provider, customer, action = GamblingModel.EventInstrumentUpdate
                        actionId = eventId, reason = msg


        nonconsuming choice ApproveEventUpdateOutcomesOdds : ContractId EventModel.EventInstrument
            with
                requestCid  : ContractId EventModel.EventInstrumentUpdateOutcomesOddsRequest
            controller provider
            do
                exercise requestCid EventModel.ApproveEventUpdateOutcomesOddsRequest with manager = customer


        nonconsuming choice ApproveEventCancelationRequest
          : Either GamblingModel.ActionFailureCid (ContractId EventModel.EventInstrument)
          with
            requestCid  : ContractId EventModel.EventInstrumentCancelRequest
          controller provider
          do
            eventLabel <- (.eventLabel) <$> fetch requestCid
            try do
              Right <$> exercise requestCid EventModel.ApproveCancelRequest with manager = customer
            catch AssertionFailed msg ->
                    Left <$> createOrLookup GamblingModel.ActionFailure with
                        operator, provider, customer, action = GamblingModel.EventInstrumentUpdate
                        actionId = eventLabel, reason = msg


        nonconsuming choice ApproveToggleFeaturedEvent : ContractId EventModel.EventInstrument
            with
                requestCid : ContractId EventModel.EventInstrumentToggleFeaturedRequest
            controller provider
            do
                exercise requestCid EventModel.ApproveToggleFeatured


template Offer
    with
        operator : Party
        provider : Party
        customer : Party
    where
        signatory operator, provider
        observer customer

        key (operator, provider, customer) : (Party, Party, Party)
        maintainer key._1

        choice Accept : (ContractId Service)
            controller customer
            do
                createOrLookup Service with operator, provider, customer

        choice Decline : ()
            controller customer
            do pure ()

        choice Withdraw : ()
            controller provider
            do pure ()


template Request
    with
        customer : Party
        provider : Party
    where
        signatory customer
        observer provider

        key (customer, provider) : (Party, Party)
        maintainer key._1

        choice Cancel : ()
            controller customer
                do pure ()

        choice Reject : ()
            controller provider
                do pure ()

        choice Approve : (ContractId Service)
            with
                operator : Party
            controller operator, provider
                do
                    createOrLookup Service with operator, customer, provider

{- ---------------------------------------HELPER FUNCTIONS---------------------------------------- -}

checkPromotionTitle : Map Text Text -> Bool
checkPromotionTitle title = Map.size title == 3
    && List.sort (Map.keys title) == (List.sort (map show $ enumerate @Utils.Locale))
    && not (any Text.isEmpty (Map.values title))
