module Gambyl.Utils where

import DA.Action qualified as Action (foldlA, when)
import DA.Date qualified as Date (addDays, fromGregorian, toGregorian, date)
import DA.Date.Types (Month(Jan))
import DA.Either qualified as Either
import DA.Finance.Asset qualified as Asset
import DA.Finance.Types (Id(..))
import DA.List ((\\))
import DA.List qualified as List
import DA.Map (Map)
import DA.Map qualified as Map hiding (Map)
import DA.Optional (whenSome)
import DA.Optional qualified as Optional
import DA.Set qualified as Set (Set, toList, fromList)
import DA.Time qualified as Time
import DA.Tuple (snd3)

import Marketplace.Issuance.AssetDescription  (AssetDescription)
import Marketplace.Issuance.Service qualified as IssuanceService

import SendgridIntegration.Message qualified as SendgridIntegration

import Gambyl.Gambling.Identity.Model qualified as IdentityModel

{- ---------------------------------------------ENUMS--------------------------------------------- -}

data Locale
    = EN_US
    | PT
    | ES
  deriving (Eq, Ord, Enum, Bounded)

{- --------------------------------------------CLASSES-------------------------------------------- -}


class VariantConstructor a where
    toText : a -> Text


{- ----------------------------------------CLASS INSTANCES---------------------------------------- -}

instance Show Locale where
  show EN_US = "en-US"
  show PT = "pt"
  show ES = "es"

{- ---------------------------------------HELPER FUNCTIONS---------------------------------------- -}

checkAssetDesc : [Party] -> Text -> Update (Id, Bool)
checkAssetDesc signatoriesList label = do
    let
        assetId = Id with
            signatories = Set.fromList signatoriesList
            label
            version = 0

    exists <- lookupByKey @AssetDescription assetId >>= \case
                        Some _ -> do return True
                        None   -> do return False
    return (assetId, exists)


parseParticipants : [Text] -> Text
parseParticipants [] = ""
parseParticipants [x] = x
parseParticipants [x, y] = x <> " and " <> y
parseParticipants (x::xs) = x <> ", " <> parseParticipants xs


fetchAndArchiveByKey : (HasFetchByKey t k, HasArchive t) => k -> Update (ContractId t, t)
fetchAndArchiveByKey _key  = do
  contract@(cid , _) <- fetchByKey _key
  archive cid
  return contract


sequenceEither : Action m => Either (a) (m b) -> m (Either a b)
sequenceEither (Left contextVal)    = return $ Left contextVal
sequenceEither (Right contextVal)   = contextVal >>= (\ val -> return $ Right val)

fromTimeToUnix : Time -> Int
fromTimeToUnix time_ = (Time.convertRelTimeToMicroseconds dayDifference) / 1_000_000
    where dayDifference = Time.subTime time_ (Time.time (Date.date 1970 Jan 1) 0 0 0)



eitherContractExists : forall t k. (TemplateKey t k) => (forall a. k -> Text -> (ContractId t -> Update (Either Text a)) ->  Update (Either Text a))
eitherContractExists key_ msg f =
    lookupByKey @t key_ >>= optional (return $ Left msg) f

maximumByA : Action m => (a -> a -> m Ordering) -> [a] -> m a
maximumByA _ [] = error "maximumBy: empty list"
maximumByA f (x::xs) = Action.foldlA keep_max x xs
    where keep_max acc e = f acc e >>= \ case
                        LT -> pure e
                        EQ -> pure acc
                        GT -> pure acc

listEquals : Eq a => [a] -> [a] -> Bool
listEquals x y = null (x \\ y) && null (y \\ x)


headTotal : [a] -> Optional a
headTotal (x::_) = Some x
headTotal [] = None


unionWith : Ord k => (v -> v -> v) -> Map k v -> Map k v -> Map k v
unionWith f' m1 m2 =
     Map.merge (\ _ v1 -> Some v1) (\ _ v2 -> Some v2) (\ _ v1 v2 -> Some $ f' v1 v2) m1 m2



unlessSome : Action a => Optional b -> a () -> a ()
unlessSome o func = Optional.optional func (return . const ()) o


setToListHead : Set.Set a -> a
setToListHead set = List.head $ Set.toList set


traceMsg : Show a => Text -> a -> a
traceMsg msg a = traceRaw (msg <> ": " <> show a) a

generateBirthday : Date -> Date
generateBirthday today = dob
  where
    (y, m, d) = Date.toGregorian today
    newY = y - 18
    birthday = Date.fromGregorian (newY, m, d)
    dob = (Date.addDays birthday (-1))

createOrfetchBirthDay : (Party, Party, Party) -> Date -> Update Date
createOrfetchBirthDay customerKey today = do
    lookupByKey @IdentityModel.GamblerIdentity customerKey
        >>= \case
            Some cId -> do
                identity <- fetch cId
                return (identity.jumioUserData.birthday)
            None -> do
                return (generateBirthday today)

whenContractExists : HasLookupByKey t k => k -> (ContractId t -> Update ()) -> Update ()
whenContractExists _key f = lookupByKey _key >>= (`whenSome` f)

getContractWithKey : (HasLookupByKey t k, HasFetch t) => k -> Text -> Update t
getContractWithKey _key template_ =
  lookupByKey _key >>= optional (assertFail $ template_ <> " contract not found") fetch

rightOrFail : (CanAssert a) => Either Text b -> a b
rightOrFail = Either.either assertFail return

whenNone : forall a o. (Action a) => Optional o -> a () -> a ()
whenNone (Some _) _ = pure ()
whenNone _ f = f

createSendGridNotification : (Party, Party, Party) -> SendgridIntegration.Subject -> Update (ContractId SendgridIntegration.Message)
createSendGridNotification gamblerIdentityKey subject = do

  IdentityModel.GamblerUnverifiedIdentity{emailAddress} <-
    lookupByKey @IdentityModel.GamblerUnverifiedIdentity gamblerIdentityKey
      >>= optional (assertFail "GamblerUnverifiedIdentity contract not found") fetch

  create SendgridIntegration.Message with
    integrationParty = snd3 gamblerIdentityKey
    toEmail = emailAddress
    subject = subject

reduceIssuanceAndArchiveIfZeroQuantity : (Party, Party, Party) -> ContractId Asset.AssetDeposit -> Text -> Id -> Update ()
reduceIssuanceAndArchiveIfZeroQuantity issuanceServiceKey depositCid issuanceId accountId = do

  issuanceServiceCid <- lookupByKey @IssuanceService.Service issuanceServiceKey >>= optional
    (assertFail $ "Issuance service with key " <> show issuanceServiceKey <> " not found")
    pure

  reduceIssuanceRequestCid <- exercise issuanceServiceCid
    IssuanceService.RequestReduceIssuance with issuanceId, accountId, depositCid

  issuanceCid <- exercise issuanceServiceCid IssuanceService.ReduceIssuance with reduceIssuanceRequestCid

  issuanceQuantity <- (.quantity) <$> fetch issuanceCid

  Action.when (issuanceQuantity == 0.0) $ archive issuanceCid