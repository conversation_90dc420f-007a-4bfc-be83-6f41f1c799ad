module Gambyl.Operator.Role where

import DA.Map qualified  as Map
import DA.Optional (isNone, fromSomeNote)
import DA.Set (Set)
import DA.Set qualified as Set (empty)

import Marketplace.Operator.Role qualified as Operator

import Gambyl.Gambling.Role qualified as Gambling



template Role
    with
        operator : Party
        optMibOperator : Optional (ContractId Operator.Role)
        public : Set Party
    where
        signatory operator

        key operator : Party
        maintainer key

        choice MarketplaceOperator : ContractId Role
            controller operator
            do
                assertMsg ("Gambyl Operator is already Operator of Markeplace") $ isNone optMibOperator

                mibOperatorCid <- create Operator.Role with
                  operator
                  observers = Set.empty

                create this with optMibOperator = Some mibOperatorCid

        nonconsuming choice OfferGamblingRole : ContractId Gambling.Offer
            with
                provider : Party
            controller operator
            do
                let mibOperatorCid = fromSomeNote ("Gambyl Operator is not the Marketplace Operator") optMibOperator

                custodyRoleOfferCid   <- exercise mibOperatorCid $ Operator.OfferCustodianRole with provider
                exchangeRoleOfferCid  <- exercise mibOperatorCid $ Operator.OfferExchangeRole with provider

                create Gambling.Offer with
                  operator, provider, public
                  aggregateFor = Some (custodyRoleOfferCid, exchangeRoleOfferCid)

        nonconsuming choice ApproveGamblingRole : Gambling.GamblingAggregate
            with
                gamblingRoleRequestCid: ContractId Gambling.Request
                integrationParties: Map.Map Text Party
            controller operator
            do
                exercise gamblingRoleRequestCid Gambling.Approve with ..
