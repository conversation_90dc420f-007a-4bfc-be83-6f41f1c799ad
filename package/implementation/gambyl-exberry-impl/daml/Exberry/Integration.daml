-- Copyright (c) 2020, Digital Asset (Switzerland) GmbH and/or its affiliates.
-- SPDX-License-Identifier: Apache-2.0

module Exberry.Integration where


data OrderData = OrderData
  with
    orderType : Text
    instrument : Text
    quantity : Decimal
    price : Decimal
    side : Text
    timeInForce : Text
    mpOrderId : Int
    userId : Text
  deriving (Ord, Eq, Show)


-- Integration --> Exberry
template NewOrderRequest
  with
    integrationParty : Party
    order : OrderData
  where
    signatory integrationParty


-- Integration <-- Exberry
template NewOrderSuccess
  with
    integrationParty : Party
    sid : Int
    orderId : Int
  where
    signatory integrationParty


-- Integration <-- Exberry
template NewOrderFailure
  with
    integrationParty : Party
    sid : Int
    errorCode : Int
    errorMessage : Text
  where
    signatory integrationParty


-- Integration --> Exberry
template CancelOrderRequest
  with
    integrationParty : Party
    instrument : Text
    orderId : Int
    userId : Text
  where
    signatory integrationParty


-- Integration <-- Exberry
template CancelOrderSuccess
  with
    integrationParty : Party
    sid : Int
    quantity: Decimal
  where
    signatory integrationParty


-- Integration <-- Exberry
template CancelOrderFailure
  with
    integrationParty : Party
    sid : Int
    errorCode : Int
    errorMessage : Text
  where
    signatory integrationParty


-- Integration ==> Exberry
template CreateInstrumentRequest
  with
    integrationParty : Party
    symbol : Text
    quoteCurrency : Text
    instrumentDescription : Text
    calendarId : Text
    pricePrecision : Int
    quantityPrecision : Int
    minQuantity : Decimal
    maxQuantity : Decimal
    status : Text
  where
    signatory integrationParty

    ensure minQuantity > 0.0 && minQuantity <= maxQuantity
        && pricePrecision >= 0 && quantityPrecision >= 0

    choice CreateInstrumentRequest_Success : ContractId Instrument
        with
            instrumentId : Text
        controller integrationParty
        do
            create Instrument with ..

    choice CreateInstrumentRequest_Failure : ContractId FailedInstrumentRequest
        with
            message : Text
            name : Text
            code : Text
        controller integrationParty
        do
            create FailedInstrumentRequest with ..

-- Integration ==> Exberry
template UpdateInstrumentRequest
  with
    integrationParty : Party
    symbol : Text
    quoteCurrency : Text
    instrumentDescription : Text
    calendarId : Text
    pricePrecision : Int
    quantityPrecision : Int
    minQuantity : Decimal
    maxQuantity : Decimal
    status       : Text
    exberry_instrument_id : Text
  where
    signatory integrationParty

    ensure minQuantity > 0.0 && minQuantity <= maxQuantity
        && pricePrecision >= 0 && quantityPrecision >= 0

    choice UpdateInstrumentRequest_Success : ContractId Instrument
        with
          instrumentId : Text
        controller integrationParty
        do
          create Instrument with status = "Disabled",instrumentId, ..

    choice UpdateInstrumentRequest_Failure : ContractId FailedUpdateInstrumentRequest
        with
          message : Text
          name : Text
          code : Text
        controller integrationParty
        do
          create FailedUpdateInstrumentRequest with ..

-- Integration <== Exberry
template Instrument
  with
    integrationParty : Party
    symbol : Text
    quoteCurrency : Text
    instrumentDescription : Text
    calendarId : Text
    pricePrecision : Int
    quantityPrecision : Int
    minQuantity : Decimal
    maxQuantity : Decimal
    status : Text
    instrumentId : Text
  where
    signatory integrationParty


-- Integration <== Exberry
template FailedInstrumentRequest
  with
    integrationParty : Party
    symbol : Text
    quoteCurrency : Text
    instrumentDescription : Text
    calendarId : Text
    pricePrecision : Int
    quantityPrecision : Int
    minQuantity : Decimal
    maxQuantity : Decimal
    status : Text
    message : Text
    name : Text
    code : Text
  where
    signatory integrationParty

-- Integration <== Exberry
template FailedUpdateInstrumentRequest
  with
    integrationParty : Party
    symbol : Text
    quoteCurrency : Text
    instrumentDescription : Text
    calendarId : Text
    pricePrecision : Int
    quantityPrecision : Int
    minQuantity : Decimal
    maxQuantity : Decimal
    message : Text
    name : Text
    code : Text
  where
    signatory integrationParty

-- Integration <-- Exberry
template ExecutionReport
  with
    integrationParty : Party
    sid : Int
    eventId : Int
    eventTimestamp : Text
    instrument : Text
    trackingNumber : Int
    makerMpId : Int
    makerMpOrderId : Int
    makerOrderId : Int
    takerMpId : Int
    takerMpOrderId : Int
    takerOrderId : Int
    matchId : Int
    executedQuantity : Decimal
    executedPrice : Decimal
  where
    signatory integrationParty


-- Integration --> Exberry
template MassCancelRequest
  with
    integrationParty : Party
    sid : Text
    instrument : Text
  where
    signatory integrationParty


-- Integration <-- Exberry
template MassCancelSuccess
  with
    integrationParty : Party
    sid : Int
    numberOfOrders : Int
  where
    signatory integrationParty


-- Integration <-- Exberry
template MassCancelFailure
  with
    integrationParty : Party
    sid : Int
    errorCode : Int
    errorMessage : Text
  where
    signatory integrationParty

