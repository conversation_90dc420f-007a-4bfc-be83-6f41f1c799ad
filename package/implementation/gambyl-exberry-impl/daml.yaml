## Copyright (c) 2020, Digital Asset (Switzerland) GmbH and/or its affiliates.
## SPDX-License-Identifier: Apache-2.0

sdk-version: 2.9.4
name: gambyl-exberry-impl
version: ${GAMBYL_EXBERRY_IMPL_CURRENT_VERSION}
source: daml
parties:
  - Alice
  - Bob
dependencies:
  - daml-prim
  - daml-stdlib
  - daml-script
sandbox-options:
  - --wall-clock-time
build-options:
  - --ghc-option=-Wall
  - --ghc-option=-Wno-name-shadowing
  - --ghc-option=-Wno-unused-do-bind
  - --ghc-option=-Wmissing-import-lists
  - --ghc-option=-Werror
  - --output=../../../build/implementation/gambyl-exberry-impl-${GAMBYL_EXBERRY_IMPL_CURRENT_VERSION}.dar
