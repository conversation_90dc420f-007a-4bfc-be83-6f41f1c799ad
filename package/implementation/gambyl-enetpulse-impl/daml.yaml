# for config file options, refer to
# https://docs.daml.com/tools/assistant.html#project-config-file-daml-yaml

sdk-version: 2.9.4
name: gambyl-enetpulse-impl
source: daml
init-script:
parties:
  - EnetPulse
  - Alice
version: ${GAMBYL_ENETPULSE_IMPL_CURRENT_VERSION}
dependencies:
  - daml-prim
  - daml-stdlib
  - daml-script
sandbox-options:
  - --wall-clock-time
  - --ledgerid=enetpulse-sandbox
  - --max-inbound-message-size=268435456
json-api-options:
  - --max-inbound-message-size=268435456
build-options:
  - --ghc-option=-Wall
  - --ghc-option=-Wno-name-shadowing
  - --ghc-option=-Wno-unused-do-bind
  - --ghc-option=-Wmissing-import-lists
  - --ghc-option=-Werror
  - --output=../../../build/implementation/gambyl-enetpulse-impl-${GAMBYL_ENETPULSE_IMPL_CURRENT_VERSION}.dar