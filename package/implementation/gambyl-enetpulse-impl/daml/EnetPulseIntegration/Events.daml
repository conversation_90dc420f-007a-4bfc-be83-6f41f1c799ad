{-# LANGUAGE MultiWayIf #-}
module EnetPulseIntegration.Events where

import DA.List (sortBy, groupBy)
import DA.Optional (catOptionals)
import DA.Text (intercalate, unwords)
import DA.Record ()
import qualified DA.List as List

{- -------------------------------------------TEMPLATES------------------------------------------- -}

template EventInstrumentUpdate
  with
    integrationParty    : Party
    observers           : [Party]
    eventId             : Text
    eventDetails        : Details
    status              : Status
    results             : [Result]
    integrationTime     : Time
    liveEvent           : Text
  where
    signatory integrationParty
    observer observers

    key (integrationParty, eventId, integrationTime) : IntegrationEventKey
    maintainer key._1

    ensure validOutcomesResults this (.eventDetails.outcomes) (.results)

    choice RetrieveArchive_Update : EventInstrumentUpdate
      with
        executingParty : Party
      controller executingParty
      do
        assertMsg ("Controller party " <> show executingParty <> "isn't a contract stakeholder") $ executingParty `elem` observers
        return this

template EventInstrument
  with
    integrationParty    : Party
    observers           : [Party]
    eventId             : Text
    eventDetails        : Details
    status              : Status
    results             : [Result]
    integrationTime     : Time
    liveEvent           : Text
  where
    signatory integrationParty
    observer observers

    ensure validOutcomesResults this (.eventDetails.outcomes) (.results)

    choice RetrieveArchive_Original : EventInstrument
      with
        executingParty : Party
      controller executingParty
      do
        assertMsg ("Controller party " <> show executingParty <> "isn't a contract stakeholder") $ executingParty `elem` observers
        return this

template EventInstrumentUpdateOnHold
  with
    integrationParty    : Party
    observers           : [Party]
    eventId             : Text
    oldEventKey         : EventInstrumentKey
    eventUpdateKey      : IntegrationEventKey
    serviceKey          : (Party, Party, Party)
    retries             : Int
  where
    signatory integrationParty
    observer observers

    ensure retries < 6

    choice RetryUpdate : Either EventInstrumentUpdateOnHold (ContractId EventInstrumentUpdateOnHold)
      with
        executingParty : Party
      controller executingParty
      do
          assertMsg ("Controller party " <> show executingParty <> "isn't a contract stakeholder") $ executingParty `elem` observers
          if (retries == 5) then
              return $ Left this
          else
              Right <$> (create this with retries = this.retries + 1)

template FailedEventInstrument
    with
        integrationParty    : Party
        observers           : [Party]
        eventId             : Text
        eventData           : Text
        eventType           : Text
        errorMessage        : Text
    where
        signatory integrationParty
        observer observers

{- ------------------------------------------DATA TYPES------------------------------------------- -}

type EventInstrumentKey = (Party, Party, Text)
type IntegrationEventKey = (Party, Text, Time)
type Result = Outcome
type Geography = Text

data Details = Details
    with
        sportFK             : Text
        tournamentStageName : Text
        geography           : Geography
        eventParticipants   : [Participant]
        eventTitle          : Text
        eventGame           : Optional Text
        startDate           : Time
        outcomes            : [OutcomeOdds]
        -- ^ List of all outcomes available for an event.
        -- Each element includes the odd for that outcome, in Decimal format
    deriving (Show, Eq)

data OutcomeOdds = OutcomeOdds
  with
    outcome : Outcome
    odd     : Decimal
  deriving (Show, Eq, Ord)

data Participant = Participant
  with
    name    : Text
    id      : Text
    order   : Int
    -- ^ Order of participant in list of all event participants
    co_op   : Optional [Int]
    -- ^ Optional List of participants in cooperation with this one (None if not in team)
  deriving (Show, Eq)

data Outcome = Outcome
  with
    participantId   : Optional Text
    participantOrder : Int
    type_           : OutcomeType
    subtype         : OutcomeSubType
    order           : Int
  deriving (Show, Eq, Ord )

data OutcomeType =
    ThreeWay                |
    TwoWay                  |
    Winner                  |
    Default                 |
    OverUnder Decimal       |
    ThreeWayHandicap Decimal
  deriving (Show, Eq, Ord)


data OutcomeSubType =
    Win     |
    Draw    |
    Over    |
    Under
  deriving (Show, Eq, Ord)

data Status =
    NotStarted  |
    InProgress  |
    Finished    |
    Cancelled   |
    Unknown     |
    Interrupted |
    Postponed   |
    Other
  deriving (Show, Eq)

{- -------------------------------------CLASSES AND INSTANCES------------------------------------- -}

class Valid a b where
    check : a -> b -> Bool

instance Valid Outcome () where
  check Outcome{type_, subtype, participantId} _ = check type_ subtype && isMatch type_ subtype participantId
    where
      isMatch _                       Win     (Some _)    = True
      isMatch ThreeWay                Draw    None        = True
      isMatch (ThreeWayHandicap _)    Draw    (Some _)    = True
      isMatch _                       Over    None        = True
      isMatch _                       Under   None        = True
      isMatch _                       _       _           = False

instance Valid OutcomeType OutcomeSubType where
  check ThreeWay              Draw    = True
  check ThreeWay              Win     = True
  check TwoWay                Win     = True
  check Winner                Win     = True
  check Default               Win     = True
  check (OverUnder _)         Over    = True
  check (OverUnder _)         Under   = True
  check (ThreeWayHandicap _)  Win     = True
  check (ThreeWayHandicap _)  Draw    = True
  check _                     _       = False

instance Valid Outcome Int where
  check Outcome{type_ = ThreeWay}             3 = True
  check Outcome{type_ = TwoWay}               2 = True
  check Outcome{type_ = Winner}               1 = True
  check Outcome{type_ = OverUnder _}          _ = True
  check Outcome{type_ = ThreeWayHandicap _}   _ = True
  check _                                     _ = False

class Description a where
    short   : a -> Text
    long    : a -> Text

instance Description OutcomeType where

  short ThreeWay              = "1x2"
  short TwoWay                = "12"
  short Winner                = "Winner"
  short Default               = "Default"
  short (OverUnder n)         = "OU_" <> show n <> ""
  short (ThreeWayHandicap n)  = "1x2_HC_" <> show n <> ""

  long ThreeWay               = short ThreeWay <> " - 3Way"
  long TwoWay                 = short TwoWay <> " - 2Way"
  long Winner                 = short Winner
  long Default                = short Default
  long ou@(OverUnder n)       = short ou <> " - Over/Under " <> show n <> ""
  long ah@(ThreeWayHandicap n)   = short ah <> " - 3Way Handicap " <> show n <> ""

instance Description Outcome where

  short Outcome{type_, subtype, participantId} = intercalate "_" $ [short type_, show subtype] <> catOptionals [participantId]

  long Outcome{type_, subtype, participantId} = unwords $ [short type_, show subtype] <> catOptionals [participantId]

class Comparison a where
  sameOutcomeType : a -> a -> Bool

instance Comparison OutcomeType where
  sameOutcomeType ThreeWay ThreeWay                         = True
  sameOutcomeType TwoWay TwoWay                             = True
  sameOutcomeType Winner Winner                             = True
  sameOutcomeType Default Default                           = True
  sameOutcomeType (OverUnder _) (OverUnder _)               = True
  sameOutcomeType (ThreeWayHandicap _) (ThreeWayHandicap _) = True
  sameOutcomeType _ _                                       = False

{- ---------------------------------------HELPER FUNCTIONS---------------------------------------- -}


filterOverUnderAndThreeWayHandicap : [OutcomeOdds] -> [OutcomeOdds]
filterOverUnderAndThreeWayHandicap outcomeOdds = unchangedOutcomeOdds <> filteredOdds
  where
    (overUnderOutcomeOdds,threeWayHOutcomeOdds, unchangedOutcomeOdds) =
        partition3 (isOverUnder . (.outcome.type_)) (isThreeWayH . (.outcome.type_)) outcomeOdds
    groupedOverUnderOutcomes = sortAndGroup (outcomeGroupByPredicate) (.outcome.order) overUnderOutcomeOdds
    groupedThreeWayOutcomes = sortAndGroup (outcomeGroupByPredicate) (.outcome.order) threeWayHOutcomeOdds
    filteredThreeWayHOdd = minimumOn (\[o1, o2, o3] -> maxNum [o1.odd, o2.odd, o3.odd]) groupedThreeWayOutcomes
    closestTo = (2.0 : Decimal)
    filteredOverUnderOdd = minimumOn (\[o1, o2] -> ((abs (o1.odd - closestTo)) + (abs (o2.odd - closestTo)))) groupedOverUnderOutcomes
    filteredOdds = (concat (catOptionals [filteredThreeWayHOdd,filteredOverUnderOdd]))

partition3 : (a -> Bool) -> (a -> Bool) -> [a] -> ([a], [a], [a])
partition3 p1 p2 = foldr (\ elem (listOnP1, listOnP2, restOfList) -> if
    | p1 elem -> (elem::listOnP1, listOnP2, restOfList)
    | p2 elem -> (listOnP1, elem::listOnP2, restOfList)
    | otherwise -> (listOnP1, listOnP2, elem::restOfList)
  ) ([], [], [])

minimumOn : (Ord k) => (a -> k) -> [a] -> Optional a
minimumOn _ [] = None
minimumOn f xs = Some (List.minimumOn f xs)

maxNum : (Ord a) => [a] -> Optional a
maxNum [x] = Some x
maxNum [] = None
maxNum (x::x'::xs) = maxNum ((if x >= x' then x else x')::xs)

isThreeWayH : OutcomeType -> Bool
isThreeWayH (ThreeWayHandicap _) = True
isThreeWayH _ = False

isOverUnder : OutcomeType -> Bool
isOverUnder (OverUnder _) = True
isOverUnder _ = False

sortAndGroup : (Ord k) => (a -> a -> Bool) -> (a -> k) -> [a] -> [[a]]
sortAndGroup groupByPredicate sortOnPredicate xs = List.groupBy groupByPredicate $ List.sortOn sortOnPredicate xs

-- | This function will work well for all outcome types except maybe for Two way due to some changes
-- for the Two Way outcomes in the sameOutcome function
outcomeGroupByPredicate : OutcomeOdds -> OutcomeOdds -> Bool
outcomeGroupByPredicate x y = sameOutcome x.outcome y.outcome

-- | This function does not fully compare two outcomes to be exactly the same, except for Two Way outcomes.
-- For the rest of the outcomes it checks if two seperate outcomes are from the same outcome group for any given event
sameOutcome : Outcome -> Outcome -> Bool
sameOutcome Outcome{type_ = ThreeWay} Outcome{type_ = ThreeWay} = True
sameOutcome Outcome{type_ = TwoWay} Outcome{type_ = TwoWay} = True
sameOutcome Outcome{type_ = Winner} Outcome{type_ = Winner} = True
sameOutcome Outcome{type_ = Default} Outcome{type_ = Default} = True
sameOutcome Outcome{type_ = OverUnder d1} Outcome{type_ = OverUnder d2} = d1 == d2
sameOutcome Outcome{type_ = ThreeWayHandicap d1, participantId = p1, subtype = s1}
            Outcome{type_ = ThreeWayHandicap d2, participantId = p2, subtype = s2}
            = (d1 == d2 && p1 == p2 && s1 == s2) || if p1 == p2 then s1 /= s2 && d1 == d2
                            else isInverse d1 d2
sameOutcome _ _ = False

isInverse : Decimal -> Decimal -> Bool
isInverse d1 d2 = d1 /= d2 && abs d1 == abs d2

validOutcomesResults : (HasField "outcome" b Outcome) => a -> (a -> [b]) -> (a -> [Result]) -> Bool
validOutcomesResults eventDetails outcomesOn resultsOn =

  validOutcomeTypes && validOutcomeCount && validOutcomes && validResults

  where
    outcomes = outcomesOn eventDetails
    sortedOutcomes = sortBy (\ o1 o2 -> compare o1.outcome.type_ o2.outcome.type_) outcomes
    groupedOutcomes = groupBy (\ o1 o2 -> o1.outcome.type_ == o2.outcome.type_) sortedOutcomes

    hasOutcome outcomeType = any (\ (o::_) -> o.outcome.type_ == outcomeType) groupedOutcomes

    hasThreeWay = hasOutcome ThreeWay
    hasTwoWay = hasOutcome TwoWay

    validOutcomeTypes = null groupedOutcomes || not (hasThreeWay && hasTwoWay)
    validOutcomeCount = all (\ group@(o::_) -> check o.outcome (length group)) groupedOutcomes
    validOutcomes = all ((`check` ()) . (.outcome)) outcomes
    validResults = all (`check` ()) $ resultsOn eventDetails



