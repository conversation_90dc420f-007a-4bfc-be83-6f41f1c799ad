module EnetPulseIntegration.Static where

import DA.Map (Map)

import EnetPulseIntegration.Events (OutcomeType, Status, Result)


template Outcomes
    with
        integrationParty    : Party
        observers           : [Party]
        outcomeMap          : Map Text OutcomeType
    where
        signatory integrationParty
        observer observers


template Statuses
    with
        integrationParty    : Party
        observers           : [Party]
        statusMap           : Map Text Status
    where
        signatory integrationParty
        observer observers


template Results
    with
        integrationParty    : Party
        observers           : [Party]
        resultsMap          : Map Text Result
    where
        signatory integrationParty
        observer observers


template SportTranslations
    with
        integrationParty    : Party
        observers           : [Party]
        -- v: Map with initial key = language (pt, es, en-US), and value = (sportFK, sportName)
        sportsMap           : Map Text (Map Text Text)
    where
        signatory integrationParty
        observer observers