# for config file options, refer to
# https://docs.daml.com/tools/assistant.html#project-config-file-daml-yaml

sdk-version: 2.9.4
name: gambyl-jumio-impl
source: daml
init-script:
parties:
  - Operator
version: ${GAMBYL_JUMIO_IMPL_CURRENT_VERSION}
dependencies:
  - daml-prim
  - daml-stdlib
  - daml-script
sandbox-options:
  - --wall-clock-time
build-options:
  - --ghc-option=-Wall
  - --ghc-option=-Wno-name-shadowing
  - --ghc-option=-Wno-unused-do-bind
  - --ghc-option=-Wmissing-import-lists
  - --ghc-option=-Werror
  - --output=../../../build/implementation/gambyl-jumio-impl-${GAMBYL_JUMIO_IMPL_CURRENT_VERSION}.dar