module JumioIntegration.Identity where

import DA.Optional qualified as O (whenSome)

{- -------------------------------------------TEMPLATES------------------------------------------- -}

template InitialIdentityVerificationRequest
    with
        operator            : Party
        provider            : Party
        requestFrom         : Party
        integrationParty    : Party
        locale              : Text
        observers           : [Party]
    where
        signatory requestFrom
        observer integrationParty

        key (integrationParty, requestFrom) : (Party, Party)
        maintainer key._2

        choice ProcessIdentityRequest : ContractId IdentityVerificationRequest
            with
                redirectUrl         : Text
                requestFromHashed   : Text
            controller integrationParty
                do
                    createdAt <- getTime
                    createOrLookup IdentityVerificationRequest with ..


template IdentityVerificationRequest
    with
        operator            : Party
        provider            : Party
        requestFrom         : Party
        integrationParty    : Party
        redirectUrl         : Text
        requestFromHashed   : Text
        observers           : [Party]
        createdAt           : Time
    where
        signatory integrationParty, requestFrom
        observer provider

        key (integrationParty, requestFrom) : (Party, Party)
        maintainer key._1, key._2

        choice StartVerification : ContractId PendingIdentityRequest
            controller requestFrom
                do
                    optRejectedIdentity <- lookupByKey @RejectedIdentity (integrationParty, requestFrom)

                    O.whenSome optRejectedIdentity archive

                    create PendingIdentityRequest with ..

        choice ArchiveIdentityRequest : ()
            controller provider
                do
                    return ()


template PendingIdentityRequest
    with
        operator            : Party
        provider            : Party
        requestFrom         : Party
        integrationParty    : Party
        redirectUrl         : Text
        requestFromHashed   : Text
        observers           : [Party]
        createdAt           : Time
    where
        signatory integrationParty, requestFrom
        observer provider

        key (integrationParty, requestFrom) : (Party, Party)
        maintainer key._1, key._2

        choice VerifyIdentity : ContractId VerifiedIdentity
            with
                dataUrl     : Text
                userData    : User
            controller integrationParty
                do
                    create VerifiedIdentity with ..


        choice RejectIdentity : ContractId RejectedIdentity
            with
                dataUrl : Text
                rejectReason : Text
            controller integrationParty
                do
                    create RejectedIdentity with ..

        choice ArchivePendingIdentityRequest : ()
            controller provider
                do
                    return ()



template VerifiedIdentity
    with
        requestFrom         : Party
        integrationParty    : Party
        userData            : User
        dataUrl             : Text
        observers           : [Party]
    where
        signatory integrationParty, requestFrom
        observer observers

        key (integrationParty, requestFrom) : (Party, Party)
        maintainer key._1, key._2

        choice Verified_RetrieveArchive : VerifiedIdentity
            with
                executingParty : Party
            controller executingParty
            do
                assertMsg ("Controller party " <> show executingParty <> "isn't a contract stakeholder") $ executingParty `elem` observers
                do return this


template RejectedIdentity
    with
        requestFrom         : Party
        integrationParty    : Party
        dataUrl             : Text
        rejectReason        : Text
        observers           : [Party]
    where
        signatory integrationParty, requestFrom
        observer observers

        key (integrationParty, requestFrom) : (Party, Party)
        maintainer key._1, key._2

        choice Rejected_RetrieveArchive : RejectedIdentity
            with
                executingParty : Party
            controller executingParty
            do
                assertMsg ("Controller party " <> show executingParty <> "isn't a contract stakeholder") $ executingParty `elem` observers
                do return this



{- ------------------------------------------DATA TYPES------------------------------------------- -}

data User = User
    with
        firstName      : Text
        lastName       : Text
        birthday       : Date
        city           : Text
        country        : Text
        postalCode     : Text
        subDivision    : Text
        addressLine1   : Text
        addressLine2   : Text

    deriving (Show, Eq)

{- ------------------------------------------Util Functions------------------------------------------- -}

createOrLookup : forall t k. (HasCreate t, Template t, TemplateKey t k) => t -> Update (ContractId t)
createOrLookup t = lookupByKey @t (key t) >>= \case
    (Some cid) -> return cid
    None       -> create t