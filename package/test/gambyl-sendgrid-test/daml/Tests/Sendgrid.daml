module Tests.Sendgrid where

import Daml.Script

import DA.Optional qualified as Optional

---------------------------------- All -----------------------------------------

-- testAll : Script ()
-- testAll = script do

--         stakeholders@[sendgrid, client] <- allocateParties

--         return ()

{- ---------------------------------------HELPER FUNCTIONS---------------------------------------- -}

allocateParties : Script [Party]
allocateParties = script do
    allocate ["Sendgrid", "Alice"]
    where
        allocate = mapA (\ p -> allocatePartyWithHint p (PartyIdHint p))

queryKey : TemplateKey t k => Party -> k -> <PERSON>ript (ContractId t)
queryKey party key_ = queryContractKey party key_ >>= (return . (._1) . Optional.fromSomeNote ("Contract doesn't exist"))