sdk-version: 2.9.4
name: gambyl-quickbooks-test
source: daml
init-script:
parties:
  - Quickbooks
  - Alice
version: ${GAMBYL_QUICKBOOKS_TEST_VERSION}
dependencies:
  - daml-prim
  - daml-stdlib
  - daml-script
data-dependencies:
  - ../../../build/implementation/gambyl-quickbooks-impl-${GAMBYL_QUICKBOOKS_IMPL_CURRENT_VERSION}.dar
sandbox-options:
  - --wall-clock-time
build-options:
  - --ghc-option=-Wall
  - --ghc-option=-Wno-name-shadowing
  - --ghc-option=-Wno-unused-do-bind
  - --ghc-option=-fno-warn-missing-signatures
  - --ghc-option=-Werror
  - --output=../../../build/test/gambyl-quickbooks-test-${GAMBYL_QUICKBOOKS_TEST_VERSION}.dar