
module Tests.Identity where

import Daml.Script
import DA.Optional qualified as O (fromSome, fromSomeNote, isSome, isNone)
import JumioIntegration.Identity qualified as IntegrationIdentity
import DA.Date (date, Month(..))


testInitialIdentityVerificationRequest : [Party] -> Script [Party]
testInitialIdentityVerificationRequest ~stakeholders@[jumio, requestFrom, observer_, _] = script do

    submit requestFrom do
        createCmd IntegrationIdentity.InitialIdentityVerificationRequest
            with
                operator = observer_
                provider = observer_
                integrationParty = jumio
                requestFrom
                locale = "pt-PT"
                observers = [observer_]

    return stakeholders


run_testInitialIdentityVerificationRequest = script do
    allocateParties >>=
        testInitialIdentityVerificationRequest


testProcessIdentityRequest : [Party] -> Script [Party]
testProcessIdentityRequest ~stakeholders@[jumio, requestFrom, _, _] = script do

    submit jumio do
        exerciseByKeyCmd @IntegrationIdentity.InitialIdentityVerificationRequest (jumio, requestFrom)
            IntegrationIdentity.ProcessIdentityRequest
                with
                    redirectUrl = "localhost"
                    requestFromHashed = "some_hashed_value"

    return stakeholders


run_testProcessIdentityRequest = script do
    allocateParties >>=
        testInitialIdentityVerificationRequest >>=
        testProcessIdentityRequest


testStartVerification : [Party] -> Script [Party]
testStartVerification ~stakeholders@[jumio, requestFrom, _, _] = script do

    submit requestFrom do
        exerciseByKeyCmd @IntegrationIdentity.IdentityVerificationRequest (jumio, requestFrom)
            IntegrationIdentity.StartVerification

    return stakeholders


run_testStartVerification = script do
    allocateParties >>=
        testInitialIdentityVerificationRequest >>=
        testProcessIdentityRequest >>=
        testStartVerification



testRejectIdentity : [Party] -> Script [Party]
testRejectIdentity ~stakeholders@[jumio, requestFrom, _, _] = script do

    submit jumio do
        exerciseByKeyCmd @IntegrationIdentity.PendingIdentityRequest (jumio, requestFrom)
            IntegrationIdentity.RejectIdentity with dataUrl = "localhost", rejectReason = "Testing"

    return stakeholders


run_testRejectIdentity = script do
    allocateParties >>=
        testInitialIdentityVerificationRequest >>=
        testProcessIdentityRequest >>=
        testStartVerification >>=
        testRejectIdentity


testRetrieveArchiveRejectedIdentity : [Party] -> Script ()
testRetrieveArchiveRejectedIdentity ~[jumio, requestFrom, observer_, _] = script do

    submit observer_ do
        exerciseByKeyCmd @IntegrationIdentity.RejectedIdentity (jumio, requestFrom)
            IntegrationIdentity.Rejected_RetrieveArchive with executingParty = observer_

    optRejectedIdentity <- queryContractKey @IntegrationIdentity.RejectedIdentity observer_ (jumio, requestFrom)
    assertMsg ("Rejected Identity Contract wasn't archived") $ O.isNone optRejectedIdentity

    return ()

run_testRetrieveArchiveRejectedIdentity = script do
    allocateParties >>=
        testInitialIdentityVerificationRequest >>=
        testProcessIdentityRequest >>=
        testStartVerification >>=
        testRejectIdentity >>=
        testRetrieveArchiveRejectedIdentity


testRetrieveArchiveRejectedIdentityFailNotStakeholder : [Party] -> Script ()
testRetrieveArchiveRejectedIdentityFailNotStakeholder ~[jumio, requestFrom, observer_, notStakeholder] = script do

    submitMustFail notStakeholder do
        exerciseByKeyCmd @IntegrationIdentity.RejectedIdentity (jumio, requestFrom)
            IntegrationIdentity.Rejected_RetrieveArchive with executingParty = notStakeholder

    optRejectedIdentity <- queryContractKey @IntegrationIdentity.RejectedIdentity observer_ (jumio, requestFrom)
    assertMsg ("Rejected Identity Contract was archived by a non stakeholder") $ O.isSome optRejectedIdentity


    return ()

run_testRetrieveArchiveRejectedIdentityFailNotStakeholder = script do
    allocateParties >>=
        testInitialIdentityVerificationRequest >>=
        testProcessIdentityRequest >>=
        testStartVerification >>=
        testRejectIdentity >>=
        testRetrieveArchiveRejectedIdentityFailNotStakeholder



testRetryStartVerification : [Party] -> Script ()
testRetryStartVerification ~stakeholders@[jumio, requestFrom, _, _] = script do

    optContract <- queryContractKey @IntegrationIdentity.RejectedIdentity jumio (jumio, requestFrom)
    assertMsg ("No Reject Identity Contract found") $ O.isSome optContract

    testStartVerification stakeholders

    optContract <- queryContractKey @IntegrationIdentity.RejectedIdentity jumio (jumio, requestFrom)
    assertMsg ("A Reject Identity Contract was found, where there should be none") $ O.isNone optContract

    return ()

run_testRetryIdentityRequest = script do
    allocateParties >>=
    -- First attempt of verification
        testInitialIdentityVerificationRequest >>=
        testProcessIdentityRequest >>=
        testStartVerification >>=
        testRejectIdentity >>=
    -- Retry of verification
        testInitialIdentityVerificationRequest >>=
        testProcessIdentityRequest >>=
        testRetryStartVerification


testVerifyIdentity : ([Party], Date)-> Script [Party]
testVerifyIdentity ~(stakeholders@[jumio, requestFrom, _, _], dateOfBirth) = script do

    submit jumio do
        exerciseByKeyCmd @IntegrationIdentity.PendingIdentityRequest (jumio, requestFrom)
            IntegrationIdentity.VerifyIdentity
                with
                    dataUrl = "localhost"
                    userData = IntegrationIdentity.User
                        with
                            firstName = show requestFrom
                            lastName = "Doe"
                            birthday = dateOfBirth
                            city ="Lisbon"
                            country = "Portugal"
                            postalCode ="00000-00"
                            subDivision ="Douro"
                            addressLine1 ="Muito Fixe"
                            addressLine2 = "Lumiar"

    return stakeholders


run_testVerifyIdentity = script do
    allocateParties >>=
        testInitialIdentityVerificationRequest >>=
        testProcessIdentityRequest >>=
        testStartVerification >>=
        testVerifyIdentity . ( , date 1996 Jul 10)


testRetrieveArchiveVerifiedIdentity : [Party] -> Script ()
testRetrieveArchiveVerifiedIdentity ~[jumio, requestFrom, observer_, _] = script do

    submit observer_ do
        exerciseByKeyCmd @IntegrationIdentity.VerifiedIdentity (jumio, requestFrom)
            IntegrationIdentity.Verified_RetrieveArchive with executingParty = observer_

    optVerifiedIdentity <- queryContractKey @IntegrationIdentity.VerifiedIdentity observer_ (jumio, requestFrom)
    assertMsg ("Verified Identity Contract wasn't archived") $ O.isNone optVerifiedIdentity

    return ()

run_testRetrieveArchiveVerifiedIdentity = script do
    allocateParties >>=
        testInitialIdentityVerificationRequest >>=
        testProcessIdentityRequest >>=
        testStartVerification >>=
        testVerifyIdentity . ( , date 1996 Jul 10) >>=
        testRetrieveArchiveVerifiedIdentity


testRetrieveArchiveVerifiedIdentityFailNotStakeholder : [Party] -> Script ()
testRetrieveArchiveVerifiedIdentityFailNotStakeholder ~[jumio, requestFrom, observer_, notStakeholder] = script do

    submitMustFail notStakeholder do
        exerciseByKeyCmd @IntegrationIdentity.VerifiedIdentity (jumio, requestFrom)
            IntegrationIdentity.Verified_RetrieveArchive with executingParty = notStakeholder

    optVerifiedIdentity <- queryContractKey @IntegrationIdentity.VerifiedIdentity observer_ (jumio, requestFrom)
    assertMsg ("Verified Identity Contract was archived by a non stakeholder") $ O.isSome optVerifiedIdentity


    return ()

run_testRetrieveArchiveVerifiedIdentityFailNotStakeholder = script do
    allocateParties >>=
        testInitialIdentityVerificationRequest >>=
        testProcessIdentityRequest >>=
        testStartVerification >>=
        testVerifyIdentity . ( , date 1996 Jul 10)>>=
        testRetrieveArchiveVerifiedIdentityFailNotStakeholder

testAll : Script ()
testAll = script do

        stakeholders <- allocateParties

    -- First attempt of verification
        testInitialIdentityVerificationRequest stakeholders

        testProcessIdentityRequest stakeholders

        testStartVerification stakeholders

        testRejectIdentity stakeholders

        testRetrieveArchiveRejectedIdentity stakeholders

    -- Retry of verification
        testInitialIdentityVerificationRequest stakeholders

        testProcessIdentityRequest stakeholders

        testStartVerification stakeholders

        testVerifyIdentity (stakeholders, date 2014 Jul 9)

        testRetrieveArchiveVerifiedIdentity stakeholders

        return ()

{- ---------------------------------------HELPER FUNCTIONS---------------------------------------- -}

allocateParties : Script [Party]
allocateParties = script do
    allocate ["Jumio", "Alice", "Bob", "Charlie"]
    where
        allocate = mapA (\ p -> allocatePartyWithHint p (PartyIdHint p))


getExistingParties : Script [Party]
getExistingParties = do
    retrieve ["Jumio", "Alice", "Bob", "Charlie"]
    where
        retrieve = pure . O.fromSome . mapA partyFromText


queryKey : TemplateKey t k => Party -> k -> Script (ContractId t)
queryKey party key_ = queryContractKey party key_ >>= (return . (._1) . O.fromSomeNote ("Contract doesn't exist"))