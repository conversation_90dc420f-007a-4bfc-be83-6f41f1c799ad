# for config file options, refer to
# https://docs.daml.com/tools/assistant.html#project-config-file-daml-yaml

sdk-version: 2.9.4
name: gambyl-enetpulse-test
source: daml
init-script:
parties:
  - EnetPulse
  - Alice
version: ${GAMBYL_ENETPULSE_TEST_VERSION}
dependencies:
  - daml-prim
  - daml-stdlib
  - daml-script
data-dependencies:
  - ../../../build/implementation/gambyl-enetpulse-impl-${GAMBYL_ENETPULSE_IMPL_CURRENT_VERSION}.dar
sandbox-options:
  - --wall-clock-time
build-options:
  - --ghc-option=-Wall
  - --ghc-option=-Wno-name-shadowing
  - --ghc-option=-Wno-unused-do-bind
  - --ghc-option=-fno-warn-missing-signatures
  - --ghc-option=-Werror
  - --output=../../../build/test/gambyl-enetpulse-test-${GAMBYL_ENETPULSE_TEST_VERSION}.dar