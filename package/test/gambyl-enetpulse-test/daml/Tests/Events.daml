module Tests.Events where

import Daml.Script

import DA.Optional qualified as Optional (fromSome)
import DA.<PERSON><PERSON> qualified as Stack

import EnetPulseIntegration.Configuration qualified as Configuration
import EnetPulseIntegration.Events qualified as IntegrationEvents hiding (Status(..), Outcome(..), OutcomeType(..), OutcomeSubType(..))
import EnetPulseIntegration.Events (IntegrationEventKey, Status(..), OutcomeOdds(..), Outcome(..), OutcomeType(..), OutcomeSubType(..))
import DA.List (head)
import DA.Either (isLeft, isRight, fromRight)

testCreateConfiguration : [Party] -> Script ()
testCreateConfiguration ~[gambyl, enetPulse] = do

  submit enetPulse do
    createCmd Configuration.Configuration
      with
        integrationParty    = enetPulse
        user                = "test-user"
        token               = "test-token"
        observers           = [gambyl]

  return ()

run_testCreateConfiguration = script do
    allocateParties >>=
        testCreateConfiguration

testOnHoldSuccessLoop : ([Party], Text) -> Script ([Party], Text)
testOnHoldSuccessLoop result@(~[gambyl, enetPulse], eventId) = script do

    now <- getTime
    let integrationTime = now
    submit enetPulse do
        createCmd $ getDefaultUpdateEvent enetPulse [gambyl] eventId now integrationTime "yes"

    let
        integrationEventKey : IntegrationEventKey = (enetPulse, eventId, integrationTime)

    eventInstrumentUpdateOnHold <- submit gambyl do
        createCmd $ getDefaultOnHoldUpdate gambyl [gambyl, enetPulse] eventId (gambyl, gambyl, eventId) integrationEventKey (gambyl, gambyl, gambyl)
    retry1 <- submit gambyl do
        exerciseCmd eventInstrumentUpdateOnHold IntegrationEvents.RetryUpdate with executingParty = gambyl

    query1 <- query @IntegrationEvents.EventInstrumentUpdateOnHold gambyl
    let retry1data = snd (head query1)
    assertMsg "New contract was not created" (isRight retry1)
    assertMsg "Retries should be 1" (retry1data.retries == 1)

    let cid = fromRight eventInstrumentUpdateOnHold retry1
    retry2 <- submit gambyl do
        exerciseCmd cid IntegrationEvents.RetryUpdate with executingParty = gambyl

    query2 <- query @IntegrationEvents.EventInstrumentUpdateOnHold gambyl
    let retry2data = snd (head query2)
    assertMsg "New contract was not created" (isRight retry2)
    assertMsg "Retries should be 2" (retry2data.retries == 2)

    let cid2 = fromRight eventInstrumentUpdateOnHold retry2
    retry3 <- submit gambyl do
        exerciseCmd cid2 IntegrationEvents.RetryUpdate with executingParty = gambyl

    query3 <- query @IntegrationEvents.EventInstrumentUpdateOnHold gambyl
    let retry3data = snd (head query3)
    assertMsg "New contract was not created" (isRight retry3)
    assertMsg "Retries should be 3" (retry3data.retries == 3)

    let cid3 = fromRight eventInstrumentUpdateOnHold retry3
    retry4 <- submit gambyl do
        exerciseCmd cid3 IntegrationEvents.RetryUpdate with executingParty = gambyl

    query4 <- query @IntegrationEvents.EventInstrumentUpdateOnHold gambyl
    let retry4data = snd (head query4)
    assertMsg "New contract was not created" (isRight retry4)
    assertMsg "Retries should be 4" (retry4data.retries == 4)

    let cid4 = fromRight eventInstrumentUpdateOnHold retry4
    retry5 <- submit gambyl do
        exerciseCmd cid4 IntegrationEvents.RetryUpdate with executingParty = gambyl

    query5 <- query @IntegrationEvents.EventInstrumentUpdateOnHold gambyl
    let retry5data = snd (head query5)
    assertMsg "New contract was not created" (isRight retry5)
    assertMsg "Retries should be 5" (retry5data.retries == 5)

    let cid5 = fromRight eventInstrumentUpdateOnHold retry5
    retry6 <- submit gambyl do
        exerciseCmd cid5 IntegrationEvents.RetryUpdate with executingParty = gambyl

    assertMsg "Contract was not archived" (isLeft retry6)
    query6 <- query @IntegrationEvents.EventInstrumentUpdateOnHold gambyl
    assertMsg "Query returned contracts" (null query6)

    return result

run_testCreateOnHold = script do
    allocateParties >>=
        testOnHoldSuccessLoop . (, "AvB")


testCreateEvent : ([Party], Text) -> Script ([Party], Text)
testCreateEvent result@(~[gambyl, enetPulse], eventId) = script do

  now <- getTime
  submit enetPulse do
    createCmd $ getDefaultCreateEvent enetPulse [gambyl] eventId now now "yes"

  return result

run_testCreateEvent = script do
    allocateParties >>=
        testCreateEvent . (, "AvB")


testCreateEventFailBadOutcomes : ([Party], Text) -> Script ([Party], Text)
testCreateEventFailBadOutcomes result@(~[gambyl, enetPulse], eventId) = script do

    now <- getTime
    let
        event       = getDefaultCreateEvent enetPulse [gambyl] eventId now now "yes"
        [p1, p2]    = event.eventDetails.eventParticipants


    -- Outcomes with more than on type
    submitMustFail enetPulse do
        createCmd event with
            eventDetails.outcomes = [
                        outcomeWithOdd None p1.order 5 ThreeWay Draw 2.0,
                        outcomeWithOdd None p1.order 5 TwoWay Draw 2.0
                    ]

    -- Outcomes for Threeway, with less than 3 outcomes
    submitMustFail enetPulse do
        createCmd event with
            eventDetails.outcomes = [
                    outcomeWithOdd None p1.order 5 ThreeWay Draw 2.0,
                    outcomeWithOdd None p1.order 5 ThreeWay Draw 2.0
                ]

    -- Outcomes for Threeway, with more than 3 outcomes
    submitMustFail enetPulse do
        createCmd event with
            eventDetails.outcomes = [
                    outcomeWithOdd None p1.order 5 ThreeWay Draw 2.0,
                    outcomeWithOdd None p1.order 5 ThreeWay Draw 2.0,
                    outcomeWithOdd None p1.order 5 ThreeWay Draw 2.0,
                    outcomeWithOdd None p1.order 5 ThreeWay Draw 2.0
                ]

    -- Outcomes for Twoway, with less than 2 outcomes
    submitMustFail enetPulse do
        createCmd event with
            eventDetails.outcomes = [
                        outcomeWithOdd None p1.order 5 TwoWay Draw 2.0
                    ]

    -- Outcomes for Twoway, with more than 2 outcomes
    submitMustFail enetPulse do
        createCmd event with
            eventDetails.outcomes = [
                        outcomeWithOdd None p1.order 5 TwoWay Draw 2.0,
                        outcomeWithOdd None p1.order 5 TwoWay Draw 2.0,
                        outcomeWithOdd None p1.order 5 TwoWay Draw 2.0
                    ]

    -- Outcomes for Default, with more than 1 outcome
    submitMustFail enetPulse do
        createCmd event with
            eventDetails.outcomes = [
                        outcomeWithOdd None p1.order 5 Default Draw 2.0,
                        outcomeWithOdd None p1.order 5 Default Draw 2.0
                    ]

    -- Outcomes for Threeway, incorrect outcome with Subtype Win but no associated participant
    let
        goodTwo_ThreeWayOutcomes = [
                            outcomeWithOdd None p2.order 5 ThreeWay Draw 2.0,
                            outcomeWithOdd (Some p2.id) (p2.order) 5 ThreeWay Win 2.0
                        ]

    submitMustFail enetPulse do
        createCmd event with
            eventDetails.outcomes = (outcomeWithOdd None p1.order 5 ThreeWay Win 2.0) :: goodTwo_ThreeWayOutcomes

    -- Outcomes for Threeway, incorrect outcome with Subtype Draw but has associated participant
    submitMustFail enetPulse do
        createCmd event with
            eventDetails.outcomes = (outcomeWithOdd (Some p1.id) (p1.order) 5 ThreeWay Draw 2.0) :: goodTwo_ThreeWayOutcomes

    -- Outcomes for Two, incorrect outcome with Subtype Win but no associated participant
    let
        goodTwo_TwoWayOutcomes = [
                            outcomeWithOdd (Some p2.id) (p2.order) 5 ThreeWay Win 2.0
                        ]

    submitMustFail enetPulse do
        createCmd event with
            eventDetails.outcomes = (outcomeWithOdd None p1.order 5 TwoWay Win 2.0) :: goodTwo_TwoWayOutcomes

    return result

run_testCreateEventFailBadOutcomes = script do
    allocateParties >>=
        testCreateEventFailBadOutcomes . (, "AvB")


testUpdateEvent : Stack.HasCallStack => ([Party], Text) -> Script ()
testUpdateEvent (~[gambyl, enetPulse], eventId) = script do

  now <- getTime
  let
    eventToUpdate   = getDefaultUpdateEvent enetPulse [gambyl] eventId now now "yes"
    participant1::_ = eventToUpdate.eventDetails.eventParticipants

  submit enetPulse do
    createCmd eventToUpdate with
      status = Finished
      results = [
          Outcome with
            participantId = Some participant1.id
            participantOrder = participant1.order
            order = 5
            type_ = ThreeWay
            subtype = Win
        ]

  return ()

run_testUpdateEvent = script do
    allocateParties >>=
        testCreateEvent . (, "AvB") >>=
        testUpdateEvent


testUpdateEventFailBadResults : Stack.HasCallStack => ([Party], Text) -> Script ()
testUpdateEventFailBadResults (~[gambyl, enetPulse], eventId) = script do

    now <- getTime
    let
        eventToUpdate   = getDefaultUpdateEvent enetPulse [gambyl] eventId now now "yes"
        participant1::_ = eventToUpdate.eventDetails.eventParticipants

    -- Result Draw outcome
    submitMustFail enetPulse do
        createCmd eventToUpdate with
            results = [Outcome (Some participant1.id) (participant1.order) TwoWay Draw 5]

    -- Result code Rank with Win outcome, but no associated participant
    submitMustFail enetPulse do
        createCmd eventToUpdate with
            results = [Outcome None participant1.order TwoWay Win 5]

    -- Result code FinalResult with Win outcome, but no associated participant
    submitMustFail enetPulse do
        createCmd eventToUpdate with
            results = [Outcome None participant1.order TwoWay Win 5]

    -- Result code FinalResult with Draw outcome, but has an associated participant
    submitMustFail enetPulse do
        createCmd eventToUpdate with
            results = [Outcome (Some participant1.id) (participant1.order) ThreeWay Draw 5]

    -- Result code FinalResult with TwoWay type but, Draw outcome
    submitMustFail enetPulse do
        createCmd eventToUpdate with
            results = [Outcome None participant1.order TwoWay Draw 5]

run_testUpdateEventFailBadResults = script do
    allocateParties >>=
        testCreateEvent . (, "AvB") >>=
        testUpdateEventFailBadResults

{- -------------------------------------------ALL TESTS------------------------------------------- -}

testAll : Script ()
testAll = script do

    stakeholders <- allocateParties

    testCreateConfiguration stakeholders

    testCreateEvent (stakeholders, "AvB")

    testUpdateEvent (stakeholders, "AvB")

    return ()

{- ---------------------------------------HELPER FUNCTIONS---------------------------------------- -}

allocateParties : Script [Party]
allocateParties = script do
    allocate ["Gambyl", "EnetPulse"]
    where
        allocate = mapA (\ p -> allocatePartyWithHint p (PartyIdHint p))


getExistingParties : Script [Party]
getExistingParties = do
    retrieve ["Gambyl", "EnetPulse"]
    where
        retrieve = pure . Optional.fromSome . mapA partyFromText


getDefaultCreateEvent : Party -> [Party] -> Text -> Time -> Time -> Text -> IntegrationEvents.EventInstrument
getDefaultCreateEvent integrationParty observers eventId startDate integrationTime liveEvent =


    IntegrationEvents.EventInstrument with
            integrationParty
            observers
            eventId
            eventDetails        = IntegrationEvents.Details with
                                    sportFK             = "1"
                                    tournamentStageName = "Premier League"
                                    geography           = "England"
                                    eventParticipants   = [everton, arsenal]
                                    eventTitle          = "Everton vs Arsenal"
                                    eventGame          =  Some "1"
                                    startDate
                                    outcomes
            integrationTime
            status              = NotStarted
            results             = []
            liveEvent           = liveEvent

    where
        participant name order = IntegrationEvents.Participant with
            name, id = name <> "-id", order, co_op = None

        everton = participant "Everton" 1
        arsenal = participant "Arsenal" 2

        outcomes = [
                outcomeWithOdd (Some everton.id) (everton.order) 5 ThreeWay Win 2.0,
                outcomeWithOdd None everton.order 5 ThreeWay Draw 2.0,
                outcomeWithOdd (Some arsenal.id) (arsenal.order) 5 ThreeWay Win 2.0,
                outcomeWithOdd None 0 5 (OverUnder 0.5) Over 2.0,
                outcomeWithOdd None 0 5 (OverUnder 0.5) Under 2.0
            ]


getDefaultUpdateEvent : Party -> [Party] -> Text -> Time -> Time -> Text -> IntegrationEvents.EventInstrumentUpdate
getDefaultUpdateEvent integrationParty observers eventId startDate integrationTime liveEvent =

    IntegrationEvents.EventInstrumentUpdate with
            integrationParty
            observers
            eventId
            eventDetails        = IntegrationEvents.Details with
                                    sportFK             = "1"
                                    tournamentStageName = "Premier League"
                                    geography           = "England"
                                    eventParticipants   = [everton, arsenal]
                                    eventTitle          = "Everton vs Arsenal"
                                    eventGame          =  Some "1"
                                    startDate
                                    outcomes
            integrationTime
            status              = NotStarted
            results             = []
            liveEvent           = liveEvent

    where
        participant name order = IntegrationEvents.Participant with
            name, id = name <> "-id", order, co_op = None

        everton = participant "Everton" 1
        arsenal = participant "Arsenal" 2

        outcomes = [
                outcomeWithOdd (Some everton.id) (everton.order) 5 ThreeWay Win 2.0,
                outcomeWithOdd None everton.order 5 ThreeWay Draw 2.0,
                outcomeWithOdd (Some arsenal.id) (everton.order) 5 ThreeWay Win 2.0
            ]


getDefaultOnHoldUpdate : Party -> [Party] -> Text -> (Party, Party, Text) -> IntegrationEvents.IntegrationEventKey -> (Party, Party, Party) -> IntegrationEvents.EventInstrumentUpdateOnHold
getDefaultOnHoldUpdate integrationParty observers eventId oldEventKey eventUpdateKey serviceKey =

    IntegrationEvents.EventInstrumentUpdateOnHold with
            integrationParty
            observers
            eventId
            oldEventKey
            eventUpdateKey
            serviceKey
            retries = 0


outcomeWithOdd : Optional Text -> Int -> Int -> OutcomeType -> OutcomeSubType -> Decimal -> OutcomeOdds
outcomeWithOdd participantId participantOrder order type_ subtype odd  =
    OutcomeOdds with
        outcome = Outcome with
                    participantId = participantId
                    participantOrder = participantOrder
                    order = order
                    type_ = type_
                    subtype = subtype
        odd