module Tests.MarketMapSpec where

import Daml.Script

import DA.Map qualified as Map
import DA.Set qualified as Set

import Gambyl.Gambling.Event.Model (UpdateMap(..), Submarket(..), Market(..), AddToMap(..), MarketMap(..))
import EnetPulseIntegration.Events qualified as IntegrationEvents

testAddToMap : Script ()
testAddToMap = script $ do
    parties <- setupTestParties
    let initialMap = Map.empty
    withMarketMap parties initialMap $ \(mmCid, contractKey) -> do
        mmCid' <- multiSubmit parties.provider mmCid $ [
                -- add first new entry
                \mmCid -> exerciseCmd mmCid AddToMap with
                    market = Sport "boxing"
                    submarketList = [Tournament "golden gloves"]
                    geo = "USA"
                , -- add second new entry
                \mmCid -> exerciseCmd mmCid AddToMap with
                    market = Sport "futbol"
                    submarketList = [Tournament "premier league"]
                    geo = "England"
                , -- add another entry to the first one
                \mmCid -> exerciseCmd mmCid AddToMap with
                    market = Sport "boxing"
                    submarketList = [Tournament "world boxing super series"]
                    geo = "USA"
                , -- duplicate the second entry
                \mmCid -> exerciseCmd mmCid AddToMap with
                    market = Sport "futbol"
                    submarketList = [Tournament "premier league"]
                    geo = "England"

                , -- add Third new entry
                \mmCid -> exerciseCmd mmCid AddToMap with
                    market = Sport "basketBall"
                    submarketList = [Tournament "Uefa Champions League"]
                    geo = "Europe"

                , -- add Fourth new entry
                \mmCid -> exerciseCmd mmCid AddToMap with
                    market = Sport "tennis"
                    submarketList = [Tournament "Libertadores"]
                    geo = "Latin America"
            ]

        -- accept empty and return same cid
        mmCid'' <- submit parties.provider $
            exerciseCmd mmCid' AddToMap with
                market = Sport "futbol"
                submarketList = []
                geo = "England"
        assertMsg "Error: cid should not change" (mmCid'' == mmCid')

        -- verify expected

        let submap1 = Map.fromList [("USA", [Tournament "golden gloves", Tournament "world boxing super series"])]
            submap2 = Map.fromList [("England", [Tournament "premier league"])]
            submap3 = Map.fromList [("Europe", [Tournament "Uefa Champions League"])]
            submap4 = Map.fromList [("Latin America", [Tournament "Libertadores"])]
            expected = Map.fromList $ [(Sport "boxing", submap1), (Sport "futbol", submap2), (Sport "basketBall", submap3), (Sport "tennis", submap4)]

        assertExpectedMarketMap expected contractKey

testUpdateMap : Script ()
testUpdateMap = script $ do
    parties <- setupTestParties
    let  initialSubmap1 = Map.fromList [("USA", [Tournament "golden gloves", Tournament "world boxing super series"])]
         initialSubmap2 = Map.fromList [("England", [Tournament "premier league"])]
         submap1 = Map.fromList [("USA", [Tournament "world boxing super series"])]
         submap2 = Map.fromList [("USA", [Tournament "US Open"])]

         initialMap = Map.fromList $ [
               (Sport "boxing", initialSubmap1),
                (Sport "futbol", initialSubmap2)]

    withMarketMap parties initialMap $ \(mmCid, contractKey) -> do
        mmCid' <- multiSubmit parties.provider mmCid $ [
                -- remove value from existing entry
                \mmCid -> exerciseCmd mmCid UpdateMap with
                    entries = Map.fromList [
                        (Sport "boxing", submap1),
                        (Sport "futbol", initialSubmap2)]
                ,-- remove existing entry
                \mmCid -> exerciseCmd mmCid UpdateMap with
                    entries = Map.fromList $ [(Sport "boxing", submap1)]
                 ,-- remove all entries
                \mmCid -> exerciseCmd mmCid UpdateMap with
                    entries = Map.empty
                 ,-- add new entries
                \mmCid -> exerciseCmd mmCid UpdateMap with
                    entries = Map.fromList [
                            (Sport "boxing", submap1),
                            (Sport "tennis", submap2),
                            (Sport "futbol", initialSubmap2)]
            ]
        -- verify expected
        let expected = Map.fromList [(Sport "boxing", submap1),
                                     (Sport "tennis", submap2),
                                     (Sport "futbol", initialSubmap2)]
        assertExpectedMarketMap expected contractKey

        -- accept entries equals to the currents and return same cid
        mmCid'' <- submit parties.provider $
            exerciseCmd mmCid' UpdateMap with
                entries = expected
        assertMsg "Error: cid should not change" (mmCid'' == mmCid')

{- -----------------------------------------CUSTOM TYPES------------------------------------------ -}

data TestParties = TestParties with
  operator: Party
  provider: Party

{- ---------------------------------------HELPER FUNCTIONS---------------------------------------- -}

setupTestParties: Script TestParties
setupTestParties = script do
  let parties =  ["operator", "provider"]
  [operator, provider] <- mapA allocateParty parties
  return $ TestParties with
    operator
    provider

withMarketMap : TestParties -> Map.Map Market (Map.Map IntegrationEvents.Geography [Submarket]) -> ((ContractId MarketMap, (Party, Party)) -> Script ()) -> Script ()
withMarketMap parties initialMap fn = script $ do
    let signers = [parties.operator, parties.provider]
    mmCid <- submitMulti signers [] $
            createCmd MarketMap with
                operator = parties.operator
                provider = parties.provider
                map = initialMap
                public = Set.empty
    let contractKey = (parties.operator, parties.provider)
    fn (mmCid, contractKey)

multiSubmit : (Action f, HasSubmit f cmds) =>
    Party ->
    (ContractId t) ->
    [(ContractId t) ->
    cmds (ContractId t)] ->
    f (ContractId t)
multiSubmit party cid  = \case
        [] -> pure cid
        (cmd::rest) -> do
            cid' <- submit party $ cmd cid
            multiSubmit party cid' rest

assertExpectedMarketMap : Map.Map Market (Map.Map IntegrationEvents.Geography [Submarket]) -> (Party, Party) -> Script ()
assertExpectedMarketMap expected contractKey = script $ do
    mMarketMap <- queryContractKey @MarketMap contractKey._1 contractKey
    case mMarketMap of
        Some (_, MarketMap{map}) | map == expected -> pure ()
        other -> fail $ "got other" <> show other
