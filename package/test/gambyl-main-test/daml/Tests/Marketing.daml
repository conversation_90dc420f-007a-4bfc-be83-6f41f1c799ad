module Tests.Marketing where

import Daml.Script

import DA.Action    ((>=>))
import DA.Date      (Month(..))
import DA.Date qualified as Date hiding (Month(..))
import DA.Either qualified as Either
import DA.List      ((\\))
import DA.Map qualified as Map hiding (Map)
import DA.Optional qualified as Optional
import DA.Set qualified as Set
import DA.<PERSON>ack qualified as Stack (HasCallStack)
import DA.Time qualified as Time

import EnetPulseIntegration.Events  (OutcomeType(ThreeWay), OutcomeSubType(..))
import EnetPulseIntegration.Events qualified as IntegrationEvents hiding (OutcomeType(..), OutcomeSubType(..))

import Gambyl.Gambling.Event.Model qualified as EventModel
import Gambyl.Gambling.Role qualified as GamblingRole
import Gambyl.Marketing.Model       (PromotionType(..), PromotionAction(..), PromotionDetails(..), Redemption(..))
import Gambyl.Marketing.Model qualified as MarketingModel hiding (PromotionType(..), PromotionAction(..), PromotionDetails(..), Redemption(..))
import Gambyl.Marketing.Service qualified as MarketingService
import Gambyl.Utils qualified as GambylUtils

import Tests.Onboarding qualified as TestOnboarding
import Tests.Utils qualified as TestUtils
import Tests.Utils      (OutcomeListWithOddType(..))

import Scripts.Bootstrap.Onboarding.Parties (Parties(..))
import Scripts.Common qualified as Common (msgWithFunc)

{- ------------------------------------MARKETING SERVICE TESTS------------------------------------- -}

testRequestMarketingService : Parties -> Script Parties
testRequestMarketingService parties@Parties{operator, gambyl, marketingManager} = script do

    serviceRequest <- submit marketingManager do
       createCmd MarketingService.Request
          with
             customer = marketingManager
             provider = gambyl

    submit gambyl do
       exerciseByKeyCmd @GamblingRole.Role (operator, gambyl) GamblingRole.ApproveMarketingRequest
          with
             marketingRequestCid = serviceRequest

    return parties

run_testRequestMarketingService =
    TestOnboarding.testGamblingRole >>=
    testRequestMarketingService

testRequestMarketingServiceForExistingService : Parties -> Script Parties
testRequestMarketingServiceForExistingService parties@Parties{operator, gambyl, marketingManager} = script do

    serviceRequest <- submit marketingManager do
       createCmd MarketingService.Request
          with
             customer = marketingManager
             provider = gambyl

    submit gambyl do
       exerciseByKeyCmd @GamblingRole.Role (operator, gambyl) GamblingRole.ProcessMarketingRequest
          with
             marketingRequestCid = serviceRequest

    serviceRequest <- submit marketingManager do
       createCmd MarketingService.Request
          with
             customer = marketingManager
             provider = gambyl

    submit gambyl do
       exerciseByKeyCmd @GamblingRole.Role (operator, gambyl) GamblingRole.ProcessMarketingRequest
          with
             marketingRequestCid = serviceRequest

    return parties

run_testRequestMarketingServiceForExistingService =
    TestOnboarding.testGamblingRole >>=
    testRequestMarketingServiceForExistingService


testMultipleRequestMarketingService : Parties -> Script Parties
testMultipleRequestMarketingService parties@Parties{gambyl, marketingManager} = script do

    submit marketingManager do
       createCmd MarketingService.Request
          with
             customer = marketingManager
             provider = gambyl

    submitMustFail marketingManager do
       createCmd MarketingService.Request
          with
             customer = marketingManager
             provider = gambyl

    return parties

run_testMultipleRequestMarketingService =
    TestOnboarding.testGamblingRole >>=
    testMultipleRequestMarketingService


testOfferMarketingService : Stack.HasCallStack => Parties -> Script Parties
testOfferMarketingService parties@Parties{operator, gambyl, marketingManager} = script do

    marketingServiceOfferCid <- submit gambyl do
        exerciseByKeyCmd @GamblingRole.Role (operator, gambyl) GamblingRole.OfferMarketingService with customer = marketingManager

    marketingServiceCid <- submit marketingManager do
        exerciseCmd marketingServiceOfferCid MarketingService.Accept

    marketingServiceOpt <- queryContractId marketingManager marketingServiceCid
    assertMsg (Common.msgWithFunc  "Marketing Service not created") $ Optional.isSome marketingServiceOpt

    return parties

run_testOfferMarketingService =
    TestOnboarding.testGamblingRole >>=
    testOfferMarketingService

testMultipleOfferMarketingService : Stack.HasCallStack => Parties -> Script Parties
testMultipleOfferMarketingService parties@Parties{operator, gambyl, marketingManager} = script do

    submit gambyl do
        exerciseByKeyCmd @GamblingRole.Role (operator, gambyl) GamblingRole.OfferMarketingService with customer = marketingManager

    submitMustFail gambyl do
        exerciseByKeyCmd @GamblingRole.Role (operator, gambyl) GamblingRole.OfferMarketingService with customer = marketingManager

    return parties

run_testMultipleOfferMarketingService =
    TestOnboarding.testGamblingRole >>=
    testMultipleOfferMarketingService


{- ---------------------------TEST MARKETING MANAGER EVENT CHOICES---------------------------- -}
data InputData = InputData with
  parties    : Parties
  eventLabel : Text

testEventOrigination : Stack.HasCallStack => Parties -> Script InputData
testEventOrigination parties@Parties{operator, gambyl, marketingManager, eventManager} = script do

    (marketingServiceCid, _) <- TestUtils.queryContractWithKey @MarketingService.Service marketingManager (operator, gambyl, marketingManager) "Marketing Service doesn't exist"

    ----- Event Origination ----

    let
        eventLabel = "AvC"
        arsenal = TestUtils.getParticipant "Arsenal" 1
        chelsea = TestUtils.getParticipant "Chelsea" 2

    eventRequest <- submit marketingManager do
        exerciseCmd marketingServiceCid MarketingService.RequestEventOrigination  with
            eventOrigin = EventModel.CustomerEvent with
                            assetLabel  = eventLabel
                            description = "Match " <> eventLabel
                            market              = (EventModel.Sport "Soccer")
                            submarkets          = [EventModel.Tournament "Premier League"]
                            geography           = "England"
                            outcomes            = TestUtils.buildOutcomesListWithOddType [
                                                        (OutcomeListWithOddType with participant = Some arsenal,participantOrder = 1, order = 5, type_ = ThreeWay, subtype = Win, odd = 2.0),
                                                        (OutcomeListWithOddType with participant = None,participantOrder = 1, order = 5, type_ = ThreeWay, subtype = Draw, odd = 2.0),
                                                        OutcomeListWithOddType with participant = Some chelsea,participantOrder =  2 ,order = 5 ,type_ = ThreeWay,subtype =  Win, odd = 2.0
                                                    ]
                            eventParticipants   = [arsenal, chelsea]
                            startDate           = (Time.time (Date.date 2021 Nov 04) 0 0 0)
                            eventTitle          = Map.fromList [(show GambylUtils.EN_US, "Arsenal vs Chelsea")]
                            eventStatus         = IntegrationEvents.NotStarted
                            eventResults        = []

    submit gambyl do
        exerciseCmd marketingServiceCid MarketingService.ApproveEventOrigination
            with
                requestCid  = eventRequest
                managers    = Set.fromList [eventManager]

    return InputData with ..

run_testEventOrigination =
    TestOnboarding.testGamblingRole >>=
    testOfferMarketingService >>=
    testEventOrigination


testEventOriginationFailEmptyTournament : Stack.HasCallStack => Parties -> Script Parties
testEventOriginationFailEmptyTournament parties@Parties{operator, gambyl, marketingManager} = script do


    (marketingServiceCid, _) <- TestUtils.queryContractWithKey @MarketingService.Service marketingManager (operator, gambyl, marketingManager) "Marketing Service doesn't exist"

    ----- Event Origination ----

    let
        eventLabel = "AvC"
        arsenal = TestUtils.getParticipant "Arsenal" 1
        chelsea = TestUtils.getParticipant "Chelsea" 2

    submit marketingManager do
      exerciseCmd marketingServiceCid MarketingService.RequestEventOrigination  with
        eventOrigin = EventModel.CustomerEvent with
          assetLabel  = eventLabel
          description = "Match " <> eventLabel
          market              = (EventModel.Sport "Soccer")
          submarkets          = [EventModel.Tournament "Premier League"]
          geography           = "England"
          outcomes            = TestUtils.buildOutcomesListWithOddType [
                                      (OutcomeListWithOddType with participant = Some arsenal,participantOrder = 1, order = 5, type_ = ThreeWay, subtype = Win, odd = 2.0),
                                      (OutcomeListWithOddType with participant = None,participantOrder = 1, order = 5, type_ = ThreeWay, subtype = Draw, odd = 2.0),
                                      OutcomeListWithOddType with participant = Some chelsea,participantOrder =  2 ,order = 5 ,type_ = ThreeWay,subtype =  Win, odd = 2.0
                                  ]
          eventParticipants   = [arsenal, chelsea]
          startDate           = (Time.time (Date.date 2021 Nov 04) 0 0 0)
          eventTitle          = Map.fromList [(show GambylUtils.EN_US, "Arsenal vs Chelsea")]
          eventStatus         = IntegrationEvents.NotStarted
          eventResults        = []

    return (parties)

run_testEventOriginationFailEmptyTournament =
    TestOnboarding.testGamblingRole >>=
    testOfferMarketingService >>=
    testEventOriginationFailEmptyTournament

testEventOriginationFailTooManyTournaments : Stack.HasCallStack => Parties -> Script Parties
testEventOriginationFailTooManyTournaments parties@Parties{operator, gambyl, marketingManager} = script do

  (marketingServiceCid, _) <- TestUtils.queryContractWithKey @MarketingService.Service marketingManager (operator, gambyl, marketingManager) "Marketing Service doesn't exist"

  ----- Event Origination ----

  let
    eventLabel = "AvC"
    arsenal = TestUtils.getParticipant "Arsenal" 1
    chelsea = TestUtils.getParticipant "Chelsea" 2

  submit marketingManager do
    exerciseCmd marketingServiceCid MarketingService.RequestEventOrigination  with
      eventOrigin = EventModel.CustomerEvent with
        assetLabel  = eventLabel
        description = "Match " <> eventLabel
        market              = (EventModel.Sport "Soccer")
        submarkets          = [EventModel.Tournament "Premier League"]
        geography           = "England"
        outcomes            = TestUtils.buildOutcomesListWithOddType [
                                    (OutcomeListWithOddType with participant = Some arsenal,participantOrder = 1, order = 5, type_ = ThreeWay, subtype = Win, odd = 2.0),
                                    (OutcomeListWithOddType with participant = None,participantOrder = 1, order = 5, type_ = ThreeWay, subtype = Draw, odd = 2.0),
                                    OutcomeListWithOddType with participant = Some chelsea,participantOrder =  2 ,order = 5 ,type_ = ThreeWay,subtype =  Win, odd = 2.0
                                ]
        eventParticipants   = [arsenal, chelsea]
        startDate           = (Time.time (Date.date 2021 Nov 04) 0 0 0)
        eventTitle          = Map.fromList [(show GambylUtils.EN_US, "Arsenal vs Chelsea")]
        eventStatus         = IntegrationEvents.NotStarted
        eventResults        = []

  return (parties)

run_testEventOriginationFailTooManyTournaments =
    TestOnboarding.testGamblingRole >>=
    testOfferMarketingService >>=
    testEventOriginationFailTooManyTournaments


testEventOriginationFailEmptyTournamentText : Stack.HasCallStack => Parties -> Script Parties
testEventOriginationFailEmptyTournamentText parties@Parties{operator, gambyl, marketingManager} = script do


    (marketingServiceCid, _) <- TestUtils.queryContractWithKey @MarketingService.Service marketingManager (operator, gambyl, marketingManager) "Marketing Service doesn't exist"

    ----- Event Origination ----

    let
        eventLabel = "AvC"
        arsenal = TestUtils.getParticipant "Arsenal" 1
        chelsea = TestUtils.getParticipant "Chelsea" 2

    submit marketingManager do
      exerciseCmd marketingServiceCid MarketingService.RequestEventOrigination  with
          eventOrigin = EventModel.CustomerEvent with
            assetLabel  = eventLabel
            description = "Match " <> eventLabel
            market              = (EventModel.Sport "Soccer")
            submarkets          = [EventModel.Tournament "Premier League"]
            geography           = "England"
            outcomes            = TestUtils.buildOutcomesListWithOddType [
                                        (OutcomeListWithOddType with participant = Some arsenal,participantOrder = 1, order = 5, type_ = ThreeWay, subtype = Win, odd = 2.0)
                                        , (OutcomeListWithOddType with participant = None,participantOrder = 1, order = 5, type_ = ThreeWay, subtype = Draw, odd = 2.0)
                                        , (OutcomeListWithOddType with participant = Some chelsea,participantOrder =  2 ,order = 5 ,type_ = ThreeWay,subtype =  Win, odd = 2.0)
                                    ]
            eventParticipants   = [arsenal, chelsea]
            startDate           = (Time.time (Date.date 2021 Nov 04) 0 0 0)
            eventTitle          = Map.fromList [(show GambylUtils.EN_US, "Arsenal vs Chelsea")]
            eventStatus         = IntegrationEvents.NotStarted
            eventResults        = []

    return (parties)

run_testEventOriginationFailEmptyTournamentText =
    TestOnboarding.testGamblingRole >>=
    testOfferMarketingService >>=
    testEventOriginationFailEmptyTournamentText


testToggleFeatureEvent : Stack.HasCallStack => InputData -> Script ()
testToggleFeatureEvent input = script do
    (eventCid, event) <- queryContractKey @EventModel.EventInstrument input.parties.marketingManager (input.parties.operator, input.parties.gambyl, input.eventLabel) >>= (return . Optional.fromSomeNote (Common.msgWithFunc "Event Instrument doesn't exist"))

    oldFeatured <- (.featured) . Optional.fromSome <$> queryContractId input.parties.marketingManager ( eventCid)

    toggleRequestCid <- submit input.parties.marketingManager do exerciseByKeyCmd @MarketingService.Service (input.parties.operator, input.parties.gambyl, input.parties.marketingManager) MarketingService.RequestToggleFeaturedEvent with label = event.eventId.label

    eitherUpdatedEventCid <- submit input.parties.gambyl do exerciseByKeyCmd @MarketingService.Service (input.parties.operator, input.parties.gambyl, input.parties.marketingManager) MarketingService.ApproveToggleFeaturedEvent with requestCid = toggleRequestCid

    debug eitherUpdatedEventCid

    featured <- (.featured) . Optional.fromSome <$> queryContractId input.parties.marketingManager ( eitherUpdatedEventCid)
    assertMsg (Common.msgWithFunc "Update event failed") $ featured == not oldFeatured

    return ()

run_testToggleFeatureEvent =
    TestOnboarding.testGamblingRole >>=
    testOfferMarketingService >>=
    testEventOrigination >>=
    testToggleFeatureEvent

{- ----------------------------------------TEST PROMOTIONS---------------------------------------- -}

testCreatePromotion : Stack.HasCallStack => (Text, MarketingModel.PromotionConfig, Optional Time) -> Parties -> Script (Parties, MarketingModel.PromotionKey)
testCreatePromotion (promotionId, configurations, optStartDate) parties@Parties{operator, gambyl, marketingManager}  = script do

    marketingServiceCid <- fst <$> TestUtils.queryContractWithKey @MarketingService.Service marketingManager (operator, gambyl, marketingManager) "Marketing Service doesn't exist"

    now <- getTime

    promotionReqCid <- submit marketingManager do
        exerciseCmd marketingServiceCid MarketingService.CreatePromotionRequest
            with
                title = Map.fromList [(show GambylUtils.EN_US, "Discount"),(show GambylUtils.PT,"Desconto"),(show GambylUtils.ES,"Descuento")]
                shortDescription = Map.fromList [(show GambylUtils.EN_US, "20% off"),(show GambylUtils.PT,"20% de desconto"),(show GambylUtils.ES,"20% de descuento")]
                longDescription  = Map.fromList [(show GambylUtils.EN_US,"Promotion to Offer a 20% discount on Deposits for Gamblers"),(show GambylUtils.PT,"Promoção para oferecer um desconto de 20% em depósitos para jogadores"),(show GambylUtils.ES,"Promoción para Ofrecer un 20% de descuento en Depósitos para Jugadores")]
                config = configurations
                promotionId
                status = MarketingModel.Active
                startDate = Optional.fromOptional now optStartDate
                thumbnailUrl = Map.fromList [(show GambylUtils.EN_US, "thumbnail-url"),(show GambylUtils.PT, "thumbnail-url"),(show GambylUtils.ES, "thumbnail-url")]
                bannerUrl = Map.fromList [(show GambylUtils.EN_US, "banner-url"),(show GambylUtils.PT, "banner-url"),(show GambylUtils.ES, "banner-url") ]
                baseUrl = Map.fromList [(show GambylUtils.EN_US, "base-url"),(show GambylUtils.PT, "base-url"),(show GambylUtils.ES, "base-url")]

    promotionCid <- submit gambyl do
        exerciseCmd marketingServiceCid MarketingService.ApprovePromotionRequest with ..

    promotionKey <- queryContractId gambyl promotionCid >>=
        optional
            (assertFail $ Common.msgWithFunc "Promotion not created")
            (\ promotion -> do
                assertMsg "Promotion id doesn't match input value" $ promotion.promotionId == promotionId
                return $ key promotion
            )

    MarketingModel.GlobalPromotions{
        promotions
    } <- snd <$> TestUtils.queryContractWithKey @MarketingModel.GlobalPromotions gambyl (operator, gambyl) "GlobalPromotions contract doesn't exist"

    assertMsg (Common.msgWithFunc "Promotion not saved to Global") $ promotionKey `elem` promotions

    return (parties, promotionKey)

testCreatePromotionWrongTitle : Stack.HasCallStack => (Text, MarketingModel.PromotionConfig, Optional Time) -> Parties -> Script Parties
testCreatePromotionWrongTitle (promotionId, configurations, optStartDate) parties@Parties{operator, gambyl, marketingManager}  = script do

    marketingServiceCid <- fst <$> TestUtils.queryContractWithKey @MarketingService.Service marketingManager (operator, gambyl, marketingManager) "Marketing Service doesn't exist"

    now <- getTime

    let
        shortDescription = Map.fromList [(show GambylUtils.EN_US, "20% off"),(show GambylUtils.PT,"20% de desconto"),(show GambylUtils.ES,"20% de descuento")]
        longDescription  = Map.fromList [(show GambylUtils.EN_US,"Promotion to Offer a 20% discount on Deposits for Gamblers"),(show GambylUtils.PT,"Promoção para oferecer um desconto de 20% em depósitos para jogadores"),(show GambylUtils.ES,"Promoción para Ofrecer un 20% de descuento en Depósitos para Jugadores")]
        config = configurations
        status = MarketingModel.Active
        startDate = Optional.fromOptional now optStartDate
        thumbnailUrl = Map.fromList [(show GambylUtils.EN_US, "thumbnail-url"),(show GambylUtils.PT, "thumbnail-url"),(show GambylUtils.ES, "thumbnail-url")]
        bannerUrl = Map.fromList [(show GambylUtils.EN_US, "banner-url"),(show GambylUtils.PT, "banner-url"),(show GambylUtils.ES, "banner-url") ]
        baseUrl = Map.fromList [(show GambylUtils.EN_US, "base-url"),(show GambylUtils.PT, "base-url"),(show GambylUtils.ES, "base-url")]

    -- empty title
    submitMustFail marketingManager do
      exerciseCmd marketingServiceCid MarketingService.CreatePromotionRequest with title = Map.fromList [], ..

    -- title has 1 element
    submitMustFail marketingManager do
      exerciseCmd marketingServiceCid MarketingService.CreatePromotionRequest with title = Map.fromList [(show GambylUtils.EN_US, "Discount")], ..

    -- title has 2 elements
    submitMustFail marketingManager do
      exerciseCmd marketingServiceCid MarketingService.CreatePromotionRequest with title = Map.fromList [(show GambylUtils.EN_US, "Discount"), (show GambylUtils.PT,"Desconto")], ..

    -- title doesn't containt english key
    submitMustFail marketingManager do
      exerciseCmd marketingServiceCid MarketingService.CreatePromotionRequest with title = Map.fromList [(show GambylUtils.PT,"Desconto"), (show GambylUtils.PT,"Desconto"), (show GambylUtils.ES,"Descuento")], ..

    -- title doesn't containt spanish key
    submitMustFail marketingManager do
      exerciseCmd marketingServiceCid MarketingService.CreatePromotionRequest with title = Map.fromList [(show GambylUtils.EN_US, "Discount"), (show GambylUtils.PT,"Desconto"), (show GambylUtils.EN_US, "Discount")], ..

    -- title doesn't containt portuguese key
    submitMustFail marketingManager do
      exerciseCmd marketingServiceCid MarketingService.CreatePromotionRequest with title = Map.fromList [(show GambylUtils.EN_US, "Discount"), (show GambylUtils.EN_US, "Discount"), (show GambylUtils.ES,"Descuento")], ..

    -- tittle doesn't contain english value
    submitMustFail marketingManager do
      exerciseCmd marketingServiceCid MarketingService.CreatePromotionRequest with title = Map.fromList [(show GambylUtils.EN_US, ""),(show GambylUtils.PT,"Desconto"),(show GambylUtils.ES,"Descuento")], ..

    -- tittle doesn't contain spanish value
    submitMustFail marketingManager do
      exerciseCmd marketingServiceCid MarketingService.CreatePromotionRequest with title = Map.fromList [(show GambylUtils.EN_US, "Discount"),(show GambylUtils.PT,"Desconto"),(show GambylUtils.ES,"")], ..

    -- tittle doesn't contain portuguese value
    submitMustFail marketingManager do
      exerciseCmd marketingServiceCid MarketingService.CreatePromotionRequest with title = Map.fromList [(show GambylUtils.EN_US, "Discount"),(show GambylUtils.PT,""),(show GambylUtils.ES,"Descuento")], ..

    return parties


run_testCreatePromotion =
    TestOnboarding.testGamblingRole >>=
    testOfferMarketingService >>=
    testCreatePromotion ("1",
        (MarketingModel.PromotionConfig with
            promoType = Other
            action = Deposit (Bonus (Percentage 0.2))
            endDate = Some (Date.date 2022 Dec 24)
            limitedPromotion = None, maxAmount = Some 200.0
            minAmount = None), None)


testCreatePromotionFail : Stack.HasCallStack => (Text, MarketingModel.PromotionConfig) -> Parties -> Script ()
testCreatePromotionFail (promotionId, configurations) Parties{operator, gambyl, marketingManager}  = script do

  marketingServiceCid <- fst <$> TestUtils.queryContractWithKey @MarketingService.Service marketingManager (operator, gambyl, marketingManager) "Marketing Service doesn't exist"

  now <- getTime

  submitMustFail marketingManager do
    exerciseCmd marketingServiceCid MarketingService.CreatePromotionRequest with
      title = Map.fromList [(show GambylUtils.EN_US, "Discount"),(show GambylUtils.PT,"Desconto"),(show GambylUtils.ES,"Descuento")]
      shortDescription = Map.fromList [(show GambylUtils.EN_US, "20% off"),(show GambylUtils.PT,"20% de desconto"),(show GambylUtils.ES,"20% de descuento")]
      longDescription  = Map.fromList [(show GambylUtils.EN_US,"Promotion to Offer a 20% disccount on Deposits for Gamblers"),(show GambylUtils.PT,"Promoção para oferecer um desconto de 20% em depósitos para jogadores"),(show GambylUtils.ES,"Promoción para Ofrecer un 20% de descuento en Depósitos para Jugadores")]
      config = configurations
      promotionId
      status = MarketingModel.Active
      startDate = now
      thumbnailUrl = Map.fromList [(show GambylUtils.EN_US, "thumbnail-url"),(show GambylUtils.PT, "thumbnail-url"),(show GambylUtils.ES, "thumbnail-url")]
      bannerUrl = Map.fromList [(show GambylUtils.EN_US, "banner-url"),(show GambylUtils.PT, "banner-url"),(show GambylUtils.ES, "banner-url") ]
      baseUrl = Map.fromList [(show GambylUtils.EN_US, "base-url"),(show GambylUtils.PT, "base-url"),(show GambylUtils.ES, "base-url")]

  return ()


run_testCreatePromotionFailWithActionDepositPromoDetailsDiscountAndMaxAmount =
    TestOnboarding.testGamblingRole >>=
    testOfferMarketingService >>=
    testCreatePromotionFail ("1",
        MarketingModel.PromotionConfig with
            promoType = Other
            action = Deposit (Discount (Percentage 1.0))
            maxAmount = Some 200.0
            endDate = None
            limitedPromotion = None
            minAmount = None
    )

run_testCreatePromotionFailWithActionWithdrawalPromoDetailsDiscountAndMaxAmount =
    TestOnboarding.testGamblingRole >>=
    testOfferMarketingService >>=
    testCreatePromotionFail ("1",
        MarketingModel.PromotionConfig with
            promoType = Other
            action = Withdrawal (Discount (Percentage 1.0))
            endDate = None
            limitedPromotion = None
            maxAmount = Some 200.0
            minAmount = None
    )

run_testCreatePromotionFailWithActionBetsPromoDetailsDiscountAndMaxAmount =
    TestOnboarding.testGamblingRole >>=
    testOfferMarketingService >>=
    testCreatePromotionFail ("1",
        MarketingModel.PromotionConfig with
            promoType = Other
            action = Withdrawal (Discount (Percentage 1.0))
            endDate = None
            limitedPromotion = None
            maxAmount = Some 200.0
            minAmount = None
    )

run_testCreatePromotionFailWithActionDepositPromoDetailsBonusRedemptionCashAndMaxAmount =
    TestOnboarding.testGamblingRole >>=
    testOfferMarketingService >>=
    testCreatePromotionFail ("1",
        MarketingModel.PromotionConfig with
            promoType = Other
            action = Deposit (Bonus (Cash 1.0))
            endDate = None
            limitedPromotion = None
            maxAmount = Some 200.0
            minAmount = None
    )

run_testCreatePromotionFailWithActionWithdrawalPromoDetailsBonusRedemptionCashAndMaxAmount =
    TestOnboarding.testGamblingRole >>=
    testOfferMarketingService >>=
    testCreatePromotionFail ("1",
        MarketingModel.PromotionConfig with
            promoType = Other
            action = Withdrawal (Bonus (Cash 1.0))
            endDate = None
            limitedPromotion = None
            maxAmount = Some 200.0
            minAmount = None
    )

run_testCreatePromotionFailWithActionBetPromoDetailsBonusRedemptionCashAndMaxAmount =
    TestOnboarding.testGamblingRole >>=
    testOfferMarketingService >>=
    testCreatePromotionFail ("1",
        MarketingModel.PromotionConfig with
            promoType = Other
            action = Bet (Bonus (Cash 1.0))
            endDate = None
            limitedPromotion = None
            maxAmount = Some 200.0
            minAmount = None
    )

run_testCreatePromotionFailWithActionBetPromoDetailsBonusRedemptionCash =
    TestOnboarding.testGamblingRole >>=
    testOfferMarketingService >>=
    testCreatePromotionFail ("1",
        MarketingModel.PromotionConfig with
            promoType = FirstTime None
            action = Bet (Bonus (Cash 1.0))
            endDate = None
            limitedPromotion = None
            maxAmount = None
            minAmount = None
    )

run_testCreatePromotionFailWithActionWithdrawalPromoDetailsBonus =
    TestOnboarding.testGamblingRole >>=
    testOfferMarketingService >>=
    testCreatePromotionFail ("1",
        MarketingModel.PromotionConfig with
            promoType = Other
            action = Withdrawal (Bonus (Cash 1.0))
            endDate = None
            limitedPromotion = None
            maxAmount = None
            minAmount = None
    )

run_testCreatePromotionFailWithPromoDetailsDiscountRedemptionCash =
    TestOnboarding.testGamblingRole >>=
    testOfferMarketingService >>=
    testCreatePromotionFail ("1",
        MarketingModel.PromotionConfig with
            promoType = Other
            action = Deposit (Discount (Cash 1.0))
            endDate = None
            limitedPromotion = None
            maxAmount = None
            minAmount = None
    )

testUpdatePromotion : Stack.HasCallStack => (Parties, MarketingModel.PromotionKey) -> Script Parties
testUpdatePromotion (parties@Parties{operator, gambyl, marketingManager}, promotionKey) = script do

  (marketingServiceCid, _) <- TestUtils.queryContractWithKey @MarketingService.Service marketingManager (operator, gambyl, marketingManager) "Marketing Service doesn't exist"
  (promotionCid, oldPromo) <- TestUtils.queryContractWithKey @MarketingModel.Promotion marketingManager promotionKey "Promotion doesn't exist"

  let
    now = Some (Time.time (Date.date 2021 Feb 24) 0 0 0)
    newConfig = None

  promotionUpdateReqCid <- submit marketingManager do
      exerciseCmd marketingServiceCid MarketingService.UpdatePromotionRequest
          with
              promotionCid
              newConfig
              newStartDate = now
              newBannerUrl = Map.empty
              newThumbnailUrl = Map.empty
              newBaseUrl = Map.empty
              newTitle =  Map.fromList [(show GambylUtils.EN_US, "Promo 2022")]
              newShortDescription = Map.empty
              newLongDescription = Map.fromList [(show GambylUtils.EN_US,"New Long Description for Promo")]
              ..

  eitherPromotionOrFail <- submit gambyl do
      exerciseCmd marketingServiceCid MarketingService.ApprovePromotionUpdateRequest with promotionUpdateReqCid

  promotionCid <- either
    (queryContractId gambyl
      >=> assertFail . Common.msgWithFunc . optional ("ActionFailure contract not found") (.reason))
    (pure)
    eitherPromotionOrFail

  promotionOpt <- queryContractId marketingManager promotionCid
  assertMsg (Common.msgWithFunc "Promotion not Found") $ Optional.isSome promotionOpt

  let Some promotion = promotionOpt
      promotionKey = key promotion

  assertMsg (Common.msgWithFunc "PromoConfig was updated") $ promotion.config == oldPromo.config
  assertMsg (Common.msgWithFunc "Promo shortDescription was not updated") $ promotion.shortDescription == oldPromo.shortDescription
  assertMsg (Common.msgWithFunc "ThumbnailUrl was updated") $ promotion.thumbnailUrl == oldPromo.thumbnailUrl
  assertMsg (Common.msgWithFunc "Promo title was not updated") $ promotion.title /= oldPromo.title
  assertMsg (Common.msgWithFunc "Promo startDate was not updated") $ promotion.startDate /= oldPromo.startDate
  assertMsg (Common.msgWithFunc "Promo longDescription was not updated") $ promotion.longDescription /= oldPromo.longDescription

  (_, MarketingModel.GlobalPromotions{promotions}) <- TestUtils.queryContractWithKey @MarketingModel.GlobalPromotions gambyl (operator, gambyl) "GlobalPromotions contract doesn't exist"
  assertMsg (Common.msgWithFunc "Promotion not saved to Global") $ null ([promotionKey] \\ promotions)

  return parties

testUpdatePartialPromotionMultipleFields : Stack.HasCallStack => (Parties, MarketingModel.PromotionKey) -> Script Parties
testUpdatePartialPromotionMultipleFields (parties@Parties{operator, gambyl, marketingManager}, promotionKey) = script do

    (marketingServiceCid, _) <- TestUtils.queryContractWithKey @MarketingService.Service marketingManager (operator, gambyl, marketingManager) "Marketing Service doesn't exist"
    (promotionCid, oldPromo) <- TestUtils.queryContractWithKey @MarketingModel.Promotion marketingManager promotionKey "Promotion doesn't exist"

    let
        newTitle = (MarketingModel.PromoTitle (Map.fromList [(show GambylUtils.EN_US, "Promo 2027")]))
        newShortDescription = (MarketingModel.PromoShortDescription (Map.fromList [(show GambylUtils.EN_US, "This Promotion is a Test")]))
        newConfig = (MarketingModel.PromoConfig (MarketingModel.PromotionConfig with promoType = Other
                                                                                     action = Deposit (Bonus (Percentage 0.05))
                                                                                     endDate = None
                                                                                     limitedPromotion = None
                                                                                     maxAmount = None
                                                                                     minAmount = None))
        newBannerUrl = (MarketingModel.PromoBannerUrl (Map.fromList [(show GambylUtils.EN_US, "banner1-url"),(show GambylUtils.PT, "banner2-url"),(show GambylUtils.ES, "banner3-url")]))

        promoFields =[newConfig,newTitle,newShortDescription,newBannerUrl]

    promotionUpdateReqCid <- submit marketingManager do
        exerciseCmd marketingServiceCid MarketingService.RequestPromotionPartialUpdate
            with
                promotionCid, ..

    promotionUpdateReq <-
        Optional.fromSomeNote (Common.msgWithFunc "Promotion Update Contract not found") <$> queryContractId gambyl promotionUpdateReqCid

    assertMsg (Common.msgWithFunc "PromoUpdateReq baseUrl was not updated") $ promotionUpdateReq.newBannerUrl /= oldPromo.bannerUrl
    assertMsg (Common.msgWithFunc "PromoUpdateReq thumbnailUrl updated") $ promotionUpdateReq.newThumbnailUrl == oldPromo.thumbnailUrl
    assertMsg (Common.msgWithFunc "PromoUpdateReq longDescription updated") $ promotionUpdateReq.newLongDescription == oldPromo.longDescription
    assertMsg (Common.msgWithFunc "PromoUpdateReq shortDescription was not updated") $ promotionUpdateReq.newShortDescription /= oldPromo.shortDescription
    assertMsg (Common.msgWithFunc "PromoUpdateReq BaseUrl was updated") $ promotionUpdateReq.newBaseUrl == oldPromo.baseUrl
    assertMsg (Common.msgWithFunc "PromoUpdateReq title was not updated") $ promotionUpdateReq.newTitle /= oldPromo.title
    assertMsg (Common.msgWithFunc "PromoUpdateReq startDate was updated") $ Optional.fromSome promotionUpdateReq.newStartDate == oldPromo.startDate
    assertMsg (Common.msgWithFunc "PromoUpdateReq Configuration notUpdated") $ Optional.fromSome promotionUpdateReq.newConfig /= oldPromo.config

    eitherPromotionOrFail <- submit gambyl do
        exerciseCmd marketingServiceCid MarketingService.ApprovePromotionUpdateRequest with promotionUpdateReqCid

    promotionCid <- either
      (queryContractId gambyl
        >=> assertFail . Common.msgWithFunc . optional ("ActionFailure contract not found") (.reason))
      (pure)
      eitherPromotionOrFail

    promotionOpt <- queryContractId marketingManager promotionCid
    assertMsg (Common.msgWithFunc "Promotion not Found") $ Optional.isSome promotionOpt

    let Some promotion = promotionOpt
        promotionKey = key promotion

    assertMsg (Common.msgWithFunc "Promo baseUrl was not updated") $ promotion.bannerUrl /= oldPromo.bannerUrl
    assertMsg (Common.msgWithFunc "Promo thumbnailUrl updated") $ promotion.thumbnailUrl == oldPromo.thumbnailUrl
    assertMsg (Common.msgWithFunc "Promo longDescription updated") $ promotion.longDescription == oldPromo.longDescription
    assertMsg (Common.msgWithFunc "Promo shortDescription was not updated") $ promotion.shortDescription /= oldPromo.shortDescription
    assertMsg (Common.msgWithFunc "Promo BaseUrl was updated") $ promotion.baseUrl == oldPromo.baseUrl
    assertMsg (Common.msgWithFunc "Promo title was not updated") $ promotion.title /= oldPromo.title
    assertMsg (Common.msgWithFunc "Promo startDate was updated") $ promotion.startDate == oldPromo.startDate
    assertMsg (Common.msgWithFunc "Promotion Configuration notUpdated") $ promotion.config /= oldPromo.config

    (_, MarketingModel.GlobalPromotions{promotions}) <- TestUtils.queryContractWithKey @MarketingModel.GlobalPromotions gambyl (operator, gambyl) "GlobalPromotions contract doesn't exist"
    assertMsg (Common.msgWithFunc "Promotion not saved to Global") $ null ([promotionKey] \\ promotions)

    return parties

testUpdatePartialPromotionSingleField : Stack.HasCallStack => (Parties, MarketingModel.PromotionKey) -> Script Parties
testUpdatePartialPromotionSingleField (parties@Parties{operator, gambyl, marketingManager}, promotionKey) = script do

    (marketingServiceCid, _) <- TestUtils.queryContractWithKey @MarketingService.Service marketingManager (operator, gambyl, marketingManager) "Marketing Service doesn't exist"
    (promotionCid, oldPromo) <- TestUtils.queryContractWithKey @MarketingModel.Promotion marketingManager promotionKey "Promotion doesn't exist"

    let
        now =  (Time.time (Date.date 2021 Feb 24) 0 0 0)
        newDate = (MarketingModel.PromoStartDate now)
        promoFields =[newDate]

    promotionUpdateReqCid <- submit marketingManager do
        exerciseCmd marketingServiceCid MarketingService.RequestPromotionPartialUpdate
            with
                promotionCid, ..

    promotionUpdateReq <-
        Optional.fromSomeNote (Common.msgWithFunc "Promotion Update Contract not found") <$> queryContractId gambyl promotionUpdateReqCid

    assertMsg (Common.msgWithFunc "PromoUpdateReq baseUrl was updated") $ promotionUpdateReq.newBaseUrl == oldPromo.baseUrl
    assertMsg (Common.msgWithFunc "PromoUpdateReq thumbnailUrl was updated") $ promotionUpdateReq.newThumbnailUrl == oldPromo.thumbnailUrl
    assertMsg (Common.msgWithFunc "PromoUpdateReq longDescription was updated") $ promotionUpdateReq.newLongDescription == oldPromo.longDescription
    assertMsg (Common.msgWithFunc "PromoUpdateReq shortDescription was updated") $ promotionUpdateReq.newShortDescription == oldPromo.shortDescription
    assertMsg (Common.msgWithFunc "PromoUpdateReq was updated") $ promotionUpdateReq.newBannerUrl == oldPromo.bannerUrl
    assertMsg (Common.msgWithFunc "PromoUpdateReq was updated") $ promotionUpdateReq.newTitle == oldPromo.title
    assertMsg (Common.msgWithFunc "PromoUpdateReq StartDate was not updated") $ Optional.fromSome promotionUpdateReq.newStartDate /= oldPromo.startDate
    assertMsg (Common.msgWithFunc "PromoUpdateReq Config was Updated") $ Optional.fromSome promotionUpdateReq.newConfig == oldPromo.config

    eitherPromotionOrFail <- submit gambyl do
        exerciseCmd marketingServiceCid MarketingService.ApprovePromotionUpdateRequest with promotionUpdateReqCid

    promotionCid <- either
      (queryContractId gambyl
        >=> assertFail . Common.msgWithFunc . optional ("ActionFailure contract not found") (.reason))
      (pure)
      eitherPromotionOrFail

    promotionOpt <- queryContractId marketingManager promotionCid
    assertMsg (Common.msgWithFunc "Promotion not Found") $ Optional.isSome promotionOpt

    let Some promotion = promotionOpt
        promotionKey = key promotion

    assertMsg (Common.msgWithFunc "Promo baseUrl was updated") $ promotion.baseUrl == oldPromo.baseUrl
    assertMsg (Common.msgWithFunc "Promo thumbnailUrl was updated") $ promotion.thumbnailUrl == oldPromo.thumbnailUrl
    assertMsg (Common.msgWithFunc "Promo longDescription was updated") $ promotion.longDescription == oldPromo.longDescription
    assertMsg (Common.msgWithFunc "Promo shortDescription was updated") $ promotion.shortDescription == oldPromo.shortDescription
    assertMsg (Common.msgWithFunc "Promo was updated") $ promotion.bannerUrl == oldPromo.bannerUrl
    assertMsg (Common.msgWithFunc "Promo was updated") $ promotion.title == oldPromo.title
    assertMsg (Common.msgWithFunc "Promo StartDate was not updated") $ promotion.startDate /= oldPromo.startDate
    assertMsg (Common.msgWithFunc "Promotion Config was Updated") $ promotion.config == oldPromo.config

    (_, MarketingModel.GlobalPromotions{promotions}) <- TestUtils.queryContractWithKey @MarketingModel.GlobalPromotions gambyl (operator, gambyl) "GlobalPromotions contract doesn't exist"
    assertMsg (Common.msgWithFunc "Promotion not saved to Global") $ null ([promotionKey] \\ promotions)

    return parties


testUpdatePromotionFail : Stack.HasCallStack => (Parties, MarketingModel.PromotionKey) -> Script Parties
testUpdatePromotionFail (parties@Parties{operator, gambyl, marketingManager}, promotionKey) = script do

    marketingServiceCid <- fst <$> TestUtils.queryContractWithKey @MarketingService.Service marketingManager (operator, gambyl, marketingManager) "Marketing Service doesn't exist"
    promotionCid <- fst <$> TestUtils.queryContractWithKey @MarketingModel.Promotion marketingManager promotionKey "Promotion doesn't exist"

    let
      now = Some (Time.time (Date.date 2021 Feb 24) 0 0 0)
      newConfig = Some  MarketingModel.PromotionConfig with
        promoType = Other
        action = Deposit (Discount (Cash 1.0))
        endDate = None
        limitedPromotion = None
        maxAmount = None
        minAmount = None

    submitMustFail marketingManager do
      exerciseCmd marketingServiceCid MarketingService.UpdatePromotionRequest with
        promotionCid
        newConfig
        newStartDate = now
        newBannerUrl = Map.empty
        newThumbnailUrl = Map.empty
        newBaseUrl = Map.empty
        newTitle =  Map.fromList [(show GambylUtils.EN_US, "Promo 2022")]
        newShortDescription = Map.empty
        newLongDescription = Map.fromList [(show GambylUtils.EN_US,"New Long Description for Promo")]
        ..

    return parties


run_testUpdatePromotion =
    TestOnboarding.testGamblingRole >>=
    testOfferMarketingService >>=
        testCreatePromotion ("1",
            (MarketingModel.PromotionConfig with
                promoType = Other
                action = Deposit (Bonus (Percentage 0.2))
                endDate = Some (Date.date 2022 Jan 01)
                limitedPromotion = None, maxAmount = Some 200.0
                minAmount = None), None) >>=
    testUpdatePromotion

run_testUpdatePartialPromotionMultipleFields =
    TestOnboarding.testGamblingRole >>=
    testOfferMarketingService >>=
    testCreatePromotion ("1",
        (MarketingModel.PromotionConfig with
            promoType = Other
            action = Deposit (Bonus (Percentage 0.2))
            endDate = Some (Date.date 2022 Jan 01)
            limitedPromotion = None, maxAmount = Some 200.0
            minAmount = None), None) >>=
    testUpdatePartialPromotionMultipleFields

run_testUpdatePartialPromotionSingleField =
    TestOnboarding.testGamblingRole >>=
    testOfferMarketingService >>=
    testCreatePromotion ("1",
        (MarketingModel.PromotionConfig with
            promoType = Other
            action = Deposit (Bonus (Percentage 0.2))
            endDate = Some (Date.date 2022 Jan 01)
            limitedPromotion = None, maxAmount = Some 200.0
            minAmount = None), None) >>=
    testUpdatePartialPromotionSingleField

run_testUpdatePromotionFail =
    TestOnboarding.testGamblingRole >>=
    testOfferMarketingService >>=
    testCreatePromotion ("1",
        (MarketingModel.PromotionConfig with
            promoType = Other
            action = Deposit (Bonus (Percentage 0.2))
            endDate = Some (Date.date 2022 Jan 01)
            limitedPromotion = None, maxAmount = Some 200.0
            minAmount = None), None) >>=
    testUpdatePromotionFail

testExpirePromotion : Stack.HasCallStack => (Parties, MarketingModel.PromotionKey) -> Script (Parties, MarketingModel.PromotionKey)
testExpirePromotion (parties@Parties{operator, gambyl, marketingManager}, promotionKey) = script do

    marketingServiceCid <- fst <$> TestUtils.queryContractWithKey @MarketingService.Service marketingManager (operator, gambyl, marketingManager) "Marketing Service doesn't exist"
    promotionCid        <- fst <$> TestUtils.queryContractWithKey @MarketingModel.Promotion marketingManager promotionKey "Promotion doesn't exist"

    promotion <- submit gambyl do
        exerciseCmd marketingServiceCid MarketingService.ExpirePromotion with promotionCid

    assertMsg (Common.msgWithFunc "Error expiring promotion") $ Either.isRight promotion

    promotionOpt <- queryContractKey @MarketingModel.Promotion gambyl promotionKey
    assertMsg (Common.msgWithFunc "Promotion not expired") $ Optional.optional False ((== MarketingModel.Expired) . (._2.status)) promotionOpt

    let Some (_, promotion) = promotionOpt
        promotionKey = key promotion

    (_, MarketingModel.GlobalPromotions{promotions}) <- TestUtils.queryContractWithKey @MarketingModel.GlobalPromotions gambyl (operator, gambyl) "GlobalPromotions contract doesn't exist"

    assertMsg (Common.msgWithFunc "Promotion still in Global") $ not $ null ([promotionKey] \\ promotions)

    return (parties, promotionKey)


run_testExpirePromotion =
    TestOnboarding.testGamblingRole >>=
    testOfferMarketingService >>=
    testCreatePromotion ("1",
        (MarketingModel.PromotionConfig with
            promoType = Other
            action = Deposit (Bonus (Percentage 0.2))
            endDate = Some (Date.date 2022 Jan 01)
            limitedPromotion = None, maxAmount = Some 200.0
            minAmount = None), None) >>=
    testExpirePromotion


testExpirePromotionFailAlreadyExpired : Stack.HasCallStack => (Parties, MarketingModel.PromotionKey) -> Script Parties
testExpirePromotionFailAlreadyExpired (parties@Parties{operator, gambyl, marketingManager}, promotionKey) = script do

    marketingServiceCid <- fst <$> TestUtils.queryContractWithKey @MarketingService.Service marketingManager (operator, gambyl, marketingManager) "Marketing Service doesn't exist"

    promotionCid <- fst <$> TestUtils.queryContractWithKey @MarketingModel.Promotion marketingManager promotionKey "Promotion doesn't exist"

    promotion <- submit gambyl do
        exerciseCmd marketingServiceCid MarketingService.ExpirePromotion with promotionCid

    assertMsg (Common.msgWithFunc "Promotion wasn't expired") $ Either.isLeft promotion

    return parties

run_testExpirePromotionFail =
    TestOnboarding.testGamblingRole >>=
    testOfferMarketingService >>=
    testCreatePromotion ("1",
        (MarketingModel.PromotionConfig with
            promoType = Other
            action = Deposit (Bonus (Percentage 0.2))
            endDate = Some (Date.date 2022 Jan 01)
            limitedPromotion = None, maxAmount = Some 200.0
            minAmount = None), None) >>=
    testExpirePromotion >>=
    testExpirePromotionFailAlreadyExpired


testArchivePromotion : Stack.HasCallStack => (Parties, MarketingModel.PromotionKey) -> Script Parties
testArchivePromotion (parties@Parties{operator, gambyl, marketingManager}, promotionKey) = script do

    (marketingServiceCid, _) <- TestUtils.queryContractWithKey @MarketingService.Service marketingManager (operator, gambyl, marketingManager) "Marketing Service doesn't exist"

    (promotionCid, _) <- TestUtils.queryContractWithKey @MarketingModel.Promotion marketingManager promotionKey "Promotion doesn't exist"

    submit gambyl do
        exerciseCmd marketingServiceCid MarketingService.ArchivePromotion
            with
                promotionCid

    promotionOpt <- queryContractKey @MarketingModel.Promotion gambyl promotionKey
    assertMsg (Common.msgWithFunc "Promotion not archived") $ Optional.isNone promotionOpt

    return parties

run_testArchivePromotion =
    TestOnboarding.testGamblingRole >>=
    testOfferMarketingService >>=
    testCreatePromotion ("1",
        (MarketingModel.PromotionConfig with
            promoType = Other
            action = Deposit (Bonus (Percentage 0.2))
            endDate = Some (Date.date 2022 Jan 01)
            limitedPromotion = None, maxAmount = Some 200.0
            minAmount = None), None) >>=
    testExpirePromotion >>=
    testArchivePromotion


testArchivePromotionFail : Stack.HasCallStack => (Parties, MarketingModel.PromotionKey) -> Script Parties
testArchivePromotionFail (parties@Parties{operator, gambyl, marketingManager}, promotionKey) = script do

    marketingServiceCid <- fst <$> TestUtils.queryContractWithKey @MarketingService.Service marketingManager (operator, gambyl, marketingManager) "Marketing Service doesn't exist"
    promotionCid        <- fst <$> TestUtils.queryContractWithKey @MarketingModel.Promotion marketingManager promotionKey "Promotion doesn't exist"

    promotion <- submit gambyl do
        exerciseCmd marketingServiceCid MarketingService.ArchivePromotion
            with
                promotionCid = promotionCid

    assertMsg (Common.msgWithFunc "Promotion still active") $ Either.isLeft promotion

    return parties

run_testArchivePromotionFail =
    TestOnboarding.testGamblingRole >>=
    testOfferMarketingService >>=
    testCreatePromotion ("1",
        (MarketingModel.PromotionConfig with
            promoType = Other
            action = Deposit (Bonus (Percentage 0.2))
            endDate = Some (Date.date 2022 Jan 01)
            limitedPromotion = None, maxAmount = Some 200.0
            minAmount = None), None) >>=
    testArchivePromotionFail

{- ---------------------------------------HELPER FUNCTIONS---------------------------------------- -}

isBonusPromo : PromotionAction -> Bool
isBonusPromo (Deposit (Bonus _))    = True
isBonusPromo (Withdrawal (Bonus _)) = True
isBonusPromo _                      = False
