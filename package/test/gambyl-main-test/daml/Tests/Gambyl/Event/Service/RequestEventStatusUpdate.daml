{-# LANGUAGE ApplicativeDo #-}
module Tests.Gambyl.Event.Service.RequestEventStatusUpdate where

import Daml.Script

import DA.Action ((>=>))
import DA.Assert ((=/=), (===))
import DA.List qualified as List
import DA.Optional qualified as Optional
import DA.Text (isInfixOf)
import DA.Stack qualified as Stack (HasCallStack)

import EnetPulseIntegration.Events qualified as EnetPulseEvents

import Gambyl.Gambling.Event.Service qualified as EventService
import Gambyl.Marketing.Service qualified as MarketingService
import Gambyl.Gambling.Event.Model qualified as EventModel

import Tests.Event qualified as TestEvents
import Tests.Onboarding qualified as TestOnboarding
import Tests.Marketing qualified as TestMarketing
import Tests.Utils qualified as TestUtils

import Scripts.Bootstrap.Onboarding.Parties       (Parties(..))
import Scripts.Common qualified as Common (msgWithFunc)


testEventStatusUpdate_NewStatusFinishedResultsNotEmptySucceeds : Stack.HasCallStack =>
    (Parties, Text) -> Script (Parties, EventModel.EventInstrument)
testEventStatusUpdate_NewStatusFinishedResultsNotEmptySucceeds
    (parties@Parties{operator, gambyl, marketingManager, eventManager}, eventLabel) = script do

    -- Arrange: Query existingf marketing service and event instrument
    (marketingServiceCid, _) <- TestUtils.queryContractWithKey @MarketingService.Service marketingManager
        (operator, gambyl, marketingManager)  "Marketing Service doesn't exist"

    event <- queryContractKey @EventModel.EventInstrument eventManager (operator, gambyl, eventLabel) >>=
        (return . snd . Optional.fromSomeNote (Common.msgWithFunc "Event Instrument doesn't exist"))

    let eventResults = [(List.head event.details.outcomes).outcome]

    -- Act: Create request to update event status
    eitherEventUpdateReqCid <- submit eventManager do
       exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
             EventService.RequestEventStatusUpdate with
                newEventStatus = EnetPulseEvents.Finished
                eventResults = Some eventResults
                oldEventKey = key event

    -- Assert: Verify event status update was created
    eventStatusUpdateReqCid <- either
        (\ _ -> assertFail $ Common.msgWithFunc "Event status update request not created")
        pure
        eitherEventUpdateReqCid

    -- Act: Approve status update request successfully
    eitherStatusEventUpdateCid <- submit gambyl do
        exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
             EventService.ApproveEventStatusUpdate with requestCid = eventStatusUpdateReqCid, marketingServiceCid

    -- Assert: Verify event status was changed and valida
    updatedEvent <- either
        (\ failureCid -> do
            queryContractId gambyl failureCid >>= optional
                (assertFail $ Common.msgWithFunc "ActionFailure contract not found for event status update")
                (\ failure -> assertFail $ Common.msgWithFunc "Event status was not updated. Reason: " <> failure.reason)
            )
        (queryContractId gambyl >=> optional
            (assertFail $ Common.msgWithFunc "EventInstrument contract not found for event status update")
            pure)
        eitherStatusEventUpdateCid

    updatedEvent.details.eventStatus =/= event.details.eventStatus
    updatedEvent.details.eventResults === eventResults

    eventQuery <- queryFilter @EventModel.EventInstrument gambyl (\event -> event.eventId.label == eventLabel)

    return (parties, snd (List.head eventQuery))

run_testEventStatusUpdate_NewStatusFinishedResultsNotEmptySucceeds =
    TestOnboarding.testGamblingRole >>=
    TestMarketing.testOfferMarketingService >>=
    TestEvents.testOfferEventService >>=
    TestEvents.testEventOrigination >>=
    testEventStatusUpdate_NewStatusFinishedResultsNotEmptySucceeds

testEventStatusUpdate_NewStatusPostponedResultsAreNoneSucceeds : Stack.HasCallStack =>
    (Parties, Text) -> Script (Parties, EventModel.EventInstrument)
testEventStatusUpdate_NewStatusPostponedResultsAreNoneSucceeds
    (parties@Parties{operator, gambyl, marketingManager, eventManager}, eventLabel) = script do

    -- Arrange: Query existingf marketing service and event instrument
    (marketingServiceCid, _) <- TestUtils.queryContractWithKey @MarketingService.Service marketingManager
        (operator, gambyl, marketingManager)  "Marketing Service doesn't exist"

    event <- queryContractKey @EventModel.EventInstrument eventManager (operator, gambyl, eventLabel) >>=
        (return . snd . Optional.fromSomeNote (Common.msgWithFunc "Event Instrument doesn't exist"))

    -- Act: Create request to update event status
    eitherEventUpdateReqCid <- submit eventManager do
       exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
             EventService.RequestEventStatusUpdate with
                newEventStatus = EnetPulseEvents.Postponed
                eventResults = None
                oldEventKey = key event

    -- Assert: Verify event status update was created
    eventStatusUpdateReqCid <- either
        (\ _ -> assertFail $ Common.msgWithFunc "Event status update request not created")
        pure
        eitherEventUpdateReqCid

    -- Act: Approve status update request successfully
    eitherStatusEventUpdateCid <- submit gambyl do
        exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
             EventService.ApproveEventStatusUpdate with requestCid = eventStatusUpdateReqCid, marketingServiceCid

    -- Assert: Verify event status was changed and valida
    updatedEvent <- either
        (\ failureCid -> do
            queryContractId gambyl failureCid >>= optional
                (assertFail $ Common.msgWithFunc "ActionFailure contract not found for event status update")
                (\ failure -> assertFail $ Common.msgWithFunc "Event status was not updated. Reason: " <> failure.reason)
            )
        (queryContractId gambyl >=> optional
            (assertFail $ Common.msgWithFunc "EventInstrument contract not found for event status update")
            pure)
        eitherStatusEventUpdateCid

    updatedEvent.details.eventStatus =/= event.details.eventStatus
    updatedEvent.details.eventResults === event.details.eventResults
    updatedEvent.status === EventModel.Expired

    eventQuery <- queryFilter @EventModel.EventInstrument gambyl (\event -> event.eventId.label == eventLabel)

    return (parties, snd (List.head eventQuery))

run_testEventStatusUpdate_NewStatusPostponedResultsAreNoneSucceeds =
    TestOnboarding.testGamblingRole >>=
    TestMarketing.testOfferMarketingService >>=
    TestEvents.testOfferEventService >>=
    TestEvents.testEventOrigination >>=
    testEventStatusUpdate_NewStatusPostponedResultsAreNoneSucceeds


testEventStatusUpdate_NewStatusPostponedResultsNotEmptyFails : Stack.HasCallStack =>
    (Parties, Text) -> Script ()
testEventStatusUpdate_NewStatusPostponedResultsNotEmptyFails
    (Parties{operator, gambyl, eventManager}, eventLabel) = script do

    -- Arrange: Query existing event instrument
    event <- queryContractKey @EventModel.EventInstrument eventManager (operator, gambyl, eventLabel) >>=
        (return . snd . Optional.fromSomeNote (Common.msgWithFunc "Event Instrument doesn't exist"))

    let eventResults = [(List.head event.details.outcomes).outcome]

    -- Act: Create request to update event status
    eitherEventUpdateReqCid <- submit eventManager do
       exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
             EventService.RequestEventStatusUpdate with
                newEventStatus = EnetPulseEvents.Postponed
                eventResults = Some eventResults
                oldEventKey = key event

    -- Assert: Verify event status update was created
    either
      (queryContractId gambyl
        >=> optional
          (assertFail $ Common.msgWithFunc "ActionFailure contract not found")
          (\ failure -> assertMsg (Common.msgWithFunc "Event status update request failed. Reason: " <> failure.reason)
                $ isInfixOf "Template precondition violated" failure.reason)
      )
      (\ _ -> assertFail $ Common.msgWithFunc  "Event instrument status update created under failing conditions")
      eitherEventUpdateReqCid

    return ()

run_testEventStatusUpdate_NewStatusPostponedResultsNotEmptyFails =
    TestOnboarding.testGamblingRole >>=
    TestMarketing.testOfferMarketingService >>=
    TestEvents.testOfferEventService >>=
    TestEvents.testEventOrigination >>=
    testEventStatusUpdate_NewStatusPostponedResultsNotEmptyFails


testEventStatusUpdate_NewStatusFinishedResultsAreNoneFails : Stack.HasCallStack =>
    (Parties, Text) -> Script ()
testEventStatusUpdate_NewStatusFinishedResultsAreNoneFails
    (Parties{operator, gambyl, eventManager}, eventLabel) = script do

    -- Arrange: Query existing event instrument

    event <- queryContractKey @EventModel.EventInstrument eventManager (operator, gambyl, eventLabel) >>=
        (return . snd . Optional.fromSomeNote (Common.msgWithFunc "Event Instrument doesn't exist"))

    -- Act: Create request to update event status
    eitherEventUpdateReqCid <- submit eventManager do
       exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
             EventService.RequestEventStatusUpdate with
                newEventStatus = EnetPulseEvents.Finished
                eventResults = None
                oldEventKey = key event

    -- Assert: Verify event status update was created
    either
      (queryContractId gambyl
        >=> optional
          (assertFail $ Common.msgWithFunc "ActionFailure contract not found")
          (\ failure -> assertMsg (Common.msgWithFunc "Event status update request failed. Reason: " <> failure.reason)
                $ isInfixOf "Template precondition violated" failure.reason)
      )
      (\ _ -> assertFail $ Common.msgWithFunc  "Event instrument status update created under failing conditions")
      eitherEventUpdateReqCid

    return ()

run_testEventStatusUpdate_NewStatusFinishedResultsAreNoneFails =
    TestOnboarding.testGamblingRole >>=
    TestMarketing.testOfferMarketingService >>=
    TestEvents.testOfferEventService >>=
    TestEvents.testEventOrigination >>=
    testEventStatusUpdate_NewStatusFinishedResultsAreNoneFails

testEventStatusUpdate_NewStatusFinishedResultsEmptyFails : Stack.HasCallStack =>
    (Parties, Text) -> Script ()
testEventStatusUpdate_NewStatusFinishedResultsEmptyFails
    (Parties{operator, gambyl, eventManager}, eventLabel) = script do

    -- Arrange: Query existing event instrument

    event <- queryContractKey @EventModel.EventInstrument eventManager (operator, gambyl, eventLabel) >>=
        (return . snd . Optional.fromSomeNote (Common.msgWithFunc "Event Instrument doesn't exist"))

    -- Act: Create request to update event status
    eitherEventUpdateReqCid <- submit eventManager do
       exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
             EventService.RequestEventStatusUpdate with
                newEventStatus = EnetPulseEvents.Finished
                eventResults = Some []
                oldEventKey = key event

    -- Assert: Verify event status update was created
    either
      (queryContractId gambyl
        >=> optional
          (assertFail $ Common.msgWithFunc "ActionFailure contract not found")
          (\ failure -> assertMsg (Common.msgWithFunc "Event status update request failed. Reason: " <> failure.reason)
                $ isInfixOf "Template precondition violated" failure.reason)
      )
      (\ _ -> assertFail $ Common.msgWithFunc "Event instrument status update created under failing conditions")
      eitherEventUpdateReqCid

    return ()

run_testEventStatusUpdate_NewStatusFinishedResultsEmptyFails =
    TestOnboarding.testGamblingRole >>=
    TestMarketing.testOfferMarketingService >>=
    TestEvents.testOfferEventService >>=
    TestEvents.testEventOrigination >>=
    testEventStatusUpdate_NewStatusFinishedResultsEmptyFails


testEventStatusUpdate_EventAlreadyFinishedFails : Stack.HasCallStack =>
    (Parties, Text) -> Script ()
testEventStatusUpdate_EventAlreadyFinishedFails
    (Parties{operator, gambyl, marketingManager, eventManager}, eventLabel) = script do

    -- Arrange: Query existing event instrument
    (eventCid, event) <- queryContractKey @EventModel.EventInstrument eventManager (operator, gambyl, eventLabel) >>=
        (return . Optional.fromSomeNote (Common.msgWithFunc "Event Instrument doesn't exist"))

    submitMulti [operator, gambyl, marketingManager, eventManager] [] do
        archiveCmd eventCid
        createCmd event with
            details = event.details with
                eventStatus = EnetPulseEvents.Finished
        pure ()

    let eventResults = [(List.head event.details.outcomes).outcome]

    -- Act: Create request to update event status
    eitherEventUpdateReqCid <- submit eventManager do
       exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
             EventService.RequestEventStatusUpdate with
                newEventStatus = EnetPulseEvents.Finished
                eventResults = Some eventResults
                oldEventKey = key event

    -- Assert: Verify event status update was created
    either
      (queryContractId gambyl
        >=> optional
          (assertFail $ Common.msgWithFunc "ActionFailure contract not found")
          (\ failure -> assertMsg (Common.msgWithFunc "Event status update request failed. Reason: " <> failure.reason)
                $ isInfixOf "Finished event cannot have its status changed" failure.reason)
      )
      (\ _ -> assertFail $ Common.msgWithFunc  "Event instrument status update created under failing conditions")
      eitherEventUpdateReqCid

    return ()

run_testEventStatusUpdate_EventAlreadyFinishedFails =
    TestOnboarding.testGamblingRole >>=
    TestMarketing.testOfferMarketingService >>=
    TestEvents.testOfferEventService >>=
    TestEvents.testEventOrigination >>=
    testEventStatusUpdate_EventAlreadyFinishedFails
