module Tests.Gambyl.Event.Service.RequestEventUpdate where

import Daml.Script

import DA.Assert    ((===))
import DA.Either qualified as Either
import DA.Map qualified as Map
import DA.Optional qualified as Optional
import DA.<PERSON>ack qualified as Stack (HasCallStack)

import Gambyl.Gambling.Event.Service qualified as EventService
import Gambyl.Gambling.Event.Model qualified as EventModel
import Gambyl.Gambling.Bet.Odds.Model qualified as Odds
import Gambyl.Utils qualified as GambylUtils

import Tests.Event qualified as TestEvents
import Tests.Marketing qualified as TestMarketing
import Tests.Onboarding qualified as TestOnboarding

import Scripts.Bootstrap.Onboarding.Parties (Parties(..))
import Scripts.Common qualified as Common (msgWithFunc)

testRejectEventUpdate : Stack.HasCallStack => (Parties, Text) -> Script ()
testRejectEventUpdate (Parties{operator, gambyl, eventManager}, eventLabel) = script do

    -- Arrange:
    event <- queryContractKey @EventModel.EventInstrument eventManager (operator, gambyl, eventLabel)
        >>= (return . snd . Optional.fromSomeNote (Common.msgWithFunc "Event Instrument doesn't exist"))

    -- Act: Request Event update
    eitherEventUpdateReqCid <- submit eventManager do
       exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
             EventService.RequestEventUpdate with
              eventOrigin = EventModel.CustomerUpdate with
                            assetLabel          = eventLabel
                            market              = event.details.market
                            submarkets          = event.details.submarkets
                            outcomes            = map (\ EventModel.OutcomeOdds{outcome} ->
                                EventModel.InputOutcomeOdd with outcome, odd = Odds.Decimal 1.0) event.details.outcomes
                            eventParticipants   = event.details.eventParticipants
                            eventTitle          = Map.fromList [(show GambylUtils.EN_US, "New Title")]
                            eventStatus         = event.details.eventStatus
                            eventResults        = event.details.eventResults
                            startDate           = event.details.startDate
              oldEventKey = key event

    -- Assert: Verify request was created successfully
    assertMsg (Common.msgWithFunc "Event update request not created") $ Either.isRight eitherEventUpdateReqCid
    let Right eventUpdateReqCid = eitherEventUpdateReqCid

    -- Act: Reject event update request
    let failureReason = "Event cannot be updated, because there are bets placed for it"
    actionFailureCid <- submit gambyl do
        exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
            EventService.RejectEventUpdate with
                requestCid = eventUpdateReqCid
                reason = failureReason

    eventUpdateRequestCidList <- query @EventModel.EventInstrumentUpdateRequest gambyl
    assertMsg "Event update request contract not archived" $ null eventUpdateRequestCidList

    actionFailure <- optional
        (assertFail "Failure contract not found for event update rejection")
        pure
        =<< queryContractId gambyl actionFailureCid

    actionFailure.reason === failureReason
    return ()

run_testRejectEventUpdate =
    TestOnboarding.testGamblingRole >>=
    TestMarketing.testOfferMarketingService >>=
    TestEvents.testOfferEventService >>=
    TestEvents.testEventOrigination >>=
    testRejectEventUpdate