module Tests.GoldenCancellations.GoldenUtils where

import Daml.Script

import DA.Action                                  (when)
import DA.Foldable                                (forA_)
import DA.Functor                                 (void)
import DA.List qualified as List
import DA.Optional qualified as Optional
import DA.Stack                                   (HasCallStack)

import EnetPulseIntegration.Events qualified as IntegrationEvents

import Gambyl.Gambling.Account.Model qualified as GamblingAccount
import Gambyl.Gambling.Account.Model              ()
import Gambyl.Gambling.Bet.Model qualified as BetModel
import Gambyl.Gambling.Bet.Odds.Model qualified as BetOddsModel
import Gambyl.Gambling.Bet.Odds.Model             (OddType(..))
import Gambyl.Gambling.Event.Model qualified as EventModel

import Scripts.Bootstrap.Onboarding.Parties       (Parties(..))
import Scripts.Common qualified as Common

import Tests.Gambling (testTwoWayEventOrigination,
    onboardCustomerWithDeposit
    , originateAsset
    , testBetPlacement
    , testEventOrigination
    , testGiveBugBountyBonus
    , testMatchBets
    , testManualBetCancel
    , testAutoCancelBetAfterEventStatusChange
  )
import Tests.Marketing qualified as TestMarketing
import Tests.Onboarding qualified as TestOnboarding
import Tests.Utils qualified as TestUtils

-- Helpers
back : BetOddsModel.OddType -> Numeric 2 -> IntegrationEvents.Outcome -> BetModel.Side
back oddType stake = TestUtils.buildSide BetModel.Back oddType stake

lay : BetOddsModel.OddType -> Numeric 2 -> IntegrationEvents.Outcome -> BetModel.Side
lay oddType stake = TestUtils.buildSide BetModel.Lay oddType stake

-- Used to keep state (memory) while the test-driver is running.
-- This allows to verify expectations to against history.
template RunningPartyAccount with
    party: Party
    totalMainBalance: Numeric 2
    totalBetBalance: Numeric 2
    totalBonusBalance: Numeric 2
    totalBonusBetBalance: Numeric 2
  where
    signatory party
    key party: Party
    maintainer key

-- XXX: it does not look for subsequents splits; it only supports 1 split per bet.
lookupForPartialMatches : HasCallStack => Party -> Text -> Script (Text, Optional Text)
lookupForPartialMatches gambyl betId = script $ do
  maybePartialMatchedBetIds <- queryFilter @BetModel.BetPlacement gambyl $ \BetModel.BetPlacement{details = BetModel.Details{betPlacementId}} ->
      betPlacementId == betId <> "_1"
  let maybePartialMatchedBetId =
          fmap
          (\BetModel.BetPlacement{details = BetModel.Details{betPlacementId}} -> betPlacementId)
          $ snd <$> maybePartialMatchedBetIds
  let matchedBetId = if null maybePartialMatchedBetId then betId else List.head maybePartialMatchedBetId

  maybePartialUnmatchedBetIds <- queryFilter @BetModel.BetPlacement gambyl $ \BetModel.BetPlacement{details = BetModel.Details{betPlacementId}} ->
      betPlacementId == betId <> "_2"
  let maybePartialUnmatchedBetId =
          fmap
          (\BetModel.BetPlacement{details = BetModel.Details{betPlacementId}} -> betPlacementId)
          $ snd <$> maybePartialUnmatchedBetIds
  let unmatchedBetId = if null maybePartialMatchedBetId then None else Some $ List.head maybePartialUnmatchedBetId

  pure (matchedBetId, unmatchedBetId)

-- Model
data DepositInputs = DepositInputs with
  deposit: Numeric 2
  bonusAmount: Optional (Numeric 2)

data BetSide = Back | Lay

data BetInputs = BetInputs with
  stake: Numeric 2
  odd: BetOddsModel.OddType
  side: BetSide

data ExpectationStage =
  UnmatchedStage
  | MatchedStage
  | SplitUnmatchedStage
  | CancelStage

data Expectation =
  Stake (Numeric 2)
  | Odd BetOddsModel.OddType
  | Profit (Numeric 2)
  | Liability (Numeric 2)
  | CashUsed (Numeric 2)
  | BonusUsed (Numeric 2)
  | CashReturned (Numeric 2)
  | BonusReturned (Numeric 2)

data BetExpectation = BetExpectation with
  unmatched: [Expectation]
  matched: [Expectation]
  splitUnmatched: [Expectation]

data BetEntry = BetEntry with
  inputs: BetInputs
  expectations: BetExpectation

data CancelExpectation =
  AccountMainBalance (Numeric 2)
  | AccountBonusBalance (Numeric 2)

data CancelBetsAction = Manual | Automatic IntegrationEvents.Status

data CancelAction = CancelAction with
  status : [BetModel.Status]
  action : CancelBetsAction

data PartyEntry = PartyEntry with
  party: Party
  depositInputs: DepositInputs
  bets : [BetEntry]
  afterActionExpectations: [CancelExpectation]

data Entry = Entry with
  party1Entry : Parties -> PartyEntry
  party2Entry : Parties -> PartyEntry
  action : CancelAction

execute : Parties -> Party -> CancelAction -> Text -> Script ()
execute parties party CancelAction{action} betId =
  case action of
    Automatic newStatus -> void $ testAutoCancelBetAfterEventStatusChange (parties, party, betId, newStatus)
    Manual -> void $ testManualBetCancel (parties, party, betId)

verify : Party -> Party -> BetInputs -> Text -> ExpectationStage -> Expectation -> Script ()
verify gambyl party BetInputs{..} betId stage = do
  let statusFilter = case stage of
        UnmatchedStage -> BetModel.Unmatched
        MatchedStage -> BetModel.Matched
        SplitUnmatchedStage -> BetModel.Unmatched
        CancelStage -> BetModel.Matched
  \case
    -- the stakes used are consistent
    Stake value ->  do
      allBetPlacements <- queryFilter @BetModel.BetPlacement gambyl $ \BetModel.BetPlacement{status} -> status == statusFilter
      let [partyStake] =
              fmap (\BetModel.BetPlacement{details = BetModel.Details{side}} ->
                  case side of
                      BetModel.Back BetModel.SideDetails{stake} -> (stake)
                      BetModel.Lay BetModel.SideDetails{stake} -> (stake)
              ) . filter (\BetModel.BetPlacement{details = BetModel.Details{betPlacementId}} -> betPlacementId == betId)
              $ snd <$> allBetPlacements
      assertMsg ("Error on verify Stake. Expected value: " <> show partyStake <> ". Actual value: " <> show value <> ". On bet: " <> show betId) $ partyStake == value
    -- the odds used are consistent
    Odd value -> do
      allBetPlacements <- queryFilter @BetModel.BetPlacement gambyl $ \BetModel.BetPlacement{status} -> status == statusFilter
      let [partyOdd] =
              fmap (\BetModel.BetPlacement{details = BetModel.Details{side}} ->
                  case side of
                      BetModel.Back BetModel.SideDetails{odd} -> (odd)
                      BetModel.Lay BetModel.SideDetails{odd} -> (odd)
              ) . filter (\BetModel.BetPlacement{details = BetModel.Details{betPlacementId}} -> betPlacementId == betId)
              $ snd <$> allBetPlacements
      assertMsg ("Error on verify Odd. Expected value: " <> show partyOdd <> ". Actual value: " <> show value <> ". On bet: " <> show betId) $ partyOdd == value
    -- check party cash used
    -- should be equals to: partyInput.depositInputs.deposit - totalMainBalance
    CashUsed value -> do
      allBetPlacements <- queryFilter @BetModel.BetPlacement gambyl $ \BetModel.BetPlacement{status} -> status == statusFilter
      let partyBetPlacements =
              filter (\BetModel.BetPlacement{details = BetModel.Details{betPlacementId}} -> betPlacementId == betId)
              $ snd <$> allBetPlacements
      [partyExpectedCashUsed] <-
              mapA (\BetModel.BetPlacement{bonusBetAmount, details = BetModel.Details{side}} ->
                  case side of
                      BetModel.Back BetModel.SideDetails{stake} -> do
                          let bonusAmount = Optional.fromOptional 0.0 bonusBetAmount
                          pure $ stake - bonusAmount
                      BetModel.Lay BetModel.SideDetails{odd, stake} ->
                          case BetOddsModel.calculatePayout odd stake of
                              Left err -> assertFail $ ("Error on verify cash used: " <> err)
                              Right payout -> do
                                  let bonusAmount = Optional.fromOptional 0.0 bonusBetAmount
                                      result = if bonusAmount >= payout then 0.0 else payout - bonusAmount
                                  pure $ result
              ) $ partyBetPlacements
      assertMsg ("Error on verify Cash Used. Expected value: " <> show partyExpectedCashUsed <> ". Actual value: " <> show value <> ". On bet: " <> show betId) $
          partyExpectedCashUsed == value
    -- the bonuses used are consistent
    BonusUsed value -> do
      allBetPlacements <- queryFilter @BetModel.BetPlacement gambyl $ \BetModel.BetPlacement{status} -> status == statusFilter
      let [partyBonus] =
              fmap (\ BetModel.BetPlacement{bonusBetAmount} -> bonusBetAmount)
              . filter (\BetModel.BetPlacement{details = BetModel.Details{betPlacementId}} -> betPlacementId == betId)
              $ snd <$>  allBetPlacements
      assertMsg ("Error on verify Bonus. Expected value: " <> show partyBonus <> ". Actual value: " <> show value <> ". On bet: " <> show betId) $ Optional.fromOptional 0.0 partyBonus == value
    -- Check if the profit value of the Back bet is correct
    Profit value -> do
      allBetPlacements <- queryFilter @BetModel.BetPlacement gambyl $ \BetModel.BetPlacement{status} -> status == statusFilter
      let [side] =
              fmap (\ BetModel.BetPlacement{details} -> details.side)
              . filter (\BetModel.BetPlacement{details = BetModel.Details{betPlacementId}} -> betPlacementId == betId)
              $ snd <$>  allBetPlacements
      assertMsg "Bet must be Back side" $ (BetModel.getSide side) == "Back"
      let profit = Common.fromRightNote "Not possible to calculate profit" (BetModel.getPayout side)
      assertMsg ("Error on verify Profit. Expected value: " <> show profit <> ". Actual value: " <> show value <> ". On bet: " <> show betId) $ profit == value
    -- Check if the liability value of the Lay bet is correct
    Liability value -> do
      allBetPlacements <- queryFilter @BetModel.BetPlacement gambyl $ \BetModel.BetPlacement{status} -> status == statusFilter
      let [side] =
              fmap (\ BetModel.BetPlacement{details} -> details.side)
              . filter (\BetModel.BetPlacement{details = BetModel.Details{betPlacementId}} -> betPlacementId == betId)
              $ snd <$>  allBetPlacements
      assertMsg "Bet must be Lay side" $ (BetModel.getSide side) == "Lay"
      let (odd, stake) = case side of
            BetModel.Lay BetModel.SideDetails{odd, stake} -> (odd, stake)
            BetModel.Back BetModel.SideDetails{odd, stake} -> (odd, stake)
      let Right liability = BetOddsModel.calculatePayout odd stake
      assertMsg ("Error on verify Liability. Expected value: " <> show liability <> ". Actual value: " <> show value <> ". On bet: " <> show betId) $ liability == value
    CashReturned value ->
      case stage of
          MatchedStage -> do
              [GamblingAccount.Account{..}] <- fmap snd <$> query @GamblingAccount.Account party
              Some (_, unmatchedRunningPartyAccount) <- queryContractKey @RunningPartyAccount party party
              let cashReturned = totalMainBalance - unmatchedRunningPartyAccount.totalMainBalance
              assertMsg ("Error on CashReturned. Expected value: " <> show cashReturned <> ". Actual value: " <> show value <> ". On bet: " <> show betId) $ cashReturned == value
          _ -> assertFail "CashReturned is only supported during Matched stage"
    BonusReturned value -> do
      case stage of
          MatchedStage -> do
              [GamblingAccount.Account{..}] <- fmap snd <$> query @GamblingAccount.Account party
              Some (_, unmatchedRunningPartyAccount) <- queryContractKey @RunningPartyAccount party party
              let bonusReturned = totalBonusBalance - unmatchedRunningPartyAccount.totalBonusBalance
              assertMsg ("Error on BonusReturned. Expected value: " <> show bonusReturned <> ". Actual value: " <> show value <> ". On bet: " <> show betId) $ bonusReturned == value
          _ -> assertFail "BonusReturned is only supported during Matched stage"

-- Interpreter
checkEntry : Entry -> Script ()
checkEntry Entry{party1Entry, party2Entry, action} = script do
  -- [SetUp]
  partyWithCurrency@(parties, _) <- setup
  -- 1/ init 2 parties (each takes a side)
  let entryParty1 = party1Entry parties
  let entryParty2 = party2Entry parties
  -- 2/ deposit cash + bonus (opt)
  --  assert they match expected (action result over input)
  -- NOTE: 2/ inputs and expectations are the same
  onboardParty partyWithCurrency entryParty1
  onboardParty partyWithCurrency entryParty2
  -- 3/ place bets on event
  -- 3.a/ create event
  (eventId, outcomes) <- createEvent parties
  -- 3.b/ place n bets per party
  --    ie: p1=[Bet(B1)] & p2=[Bet(B2)]
  --    assert on unmatched bets expectations
  forA_ (entryParty1.bets `zip` [1..(length entryParty1.bets)]) $ uncurry (placeBet parties (eventId, outcomes) entryParty1.party)
  forA_ (entryParty2.bets `zip` [1..(length entryParty2.bets)]) $ uncurry (placeBet parties (eventId, outcomes) entryParty2.party)
  -- XXX: Load running parties to store their accout balance details after placing the bets.
  -- See usage during `verify` (CashReturn & BonusReturned)
  loadRunningPartyAccount entryParty1.party
  loadRunningPartyAccount entryParty2.party
  -- NOTE: We skipped the following assertions as they seem to be redudant and are present on the settlment test
  --  1. there should be two bet-placements with status unmatched and
  --  2. the ids are the same as we input
  -- 4/ match the bets
  testMatchBets parties
  -- 5/ verify after matching
  -- 5.a/ assert on matched bets expectations (there might be empty expectations)
  --    if empty then check there exist none (they remain unmatched)
  --    ie: Matched(B1,B2,...)
  -- 5.b/ assert on unmatching bets expectations (there might be empty expectations)
  --    if empty then check there exist none
  --    ie: UnMatched(B1,B2,...)
  forA_ (entryParty1.bets `zip` [1..(length entryParty1.bets)]) $ uncurry (verifyBetAfterMatching parties entryParty1.party)
  forA_ (entryParty2.bets `zip` [1..(length entryParty2.bets)]) $ uncurry (verifyBetAfterMatching parties entryParty2.party)

  -- [Manual + Unmatched]
  -- 1/ cancel unmatched bets using manual action
  --
  -- action = CancelAction with
  --           status = [BetModel.Unmatched]
  --           action = Manual

  -- [Automatic + Unmatched]
  -- 1/ update an event to be InProgress/Finished
  -- 2/ cancel unmatched bets using automatic action
  --
  -- action = CancelAction with
  --           status = [BetModel.Unmatched]
  --           action = Automatic IntegrationEvents.InProgress/Finished

  -- [Manual + PartialMatch]
  -- 1/ when matching the bets, they need to be splitted
  -- 2/ cancel unmatched splitted bets using manual action
  --
  -- action = CancelAction with
  --           status = [BetModel.Unmatched]
  --           action = Manual

  -- [Automatic + PartialMatch]
  -- 1/ when matching the bets, they need to be splitted
  -- 2/ update an event to be InProgress/Finished
  -- 3/ cancel unmatched splitted bets using automatic action
  --
  -- action = CancelAction with
  --           status = [BetModel.Unmatched]
  --           action = Automatic IntegrationEventsInProgress/Finished

  -- [Cancelled]
  -- 1/ update an event to be Cancelled
  -- 2/ cancel matched & unmatched bets using automatic action
  --
  -- action = CancelAction with
  --           status = [BetModel.Unmatched, BetModel.Matched]
  --           action = Automatic IntegrationEvents.InProgress/Finished


  -- [Postponed]
  -- 1/ update an event to be Postponed
  -- 2/ cancel matched & unmatched bets using automatic action
  -- NOTE: this is going to be tricky
  --
  -- action = CancelAction with
  --           status = [BetModel.Unmatched, BetModel.Matched]
  --           action = Automatic IntegrationEvents.Postponed

  executeCancelActions parties entryParty1.party action.status
  executeCancelActions parties entryParty2.party action.status

  forA_ entryParty1.afterActionExpectations $ verifyAccountAfterCanceling parties entryParty1.party
  forA_ entryParty2.afterActionExpectations $ verifyAccountAfterCanceling parties entryParty2.party

  pure ()
  where
    setup : Script (Parties, Text)
    setup = script do
      parties <- TestOnboarding.testGamblingRole
      TestMarketing.testOfferMarketingService parties
      let assetLabel = "USD"
      let partyWithCurrency = (parties, assetLabel)
      originateAsset partyWithCurrency
      pure partyWithCurrency

    onboardParty : (Parties, Text) -> PartyEntry -> Script ()
    onboardParty partyWithCurrency partyEntry = script do
      onboardCustomerWithDeposit (partyWithCurrency, partyEntry.party, "123", partyEntry.depositInputs.deposit, None, "EN")
      forA_ partyEntry.depositInputs.bonusAmount $ testGiveBugBountyBonus . (partyWithCurrency, partyEntry.party,)

    createEvent : Parties -> Script (Text, [EventModel.OutcomeOdds])
    createEvent parties = script do
      (_, EventModel.EventInstrument{
              details = EventModel.Details{outcomes},
              eventId
      }) <- testEventOrigination (parties, "AvB")
      pure (eventId.label, outcomes)

    placeBet : Parties -> (Text, [EventModel.OutcomeOdds]) -> Party -> BetEntry -> Int -> Script ()
    placeBet parties@Parties{gambyl} (eventId, outcomes) party BetEntry{inputs = inputs@BetInputs{stake, odd, side}, expectations = BetExpectation{unmatched}} id = script do
      let partyBetId = show party <> show id
      let (outcomeOdds1::_) = outcomes
      let partySide = case side of
              Back -> back odd stake outcomeOdds1.outcome
              Lay -> lay odd stake outcomeOdds1.outcome
      testBetPlacement ((parties, eventId), party, partySide, partyBetId, None)
      assertMsg ("Error on place bet. Expected unmatched to be NonEmpty. On bet: " <> show partyBetId) $ not . null $ unmatched
      -- derive stake + odd unmatched expectations from bet inputs
      let derived = (Stake stake) :: (Odd odd) :: unmatched
      forA_ derived $ verify gambyl party inputs partyBetId UnmatchedStage
      pure ()

    loadRunningPartyAccount : Party -> Script ()
    loadRunningPartyAccount party = script do
      maybeRunningPartyAccount <- queryContractKey @RunningPartyAccount party party
      forA_ (fst <$> maybeRunningPartyAccount) $ \runningPartyAccount -> submit party do archiveCmd runningPartyAccount
      [GamblingAccount.Account{..}] <- fmap snd <$> query @GamblingAccount.Account party
      void . submit party $ createCmd RunningPartyAccount with ..

    verifyBetAfterMatching : Parties -> Party -> BetEntry -> Int -> Script ()
    verifyBetAfterMatching Parties{gambyl} party BetEntry{inputs, expectations = BetExpectation{unmatched, matched, splitUnmatched}} id = script do
      let partyBetId = show party <> show id
      (matchedBetId, maybePartialUnmatchedBetId) <- lookupForPartialMatches gambyl partyBetId

      -- check the matched side of the bet
      forA_ matched $ verify gambyl party inputs matchedBetId MatchedStage
      -- if we do not expect bets to be matched
      when (null matched) $ do
        -- then check the bet remains unmatched
        forA_ unmatched $ verify gambyl party inputs matchedBetId UnmatchedStage

      -- check the unmatched side of the bet
      case maybePartialUnmatchedBetId of
        -- if not splitted
        None -> do
          -- then we check we do not expect any split unmatched
          assertMsg ("Error on verify after bet matching. Expected splitUnmatched to be Empty. On bet: " <> show partyBetId) $ null $ splitUnmatched
        -- if splitted
        Some unmatchedBetId -> do
          -- then check we do expect on split unmatched
          assertMsg ("Error on verify after bet matching. Expected splitUnmatched to be NonEmpty. On bet: " <> show partyBetId) $ not . null $ splitUnmatched
          -- and verify the splitted side of the bet
          forA_ splitUnmatched $ verify gambyl party inputs unmatchedBetId SplitUnmatchedStage
      pure ()

    executeCancelActions: Parties -> Party -> [BetModel.Status] -> Script ()
    executeCancelActions parties@Parties{gambyl} party betStatus = script do
        allBets <- queryFilter @BetModel.BetPlacement gambyl $ \BetModel.BetPlacement{status} -> status `elem` betStatus
        let partyBets =
              fmap (\BetModel.BetPlacement{details = BetModel.Details{betPlacementId}} -> betPlacementId )
              . filter (\BetModel.BetPlacement{customer} -> customer == party)
              $ snd <$> allBets
        forA_ partyBets $ execute parties party action

    verifyAccountAfterCanceling : Parties -> Party -> CancelExpectation -> Script ()
    verifyAccountAfterCanceling _parties party = \case
      AccountMainBalance value -> do
        [GamblingAccount.Account{..}] <- fmap snd <$> query @GamblingAccount.Account party
        assertMsg ("Error on AccountMainBalance. Expected value: " <> show totalMainBalance <> ". Actual value: " <> show value <> ". On party: " <> show party) $ totalMainBalance == value
      AccountBonusBalance value -> do
        [GamblingAccount.Account{..}] <- fmap snd <$> query @GamblingAccount.Account party
        assertMsg ("Error on AccountBonusBalance. Expected value: " <> show totalBonusBalance <> ". Actual value: " <> show value <> ". On party: " <> show party) $ totalBonusBalance == value

-- Driver (Main)
testEntry = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = Some 15.0
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 15.0
                  odd = Decimal 2.0
                  side = Back
                expectations = BetExpectation with
                  unmatched = [
                      Profit 15.0,
                      CashUsed 0.0,
                      BonusUsed 15.0
                    ]
                  matched = [
                    Stake 10.0
                    ]
                  splitUnmatched = [
                    Stake 5.0
                    ]
              ]
            afterActionExpectations = []
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 10.0
                  odd = Decimal 2.0
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 10.0,
                      CashUsed 10.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                    Stake 10.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = []
        action = CancelAction with
            status = [BetModel.Unmatched]
            action = Manual
  checkEntry entry

gBEP21_2383_scenario = script do
  partyWithCurrency@(parties, _) <- setup
  onboardParty partyWithCurrency parties.alice
  onboardParty partyWithCurrency parties.bob
  (eventId, outcomes) <- createEvent parties
  let (outcomeTeam1::outcomeTeam2::_) = outcomes
  let betEntry1 = BetInputs with
        stake = 10.0
        odd = Decimal 2.0
        side = Back
  void $ placeBet parties (eventId, outcomeTeam1) parties.alice betEntry1 1
  let betEntry2 = BetInputs with
        stake = 6.0
        odd = Decimal 2.0
        side = Back
  void $ placeBet parties (eventId, outcomeTeam2) parties.alice betEntry2 2
  let betEntry3 = BetInputs with
        stake = 5.0
        odd = Decimal 2.0
        side = Lay
  void $ placeBet parties (eventId, outcomeTeam1) parties.bob betEntry3 3
  let betEntry4 = BetInputs with
        stake = 4.0
        odd = Decimal 2.0
        side = Lay
  void $ placeBet parties (eventId, outcomeTeam2) parties.bob betEntry4 4
  testMatchBets parties
  executeCancelActions parties parties.alice CancelAction with
    status = [BetModel.Matched, BetModel.Unmatched]
    action = Automatic IntegrationEvents.Postponed
  executeCancelActions parties parties.bob CancelAction with
    status = [BetModel.Matched, BetModel.Unmatched]
    action = Automatic IntegrationEvents.Postponed
  pure ()

  where
    setup : Script (Parties, Text)
    setup = script do
      parties <- TestOnboarding.testGamblingRole
      TestMarketing.testOfferMarketingService parties
      let assetLabel = "USD"
      let partyWithCurrency = (parties, assetLabel)
      originateAsset partyWithCurrency
      pure partyWithCurrency

    onboardParty : (Parties, Text) -> Party -> Script ()
    onboardParty partyWithCurrency partyEntry = script do
      onboardCustomerWithDeposit (partyWithCurrency, partyEntry, "123", 200.00, None, "EN")
      -- forA_ partyEntry.depositInputs.bonusAmount $ testGiveBugBountyBonus . (partyWithCurrency, partyEntry.party,)
      pure ()

    createEvent : Parties -> Script (Text, [EventModel.OutcomeOdds])
    createEvent parties = script do
      (_, EventModel.EventInstrument{
              details = EventModel.Details{outcomes},
              eventId
      }) <- testTwoWayEventOrigination (parties, "AvB")
      pure (eventId.label, outcomes)

    placeBet : Parties -> (Text, EventModel.OutcomeOdds) -> Party -> BetInputs -> Int -> Script ()
    placeBet parties (eventId, outcomeOdds) party BetInputs{stake, odd, side} id = script do
      let partyBetId = show party <> show id
      let partySide = case side of
              Back -> back odd stake outcomeOdds.outcome
              Lay -> lay odd stake outcomeOdds.outcome
      testBetPlacement ((parties, eventId), party, partySide, partyBetId, None)
      pure ()

    executeCancelActions: Parties -> Party -> CancelAction -> Script ()
    executeCancelActions parties@Parties{gambyl} party action = script do
        allBets <- queryFilter @BetModel.BetPlacement gambyl $ \BetModel.BetPlacement{status} -> status `elem` action.status
        let partyBets =
              fmap (\BetModel.BetPlacement{details = BetModel.Details{betPlacementId}} -> betPlacementId )
              . filter (\BetModel.BetPlacement{customer} -> customer == party)
              $ snd <$> allBets
        forA_ partyBets $ execute parties party action
