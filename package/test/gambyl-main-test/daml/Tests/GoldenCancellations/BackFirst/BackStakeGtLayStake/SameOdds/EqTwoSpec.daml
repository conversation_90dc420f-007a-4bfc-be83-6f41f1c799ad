module Tests.GoldenCancellations.BackFirst.BackStakeGtLayStake.SameOdds.EqTwoSpec where

import Daml.Script

import Tests.GoldenCancellations.GoldenUtils

import Scripts.Bootstrap.Onboarding.Parties       (Parties(..))
import Gambyl.Gambling.Bet.Odds.Model             (OddType(..))
import Gambyl.Gambling.Bet.Model qualified as BetModel
import EnetPulseIntegration.Events qualified as IntegrationEvents

run_7 = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = Some 15.0
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 10.0
                  odd = Decimal 2.0
                  side = Back
                expectations = BetExpectation with
                  unmatched = [
                      Profit 10.0,
                      CashUsed 0.0,
                      BonusUsed 10.0
                    ]
                  matched = [
                      Stake 5.0,
                      Odd (Decimal 2.0),
                      CashUsed 0.0,
                      BonusUsed 5.0
                    ]
                  splitUnmatched = [
                      CashUsed 0.0,
                      BonusUsed 5.0
                    ]
              ]
            afterActionExpectations = [
                AccountBonusBalance 10.0,
                AccountMainBalance 200.0
              ]
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 5.0
                  odd = Decimal 2.0
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 5.0,
                      CashUsed 5.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 5.0,
                      Odd (Decimal 2.0),
                      Liability 5.0,
                      CashUsed 5.0,
                      BonusUsed 0.0,
                      CashReturned 0.0,
                      BonusReturned 0.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = []
        action = CancelAction with
          status = [BetModel.Unmatched]
          action = Automatic IntegrationEvents.InProgress
  checkEntry entry

run_8 = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = Some 10.0
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 15.0
                  odd = Decimal 2.0
                  side = Back
                expectations = BetExpectation with
                  unmatched = [
                      Profit 15.0,
                      CashUsed 5.0,
                      BonusUsed 10.0
                    ]
                  matched = [
                      Stake 5.0,
                      Odd (Decimal 2.0),
                      CashUsed 0.0,
                      BonusUsed 5.0
                    ]
                  splitUnmatched = [
                      CashUsed 5.0,
                      BonusUsed 5.0
                    ]
              ]
            afterActionExpectations = [
                AccountBonusBalance 5.0,
                AccountMainBalance 200.0
              ]
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 5.0
                  odd = Decimal 2.0
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 5.0,
                      CashUsed 5.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 5.0,
                      Odd (Decimal 2.0),
                      Liability 5.0,
                      CashUsed 5.0,
                      BonusUsed 0.0,
                      CashReturned 0.0,
                      BonusReturned 0.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = []
        action = CancelAction with
          status = [BetModel.Unmatched]
          action = Automatic IntegrationEvents.InProgress
  checkEntry entry

run_9 = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 10.0
                  odd = Decimal 2.0
                  side = Back
                expectations = BetExpectation with
                  unmatched = [
                      Profit 10.0,
                      CashUsed 10.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 5.0,
                      Odd (Decimal 2.0),
                      CashUsed 5.0,
                      BonusUsed 0.0,
                      CashReturned 0.0,
                      BonusReturned 0.0
                    ]
                  splitUnmatched = [
                      CashUsed 5.0,
                      BonusUsed 0.0
                    ]
              ]
            afterActionExpectations = [
                AccountBonusBalance 0.0,
                AccountMainBalance 195.0
              ]
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 5.0
                  odd = Decimal 2.0
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 5.0,
                      CashUsed 5.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 5.0,
                      Odd (Decimal 2.0),
                      Liability 5.0,
                      CashUsed 5.0,
                      BonusUsed 0.0,
                      CashReturned 0.0,
                      BonusReturned 0.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = []
        action = CancelAction with
          status = [BetModel.Unmatched]
          action = Automatic IntegrationEvents.InProgress
  checkEntry entry

run_10 = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = Some 15.0
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 10.0
                  odd = Decimal 2.0
                  side = Back
                expectations = BetExpectation with
                  unmatched = [
                      Profit 10.0,
                      CashUsed 0.0,
                      BonusUsed 10.0
                    ]
                  matched = [
                      Stake 5.0,
                      Odd (Decimal 2.0),
                      CashUsed 0.0,
                      BonusUsed 5.0
                    ]
                  splitUnmatched = [
                      CashUsed 0.0,
                      BonusUsed 5.0
                    ]
              ]
            afterActionExpectations = [
                AccountBonusBalance 10.0,
                AccountMainBalance 200.0
              ]
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 5.0
                  odd = Decimal 2.0
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 5.0,
                      CashUsed 5.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 5.0,
                      Odd (Decimal 2.0),
                      Liability 5.0,
                      CashUsed 5.0,
                      BonusUsed 0.0,
                      CashReturned 0.0,
                      BonusReturned 0.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = []
        action = CancelAction with
          status = [BetModel.Unmatched]
          action = Manual
  checkEntry entry

run_11 = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = Some 10.0
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 15.0
                  odd = Decimal 2.0
                  side = Back
                expectations = BetExpectation with
                  unmatched = [
                      Profit 15.0,
                      CashUsed 5.0,
                      BonusUsed 10.0
                    ]
                  matched = [
                      Stake 5.0,
                      Odd (Decimal 2.0),
                      CashUsed 0.0,
                      BonusUsed 5.0
                    ]
                  splitUnmatched = [
                      CashUsed 5.0,
                      BonusUsed 5.0
                    ]
              ]
            afterActionExpectations = [
                AccountBonusBalance 5.0,
                AccountMainBalance 200.0
              ]
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 5.0
                  odd = Decimal 2.0
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 5.0,
                      CashUsed 5.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 5.0,
                      Odd (Decimal 2.0),
                      Liability 5.0,
                      CashUsed 5.0,
                      BonusUsed 0.0,
                      CashReturned 0.0,
                      BonusReturned 0.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = []
        action = CancelAction with
          status = [BetModel.Unmatched]
          action = Manual
  checkEntry entry

run_12 = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 10.0
                  odd = Decimal 2.0
                  side = Back
                expectations = BetExpectation with
                  unmatched = [
                      Profit 10.0,
                      CashUsed 10.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 5.0,
                      Odd (Decimal 2.0),
                      CashUsed 5.0,
                      BonusUsed 0.0
                    ]
                  splitUnmatched = [
                      CashUsed 5.0,
                      BonusUsed 0.0
                    ]
              ]
            afterActionExpectations = [
                AccountBonusBalance 0.0,
                AccountMainBalance 195.0
              ]
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 5.0
                  odd = Decimal 2.0
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 5.0,
                      CashUsed 5.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 5.0,
                      Odd (Decimal 2.0),
                      Liability 5.0,
                      CashUsed 5.0,
                      BonusUsed 0.0,
                      CashReturned 0.0,
                      BonusReturned 0.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = []
        action = CancelAction with
          status = [BetModel.Unmatched]
          action = Manual
  checkEntry entry

run_37 = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = Some 15.0
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 10.0
                  odd = Decimal 2.0
                  side = Back
                expectations = BetExpectation with
                  unmatched = [
                      Profit 10.0,
                      CashUsed 0.0,
                      BonusUsed 10.0
                    ]
                  matched = [
                      Stake 5.0,
                      Odd (Decimal 2.0),
                      CashUsed 0.0,
                      BonusUsed 5.0
                    ]
                  splitUnmatched = [
                      CashUsed 0.0,
                      BonusUsed 5.0
                    ]
              ]
            afterActionExpectations = [
                AccountBonusBalance 15.0,
                AccountMainBalance 200.0
              ]
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 5.0
                  odd = Decimal 2.0
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 5.0,
                      CashUsed 5.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 5.0,
                      Odd (Decimal 2.0),
                      Liability 5.0,
                      CashUsed 5.0,
                      BonusUsed 0.0,
                      CashReturned 0.0,
                      BonusReturned 0.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = [
                AccountBonusBalance 0.0,
                AccountMainBalance 200.0
              ]
        action = CancelAction with
          status = [BetModel.Unmatched, BetModel.Matched]
          action = Automatic IntegrationEvents.Cancelled
  checkEntry entry

run_38 = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = Some 10.0
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 15.0
                  odd = Decimal 2.0
                  side = Back
                expectations = BetExpectation with
                  unmatched = [
                      Profit 15.0,
                      CashUsed 5.0,
                      BonusUsed 10.0
                    ]
                  matched = [
                      Stake 5.0,
                      Odd (Decimal 2.0),
                      CashUsed 0.0,
                      BonusUsed 5.0
                    ]
                  splitUnmatched = [
                      CashUsed 5.0,
                      BonusUsed 5.0
                    ]
              ]
            afterActionExpectations = [
                AccountBonusBalance 10.0,
                AccountMainBalance 200.0
              ]
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 5.0
                  odd = Decimal 2.0
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 5.0,
                      CashUsed 5.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 5.0,
                      Odd (Decimal 2.0),
                      Liability 5.0,
                      CashUsed 5.0,
                      BonusUsed 0.0,
                      CashReturned 0.0,
                      BonusReturned 0.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = [
                AccountBonusBalance 0.0,
                AccountMainBalance 200.0
              ]
        action = CancelAction with
          status = [BetModel.Unmatched, BetModel.Matched]
          action = Automatic IntegrationEvents.Cancelled
  checkEntry entry

run_39 = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 10.0
                  odd = Decimal 2.0
                  side = Back
                expectations = BetExpectation with
                  unmatched = [
                      Profit 10.0,
                      CashUsed 10.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 5.0,
                      Odd (Decimal 2.0),
                      CashUsed 5.0,
                      BonusUsed 0.0
                    ]
                  splitUnmatched = [
                      CashUsed 5.0,
                      BonusUsed 0.0
                    ]
              ]
            afterActionExpectations = [
                AccountBonusBalance 0.0,
                AccountMainBalance 200.0
              ]
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 5.0
                  odd = Decimal 2.0
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 5.0,
                      CashUsed 5.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 5.0,
                      Odd (Decimal 2.0),
                      Liability 5.0,
                      CashUsed 5.0,
                      BonusUsed 0.0,
                      CashReturned 0.0,
                      BonusReturned 0.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = [
                AccountBonusBalance 0.0,
                AccountMainBalance 200.0
              ]
        action = CancelAction with
          status = [BetModel.Unmatched, BetModel.Matched]
          action = Automatic IntegrationEvents.Cancelled
  checkEntry entry

run_40 = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = Some 15.0
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 10.0
                  odd = Decimal 2.0
                  side = Back
                expectations = BetExpectation with
                  unmatched = [
                      Profit 10.0,
                      CashUsed 0.0,
                      BonusUsed 10.0
                    ]
                  matched = [
                      Stake 5.0,
                      Odd (Decimal 2.0),
                      CashUsed 0.0,
                      BonusUsed 5.0
                    ]
                  splitUnmatched = [
                      CashUsed 0.0,
                      BonusUsed 5.0
                    ]
              ]
            afterActionExpectations = [
                AccountBonusBalance 15.0,
                AccountMainBalance 200.0
              ]
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 5.0
                  odd = Decimal 2.0
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 5.0,
                      CashUsed 5.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 5.0,
                      Odd (Decimal 2.0),
                      Liability 5.0,
                      CashUsed 5.0,
                      BonusUsed 0.0,
                      CashReturned 0.0,
                      BonusReturned 0.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = [
                AccountBonusBalance 0.0,
                AccountMainBalance 200.0
              ]
        action = CancelAction with
          status = [BetModel.Unmatched, BetModel.Matched]
          action = Automatic IntegrationEvents.Postponed
  checkEntry entry

run_41 = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = Some 10.0
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 15.0
                  odd = Decimal 2.0
                  side = Back
                expectations = BetExpectation with
                  unmatched = [
                      Profit 15.0,
                      CashUsed 5.0,
                      BonusUsed 10.0
                    ]
                  matched = [
                      Stake 5.0,
                      Odd (Decimal 2.0),
                      CashUsed 0.0,
                      BonusUsed 5.0
                    ]
                  splitUnmatched = [
                      CashUsed 5.0,
                      BonusUsed 5.0
                    ]
              ]
            afterActionExpectations = [
                AccountBonusBalance 10.0,
                AccountMainBalance 200.0
              ]
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 5.0
                  odd = Decimal 2.0
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 5.0,
                      CashUsed 5.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 5.0,
                      Odd (Decimal 2.0),
                      Liability 5.0,
                      CashUsed 5.0,
                      BonusUsed 0.0,
                      CashReturned 0.0,
                      BonusReturned 0.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = [
                AccountBonusBalance 0.0,
                AccountMainBalance 200.0
              ]
        action = CancelAction with
          status = [BetModel.Unmatched, BetModel.Matched]
          action = Automatic IntegrationEvents.Postponed
  checkEntry entry

run_42 = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 10.0
                  odd = Decimal 2.0
                  side = Back
                expectations = BetExpectation with
                  unmatched = [
                      Profit 10.0,
                      CashUsed 10.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 5.0,
                      Odd (Decimal 2.0),
                      CashUsed 5.0,
                      BonusUsed 0.0
                    ]
                  splitUnmatched = [
                      CashUsed 5.0,
                      BonusUsed 0.0
                    ]
              ]
            afterActionExpectations = [
                AccountBonusBalance 0.0,
                AccountMainBalance 200.0
              ]
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 5.0
                  odd = Decimal 2.0
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 5.0,
                      CashUsed 5.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 5.0,
                      Odd (Decimal 2.0),
                      Liability 5.0,
                      CashUsed 5.0,
                      BonusUsed 0.0,
                      CashReturned 0.0,
                      BonusReturned 0.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = [
                AccountBonusBalance 0.0,
                AccountMainBalance 200.0
              ]
        action = CancelAction with
          status = [BetModel.Unmatched, BetModel.Matched]
          action = Automatic IntegrationEvents.Postponed
  checkEntry entry
