module Tests.GoldenCancellations.MinStake.LayFirst.ForthBucket where

import Daml.Script

import Tests.GoldenCancellations.GoldenUtils

import Scripts.Bootstrap.Onboarding.Parties       (Parties(..))
import Gambyl.Gambling.Bet.Odds.Model             (OddType(..))

import Gambyl.Gambling.Bet.Model qualified as BetModel
import EnetPulseIntegration.Events qualified as IntegrationEvents

run_37 = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = Some 15.0
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 10.0
                  odd = Decimal 1.01
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 0.10,
                      CashUsed 0.0,
                      BonusUsed 0.10
                    ]
                  matched = [
                      Stake 5.0,
                        Odd (Decimal 1.01),
                        Liability 0.05,
                        CashUsed 0.0,
                        BonusUsed 0.05,
                        CashReturned 0.0,
                        BonusReturned 0.0
                    ]
                  splitUnmatched = [
                      CashUsed 0.0,
                      BonusUsed 0.05
                    ]
              ]
            afterActionExpectations = [
                AccountBonusBalance 14.95,
                AccountMainBalance 200.0
              ]
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 5.0
                  odd = Decimal 1.01
                  side = Back
                expectations = BetExpectation with
                  unmatched = [
                      Profit 0.05,
                      CashUsed 5.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 5.0,
                        Odd (Decimal 1.01),
                        CashUsed 5.0,
                        BonusUsed 0.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = []
        action = CancelAction with
          status = [BetModel.Unmatched]
          action = Automatic IntegrationEvents.InProgress
  checkEntry entry

run_38 = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = Some 10.0
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 10.0
                  odd = Decimal 1.01
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 0.10,
                      CashUsed 0.0,
                      BonusUsed 0.10
                    ]
                  matched = [
                      Stake 5.0,
                        Odd (Decimal 1.01),
                        Liability 0.05,
                        CashUsed 0.0,
                        BonusUsed 0.05,
                        CashReturned 0.0,
                        BonusReturned 0.0
                    ]
                  splitUnmatched = [
                      CashUsed 0.0,
                      BonusUsed 0.05
                    ]
              ]
            afterActionExpectations = [
                AccountBonusBalance 9.95,
                AccountMainBalance 200.0
              ]
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 5.0
                  odd = Decimal 1.01
                  side = Back
                expectations = BetExpectation with
                  unmatched = [
                      Profit 0.05,
                      CashUsed 5.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 5.0,
                        Odd (Decimal 1.01),
                        CashUsed 5.0,
                        BonusUsed 0.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = []
        action = CancelAction with
          status = [BetModel.Unmatched]
          action = Automatic IntegrationEvents.InProgress
  checkEntry entry

run_39 = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 10.0
                  odd = Decimal 1.01
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 0.10,
                      CashUsed 0.10,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 5.0,
                        Odd (Decimal 1.01),
                        Liability 0.05,
                        CashUsed 0.05,
                        BonusUsed 0.0,
                        CashReturned 0.0,
                        BonusReturned 0.0
                    ]
                  splitUnmatched = [
                      CashUsed 0.05,
                      BonusUsed 0.0
                    ]
              ]
            afterActionExpectations = [
                AccountBonusBalance 0.0,
                AccountMainBalance 199.95
              ]
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 5.0
                  odd = Decimal 1.01
                  side = Back
                expectations = BetExpectation with
                  unmatched = [
                      Profit 0.05,
                      CashUsed 5.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 5.0,
                        Odd (Decimal 1.01),
                        CashUsed 5.0,
                        BonusUsed 0.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = []
        action = CancelAction with
          status = [BetModel.Unmatched]
          action = Automatic IntegrationEvents.InProgress
  checkEntry entry