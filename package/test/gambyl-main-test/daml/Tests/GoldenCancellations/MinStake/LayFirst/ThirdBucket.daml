module Tests.GoldenCancellations.MinStake.LayFirst.ThirdBucket where

import Daml.Script ()

import Tests.GoldenCancellations.GoldenUtils ()

import Scripts.Bootstrap.Onboarding.Parties       () -- (Parties(..))
import Gambyl.Gambling.Bet.Odds.Model             () -- (OddType(..))
import Gambyl.Gambling.Bet.Model qualified as BetModel ()
import EnetPulseIntegration.Events qualified as IntegrationEvents ()

-- FIXME
-- run_31 = script do
--   let entry = Entry with
--         party1Entry = \Parties{alice} -> PartyEntry with
--             party = alice
--             depositInputs = DepositInputs with
--                 deposit = 200.0
--                 bonusAmount = Some 2.0
--             bets = [
--               BetEntry with
--                 inputs = BetInputs with
--                   stake = 1.5
--                   odd = Decimal 1.01
--                   side = Lay
--                 expectations = BetExpectation with
--                   unmatched = [
--                       Liability 0.02,
--                       CashUsed 0.0,
--                       BonusUsed 0.02
--                     ]
--                   matched = [
--                       Stake 1.0,
--                         Odd (Decimal 1.01),
--                         Liability 0.01,
--                         CashUsed 0.0,
--                         BonusUsed 0.01,
--                         CashReturned 0.0,
--                         BonusReturned 0.0
--                     ]
--                   splitUnmatched = [
--                       CashUsed 0.0,
--                       BonusUsed 0.01
--                     ]
--               ]
--             afterActionExpectations = [
--                 AccountBonusBalance 1.99,
--                 AccountMainBalance 200.0
--               ]
--         party2Entry = \Parties{bob} -> PartyEntry with
--             party = bob
--             depositInputs = DepositInputs with
--                 deposit = 200.0
--                 bonusAmount = None
--             bets = [
--               BetEntry with
--                 inputs = BetInputs with
--                   stake = 1.0
--                   odd = Decimal 1.01
--                   side = Back
--                 expectations = BetExpectation with
--                   unmatched = [
--                       Profit 0.01,
--                       CashUsed 1.0,
--                       BonusUsed 0.0
--                     ]
--                   matched = [
--                       Stake 1.0,
--                         Odd (Decimal 1.01),
--                         CashUsed 1.0,
--                         BonusUsed 0.0
--                     ]
--                   splitUnmatched = []
--               ]
--             afterActionExpectations = []
--         action = CancelAction with
--           status = [BetModel.Unmatched]
--           action = Automatic IntegrationEvents.InProgress
--   checkEntry entry

-- FIXME
-- run_32 = script do
--   let entry = Entry with
--         party1Entry = \Parties{alice} -> PartyEntry with
--             party = alice
--             depositInputs = DepositInputs with
--                 deposit = 200.0
--                 bonusAmount = Some 1.25
--             bets = [
--               BetEntry with
--                 inputs = BetInputs with
--                   stake = 1.5
--                   odd = Decimal 1.01
--                   side = Lay
--                 expectations = BetExpectation with
--                   unmatched = [
--                       Liability 0.02,
--                       CashUsed 0.0,
--                       BonusUsed 0.02
--                     ]
--                   matched = [
--                       Stake 1.0,
--                         Odd (Decimal 1.01),
--                         Liability 0.01,
--                         CashUsed 0.0,
--                         BonusUsed 0.01,
--                         CashReturned 0.0,
--                         BonusReturned 0.0
--                     ]
--                   splitUnmatched = [
--                       CashUsed 0.0,
--                       BonusUsed 0.01
--                     ]
--               ]
--             afterActionExpectations = [
--                 AccountBonusBalance 1.24,
--                 AccountMainBalance 200.0
--               ]
--         party2Entry = \Parties{bob} -> PartyEntry with
--             party = bob
--             depositInputs = DepositInputs with
--                 deposit = 200.0
--                 bonusAmount = None
--             bets = [
--               BetEntry with
--                 inputs = BetInputs with
--                   stake = 1.0
--                   odd = Decimal 1.01
--                   side = Back
--                 expectations = BetExpectation with
--                   unmatched = [
--                       Profit 0.01,
--                       CashUsed 1.0,
--                       BonusUsed 0.0
--                     ]
--                   matched = [
--                       Stake 1.0,
--                         Odd (Decimal 1.01),
--                         CashUsed 1.0,
--                         BonusUsed 0.0
--                     ]
--                   splitUnmatched = []
--               ]
--             afterActionExpectations = []
--         action = CancelAction with
--           status = [BetModel.Unmatched]
--           action = Automatic IntegrationEvents.InProgress
--   checkEntry entry

-- FIXME
-- run_33 = script do
--   let entry = Entry with
--         party1Entry = \Parties{alice} -> PartyEntry with
--             party = alice
--             depositInputs = DepositInputs with
--                 deposit = 200.0
--                 bonusAmount = None
--             bets = [
--               BetEntry with
--                 inputs = BetInputs with
--                   stake = 1.5
--                   odd = Decimal 1.01
--                   side = Lay
--                 expectations = BetExpectation with
--                   unmatched = [
--                       Liability 0.02,
--                       CashUsed 0.02,
--                       BonusUsed 0.0
--                     ]
--                   matched = [
--                       Stake 1.0,
--                         Odd (Decimal 1.01),
--                         Liability 0.01,
--                         CashUsed 0.01,
--                         BonusUsed 0.0,
--                         CashReturned 0.0,
--                         BonusReturned 0.0
--                     ]
--                   splitUnmatched = [
--                       CashUsed 0.01,
--                       BonusUsed 0.0
--                     ]
--               ]
--             afterActionExpectations = [
--                 AccountBonusBalance 0.0,
--                 AccountMainBalance 199.99
--               ]
--         party2Entry = \Parties{bob} -> PartyEntry with
--             party = bob
--             depositInputs = DepositInputs with
--                 deposit = 200.0
--                 bonusAmount = None
--             bets = [
--               BetEntry with
--                 inputs = BetInputs with
--                   stake = 1.0
--                   odd = Decimal 1.01
--                   side = Back
--                 expectations = BetExpectation with
--                   unmatched = [
--                       Profit 0.01,
--                       CashUsed 1.0,
--                       BonusUsed 0.0
--                     ]
--                   matched = [
--                       Stake 1.0,
--                         Odd (Decimal 1.01),
--                         CashUsed 1.0,
--                         BonusUsed 0.0
--                     ]
--                   splitUnmatched = []
--               ]
--             afterActionExpectations = []
--         action = CancelAction with
--           status = [BetModel.Unmatched]
--           action = Automatic IntegrationEvents.InProgress
--   checkEntry entry

-- FIXME
-- run_34 = script do
--   let entry = Entry with
--         party1Entry = \Parties{alice} -> PartyEntry with
--             party = alice
--             depositInputs = DepositInputs with
--                 deposit = 200.0
--                 bonusAmount = Some 2.0
--             bets = [
--               BetEntry with
--                 inputs = BetInputs with
--                   stake = 1.5
--                   odd = Decimal 1.01
--                   side = Lay
--                 expectations = BetExpectation with
--                   unmatched = [
--                       Liability 0.02,
--                       CashUsed 0.0,
--                       BonusUsed 0.02
--                     ]
--                   matched = [
--                       Stake 1.0,
--                         Odd (Decimal 1.01),
--                         Liability 0.01,
--                         CashUsed 0.0,
--                         BonusUsed 0.01,
--                         CashReturned 0.0,
--                         BonusReturned 0.0
--                     ]
--                   splitUnmatched = [
--                       CashUsed 0.0,
--                       BonusUsed 0.01
--                     ]
--               ]
--             afterActionExpectations = [
--                 AccountBonusBalance 1.99,
--                 AccountMainBalance 200.0
--               ]
--         party2Entry = \Parties{bob} -> PartyEntry with
--             party = bob
--             depositInputs = DepositInputs with
--                 deposit = 200.0
--                 bonusAmount = None
--             bets = [
--               BetEntry with
--                 inputs = BetInputs with
--                   stake = 1.0
--                   odd = Decimal 1.01
--                   side = Back
--                 expectations = BetExpectation with
--                   unmatched = [
--                       Profit 0.01,
--                       CashUsed 1.0,
--                       BonusUsed 0.0
--                     ]
--                   matched = [
--                       Stake 1.0,
--                         Odd (Decimal 1.01),
--                         CashUsed 1.0,
--                         BonusUsed 0.0
--                     ]
--                   splitUnmatched = []
--               ]
--             afterActionExpectations = []
--         action = CancelAction with
--           status = [BetModel.Unmatched]
--           action = Manual
--   checkEntry entry

-- FIXME
-- run_35 = script do
--   let entry = Entry with
--         party1Entry = \Parties{alice} -> PartyEntry with
--             party = alice
--             depositInputs = DepositInputs with
--                 deposit = 200.0
--                 bonusAmount = Some 1.25
--             bets = [
--               BetEntry with
--                 inputs = BetInputs with
--                   stake = 1.5
--                   odd = Decimal 1.01
--                   side = Lay
--                 expectations = BetExpectation with
--                   unmatched = [
--                       Liability 0.02,
--                       CashUsed 0.0,
--                       BonusUsed 0.02
--                     ]
--                   matched = [
--                       Stake 1.0,
--                         Odd (Decimal 1.01),
--                         Liability 0.01,
--                         CashUsed 0.0,
--                         BonusUsed 0.01,
--                         CashReturned 0.0,
--                         BonusReturned 0.0
--                     ]
--                   splitUnmatched = [
--                       CashUsed 0.0,
--                       BonusUsed 0.01
--                     ]
--               ]
--             afterActionExpectations = [
--                 AccountBonusBalance 1.24,
--                 AccountMainBalance 200.0
--               ]
--         party2Entry = \Parties{bob} -> PartyEntry with
--             party = bob
--             depositInputs = DepositInputs with
--                 deposit = 200.0
--                 bonusAmount = None
--             bets = [
--               BetEntry with
--                 inputs = BetInputs with
--                   stake = 1.0
--                   odd = Decimal 1.01
--                   side = Back
--                 expectations = BetExpectation with
--                   unmatched = [
--                       Profit 0.01,
--                       CashUsed 1.0,
--                       BonusUsed 0.0
--                     ]
--                   matched = [
--                       Stake 1.0,
--                         Odd (Decimal 1.01),
--                         CashUsed 1.0,
--                         BonusUsed 0.0
--                     ]
--                   splitUnmatched = []
--               ]
--             afterActionExpectations = []
--         action = CancelAction with
--           status = [BetModel.Unmatched]
--           action = Manual
--   checkEntry entry

-- FIXME
-- run_36 = script do
--   let entry = Entry with
--         party1Entry = \Parties{alice} -> PartyEntry with
--             party = alice
--             depositInputs = DepositInputs with
--                 deposit = 200.0
--                 bonusAmount = None
--             bets = [
--               BetEntry with
--                 inputs = BetInputs with
--                   stake = 1.5
--                   odd = Decimal 1.01
--                   side = Lay
--                 expectations = BetExpectation with
--                   unmatched = [
--                       Liability 0.02,
--                       CashUsed 0.02,
--                       BonusUsed 0.0
--                     ]
--                   matched = [
--                       Stake 1.0,
--                         Odd (Decimal 1.01),
--                         Liability 0.01,
--                         CashUsed 0.01,
--                         BonusUsed 0.0,
--                         CashReturned 0.0,
--                         BonusReturned 0.0
--                     ]
--                   splitUnmatched = [
--                       CashUsed 0.01,
--                       BonusUsed 0.0
--                     ]
--               ]
--             afterActionExpectations = [
--                 AccountBonusBalance 0.0,
--                 AccountMainBalance 199.99
--               ]
--         party2Entry = \Parties{bob} -> PartyEntry with
--             party = bob
--             depositInputs = DepositInputs with
--                 deposit = 200.0
--                 bonusAmount = None
--             bets = [
--               BetEntry with
--                 inputs = BetInputs with
--                   stake = 1.0
--                   odd = Decimal 1.01
--                   side = Back
--                 expectations = BetExpectation with
--                   unmatched = [
--                       Profit 0.01,
--                       CashUsed 1.0,
--                       BonusUsed 0.0
--                     ]
--                   matched = [
--                       Stake 1.0,
--                         Odd (Decimal 1.01),
--                         CashUsed 1.0,
--                         BonusUsed 0.0
--                     ]
--                   splitUnmatched = []
--               ]
--             afterActionExpectations = []
--         action = CancelAction with
--           status = [BetModel.Unmatched]
--           action = Manual
--   checkEntry entry
