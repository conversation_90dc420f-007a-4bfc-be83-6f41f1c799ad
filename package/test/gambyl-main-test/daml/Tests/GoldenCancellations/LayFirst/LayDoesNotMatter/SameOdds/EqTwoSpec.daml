module Tests.GoldenCancellations.LayFirst.LayDoesNotMatter.SameOdds.EqTwoSpec where

import Daml.Script

import Tests.GoldenCancellations.GoldenUtils

import Scripts.Bootstrap.Onboarding.Parties       (Parties(..))
import Gambyl.Gambling.Bet.Odds.Model             (OddType(..))
import Gambyl.Gambling.Bet.Model qualified as BetModel
import EnetPulseIntegration.Events qualified as IntegrationEvents

run_19 = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = []
            afterActionExpectations = []
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = Some 15.0
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 10.0
                  odd = Decimal 2.0
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 10.0,
                      CashUsed 0.0,
                      BonusUsed 10.0
                    ]
                  matched = []
                  splitUnmatched = []
              ]
            afterActionExpectations = [
                AccountBonusBalance 15.0,
                AccountMainBalance 200.0
              ]
        action = CancelAction with
          status = [BetModel.Unmatched]
          action = Manual
  checkEntry entry

run_20 = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = []
            afterActionExpectations = []
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = Some 10.0
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 15.0
                  odd = Decimal 2.0
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 15.0,
                      CashUsed 5.0,
                      BonusUsed 10.0
                    ]
                  matched = []
                  splitUnmatched = []
              ]
            afterActionExpectations = [
                AccountBonusBalance 10.0,
                AccountMainBalance 200.0
              ]
        action = CancelAction with
          status = [BetModel.Unmatched]
          action = Manual
  checkEntry entry

run_21 = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = []
            afterActionExpectations = []
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 10.0
                  odd = Decimal 2.0
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 10.0,
                      CashUsed 10.0,
                      BonusUsed 0.0
                    ]
                  matched = []
                  splitUnmatched = []
              ]
            afterActionExpectations = [
                AccountBonusBalance 0.0,
                AccountMainBalance 200.0
              ]
        action = CancelAction with
          status = [BetModel.Unmatched]
          action = Manual
  checkEntry entry

run_22 = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = []
            afterActionExpectations = []
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = Some 15.0
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 10.0
                  odd = Decimal 2.0
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 10.0,
                      CashUsed 0.0,
                      BonusUsed 10.0
                    ]
                  matched = []
                  splitUnmatched = []
              ]
            afterActionExpectations = [
                AccountBonusBalance 15.0,
                AccountMainBalance 200.0
              ]
        action = CancelAction with
          status = [BetModel.Unmatched]
          action = Automatic IntegrationEvents.InProgress
  checkEntry entry

run_23 = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = []
            afterActionExpectations = []
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = Some 10.0
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 15.0
                  odd = Decimal 2.0
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 15.0,
                      CashUsed 5.0,
                      BonusUsed 10.0
                    ]
                  matched = []
                  splitUnmatched = []
              ]
            afterActionExpectations = [
                AccountBonusBalance 10.0,
                AccountMainBalance 200.0
              ]
        action = CancelAction with
          status = [BetModel.Unmatched]
          action = Automatic IntegrationEvents.InProgress
  checkEntry entry

run_24 = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = []
            afterActionExpectations = []
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 10.0
                  odd = Decimal 2.0
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 10.0,
                      CashUsed 10.0,
                      BonusUsed 0.0
                    ]
                  matched = []
                  splitUnmatched = []
              ]
            afterActionExpectations = [
                AccountBonusBalance 0.0,
                AccountMainBalance 200.0
              ]
        action = CancelAction with
          status = [BetModel.Unmatched]
          action = Automatic IntegrationEvents.InProgress
  checkEntry entry
