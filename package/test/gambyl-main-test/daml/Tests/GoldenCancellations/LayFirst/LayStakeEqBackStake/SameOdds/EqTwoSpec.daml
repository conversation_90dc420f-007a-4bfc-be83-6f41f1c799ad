module Tests.GoldenCancellations.LayFirst.LayStakeEqBackStake.SameOdds.EqTwoSpec where

import Daml.Script

import Tests.GoldenCancellations.GoldenUtils

import Scripts.Bootstrap.Onboarding.Parties       (Parties(..))
import Gambyl.Gambling.Bet.Odds.Model             (OddType(..))
import Gambyl.Gambling.Bet.Model qualified as BetModel
import EnetPulseIntegration.Events qualified as IntegrationEvents

run_31 = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 10.0
                  odd = Decimal 2.0
                  side = Back
                expectations = BetExpectation with
                  unmatched = [
                      Profit 10.0,
                      CashUsed 10.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 10.0,
                      Odd (Decimal 2.0),
                      CashUsed 10.0,
                      BonusUsed 0.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = [
                AccountBonusBalance 0.0,
                AccountMainBalance 200.0
              ]
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = Some 15.0
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 10.0
                  odd = Decimal 2.0
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 10.0,
                      CashUsed 0.0,
                      BonusUsed 10.0
                    ]
                  matched = [
                      Stake 10.0,
                      Odd (Decimal 2.0),
                      Liability 10.0,
                      CashUsed 0.0,
                      BonusUsed 10.0,
                      CashReturned 0.0,
                      BonusReturned 0.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = [
                AccountBonusBalance 15.0,
                AccountMainBalance 200.0
              ]
        action = CancelAction with
          status = [BetModel.Matched]
          action = Automatic IntegrationEvents.Cancelled
  checkEntry entry

run_32 = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 15.0
                  odd = Decimal 2.0
                  side = Back
                expectations = BetExpectation with
                  unmatched = [
                      Profit 15.0,
                      CashUsed 15.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 15.0,
                      Odd (Decimal 2.0),
                      CashUsed 15.0,
                      BonusUsed 0.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = [
                AccountBonusBalance 0.0,
                AccountMainBalance 200.0
              ]
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = Some 10.0
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 15.0
                  odd = Decimal 2.0
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 15.0,
                      CashUsed 5.0,
                      BonusUsed 10.0
                    ]
                  matched = [
                      Stake 15.0,
                      Odd (Decimal 2.0),
                      Liability 15.0,
                      CashUsed 5.0,
                      BonusUsed 10.0,
                      CashReturned 0.0,
                      BonusReturned 0.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = [
                AccountBonusBalance 10.0,
                AccountMainBalance 200.0
              ]
        action = CancelAction with
          status = [BetModel.Matched]
          action = Automatic IntegrationEvents.Cancelled
  checkEntry entry

run_33 = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 10.0
                  odd = Decimal 2.0
                  side = Back
                expectations = BetExpectation with
                  unmatched = [
                      Profit 10.0,
                      CashUsed 10.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 10.0,
                      Odd (Decimal 2.0),
                      CashUsed 10.0,
                      BonusUsed 0.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = [
                AccountBonusBalance 0.0,
                AccountMainBalance 200.0
              ]
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 10.0
                  odd = Decimal 2.0
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 10.0,
                      CashUsed 10.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 10.0,
                      Odd (Decimal 2.0),
                      Liability 10.0,
                      CashUsed 10.0,
                      BonusUsed 0.0,
                      CashReturned 0.0,
                      BonusReturned 0.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = [
                AccountBonusBalance 0.0,
                AccountMainBalance 200.0
              ]
        action = CancelAction with
          status = [BetModel.Matched]
          action = Automatic IntegrationEvents.Cancelled
  checkEntry entry

run_34 = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 10.0
                  odd = Decimal 2.0
                  side = Back
                expectations = BetExpectation with
                  unmatched = [
                      Profit 10.0,
                      CashUsed 10.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 10.0,
                      Odd (Decimal 2.0),
                      CashUsed 10.0,
                      BonusUsed 0.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = [
                AccountBonusBalance 0.0,
                AccountMainBalance 200.0
              ]
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = Some 15.0
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 10.0
                  odd = Decimal 2.0
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 10.0,
                      CashUsed 0.0,
                      BonusUsed 10.0
                    ]
                  matched = [
                      Stake 10.0,
                      Odd (Decimal 2.0),
                      Liability 10.0,
                      CashUsed 0.0,
                      BonusUsed 10.0,
                      CashReturned 0.0,
                      BonusReturned 0.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = [
                AccountBonusBalance 15.0,
                AccountMainBalance 200.0
              ]
        action = CancelAction with
          status = [BetModel.Matched]
          action = Automatic IntegrationEvents.Postponed
  checkEntry entry

run_35 = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 15.0
                  odd = Decimal 2.0
                  side = Back
                expectations = BetExpectation with
                  unmatched = [
                      Profit 15.0,
                      CashUsed 15.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 15.0,
                      Odd (Decimal 2.0),
                      CashUsed 15.0,
                      BonusUsed 0.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = [
                AccountBonusBalance 0.0,
                AccountMainBalance 200.0
              ]
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = Some 10.0
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 15.0
                  odd = Decimal 2.0
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 15.0,
                      CashUsed 5.0,
                      BonusUsed 10.0
                    ]
                  matched = [
                      Stake 15.0,
                      Odd (Decimal 2.0),
                      Liability 15.0,
                      CashUsed 5.0,
                      BonusUsed 10.0,
                      CashReturned 0.0,
                      BonusReturned 0.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = [
                AccountBonusBalance 10.0,
                AccountMainBalance 200.0
              ]
        action = CancelAction with
          status = [BetModel.Matched]
          action = Automatic IntegrationEvents.Postponed
  checkEntry entry

run_36 = script do
  let entry = Entry with
        party1Entry = \Parties{alice} -> PartyEntry with
            party = alice
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 10.0
                  odd = Decimal 2.0
                  side = Back
                expectations = BetExpectation with
                  unmatched = [
                      Profit 10.0,
                      CashUsed 10.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 10.0,
                      Odd (Decimal 2.0),
                      CashUsed 10.0,
                      BonusUsed 0.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = [
                AccountBonusBalance 0.0,
                AccountMainBalance 200.0
              ]
        party2Entry = \Parties{bob} -> PartyEntry with
            party = bob
            depositInputs = DepositInputs with
                deposit = 200.0
                bonusAmount = None
            bets = [
              BetEntry with
                inputs = BetInputs with
                  stake = 10.0
                  odd = Decimal 2.0
                  side = Lay
                expectations = BetExpectation with
                  unmatched = [
                      Liability 10.0,
                      CashUsed 10.0,
                      BonusUsed 0.0
                    ]
                  matched = [
                      Stake 10.0,
                      Odd (Decimal 2.0),
                      Liability 10.0,
                      CashUsed 10.0,
                      BonusUsed 0.0,
                      CashReturned 0.0,
                      BonusReturned 0.0
                    ]
                  splitUnmatched = []
              ]
            afterActionExpectations = [
                AccountBonusBalance 0.0,
                AccountMainBalance 200.0
              ]
        action = CancelAction with
          status = [BetModel.Matched]
          action = Automatic IntegrationEvents.Postponed
  checkEntry entry
