module Tests.Mock where

import DA.Map qualified as Map

import Gambyl.Gambling.Role qualified as GamblingRole


mockChangeGlobalGamblingConfiguration =
  GamblingRole.ChangeGlobalGamblingConfiguration with
    newFlaggedAmount              = None
    newMinDepositAmount           = None
    newUnverifiedAccountMaxAmount  = None
    newBetFee                     = None
    newDepositFee                 = None
    newWithdrawFee                = None
    newArchiveEventDays           = None
    newLegalAge                   = None
    newMinWithdrawAmount          = None
    newIntegrationParties         = Map.empty
    newIsOnMaintenance            = None
    newMinOdd                     = None
    newDaysPostponedEventExpires  = None
    newMinutesMarketMapExpires    = None
    newAllowedPeriod              = None
    newDefaultOdds                = None