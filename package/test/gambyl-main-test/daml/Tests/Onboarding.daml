module Tests.Onboarding where

import Daml.Script

import DA.Map qualified as Map
import DA.Set qualified as Set
import DA.Stack qualified as Stack (HasCallStack)
import DA.Time qualified as Time

import Gambyl.Gambling.Role qualified as GamblingRole
import Gambyl.Operator.Role qualified as GambylOperator

import Scripts.Bootstrap.Onboarding.Parties (Parties(..))
import Scripts.Bootstrap.Onboarding.Parties qualified as Parties hiding (Parties(..))


-- As a side effect after this function 6588 days will have passed
testGambylOperatorRole : Stack.HasCallStack => Script Parties
testGambylOperatorRole = script do

    passTime $ Time.days (366 * 18) -- Pass roughly 18 years for identity verification purposes

    parties@Parties{operator, public} <- Parties.allocateParties

    gambylOperatorRoleCid <- submit operator do
      createCmd GambylOperator.Role with
        operator
        public = Set.fromList [public]
        optMibOperator = None

    submit operator do
      exerciseCmd gambylOperatorRoleCid GambylOperator.MarketplaceOperator

    return parties

{- --------------------------------------GAMBLING ROLE TESTS-------------------------------------- -}

testGamblingRole : Stack.HasCallStack => Script Parties
testGamblingRole = script do

  parties@Parties{operator, gambyl, jumio, moneyMatrix, quickBooks} <- testGambylOperatorRole

  gamblingRoleOfferCid <- submit operator do
    exerciseByKeyCmd @GambylOperator.Role operator
      GambylOperator.OfferGamblingRole with provider = gambyl

  submit gambyl do
    exerciseCmd gamblingRoleOfferCid GamblingRole.Accept with
      integrationParties = Map.fromList [("jumio", jumio), ("moneyMatrix", moneyMatrix), ("quickbooks", quickBooks)]

  return parties
