module Tests.Event where

import Daml.Script

import DA.Action    ((>=>))
import DA.Date qualified as Date
import DA.Either qualified as Either
import DA.List qualified as List
import DA.List      (head, tail)
import DA.Map qualified as Map
import DA.Optional qualified as Optional
import DA.Set qualified as Set
import DA.<PERSON>ack qualified as Stack (HasCallStack)
import DA.Text qualified as Text
import DA.Time qualified as Time
import DA.Time      (days)

import EnetPulseIntegration.Events qualified as EnetPulseEvents

import Gambyl.Gambling.Bet.Odds.Model qualified as Odds
import Gambyl.Gambling.Event.Model qualified as EventModel
import Gambyl.Gambling.Event.Service qualified as EventService
import Gambyl.Gambling.Listing.Model qualified as Listing
import Gambyl.Gambling.Role qualified as GamblingRole
import Gambyl.Gambling.Service qualified as GamblingService
import Gambyl.Marketing.Service qualified as MarketingService
import Gambyl.Utils qualified as GambylUtils

import Tests.Events qualified as TestEvents
import Tests.Onboarding qualified as TestOnboarding
import Tests.Marketing qualified as TestMarketing
import Tests.Mock qualified as TestMock
import Tests.Utils qualified as TestUtils
import Tests.Utils      (OutcomeListWithOddType(..))

import Scripts.Bootstrap.Onboarding.Parties (Parties(..))
import Scripts.Common qualified as Common (msgWithFunc)

{- ------------------------------------EVENTS SERVICE TESTS------------------------------------- -}

testRequestEventService : Parties -> Script Parties
testRequestEventService parties@Parties{operator, gambyl, eventManager} = script do

    eventRequestCid <- submit eventManager do
       createCmd EventService.Request
          with
             customer = eventManager
             provider = gambyl

    submit gambyl do
       exerciseByKeyCmd @GamblingRole.Role (operator, gambyl) GamblingRole.ApproveEventServiceRequest
          with
             eventRequestCid

    eventServiceOpt <- queryContractKey @EventService.Service eventManager (operator, gambyl, eventManager)
    assertMsg (Common.msgWithFunc  "Event Service not created") $ Optional.isSome eventServiceOpt

    return parties

run_testRequestEventService =
    TestOnboarding.testGamblingRole >>=
    testRequestEventService

testRequestEventServiceForExistingService : Parties -> Script Parties
testRequestEventServiceForExistingService parties@Parties{operator, gambyl, eventManager} = script do

    eventRequestCid <- submit eventManager do
       createCmd EventService.Request
          with
             customer = eventManager
             provider = gambyl

    submit gambyl do
       exerciseByKeyCmd @GamblingRole.Role (operator, gambyl) GamblingRole.ProcessEventServiceRequest
          with
             eventRequestCid

    eventServiceOpt <- queryContractKey @EventService.Service eventManager (operator, gambyl, eventManager)
    assertMsg (Common.msgWithFunc  "Event Service not created") $ Optional.isSome eventServiceOpt

    eventRequestCid <- submit eventManager do
       createCmd EventService.Request
          with
             customer = eventManager
             provider = gambyl

    submit gambyl do
       exerciseByKeyCmd @GamblingRole.Role (operator, gambyl) GamblingRole.ProcessEventServiceRequest
          with
             eventRequestCid

    return parties

run_testRequestEventServiceForExistingService =
    TestOnboarding.testGamblingRole >>=
    testRequestEventServiceForExistingService


testMultipleRequestEventService : Parties -> Script Parties
testMultipleRequestEventService parties@Parties{gambyl, eventManager} = script do

    submit eventManager do
       createCmd EventService.Request
          with
             customer = eventManager
             provider = gambyl

    submitMustFail eventManager do
       createCmd EventService.Request
          with
             customer = eventManager
             provider = gambyl

    return parties

run_testMultipleRequestEventService =
    TestOnboarding.testGamblingRole >>=
    testMultipleRequestEventService


testOfferEventService : Stack.HasCallStack => Parties -> Script Parties
testOfferEventService parties@Parties{operator, gambyl, eventManager} = script do

    eventServiceOfferCid <- submit gambyl do
        exerciseByKeyCmd @GamblingRole.Role (operator, gambyl) GamblingRole.OfferEventService with customer = eventManager

    eventServiceCid <- submit eventManager do
        exerciseCmd eventServiceOfferCid EventService.Accept

    eventServiceOpt <- queryContractId eventManager eventServiceCid
    assertMsg (Common.msgWithFunc  "Event Service not created") $ Optional.isSome eventServiceOpt

    return parties

run_testOfferEventService =
    TestOnboarding.testGamblingRole >>=
    testOfferEventService


testMultipleOfferEventService : Stack.HasCallStack => Parties -> Script Parties
testMultipleOfferEventService parties@Parties{operator, gambyl, eventManager} = script do

    submit gambyl do
        exerciseByKeyCmd @GamblingRole.Role (operator, gambyl) GamblingRole.OfferEventService with customer = eventManager

    submitMustFail gambyl do
        exerciseByKeyCmd @GamblingRole.Role (operator, gambyl) GamblingRole.OfferEventService with customer = eventManager

    return parties

run_testMultipleOfferEventService =
    TestOnboarding.testGamblingRole >>=
    testMultipleOfferEventService


testCustomerCancelAndReinstateEvent : Stack.HasCallStack => (Parties, Text) -> Script (Parties,Text)
testCustomerCancelAndReinstateEvent (parties@Parties{operator, gambyl, eventManager}, eventLabel) = script do
    -- Cancel Event
    event <-
      queryContractKey @EventModel.EventInstrument eventManager (operator, gambyl, eventLabel) >>=
      (return . snd . Optional.fromSomeNote (Common.msgWithFunc "Event Instrument doesn't exist"))

    requestCid <- submit eventManager do
       exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
             EventService.RequestCancelEvent with eventLabel = event.eventId.label

    eitherEvent <- submit gambyl do
        exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
             EventService.ApproveCancelEventRequest with requestCid

    eventOpt <- queryContractKey @EventModel.EventInstrument gambyl (key event)
    assertMsg (Common.msgWithFunc "Event not canceled") $ Either.isRight eitherEvent && (Optional.fromSome eventOpt)._2.details.eventStatus == EnetPulseEvents.Cancelled

    -- Reinstate event back to Not Started
    event <-
      queryContractKey @EventModel.EventInstrument eventManager (operator, gambyl, eventLabel) >>=
      (return . snd . Optional.fromSomeNote (Common.msgWithFunc "Event Instrument doesn't exist"))

    requestCid <- submit eventManager do
       exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
             EventService.RequestReinstateEvent with eventLabel = event.eventId.label

    eitherEvent <- submit gambyl do
        exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
             EventService.ApproveReinstateEventRequest with requestCid

    eventOpt <- queryContractKey @EventModel.EventInstrument gambyl (key event)
    assertMsg (Common.msgWithFunc "Event not reinstated") $ Either.isRight eitherEvent && (Optional.fromSome eventOpt)._2.details.eventStatus == EnetPulseEvents.NotStarted

    return (parties,event.eventId.label)

run_testCustomerCancelAndReinstateEvent =
    TestOnboarding.testGamblingRole >>=
    TestMarketing.testOfferMarketingService >>=
    testOfferEventService >>=
    testEventOrigination >>=
    testCustomerCancelAndReinstateEvent


testIntegrationCancelAndReinstateEvent : Stack.HasCallStack => (Parties, Text) -> Script (Parties,Text)
testIntegrationCancelAndReinstateEvent (parties@Parties{operator, gambyl, eventManager, public}, eventLabel) = script do

    now <- getTime
    let startDate = Time.addRelTime now (days 10)

    eventCid <- submit gambyl do createCmd $ TestEvents.getDefaultCreateEvent gambyl [public, gambyl] eventLabel startDate startDate "yes"

    let eventOrigin = EventModel.IntegrationEvent with ..

    Right requestCid <- submit gambyl do exerciseByKeyCmd @GamblingService.Service (operator, gambyl, gambyl) GamblingService.RequestEventOrigination with eventOrigin

    submit gambyl do exerciseByKeyCmd @GamblingService.Service (operator, gambyl, gambyl) GamblingService.ApproveEventOrigination with requestCid, managers = Set.empty

    -- Cancel Event
    event <-
      queryContractKey @EventModel.EventInstrument gambyl (operator, gambyl, eventLabel) >>=
      (return . snd . Optional.fromSomeNote (Common.msgWithFunc "Event Instrument doesn't exist"))

    requestCid <- submit eventManager do
       exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
             EventService.RequestCancelEvent with eventLabel = event.eventId.label

    eitherEvent <- submit gambyl do
        exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
             EventService.ApproveCancelEventRequest with requestCid

    eventOpt <- queryContractKey @EventModel.EventInstrument gambyl (key event)
    assertMsg (Common.msgWithFunc "Event not canceled") $ Either.isRight eitherEvent && (Optional.fromSome eventOpt)._2.details.eventStatus == EnetPulseEvents.Cancelled

    -- Reinstate event back to Not Started
    event <- queryContractKey @EventModel.EventInstrument gambyl (operator, gambyl, eventLabel) >>=
      (return . snd . Optional.fromSomeNote (Common.msgWithFunc "Event Instrument doesn't exist"))

    requestCid <- submit eventManager do
       exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
             EventService.RequestReinstateEvent with eventLabel = event.eventId.label

    eitherEvent <- submit gambyl do
        exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
             EventService.ApproveReinstateEventRequest with requestCid

    eventOpt <- queryContractKey @EventModel.EventInstrument gambyl (key event)
    assertMsg (Common.msgWithFunc "Event not reinstated") $ Either.isRight eitherEvent && (Optional.fromSome eventOpt)._2.details.eventStatus == EnetPulseEvents.NotStarted

    return (parties, event.eventId.label)

run_testIntegrationCancelAndReinstateEvent =
    TestOnboarding.testGamblingRole >>=
    TestMarketing.testOfferMarketingService >>=
    testOfferEventService >>=
    testIntegrationCancelAndReinstateEvent . (, "AvB")


testEventUpdateOdds : Stack.HasCallStack => ((Parties, Text),Text) -> Script (Parties)
testEventUpdateOdds ((parties@Parties{operator, gambyl, eventManager, marketingManager}, eventLabel), _) = script do

    (marketingServiceCid, _) <- queryContractKey @MarketingService.Service marketingManager (operator, gambyl, marketingManager) >>= (return . Optional.fromSomeNote (Common.msgWithFunc "Marketing Service doesn't exist"))
    (eventCid, event) <- queryContractKey @EventModel.EventInstrument eventManager (operator, gambyl, eventLabel) >>= (return . Optional.fromSomeNote (Common.msgWithFunc "Event Instrument doesn't exist"))

    let newOutcomes = map (\ EventModel.OutcomeOdds{outcome} -> EventModel.InputOutcomeOdd with outcome, odd = Odds.Decimal 1.23) event.details.outcomes

    requestCid <- submit eventManager do
       exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
             EventService.RequestUpdateEventOdds with eventCid, newOutcomes

    assertMsg (Common.msgWithFunc "Update odds request failed") $ Either.isRight requestCid
    let Right eventUpdateOddsCid = requestCid

    listings <- queryFilter @Listing.GamblingListing gambyl (\ listing -> listing.eventLabel == eventLabel)
    let hasBets = (List.length listings) /= 0

    eitherEventCid <- submit gambyl do
        exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
            EventService.ApproveUpdateEventOddsOutcomes with requestCid = eventUpdateOddsCid, marketingServiceCid, hasBets

    assertMsg (Common.msgWithFunc "Update odds request was not approved") $ Either.isRight eitherEventCid
    let Right eventCid = eitherEventCid

    details <- (.details) . Optional.fromSome <$> queryContractId eventManager eventCid
    assertMsg (Common.msgWithFunc "Update odds approve failed") $ GambylUtils.listEquals details.outcomes (EventModel.outcomeWithAllOdds newOutcomes)

    return (parties)

run_testEventUpdateOdds =
    TestOnboarding.testGamblingRole >>=
    TestMarketing.testOfferMarketingService >>=
    testOfferEventService >>=
    testEventOrigination >>=
    testEventUpdateOdds . (, "event")

run_testMarketingEventUpdateOdds =
    TestOnboarding.testGamblingRole >>=
    TestMarketing.testOfferMarketingService >>=
    testOfferEventService >>=
    TestMarketing.testEventOrigination -- >>=
    -- testEventUpdateOdds . (, "marketing")


testEventUpdateOddsFailHasBets : Stack.HasCallStack => ((Parties, Text),Text) -> Script (Parties)
testEventUpdateOddsFailHasBets ((parties@Parties{operator, gambyl, eventManager, marketingManager}, eventLabel), _) = script do

    (marketingServiceCid, _) <- queryContractKey @MarketingService.Service marketingManager (operator, gambyl, marketingManager) >>= (return . Optional.fromSomeNote (Common.msgWithFunc "Marketing Service doesn't exist"))
    (eventCid, event) <- queryContractKey @EventModel.EventInstrument eventManager (operator, gambyl, eventLabel) >>= (return . Optional.fromSomeNote (Common.msgWithFunc "Event Instrument doesn't exist"))

    let newOutcomes = map (\ EventModel.OutcomeOdds{outcome} -> EventModel.InputOutcomeOdd with outcome, odd = Odds.Decimal 1.23) event.details.outcomes

    requestCid <- submit eventManager do
       exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
             EventService.RequestUpdateEventOdds with eventCid, newOutcomes

    assertMsg (Common.msgWithFunc "Update odds request failed") $ Either.isRight requestCid
    let Right eventUpdateOddsCid = requestCid

    listings <- queryFilter @Listing.GamblingListing gambyl (\ listing -> listing.eventLabel == eventLabel)
    let hasBets = (List.length listings) /= 0

    eitherEventCid <- submit gambyl do
        exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
            EventService.ApproveUpdateEventOddsOutcomes with requestCid = eventUpdateOddsCid, marketingServiceCid, hasBets

    assertMsg (Common.msgWithFunc "Update odds request was approved when it shouldn't") $ Either.isLeft eitherEventCid

    return (parties)


testEventUpdateOutcomes : Stack.HasCallStack => ((Parties, Text), Text) -> Script (Parties)
testEventUpdateOutcomes ((parties@Parties{operator, gambyl, eventManager, marketingManager}, eventLabel), _) = script do

    (marketingServiceCid, _) <- queryContractKey @MarketingService.Service marketingManager (operator, gambyl, marketingManager) >>= (return . Optional.fromSomeNote (Common.msgWithFunc "Marketing Service doesn't exist"))
    (eventCid, event) <- queryContractKey @EventModel.EventInstrument gambyl (operator, gambyl, eventLabel) >>= (return . Optional.fromSomeNote (Common.msgWithFunc "Event Instrument doesn't exist"))

    let participant1 = head event.details.eventParticipants
        _ = head (tail event.details.eventParticipants)

    let newOutcomes = TestUtils.buildOutcomesListWithOddType [
                                                        OutcomeListWithOddType with participant = Some participant1, participantOrder = participant1.order, order = 5, type_ = EnetPulseEvents.Winner, subtype = EnetPulseEvents.Win, odd = 200.0
                                                    ]

    requestCid <- submit eventManager do
       exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
             EventService.RequestUpdateEventOutcomes with updateEventOutcomes = EventModel.FromEvent eventCid, newOutcomes

    listings <- queryFilter @Listing.GamblingListing gambyl (\ listing -> listing.eventLabel == eventLabel)
    let hasBets = (List.length listings) /= 0

    eitherEventCid <- submit gambyl do
        exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
             EventService.ApproveUpdateEventOddsOutcomes with marketingServiceCid, requestCid, hasBets

    assertMsg (Common.msgWithFunc "Update odds request was not approved") $ Either.isRight eitherEventCid
    let Right eventCid = eitherEventCid

    details <- (.details) . Optional.fromSome <$> queryContractId eventManager eventCid
    assertMsg (Common.msgWithFunc "Update event outcomes failed") $ details.outcomes == EventModel.outcomeWithAllOdds newOutcomes

    return (parties)

run_testEventUpdateOutcomes =
    TestOnboarding.testGamblingRole >>=
    TestMarketing.testOfferMarketingService >>=
    testOfferEventService >>=
    testEventOrigination >>=
    testEventUpdateOutcomes . (, "event")

run_testMarketingEventUpdateOutcomes =
    TestOnboarding.testGamblingRole >>=
    TestMarketing.testOfferMarketingService >>=
    testOfferEventService >>=
    TestMarketing.testEventOrigination -- >>=
    -- testEventUpdateOutcomes . (, "marketing")


testEventUpdateNoOutcomes : Stack.HasCallStack => ((Parties, Text), Text) -> Script Parties
testEventUpdateNoOutcomes ((parties@Parties{operator, gambyl, marketingManager, eventManager}, eventLabel), _) = script do

    (marketingServiceCid, _) <- queryContractKey @MarketingService.Service marketingManager (operator, gambyl, marketingManager) >>= (return . Optional.fromSomeNote (Common.msgWithFunc "Marketing Service doesn't exist"))
    (eventCid, _) <- queryContractKey @EventModel.EventInstrumentNoOutcomes gambyl (operator, gambyl, eventLabel) >>= (return . Optional.fromSomeNote (Common.msgWithFunc "Event Instrument doesn't exist"))

    let participant = EnetPulseEvents.Participant with name = "X", id = "X1", order = 1, co_op = None
    let newOutcomes = TestUtils.buildOutcomesListWithOddType [
                                                        OutcomeListWithOddType with participant= Some participant, participantOrder= participant.order, order= 5, type_= EnetPulseEvents.Winner, subtype= EnetPulseEvents.Win, odd= 200.0
                                                    ]

    requestCid <- submit eventManager do
       exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
             EventService.RequestUpdateEventOutcomes with updateEventOutcomes = EventModel.FromEventNoOutcomes eventCid, newOutcomes

    listings <- queryFilter @Listing.GamblingListing gambyl (\ listing -> listing.eventLabel == eventLabel)
    let hasBets = (List.length listings) /= 0

    eitherEventCid <- submit gambyl do
        exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
             EventService.ApproveUpdateEventOddsOutcomes with marketingServiceCid, requestCid, hasBets

    assertMsg (Common.msgWithFunc "Update odds request was not approved") $ Either.isRight eitherEventCid
    let Right eventCid = eitherEventCid

    details <- (.details) . Optional.fromSome <$> queryContractId eventManager eventCid
    debug details.outcomes
    assertMsg (Common.msgWithFunc "Update event outcomes failed") $ details.outcomes == EventModel.outcomeWithAllOdds newOutcomes

    return (parties)

run_testEventUpdateNoOutcomes =
    TestOnboarding.testGamblingRole >>=
    TestMarketing.testOfferMarketingService >>=
    testOfferEventService >>=
    testEventOriginationNoOutcomes >>=
    testEventUpdateNoOutcomes. (, "event")

testEventOrigination : Stack.HasCallStack => Parties -> Script (Parties, Text)
testEventOrigination parties@Parties{operator, gambyl, eventManager} = script do


    (eventServiceCid, _) <- TestUtils.queryContractWithKey @EventService.Service eventManager (operator, gambyl, eventManager) "Event Service doesn't exist"

    ----- Event Origination ----

    let
        eventLabel = "AvC"
        arsenal = TestUtils.getParticipant "Arsenal" 1
        chelsea = TestUtils.getParticipant "Chelsea" 2

    eventRequest <- submit eventManager do
        exerciseCmd eventServiceCid EventService.RequestEventOrigination  with
            eventOrigin = EventModel.CustomerEvent with
                            assetLabel  = eventLabel
                            description = "Match " <> eventLabel
                            market              = (EventModel.Sport "Soccer")
                            submarkets          = [EventModel.Tournament "Premier League"]
                            geography           = "England"
                            outcomes            = TestUtils.buildOutcomesListWithOddType [
                                                        (OutcomeListWithOddType with participant = Some arsenal, participantOrder = arsenal.order, order = 5, type_ = EnetPulseEvents.ThreeWay, subtype = EnetPulseEvents.Win, odd = 2.0),
                                                        (OutcomeListWithOddType with participant = None, participantOrder = 5, order = 0, type_ = EnetPulseEvents.ThreeWay, subtype = EnetPulseEvents.Draw, odd = 2.0),
                                                        (OutcomeListWithOddType with  participant= Some chelsea,  participantOrder= chelsea.order,  order= 5,  type_= EnetPulseEvents.ThreeWay,  subtype= EnetPulseEvents.Win, odd=  2.0)
                                                    ]
                            eventParticipants   = [arsenal, chelsea]
                            startDate           = (Time.time (Date.date 2021 Date.Nov 04) 0 0 0)
                            eventTitle          = Map.fromList [(show GambylUtils.EN_US, "Arsenal vs Chelsea")]
                            eventStatus         = EnetPulseEvents.NotStarted
                            eventResults        = []

    submit gambyl do
        exerciseCmd eventServiceCid EventService.ApproveEventOrigination
            with
                requestCid  = eventRequest
                managers    = Set.fromList [eventManager]

    return (parties, eventLabel)


testTwoWayEventOrigination : Stack.HasCallStack => (Parties, Text) -> Script (Parties, EventModel.EventInstrument)
testTwoWayEventOrigination  (parties@Parties{operator, gambyl, eventManager}, eventLabel) = script do
    debug $ "(operator, gambyl, eventManager): " <> "" <> show operator <> ", " <> show gambyl <> ", " <> show eventManager <> ")"

    ----- Event Origination ----

    let
        arsenal = TestUtils.getParticipant "Arsenal" 1
        chelsea = TestUtils.getParticipant "Chelsea" 2

    eventRequest <- submit eventManager do
        exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager) EventService.RequestEventOrigination  with
            eventOrigin = EventModel.CustomerEvent with
                            assetLabel  = eventLabel
                            description = "Match " <> eventLabel
                            market              = (EventModel.Sport "Soccer")
                            submarkets          = [EventModel.Tournament "Premier League"]
                            geography           = "England"
                            outcomes            = TestUtils.buildOutcomesListWithOddType [
                                                        (OutcomeListWithOddType with participant = Some arsenal, participantOrder = arsenal.order, order = 5, type_ = EnetPulseEvents.ThreeWay, subtype = EnetPulseEvents.Win, odd = 2.0),
                                                        (OutcomeListWithOddType with participant = None, participantOrder = 5, order = 0, type_ = EnetPulseEvents.ThreeWay, subtype = EnetPulseEvents.Draw, odd = 2.0),
                                                        (OutcomeListWithOddType with  participant= Some chelsea,  participantOrder= chelsea.order,  order= 5,  type_= EnetPulseEvents.ThreeWay,  subtype= EnetPulseEvents.Win, odd=  2.0)
                                                    ]
                            eventParticipants   = [arsenal, chelsea]
                            startDate           = (Time.time (Date.date 2021 Date.Nov 04) 0 0 0)
                            eventTitle          = Map.fromList [(show GambylUtils.EN_US, "Arsenal vs Chelsea")]
                            eventStatus         = EnetPulseEvents.NotStarted
                            eventResults        = []

    submit gambyl do
        exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager) EventService.ApproveEventOrigination
            with
                requestCid  = eventRequest
                managers    = Set.fromList [eventManager]

    eventQuery <- queryFilter @EventModel.EventInstrument gambyl (\event -> event.eventId.label == eventLabel)

    return (parties, snd (List.head eventQuery))


testEventOriginationRequestFail : Stack.HasCallStack => (Parties, Text) -> Script (Parties)
testEventOriginationRequestFail (parties@Parties{operator, gambyl, eventManager}, eventLabel) = script do


    (eventServiceCid, _) <- TestUtils.queryContractWithKey @EventService.Service eventManager (operator, gambyl, eventManager) "Event Service doesn't exist"

    ----- Event Origination ----

    let
        arsenal = TestUtils.getParticipant "Arsenal" 1
        chelsea = TestUtils.getParticipant "Chelsea" 2

    submitMustFail eventManager do
        exerciseCmd eventServiceCid EventService.RequestEventOrigination  with
            eventOrigin = EventModel.CustomerEvent with
                            assetLabel  = eventLabel
                            description = "Match " <> eventLabel
                            market              = (EventModel.Sport "Soccer")
                            submarkets          = [EventModel.Tournament "Premier League"]
                            geography           = "England"
                            outcomes            = TestUtils.buildOutcomesListWithOddType [
                                                        (OutcomeListWithOddType with participant = Some arsenal, participantOrder = arsenal.order, order = 5, type_ = EnetPulseEvents.ThreeWay, subtype = EnetPulseEvents.Win, odd = 2.0),
                                                        (OutcomeListWithOddType with participant = None, participantOrder = 5, order = 0, type_ = EnetPulseEvents.ThreeWay, subtype = EnetPulseEvents.Draw, odd = 2.0),
                                                        (OutcomeListWithOddType with  participant= Some chelsea,  participantOrder= chelsea.order,  order= 5,  type_= EnetPulseEvents.ThreeWay,  subtype= EnetPulseEvents.Win, odd=  2.0)
                                                    ]
                            eventParticipants   = [arsenal, chelsea]
                            startDate           = (Time.time (Date.date 2021 Date.Nov 04) 0 0 0)
                            eventTitle          = Map.fromList [(show GambylUtils.EN_US, "Arsenal vs Chelsea")]
                            eventStatus         = EnetPulseEvents.NotStarted
                            eventResults        = []

    eventInstrumentRequests <- queryFilter @EventModel.EventInstrumentRequest gambyl (\ EventModel.EventInstrumentRequest{eventId} -> eventId.label == eventLabel)
    assertMsg "EventInstrumentRequests with the same key found " $ null eventInstrumentRequests

    return (parties)

run_testEventOriginationFailDuplicateKey =
    TestOnboarding.testGamblingRole >>=
    TestMarketing.testOfferMarketingService >>=
    testOfferEventService >>=
    testEventOrigination >>=
    testEventOriginationRequestFail

testEventOriginationFailTooManyCharacters : Stack.HasCallStack => Parties -> Script (Parties, Text)
testEventOriginationFailTooManyCharacters parties@Parties{operator, gambyl, eventManager} = script do


    (eventServiceCid, _) <- TestUtils.queryContractWithKey @EventService.Service eventManager (operator, gambyl, eventManager) "Marketing Service doesn't exist"

    ----- Event Origination ----

    let
        eventLabel = "AvCDEFGHIJKL"
        arsenal = TestUtils.getParticipant "Arsenal" 1
        chelsea = TestUtils.getParticipant "Chelsea" 2

    eventRequest <- submit eventManager do
        exerciseCmd eventServiceCid EventService.RequestEventOrigination  with
            eventOrigin = EventModel.CustomerEvent with
                            assetLabel  = eventLabel
                            description = "Match " <> eventLabel
                            market              = (EventModel.Sport "Soccer")
                            submarkets          = [EventModel.Tournament "Premier League"]
                            geography           = "England"
                            outcomes            = TestUtils.buildOutcomesListWithOddType [
                                                        (OutcomeListWithOddType with participant = Some arsenal, participantOrder = arsenal.order, order = 5, type_ = EnetPulseEvents.ThreeWay, subtype = EnetPulseEvents.Win, odd = 2.0),
                                                        (OutcomeListWithOddType with participant = None, participantOrder = 5, order = 0, type_ = EnetPulseEvents.ThreeWay, subtype = EnetPulseEvents.Draw, odd = 2.0),
                                                        (OutcomeListWithOddType with  participant= Some chelsea,  participantOrder= chelsea.order,  order= 5,  type_= EnetPulseEvents.ThreeWay,  subtype= EnetPulseEvents.Win, odd=  2.0)
                                                    ]
                            eventParticipants   = [arsenal, chelsea]
                            startDate           = (Time.time (Date.date 2021 Date.Nov 04) 0 0 0)
                            eventTitle          = Map.fromList [(show GambylUtils.EN_US, "Arsenal vs Chelsea")]
                            eventStatus         = EnetPulseEvents.NotStarted
                            eventResults        = []


    submitMustFail gambyl do
        exerciseCmd eventServiceCid EventService.ApproveEventOrigination
            with
                requestCid  = eventRequest
                managers    = Set.fromList [eventManager]

    return (parties, eventLabel)

run_testEventOriginationFailTooManyCharacter =
    TestOnboarding.testGamblingRole >>=
    TestMarketing.testOfferMarketingService >>=
    testOfferEventService >>=
    testEventOriginationFailTooManyCharacters

testEventOriginationFailEmptyTournament : Stack.HasCallStack => Parties -> Script Parties
testEventOriginationFailEmptyTournament parties@Parties{operator, gambyl, eventManager} = script do


    (eventServiceCid, _) <- TestUtils.queryContractWithKey @EventService.Service eventManager (operator, gambyl, eventManager) "Marketing Service doesn't exist"

    ----- Event Origination ----

    let
        eventLabel = "AvCDEFGHIJKL"
        arsenal = TestUtils.getParticipant "Arsenal" 1
        chelsea = TestUtils.getParticipant "Chelsea" 2

    submitMustFail eventManager do
        exerciseCmd eventServiceCid EventService.RequestEventOrigination  with
            eventOrigin = EventModel.CustomerEvent with
                            assetLabel  = eventLabel
                            description = "Match " <> eventLabel
                            market              = (EventModel.Sport "Soccer")
                            submarkets          = [EventModel.Other_Submarket "Other Submarket"]
                            geography           = "England"
                            outcomes            = TestUtils.buildOutcomesListWithOddType [
                                                        (OutcomeListWithOddType with participant = Some arsenal, participantOrder = arsenal.order, order = 5, type_ = EnetPulseEvents.ThreeWay, subtype = EnetPulseEvents.Win, odd = 2.0),
                                                        (OutcomeListWithOddType with participant = None, participantOrder = 5, order = 0, type_ = EnetPulseEvents.ThreeWay, subtype = EnetPulseEvents.Draw, odd = 2.0),
                                                        (OutcomeListWithOddType with  participant= Some chelsea,  participantOrder= chelsea.order,  order= 5,  type_= EnetPulseEvents.ThreeWay,  subtype= EnetPulseEvents.Win, odd=  2.0)
                                                    ]
                            eventParticipants   = [arsenal, chelsea]
                            startDate           = (Time.time (Date.date 2021 Date.Nov 04) 0 0 0)
                            eventTitle          = Map.fromList [(show GambylUtils.EN_US, "Arsenal vs Chelsea")]
                            eventStatus         = EnetPulseEvents.NotStarted
                            eventResults        = []

    return (parties)

run_testEventOriginationFailEmptyTournament =
    TestOnboarding.testGamblingRole >>=
    TestMarketing.testOfferMarketingService >>=
    testOfferEventService >>=
    testEventOriginationFailEmptyTournament

testEventOriginationFailTooManyTournaments : Stack.HasCallStack => Parties -> Script Parties
testEventOriginationFailTooManyTournaments parties@Parties{operator, gambyl, eventManager} = script do


    (eventServiceCid, _) <- TestUtils.queryContractWithKey @EventService.Service eventManager (operator, gambyl, eventManager) "Marketing Service doesn't exist"

    ----- Event Origination ----

    let
        eventLabel = "AvCDEFGHIJKL"
        arsenal = TestUtils.getParticipant "Arsenal" 1
        chelsea = TestUtils.getParticipant "Chelsea" 2

    submitMustFail eventManager do
        exerciseCmd eventServiceCid EventService.RequestEventOrigination  with
            eventOrigin = EventModel.CustomerEvent with
                            assetLabel  = eventLabel
                            description = "Match " <> eventLabel
                            market              = (EventModel.Sport "Soccer")
                            submarkets          = [EventModel.Tournament "First Tournament", EventModel.Tournament "Second Tournament"]
                            geography           = "England"
                            outcomes            = TestUtils.buildOutcomesListWithOddType [
                                                        (OutcomeListWithOddType with participant = Some arsenal, participantOrder = arsenal.order, order = 5, type_ = EnetPulseEvents.ThreeWay, subtype = EnetPulseEvents.Win, odd = 2.0),
                                                        (OutcomeListWithOddType with participant = None, participantOrder = 5, order = 0, type_ = EnetPulseEvents.ThreeWay, subtype = EnetPulseEvents.Draw, odd = 2.0),
                                                        (OutcomeListWithOddType with  participant= Some chelsea,  participantOrder= chelsea.order,  order= 5,  type_= EnetPulseEvents.ThreeWay,  subtype= EnetPulseEvents.Win, odd=  2.0)
                                                    ]
                            eventParticipants   = [arsenal, chelsea]
                            startDate           = (Time.time (Date.date 2021 Date.Nov 04) 0 0 0)
                            eventTitle          = Map.fromList [(show GambylUtils.EN_US, "Arsenal vs Chelsea")]
                            eventStatus         = EnetPulseEvents.NotStarted
                            eventResults        = []

    return (parties)

run_testEventOriginationFailTooManyTournaments =
    TestOnboarding.testGamblingRole >>=
    TestMarketing.testOfferMarketingService >>=
    testOfferEventService >>=
    testEventOriginationFailTooManyTournaments


testEventOriginationFailEmptyTournamentText : Stack.HasCallStack => Parties -> Script Parties
testEventOriginationFailEmptyTournamentText parties@Parties{operator, gambyl, eventManager} = script do


    (eventServiceCid, _) <- TestUtils.queryContractWithKey @EventService.Service eventManager (operator, gambyl, eventManager) "Marketing Service doesn't exist"

    ----- Event Origination ----

    let
        eventLabel = "AvCDEFGHIJKL"
        arsenal = TestUtils.getParticipant "Arsenal" 1
        chelsea = TestUtils.getParticipant "Chelsea" 2

    submitMustFail eventManager do
        exerciseCmd eventServiceCid EventService.RequestEventOrigination  with
            eventOrigin = EventModel.CustomerEvent with
                            assetLabel  = eventLabel
                            description = "Match " <> eventLabel
                            market              = (EventModel.Sport "Soccer")
                            submarkets          = [EventModel.Tournament ""]
                            geography           = "England"
                            outcomes            = TestUtils.buildOutcomesListWithOddType [
                                                        (OutcomeListWithOddType with participant = Some arsenal, participantOrder = arsenal.order, order = 5, type_ = EnetPulseEvents.ThreeWay, subtype = EnetPulseEvents.Win, odd = 2.0),
                                                        (OutcomeListWithOddType with participant = None, participantOrder = 5, order = 0, type_ = EnetPulseEvents.ThreeWay, subtype = EnetPulseEvents.Draw, odd = 2.0),
                                                        (OutcomeListWithOddType with  participant= Some chelsea,  participantOrder= chelsea.order,  order= 5,  type_= EnetPulseEvents.ThreeWay,  subtype= EnetPulseEvents.Win, odd=  2.0)
                                                    ]
                            eventParticipants   = [arsenal, chelsea]
                            startDate           = (Time.time (Date.date 2021 Date.Nov 04) 0 0 0)
                            eventTitle          = Map.fromList [(show GambylUtils.EN_US, "Arsenal vs Chelsea")]
                            eventStatus         = EnetPulseEvents.NotStarted
                            eventResults        = []

    return (parties)

run_testEventOriginationFailEmptyTournamentText =
    TestOnboarding.testGamblingRole >>=
    TestMarketing.testOfferMarketingService >>=
    testOfferEventService >>=
    testEventOriginationFailEmptyTournamentText


testEventOriginationOutcomeUnderMinOdd : Stack.HasCallStack => Parties -> Script (Parties, Text)
testEventOriginationOutcomeUnderMinOdd parties@Parties{operator, gambyl, eventManager} = script do


  (eventServiceCid, _) <- TestUtils.queryContractWithKey @EventService.Service eventManager (operator, gambyl, eventManager) "Event Service doesn't exist"

  gamblingRoleCid <- queryContractKey @GamblingRole.Role operator (operator, gambyl) >>= optional
    (assertFail $ Common.msgWithFunc "GlobalGamblingConfiguration contract not found") (pure . fst)

  configCid <- submit gambyl do
    exerciseCmd gamblingRoleCid TestMock.mockChangeGlobalGamblingConfiguration with newMinOdd = Some $ Odds.Decimal 1.02

  minOdd <- queryContractId operator configCid >>= optional
    (assertFail $ Common.msgWithFunc "GlobalGamblingConfiguration contract not found") (pure . (.minOdd))

  (Odds.Decimal decimalMinOdd) <- either
    (assertFail . Common.msgWithFunc . ("Failed to convert min odd with error: " <>))
    pure
    $ Odds.getOddFromTo minOdd Odds.Decimal

  ----- Event Origination ----

  let
    eventLabel = "AvC"
    arsenal = TestUtils.getParticipant "Arsenal" 1
    chelsea = TestUtils.getParticipant "Chelsea" 2

  eventRequest <- submit eventManager do
    exerciseCmd eventServiceCid EventService.RequestEventOrigination  with
      eventOrigin = EventModel.CustomerEvent with
                      assetLabel  = eventLabel
                      description = "Match " <> eventLabel
                      market = (EventModel.Sport "Soccer")
                      submarkets = [EventModel.Tournament "Premier League"]
                      geography = "England"
                      outcomes = TestUtils.buildOutcomesListWithOddType [
                                    (OutcomeListWithOddType with participant = Some arsenal, participantOrder = arsenal.order, order = 5, type_ = EnetPulseEvents.ThreeWay, subtype = EnetPulseEvents.Win, odd = decimalMinOdd - 0.01),
                                    (OutcomeListWithOddType with participant = None, participantOrder = 5, order = 0, type_ = EnetPulseEvents.ThreeWay, subtype = EnetPulseEvents.Draw, odd = decimalMinOdd - 0.01),
                                    (OutcomeListWithOddType with  participant= Some chelsea,  participantOrder= chelsea.order,  order= 5,  type_= EnetPulseEvents.ThreeWay,  subtype= EnetPulseEvents.Win, odd = decimalMinOdd - 0.01)
                                  ]
                      eventParticipants = [arsenal, chelsea]
                      startDate = (Time.time (Date.date 2021 Date.Nov 04) 0 0 0)
                      eventTitle = Map.fromList [(show GambylUtils.EN_US, "Arsenal vs Chelsea")]
                      eventStatus = EnetPulseEvents.NotStarted
                      eventResults = []

  eitherEventOrFail <- submit gambyl do
    exerciseCmd eventServiceCid EventService.ApproveEventOrigination with
      requestCid = eventRequest
      managers = Set.fromList [eventManager]

  either
    (queryContractId gambyl >=> optional
      (assertFail $ Common.msgWithFunc "ActionFailure contract not found")
      (\ failure -> assertMsg (Common.msgWithFunc "Failing message is not correct. Should be: "<> failure.reason)
        $ Text.isInfixOf ("is lower than min odd of " <> show minOdd) failure.reason))
    (\ _  -> assertFail
      $ Common.msgWithFunc "Event origination should have failed due to outcomes having lower that min odd value")
    eitherEventOrFail

  return (parties, eventLabel)


run_testEventOriginationOutcomeUnderMinOdd =
    TestOnboarding.testGamblingRole >>=
    TestMarketing.testOfferMarketingService >>=
    testOfferEventService >>=
    testEventOriginationOutcomeUnderMinOdd


testEventOriginationOutcomeOverMaxOdd : Stack.HasCallStack => Parties -> Script (Parties, Text)
testEventOriginationOutcomeOverMaxOdd parties@Parties{operator, gambyl, eventManager} = script do


  (eventServiceCid, _) <- TestUtils.queryContractWithKey @EventService.Service eventManager (operator, gambyl, eventManager) "Event Service doesn't exist"

  gamblingRoleCid <- queryContractKey @GamblingRole.Role operator (operator, gambyl) >>= optional
    (assertFail $ Common.msgWithFunc "GlobalGamblingConfiguration contract not found") (pure . fst)

  configCid <- submit gambyl do
    exerciseCmd gamblingRoleCid TestMock.mockChangeGlobalGamblingConfiguration with newMinOdd = Some $ Odds.Decimal 1.02

  maxOdd <- queryContractId operator configCid >>= optional
    (assertFail $ Common.msgWithFunc "GlobalGamblingConfiguration contract not found") (pure . (.maxOdd))

  (Odds.Decimal decimalMaxOdd) <- either
    (assertFail . Common.msgWithFunc . ("Failed to convert min odd with error: " <>))
    pure
    $ Odds.getOddFromTo maxOdd Odds.Decimal

  ----- Event Origination ----

  let
    eventLabel = "AvC"
    arsenal = TestUtils.getParticipant "Arsenal" 1
    chelsea = TestUtils.getParticipant "Chelsea" 2

  eventRequest <- submit eventManager do
    exerciseCmd eventServiceCid EventService.RequestEventOrigination  with
      eventOrigin = EventModel.CustomerEvent with
                      assetLabel  = eventLabel
                      description = "Match " <> eventLabel
                      market = (EventModel.Sport "Soccer")
                      submarkets = [EventModel.Tournament "Premier League"]
                      geography = "England"
                      outcomes = TestUtils.buildOutcomesListWithOddType [
                                    (OutcomeListWithOddType with participant = Some arsenal, participantOrder = arsenal.order, order = 5, type_ = EnetPulseEvents.ThreeWay, subtype = EnetPulseEvents.Win, odd = decimalMaxOdd + 0.01),
                                    (OutcomeListWithOddType with participant = None, participantOrder = 5, order = 0, type_ = EnetPulseEvents.ThreeWay, subtype = EnetPulseEvents.Draw, odd = decimalMaxOdd + 0.01),
                                    (OutcomeListWithOddType with  participant = Some chelsea,  participantOrder= chelsea.order,  order= 5,  type_= EnetPulseEvents.ThreeWay,  subtype= EnetPulseEvents.Win, odd = decimalMaxOdd + 0.01)
                                  ]
                      eventParticipants = [arsenal, chelsea]
                      startDate = (Time.time (Date.date 2021 Date.Nov 04) 0 0 0)
                      eventTitle = Map.fromList [(show GambylUtils.EN_US, "Arsenal vs Chelsea")]
                      eventStatus = EnetPulseEvents.NotStarted
                      eventResults = []

  eitherEventOrFail <- submit gambyl do
    exerciseCmd eventServiceCid EventService.ApproveEventOrigination with
      requestCid = eventRequest
      managers = Set.fromList [eventManager]

  either
    (queryContractId gambyl >=> optional
      (assertFail $ Common.msgWithFunc "ActionFailure contract not found")
      (\ failure -> assertMsg (Common.msgWithFunc "Failing message is not correct. Should be: "<> failure.reason)
        $ Text.isInfixOf ("is greater than max odd of " <> show maxOdd) failure.reason))
    (\ _  -> assertFail
      $ Common.msgWithFunc "Event origination should have failed due to outcomes having greater that max odd value")
    eitherEventOrFail

  return (parties, eventLabel)


run_testEventOriginationOutcomeOverMaxOdd =
    TestOnboarding.testGamblingRole >>=
    TestMarketing.testOfferMarketingService >>=
    testOfferEventService >>=
    testEventOriginationOutcomeOverMaxOdd


testEventUpdate : Stack.HasCallStack => ((Parties, Text), EnetPulseEvents.Status) -> Script (Parties, EventModel.EventInstrument)
testEventUpdate ((parties@Parties{operator, gambyl, marketingManager, eventManager}, eventLabel), newStatus) = script do

    (marketingServiceCid, _)    <- TestUtils.queryContractWithKey @MarketingService.Service marketingManager (operator, gambyl, marketingManager) "Marketing Service doesn't exist"

    event <- queryContractKey @EventModel.EventInstrument eventManager (operator, gambyl, eventLabel) >>=
      (return . snd . Optional.fromSomeNote (Common.msgWithFunc "Event Instrument doesn't exist"))

    -- only alter these fields
    let newResults  = []

    eitherEventUpdateReqCid <- submit eventManager do
       exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
             EventService.RequestEventUpdate with
              eventOrigin = EventModel.CustomerUpdate with
                            assetLabel          = eventLabel
                            market              = event.details.market
                            submarkets          = event.details.submarkets
                            outcomes            = map (\ EventModel.OutcomeOdds{outcome} -> EventModel.InputOutcomeOdd with outcome, odd = Odds.Decimal 1.0) event.details.outcomes
                            eventParticipants   = event.details.eventParticipants
                            eventTitle          = event.details.eventTitle
                            eventStatus         = newStatus
                            eventResults        = newResults
                            startDate           = event.details.startDate
              oldEventKey = key event

    assertMsg (Common.msgWithFunc "Event update request not created") $ Either.isRight eitherEventUpdateReqCid
    let Right eventUpdateReqCid = eitherEventUpdateReqCid

    eitherEventUpdateCid <- submit gambyl do
        exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
             EventService.ApproveEventUpdate with requestCid = eventUpdateReqCid, marketingServiceCid


    assertMsg (Common.msgWithFunc "Event not updated") $ Either.isRight eitherEventUpdateCid
    let Right eventCid = eitherEventUpdateCid

    details <- (.details) . Optional.fromSome <$> queryContractId eventManager ( eventCid)
    assertMsg (Common.msgWithFunc "Update event failed") $ details.eventStatus == newStatus && details.eventResults == newResults

    eventQuery <- queryFilter @EventModel.EventInstrument gambyl (\event -> event.eventId.label == eventLabel)

    return (parties, snd (List.head eventQuery))

testEventUpdateMkt : Stack.HasCallStack => (Parties, Text) -> Script ()
testEventUpdateMkt (Parties{operator, gambyl, marketingManager, eventManager}, eventLabel) = script do

    (marketingServiceCid, _) <- TestUtils.queryContractWithKey @MarketingService.Service marketingManager (operator, gambyl, marketingManager) "Marketing Service doesn't exist"

    event <- queryContractKey @EventModel.EventInstrument marketingManager (operator, gambyl, eventLabel) >>=
      (return . snd . Optional.fromSomeNote (Common.msgWithFunc "Event Instrument doesn't exist"))

    -- only alter these fields
    let newStatus   = EnetPulseEvents.Finished
        newResults  = []

    eitherEventUpdateReqCid <- submit eventManager do
       exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
             EventService.RequestEventUpdate with
              eventOrigin = EventModel.CustomerUpdate with
                            assetLabel          = eventLabel
                            market              = event.details.market
                            submarkets          = event.details.submarkets
                            outcomes            = map (\ EventModel.OutcomeOdds{outcome} -> EventModel.InputOutcomeOdd with outcome, odd = Odds.Decimal 1.0) event.details.outcomes
                            eventParticipants   = event.details.eventParticipants
                            eventTitle          = event.details.eventTitle
                            eventStatus         = newStatus
                            eventResults        = newResults
                            startDate           = event.details.startDate
              oldEventKey = key event

    assertMsg (Common.msgWithFunc "Event cancelation request not created") $ Either.isRight eitherEventUpdateReqCid
    let Right eventUpdateReqCid = eitherEventUpdateReqCid

    eitherEventUpdateCid <- submit gambyl do
        exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
             EventService.ApproveEventUpdate with requestCid = eventUpdateReqCid, marketingServiceCid

    assertMsg (Common.msgWithFunc "Event not updated") $ Either.isRight eitherEventUpdateCid
    let Right eventCid = eitherEventUpdateCid

    details <- (.details) . Optional.fromSome <$> queryContractId eventManager ( eventCid)
    assertMsg (Common.msgWithFunc "Update event failed") $ details.eventStatus == newStatus && details.eventResults == newResults

    return ()

testEventUpdateStatusFinished : Stack.HasCallStack => (Parties, Text) -> Script ()
testEventUpdateStatusFinished (Parties{operator, gambyl, marketingManager, eventManager}, eventLabel) = script do

  (marketingServiceCid, _)    <- TestUtils.queryContractWithKey @MarketingService.Service marketingManager (operator, gambyl, marketingManager) "Marketing Service doesn't exist"

  event <- queryContractKey @EventModel.EventInstrument eventManager (operator, gambyl, eventLabel) >>=
    (return . snd . Optional.fromSomeNote (Common.msgWithFunc "Event Instrument doesn't exist"))
  -- only alter these fields
  let newStatus   = EnetPulseEvents.Finished
      newResults  = []

  eitherEventUpdateReqCid <- submit eventManager do
      exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
            EventService.RequestEventUpdate with
            eventOrigin = EventModel.CustomerUpdate with
                          assetLabel          = eventLabel
                          market              = event.details.market
                          submarkets          = event.details.submarkets
                          outcomes            = map (\ EventModel.OutcomeOdds{outcome} -> EventModel.InputOutcomeOdd with outcome, odd = Odds.Decimal 1.0) event.details.outcomes
                          eventParticipants   = event.details.eventParticipants
                          eventTitle          = event.details.eventTitle
                          eventStatus         = newStatus
                          eventResults        = newResults
                          startDate           = event.details.startDate
            oldEventKey = key event

  assertMsg (Common.msgWithFunc "Event update request not created") $ Either.isRight eitherEventUpdateReqCid

  let Right eventUpdateReqCid = eitherEventUpdateReqCid

  eitherEventUpdateCid <- submit gambyl do
      exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager)
            EventService.ApproveEventUpdate with requestCid = eventUpdateReqCid, marketingServiceCid

  assertMsg (Common.msgWithFunc "Event not updated") $ Either.isRight eitherEventUpdateCid
  let Right eventCid = eitherEventUpdateCid

  details <- (.details) . Optional.fromSome <$> queryContractId eventManager ( eventCid)
  status  <- (.status) . Optional.fromSome <$> queryContractId eventManager ( eventCid)

  assertMsg (Common.msgWithFunc "Update event failed") $ details.eventStatus == newStatus && details.eventResults == newResults && status == EventModel.Expired

  return ()

run_testEventUpdate =
    TestOnboarding.testGamblingRole >>=
    TestMarketing.testOfferMarketingService >>=
    testOfferEventService >>=
    testEventOrigination >>=
    testEventUpdate . (, EnetPulseEvents.NotStarted)

run_testEventUpdateStatusFinished =
    TestOnboarding.testGamblingRole >>=
    TestMarketing.testOfferMarketingService >>=
    testOfferEventService >>=
    testEventOrigination >>=
    testEventUpdateStatusFinished

run_testEventUpdateMarketing =
    TestOnboarding.testGamblingRole >>=
    TestMarketing.testOfferMarketingService >>=
    testOfferEventService >>=
    TestMarketing.testEventOrigination -- >>=
    -- testEventUpdateMkt

testEventOriginationNoOutcomes : Stack.HasCallStack => Parties -> Script (Parties, Text)
testEventOriginationNoOutcomes parties@Parties{operator, gambyl, eventManager} = script do

    (eventServiceCid, _) <- TestUtils.queryContractWithKey @EventService.Service eventManager (operator, gambyl, eventManager) "Event Service doesn't exist"

    ----- Event Origination No Outcomes ----
    let
        eventLabel = "AvC"
        arsenal    = TestUtils.getParticipant "Arsenal" 1
        chelsea    = TestUtils.getParticipant "Chelsea" 2


    eventRequest <- submit eventManager do
        exerciseCmd eventServiceCid EventService.RequestEventOrigination  with
            eventOrigin = EventModel.CustomerEvent with
                            assetLabel  = eventLabel
                            description = "Match " <> eventLabel
                            market              = (EventModel.Sport "Soccer")
                            submarkets          = [EventModel.Tournament "Premier League"]
                            geography           = "England"
                            outcomes            = []
                            eventParticipants   = [arsenal, chelsea]
                            startDate           = (Time.time (Date.date 2021 Date.Nov 04) 0 0 0)
                            eventTitle          = Map.fromList [(show GambylUtils.EN_US, "Arsenal vs Chelsea")]
                            eventStatus         = EnetPulseEvents.NotStarted
                            eventResults        = []

    submit gambyl do
        exerciseCmd eventServiceCid EventService.ApproveEventOrigination
            with
                requestCid  = eventRequest
                managers    = Set.fromList [eventManager]

    return (parties, eventLabel)

testSameOutcomes : Script ()
testSameOutcomes = script do
    let outcomes1 = map (.outcome) $ TestUtils.buildOutcomesListWithOddType [
                    (OutcomeListWithOddType with participant = None, participantOrder=5, order = 0, type_ = EnetPulseEvents.ThreeWay, subtype = EnetPulseEvents.Win,odd =  1.0),
                    (OutcomeListWithOddType with participant = None, participantOrder=5, order = 0, type_ = EnetPulseEvents.ThreeWay, subtype = EnetPulseEvents.Draw, odd = 1.0),
                    (OutcomeListWithOddType with participant = None, participantOrder=5, order = 0, type_ = EnetPulseEvents.ThreeWay, subtype = EnetPulseEvents.Win,odd = 1.0)]

        outcomes2 = map (.outcome) $ TestUtils.buildOutcomesListWithOddType [
                    (OutcomeListWithOddType with participant = None, participantOrder = 5, order = 0, type_ = EnetPulseEvents.ThreeWay, subtype = EnetPulseEvents.Draw, odd =1.0),
                    (OutcomeListWithOddType with participant = None, participantOrder = 5, order = 0, type_ = EnetPulseEvents.ThreeWay, subtype = EnetPulseEvents.Win, odd =1.0),
                    (OutcomeListWithOddType with participant = None, participantOrder = 5, order = 0, type_ = EnetPulseEvents.ThreeWay, subtype = EnetPulseEvents.Win,odd =1.0)]

        outcomes3 = map (.outcome) $ TestUtils.buildOutcomesListWithOddType [
                    (OutcomeListWithOddType with participant = None, participantOrder = 5, order = 0, type_ = EnetPulseEvents.ThreeWay, subtype = EnetPulseEvents.Draw, odd = 1.0),
                    (OutcomeListWithOddType with participant =  None, participantOrder = 5, order = 0, type_ = EnetPulseEvents.ThreeWay, subtype = EnetPulseEvents.Win, odd = 1.0)]

    assertMsg "" $ GambylUtils.listEquals outcomes1 outcomes2
    assertMsg "" $ not $ GambylUtils.listEquals outcomes1 []
    assertMsg "" $ not $ GambylUtils.listEquals [] outcomes2
    assertMsg "" $ not $ GambylUtils.listEquals outcomes1 outcomes3

    return ()


testToggleFeatureEvent : Stack.HasCallStack => (Parties, Text) -> Script ()
testToggleFeatureEvent (Parties{operator, gambyl, eventManager}, eventLabel) = script do

    (eventCid, event) <- queryContractKey @EventModel.EventInstrument eventManager (operator, gambyl, eventLabel) >>= (return . Optional.fromSomeNote (Common.msgWithFunc "Event Instrument doesn't exist"))

    oldFeatured <- (.featured) . Optional.fromSomeNote "Could not find Event contract" <$> queryContractId eventManager eventCid

    toggleRequestCid <- submit eventManager do exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager) EventService.RequestToggleFeaturedEvent with label = event.eventId.label

    eitherUpdatedEventCid <- submit gambyl do exerciseByKeyCmd @EventService.Service (operator, gambyl, eventManager) EventService.ApproveToggleFeaturedEvent with requestCid = toggleRequestCid

    featured <- (.featured) . Optional.fromSomeNote "Could not find Event contract" <$> queryContractId eventManager eitherUpdatedEventCid
    assertMsg (Common.msgWithFunc "Update event failed") $ featured == not oldFeatured

    return ()

run_testToggleFeatureEvent =
    TestOnboarding.testGamblingRole >>=
    TestMarketing.testOfferMarketingService >>=
    testOfferEventService >>=
    testEventOrigination >>=
    testToggleFeatureEvent
