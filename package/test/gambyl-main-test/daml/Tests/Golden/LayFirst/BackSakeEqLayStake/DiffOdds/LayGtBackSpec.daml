module Tests.Golden.LayFirst.BackSakeEqLayStake.DiffOdds.LayGtBackSpec where

import Daml.Script

import DA.Set qualified as Set

import Gambyl.Gambling.Bet.Odds.Model       (OddType(..))

import Tests.Golden.GoldenUtils

{- --------------------------------------GAMBLING GOLDEN TESTS-------------------------------------- -}

run_si3st1o4b1m1w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st1o4b1m1w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 15.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 15.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 15.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 15.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 10.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 10.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 10.0
                                        totalBonusBetBalance = 10.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 10.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 10.0
                                        totalBonusBetBalance = 10.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1014.25
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1014.25
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st1o4b1m1w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st1o4b1m1w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 15.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 15.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 15.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 15.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1009.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1009.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 10.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 10.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 10.0
                                        totalBonusBetBalance = 10.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 10.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 10.0
                                        totalBonusBetBalance = 10.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 10.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 10.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st1o4b2m1w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st1o4b2m1w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 15.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 15.0)
                                    , BonusUsed None
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 10.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 10.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 10.0
                                        totalBonusBetBalance = 10.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 10.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 10.0
                                        totalBonusBetBalance = 10.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1014.25
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1014.25
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st1o4b2m1w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st1o4b2m1w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 15.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 15.0)
                                    , BonusUsed None
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1009.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1009.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 10.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 10.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 10.0
                                        totalBonusBetBalance = 10.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 10.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 10.0
                                        totalBonusBetBalance = 10.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 10.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 10.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st1o4b3m1w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st1o4b3m1w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 15.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 15.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 15.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 15.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 10.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1014.25
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1014.25
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st1o4b3m1w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st1o4b3m1w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 15.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 15.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 15.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 15.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1009.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1009.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 10.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st1o4b4m1w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st1o4b4m1w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed (Some 5.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 10.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1014.25
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1014.25
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st1o4b4m1w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st1o4b4m1w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed (Some 5.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1009.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1009.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 10.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st1o4b5m1w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st1o4b5m1w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 15.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 15.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 15.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 15.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 10.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1014.25
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1014.25
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st1o4b5m1w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st1o4b5m1w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 15.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 15.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 15.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 15.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1009.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1009.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 10.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 00.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 00.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st1o4b6m1w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st1o4b6m1w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed (Some 5.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 10.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 10.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 10.0
                                        totalBonusBetBalance = 10.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 10.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 10.0
                                        totalBonusBetBalance = 10.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1014.25
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1014.25
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st1o4b6m1w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st1o4b6m1w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed (Some 5.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1009.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1009.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 10.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 10.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 10.0
                                        totalBonusBetBalance = 10.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 10.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 10.0
                                        totalBonusBetBalance = 10.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 10.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 10.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st1o4b7m1w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st1o4b7m1w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 15.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 15.0)
                                    , BonusUsed None
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 10.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1014.25
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1014.25
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st1o4b7m1w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st1o4b7m1w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 15.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 15.0)
                                    , BonusUsed None
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1009.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1009.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 10.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st1o4b8m1w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st1o4b8m1w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed (Some 5.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 10.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1014.25
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1014.25
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st1o4b8m1w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st1o4b8m1w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed (Some 5.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1009.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1009.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 10.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st1o4b9m1w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st1o4b9m1w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 15.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 15.0)
                                    , BonusUsed None
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 10.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1014.25
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1014.25
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st1o4b9m1w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st1o4b9m1w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 15.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 15.0)
                                    , BonusUsed None
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1009.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1009.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 10.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 10.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport
