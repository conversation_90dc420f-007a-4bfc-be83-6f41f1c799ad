module Tests.Golden.LayFirst.LayStakeGtBackSake.DiffOdds.LayGtBackSpec where

import Daml.Script

import DA.Set qualified as Set

import Gambyl.Gambling.Bet.Odds.Model       (OddType(..))

import Tests.Golden.GoldenUtils

{- --------------------------------------GAMBLING GOLDEN TESTS-------------------------------------- -}

run_si3st2o4b1m1w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st2o4b1m1w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 15.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 15.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 7.5)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 15.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 7.5)
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 7.5
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 7.5
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 12.5
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1007.12
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1007.12
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st2o4b1m1w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st2o4b1m1w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 15.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 15.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 7.5)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 15.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 7.5)
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 7.5
                                        totalBonusBalance = 12.5
                                        totalBonusBetBalance = 7.5
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st2o4b2m1w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st2o4b2m1w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 15.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 7.5)
                                    , BonusUsed None
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 7.5)
                                    , BonusUsed None
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 7.5
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 992.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1007.12
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1007.12
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st2o4b2m1w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st2o4b2m1w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 15.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 7.5)
                                    , BonusUsed None
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 7.5)
                                    , BonusUsed None
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 997.25
                                        totalBetBalance = 7.5
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st2o4b3m1w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st2o4b3m1w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 15.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 15.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 7.5)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 15.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 7.5)
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 7.5
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 7.5
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 12.5
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1007.12
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1007.12
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st2o4b3m1w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st2o4b3m1w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 15.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 15.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 7.5)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 15.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 7.5)
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 7.5
                                        totalBonusBalance = 12.5
                                        totalBonusBetBalance = 7.5
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st2o4b4m2w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st2o4b4m2w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 2.5
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 12.5)
                                    , BonusUsed (Some 2.5)
                                    , Account with
                                        totalMainBalance = 987.5
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 2.5)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 987.5
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 7.5)
                                    , BonusUsed None
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 987.5
                                        totalBetBalance = 7.5
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 2.5
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 2.5)
                                    , Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 2.5)
                                    , Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1007.12
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 2.5
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1007.12
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 2.5
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st2o4b4m2w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st2o4b4m2w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 2.5
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 12.5)
                                    , BonusUsed (Some 2.5)
                                    , Account with
                                        totalMainBalance = 987.5
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 2.5)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 987.5
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 7.5)
                                    , BonusUsed None
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 997.25
                                        totalBetBalance = 7.5
                                        totalBonusBalance = 2.5
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 2.5
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 2.5
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 2.5)
                                    , Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 2.5)
                                    , Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st2o4b4m5w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st2o4b4m5w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 10.0
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 10.0)
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 10.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 7.5)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 10.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 2.5)
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 7.5
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 2.5
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 2.5
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 2.5)
                                    , Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 2.5)
                                    , Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1007.12
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 2.5
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1007.12
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 2.5
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st2o4b4m5w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st2o4b4m5w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 10.0
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 10.0)
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 10.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 7.5)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 10.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 2.5)
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 999.75
                                        totalBetBalance = 7.5
                                        totalBonusBalance = 7.5
                                        totalBonusBetBalance = 2.5
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 10.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 2.5
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 2.5)
                                    , Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 2.5)
                                    , Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st2o4b4m4w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st2o4b4m4w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 13.5
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 1.5)
                                    , BonusUsed (Some 13.5)
                                    , Account with
                                        totalMainBalance = 998.5
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 13.5
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 7.5)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 998.5
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 13.5
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 1.5)
                                    , BonusUsed (Some 6.0)
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 998.5
                                        totalBetBalance = 7.5
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 6.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 6.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 2.5
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 2.5)
                                    , Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 2.5)
                                    , Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1007.12
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 2.5
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1007.12
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 2.5
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st2o4b4m4w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st2o4b4m4w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 13.5
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 1.5)
                                    , BonusUsed (Some 13.5)
                                    , Account with
                                        totalMainBalance = 998.5
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 13.5
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 7.5)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 998.5
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 13.5
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 1.5)
                                    , BonusUsed (Some 6.0)
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1003.25
                                        totalBetBalance = 7.5
                                        totalBonusBalance = 7.5
                                        totalBonusBetBalance = 6.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 13.5
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 2.5
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 2.5)
                                    , Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 2.5)
                                    , Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st2o4b4m3w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st2o4b4m3w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 5.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 7.5)
                                    , BonusUsed None
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 7.5
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 2.5
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 2.5)
                                    , Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 2.5)
                                    , Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1007.12
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 2.5
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1007.12
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 2.5
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st2o4b4m3w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st2o4b4m3w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 5.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 7.5)
                                    , BonusUsed None
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 997.25
                                        totalBetBalance = 7.5
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 2.5
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 2.5)
                                    , Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 2.5)
                                    , Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st2o4b5m1w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st2o4b5m1w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 15.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 15.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 7.5)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 15.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 7.5)
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 7.5
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 7.5
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 12.5
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 2.5
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 2.5)
                                    , Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 2.5)
                                    , Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1007.12
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 2.5
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1007.12
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 2.5
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st2o4b5m1w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st2o4b5m1w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 15.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 15.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 7.5)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 15.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 7.5)
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 7.5
                                        totalBonusBalance = 12.5
                                        totalBonusBetBalance = 7.5
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 2.5
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 2.5)
                                    , Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 2.5)
                                    , Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st2o4b6m1w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st2o4b6m1w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 5.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 7.5)
                                    , BonusUsed None
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 7.5
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1007.12
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1007.12
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st2o4b6m1w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st2o4b6m1w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 5.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 7.5)
                                    , BonusUsed None
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 997.25
                                        totalBetBalance = 7.5
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st2o4b7m1w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st2o4b7m1w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 15.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 7.5)
                                    , BonusUsed None
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 7.5)
                                    , BonusUsed None
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 7.5
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 992.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 2.5
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 2.5)
                                    , Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 2.5)
                                    , Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1007.12
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 2.5
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1007.12
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 2.5
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st2o4b7m1w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st2o4b7m1w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 15.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 7.5)
                                    , BonusUsed None
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 7.5)
                                    , BonusUsed None
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 997.25
                                        totalBetBalance = 7.5
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 2.5
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 2.5)
                                    , Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 2.5)
                                    , Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 2.5
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st2o4b8m1w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st2o4b8m1w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 5.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 7.5)
                                    , BonusUsed None
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 7.5
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 997.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1007.12
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1007.12
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st2o4b8m1w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st2o4b8m1w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 10.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 2.5)
                                    , BonusUsed (Some 5.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 990.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 7.5)
                                    , BonusUsed None
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 997.25
                                        totalBetBalance = 7.5
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st2o4b9m1w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st2o4b9m1w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 15.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 7.5)
                                    , BonusUsed None
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 7.5)
                                    , BonusUsed None
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 7.5
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 992.5
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1007.12
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1007.12
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si3st2o4b9m1w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si3st2o4b9m1w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 10.0
                            odd = Decimal 2.5
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 10.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 15.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 7.5)
                                    , BonusUsed None
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 985.0
                                        totalBetBalance = 15.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 7.5)
                                    , BonusUsed None
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 997.25
                                        totalBetBalance = 7.5
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.5)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport
