module Tests.Golden.BackFirst.BackSakeGtLayStake.SameOdds.EqTwoSpec where

import Daml.Script

import DA.Set qualified as Set

import Gambyl.Gambling.Bet.Odds.Model       (OddType(..))

import Tests.Golden.GoldenUtils

{- --------------------------------------GAMBLING GOLDEN TESTS-------------------------------------- -}

run_si2st3o1b1m1w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si2st3o1b1m1w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 25.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 25.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 20.0)
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 20.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 20.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 20.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 15.0)
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 999.75
                                        totalBetBalance = 20.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 15.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si2st3o1b1m1w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si2st3o1b1m1w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 25.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 25.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 20.0)
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 20.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 20.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 20.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 15.0)
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 20.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 15.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si2st3o1b2m1w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si2st3o1b2m1w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 25.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 25.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 20.0)
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 20.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 20.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 20.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 15.0)
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 999.75
                                        totalBetBalance = 20.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 15.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si2st3o1b2m1w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si2st3o1b2m1w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 25.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 25.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 20.0)
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 20.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 20.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 20.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 15.0)
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 20.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 15.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si2st3o1b3m1w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si2st3o1b3m1w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 25.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 25.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 25.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 975.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 975.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 20.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 20.0)
                                    , BonusUsed None
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 984.75
                                        totalBetBalance = 20.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si2st3o1b3m1w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si2st3o1b3m1w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 25.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 25.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 25.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 975.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 975.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 20.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 20.0)
                                    , BonusUsed None
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 975.0
                                        totalBetBalance = 20.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si2st3o1b4m1w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si2st3o1b4m1w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 25.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 25.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 20.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 980.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 980.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 20.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 20.0)
                                    , BonusUsed None
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 984.75
                                        totalBetBalance = 20.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si2st3o1b4m1w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si2st3o1b4m1w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 25.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 25.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 20.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 980.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 980.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 20.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 20.0)
                                    , BonusUsed None
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 980.0
                                        totalBetBalance = 20.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si2st3o1b5m1w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si2st3o1b5m1w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 25.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 25.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 20.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 980.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 980.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 20.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 20.0)
                                    , BonusUsed None
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 984.75
                                        totalBetBalance = 20.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si2st3o1b5m1w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si2st3o1b5m1w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 25.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 25.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 20.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 980.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 980.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 20.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 20.0)
                                    , BonusUsed None
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 980.0
                                        totalBetBalance = 20.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si2st3o1b6m1w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si2st3o1b6m1w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 25.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 25.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 20.0)
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 20.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 20.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 20.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 15.0)
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 999.75
                                        totalBetBalance = 20.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 15.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 20.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si2st3o1b6m1w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si2st3o1b6m1w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 20.0
                            stake = 25.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 25.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 20.0)
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 20.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 20.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 20.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed (Some 15.0)
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 20.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 15.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 15.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si2st3o1b7m1w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si2st3o1b7m1w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 25.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 25.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 20.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 980.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 980.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 20.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 20.0)
                                    , BonusUsed None
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 984.75
                                        totalBetBalance = 20.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si2st3o1b7m1w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si2st3o1b7m1w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 25.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 25.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 20.0)
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 980.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 980.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 20.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 20.0)
                                    , BonusUsed None
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 980.0
                                        totalBetBalance = 20.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si2st3o1b8m1w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si2st3o1b8m1w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 25.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 25.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 25.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 975.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 975.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 20.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 20.0)
                                    , BonusUsed None
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 984.75
                                        totalBetBalance = 20.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si2st3o1b8m1w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si2st3o1b8m1w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 25.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 25.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 25.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 975.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 975.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 20.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 20.0)
                                    , BonusUsed None
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 975.0
                                        totalBetBalance = 20.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = Some 5.0
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed None
                                    , BonusUsed (Some 5.0)
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 1000.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 5.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 5.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si2st3o1b9m1w1 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si2st3o1b9m1w1]"
            winner = "alice"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 25.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 25.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 25.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 975.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 975.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 20.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 20.0)
                                    , BonusUsed None
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 984.75
                                        totalBetBalance = 20.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport

run_si2st3o1b9m1w2 = script $ do
    let testReport = TestReport with
            debugPrefix = "[si2st3o1b9m1w2]"
            winner = "bob"
            partyReports = [
                    PartyReport with
                        partyInput = PartyInputs with
                            id = "alice"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 25.0
                            odd = Decimal 2.0
                            side = Back
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 25.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 25.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 975.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 975.0
                                        totalBetBalance = 25.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList [
                                    Stake 20.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 20.0)
                                    , BonusUsed None
                                ]
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 975.0
                                        totalBetBalance = 20.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                    , PartyReport with
                        partyInput = PartyInputs with
                            id = "bob"
                            depositInputs = DepositInputs with
                                deposit = 1000.0
                                bonusAmount = None
                            stake = 5.0
                            odd = Decimal 2.0
                            side = Lay
                        partyOutput = PartyOutputs with
                            unmatchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            matchedOutputs = Set.fromList [
                                    Stake 5.0
                                    , Odd (Decimal 2.0)
                                    , CashUsed (Some 5.0)
                                    , BonusUsed None
                                    , CashReturned None
                                    , BonusReturned None
                                    , Account with
                                        totalMainBalance = 995.0
                                        totalBetBalance = 5.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            unmatchingOutputs = Set.fromList []
                            settlementOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                            cancelationOutputs = Set.fromList [
                                    Account with
                                        totalMainBalance = 1004.75
                                        totalBetBalance = 0.0
                                        totalBonusBalance = 0.0
                                        totalBonusBetBalance = 0.0
                                ]
                ]
    test_runner testReport
