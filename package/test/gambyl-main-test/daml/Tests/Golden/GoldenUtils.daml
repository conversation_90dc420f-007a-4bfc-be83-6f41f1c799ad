module Tests.Golden.GoldenUtils where

import Daml.Script

import DA.Action qualified as Action
import DA.Either qualified as Either
import DA.Foldable (mapA_)
import DA.List qualified as List
import DA.Optional ()
import DA.Optional qualified as Optional
import DA.Set ()
import DA.Set qualified as Set
import DA.Stack (HasCallStack)
import DA.Text qualified as Text

import Marketplace.Issuance.Model qualified as IssuanceModel

import Gambyl.Gambling.Account.Model qualified as GamblingAccount
import Gambyl.Gambling.Bet.Model qualified as BetModel
import Gambyl.Gambling.Bet.Odds.Model qualified as BetOddsModel
import Gambyl.Gambling.Event.Model qualified as EventModel
import Gambyl.Gambling.Service qualified as GamblingService

import EnetPulseIntegration.Events qualified as IntegrationEvents

import Scripts.Bootstrap.Onboarding.Parties (Parties(..))
import Scripts.Common qualified as Common (msgWithFunc)

import Tests.Gambling (
        getBetDeposit,
        getIssuanceQuantity,
        originateAsset,
        onboardCustomerWithDeposit,
        testEventOrigination,
        testGiveBugBountyBonus,
        testBetPlacement,
        testMatchBets,
        testEventUpdateFromIntegration,
        settleBetsWithBonus
    )

import Tests.Marketing qualified as TestMarketing
import Tests.Onboarding qualified as TestOnboarding
import Tests.Utils qualified as TestUtils

{- --------------------------------------GAMBLING GOLDEN HELPERS-------------------------------------- -}

(&) : t1 -> (t1 -> t2) -> t2
(&) a fn = fn a

fmapSet : Ord k => (a -> k) -> Set.Set a -> Set.Set k
fmapSet f s = Set.fromList $ fmap f (Set.toList s)

assertMsgWithPrefix_alt : HasCallStack => Text -> Text -> Bool -> Script ()
assertMsgWithPrefix_alt debugPrefix msg = assertMsg (debugPrefix <> " " <> msg)

back : HasCallStack => BetOddsModel.OddType -> Numeric 2 -> IntegrationEvents.Outcome -> BetModel.Side
back oddType stake = TestUtils.buildSide BetModel.Back oddType stake

lay : HasCallStack => BetOddsModel.OddType -> Numeric 2 -> IntegrationEvents.Outcome -> BetModel.Side
lay oddType stake = TestUtils.buildSide BetModel.Lay oddType stake

queryBetPlacement : HasCallStack => (Template b, HasAgreement b, IsParties p) => p -> ContractId b -> Script b
queryBetPlacement party id =
    Optional.fromSomeNote (Common.msgWithFunc "Bet Placement contract doesn't exist") <$>
        queryContractId party id

withEventOrigination : HasCallStack => ((Parties, Text) -> [EventModel.OutcomeOdds] -> Script a3) -> (Parties, b3) -> Script ()
withEventOrigination checks = exec
    where
        exec (p@Parties{..}, _currency) = do
            (_, EventModel.EventInstrument{
                    details = EventModel.Details{outcomes},
                    eventId
            }) <- testEventOrigination (p, "AvB")
            let withEventId = (p, eventId.label)
            checks withEventId outcomes
            return ()

withCeremony : HasCallStack => [PartyReport] -> (((Party, PartyReport), (Party, PartyReport)) -> (Parties, Text) -> Script b) -> Script b
withCeremony partyReports fn =
    TestOnboarding.testGamblingRole >>=
    TestMarketing.testOfferMarketingService >>=
    (\p@Parties{..} -> do
        let partyWithCurrency = (p, "USD")
        withCurrency <- originateAsset partyWithCurrency

        let [partyReport1, partyReport2] = partyReports

        let ((party1, party1Report), (party2, party2Report)) =
                if partyReport1.partyInput.id == "alice"
                then ((alice, partyReport1), (bob, partyReport2))
                else ((bob, partyReport1), (alice, partyReport2))


        onboardCustomerWithDeposit (withCurrency, party1, "123", party1Report.partyInput.depositInputs.deposit, None, "EN")

        mapA_ (testGiveBugBountyBonus . (partyWithCurrency, party1,)) $ party1Report.partyInput.depositInputs.bonusAmount

        onboardCustomerWithDeposit (withCurrency, party2, "123", party2Report.partyInput.depositInputs.deposit, None, "EN")

        mapA_ (testGiveBugBountyBonus . (partyWithCurrency, party2,)) $ party2Report.partyInput.depositInputs.bonusAmount

        fn ((party1, party1Report), (party2, party2Report)) withCurrency
    )

lookupForPartialMatches : (HasCallStack, IsParties p) => p -> Text -> Script (Text, Optional Text)
lookupForPartialMatches gambyl betId = script $ do
    maybePartialMatchedBetIds <- queryFilter @BetModel.BetPlacement gambyl $ \BetModel.BetPlacement{details = BetModel.Details{betPlacementId}} ->
        betPlacementId == betId <> "_1"
    let maybePartialMatchedBetId =
            fmap
            (\BetModel.BetPlacement{details = BetModel.Details{betPlacementId}} -> betPlacementId)
            $ snd <$> maybePartialMatchedBetIds
    let matchedBetId = if null maybePartialMatchedBetId then betId else List.head maybePartialMatchedBetId

    maybePartialUnmatchedBetIds <- queryFilter @BetModel.BetPlacement gambyl $ \BetModel.BetPlacement{details = BetModel.Details{betPlacementId}} ->
        betPlacementId == betId <> "_2"
    let maybePartialUnmatchedBetId =
            fmap
            (\BetModel.BetPlacement{details = BetModel.Details{betPlacementId}} -> betPlacementId)
            $ snd <$> maybePartialUnmatchedBetIds
    let unmatchedBetId = if null maybePartialMatchedBetId then None else Some $ List.head maybePartialUnmatchedBetId

    pure (matchedBetId, unmatchedBetId)

testManualCancelBetAfterEventStatusChange : HasCallStack => (Parties, Party, Text, IntegrationEvents.Status) -> Script ()
testManualCancelBetAfterEventStatusChange (Parties{operator, gambyl}, party, betPlacementId,newStatus)  = script do

    let betPlacementKey = (operator, gambyl, party, betPlacementId)
    gamblingServiceCid <- fst <$> TestUtils.queryServiceWithKey party (operator, gambyl, party)

    (betPlacementCid
      , BetModel.BetPlacement{status = initialBetStatus, betIssuanceKey, details = BetModel.Details{eventKey}}
      ) <- Optional.fromSomeNote "Bet placement not found" <$> queryContractKey @BetModel.BetPlacement gambyl betPlacementKey

    (eventCid, EventModel.EventInstrument{details, ..}) <-
        TestUtils.queryContractWithKey @EventModel.EventInstrument gambyl eventKey "Event Instrument not found"

    submitMulti [operator, gambyl] [operator, gambyl] do
        archiveCmd eventCid
    submitMulti [operator, gambyl] [operator, gambyl] do
        createCmd EventModel.EventInstrument with
            details = details with eventStatus = newStatus
            ..

    res <- submit gambyl do
        exerciseCmd gamblingServiceCid GamblingService.CancelBet with betPlacementCid

    assertMsg (Common.msgWithFunc "Bet placement cancel request failed: " <> show res) $ Either.isRight res

    let Right finalizeBetPlacementCancelCid = res

    BetModel.FinalizeBetPlacementCancelRequest{orderId} <-
        Optional.fromSomeNote (Common.msgWithFunc "Finalize Bet Placement Cancel Request Contract not found") <$>
            queryContractId  gambyl finalizeBetPlacementCancelCid

    -- Get issuance quantity and bet asset deposit quantity prior to cancelation
    issuanceQuantity <- getIssuanceQuantity gambyl betIssuanceKey
    betDepositQuantity <- getBetDeposit gambyl orderId

    submit gambyl do
        exerciseByKeyCmd @GamblingService.Service (operator, gambyl, party) $ GamblingService.FinalizeBetPlacementCancel with
            finalizeBetPlacementCancelCid, betPlacementCid = Some betPlacementCid, errorMsg = None, eventCancelled = False

    Action.when (initialBetStatus /= BetModel.Matched) do
      let expectedQuantity = issuanceQuantity - betDepositQuantity
      if (expectedQuantity == 0.0)
        then do
          queryContractKey @IssuanceModel.Issuance gambyl betIssuanceKey >>= optional
            (pure ())
            (\ _ -> assertFail $ Common.msgWithFunc "Issuance contract still exists")
        else do
          reducedQuantity <- getIssuanceQuantity gambyl betIssuanceKey
          assertMsg (Common.msgWithFunc "Issuance amount has not been Reduced")
            $ reducedQuantity == expectedQuantity

{- --------------------------------------GAMBLING GOLDEN TYPES-------------------------------------- -}

data DepositInputs = DepositInputs with
    deposit: Numeric 2
    bonusAmount: Optional (Numeric 2)

data BetSide = Back | Lay

data PartyInputs = PartyInputs with
    id: Text
    depositInputs: DepositInputs
    stake: Numeric 2
    odd: BetOddsModel.OddType
    side: BetSide

data PartyOutputs = PartyOutputs with
    unmatchedOutputs: Set.Set Expectation
    matchedOutputs: Set.Set Expectation
    unmatchingOutputs: Set.Set Expectation
    settlementOutputs: Set.Set Expectation
    cancelationOutputs: Set.Set Expectation

data PartyReport = PartyReport with
    partyInput : PartyInputs
    partyOutput : PartyOutputs

data TestReport = TestReport with
    debugPrefix: Text
    winner: Text
    partyReports: [PartyReport]

{- --------------------------------------GAMBLING GOLDEN TEST ASSERTIONS-------------------------------------- -}

data Expectation =
    Stake (Numeric 2)
    | Odd BetOddsModel.OddType
    | CashUsed (Optional (Numeric 2))
    | BonusUsed (Optional (Numeric 2))
    | Account with
        totalMainBalance: Numeric 2
        totalBetBalance: Numeric 2
        totalBonusBalance: Numeric 2
        totalBonusBetBalance: Numeric 2
    | CashReturned (Optional (Numeric 2))
    | BonusReturned (Optional (Numeric 2))
    deriving (Show, Eq, Ord)

data ExpectationStage =
    Unmatched
    | Matched
    | Unmatching
    | Settlement
    | Cancel
    deriving (Show, Eq, Ord)

-- Used to keep state (memory) while the test-driver is running.
-- This allows to verify expectations to against history.
template RunningParty with
        id: Text
        stage: ExpectationStage
        party: Party
        totalMainBalance: Numeric 2
        totalBetBalance: Numeric 2
        totalBonusBalance: Numeric 2
        totalBonusBetBalance: Numeric 2
    where
        signatory party
        key (party, id, stage): (Party, Text, ExpectationStage)
        maintainer key._1

verify : (Text -> Bool -> Script ()) -> Party -> Party -> PartyInputs -> Int -> Text -> (ExpectationStage, Expectation) -> Script ()
verify assertMsgWith gambyl party PartyInputs{..} partyOrder partyBetId (stage, expectation) = do
    -- REVIEW
    let statusFilter = case stage of
            Unmatched -> BetModel.Unmatched
            Unmatching -> BetModel.Unmatched
            Matched -> BetModel.Matched
            Settlement -> BetModel.Matched
            Cancel -> BetModel.Matched
        msgTemplate value shouldBe = "Party" <> "-" <> show partyOrder <> ": " <> id <> " " <> show stage <> " " <> value <> " should be: " <> shouldBe
    case expectation of
        -- the stakes used are consistent
        Stake value ->  do
                allBetPlacements <- queryFilter @BetModel.BetPlacement gambyl $ \BetModel.BetPlacement{status} -> status == statusFilter
                let [partyStake] =
                        fmap (\BetModel.BetPlacement{details = BetModel.Details{side}} ->
                            case side of
                                BetModel.Back BetModel.SideDetails{stake} -> (stake)
                                BetModel.Lay BetModel.SideDetails{stake} -> (stake)
                        ) . filter (\BetModel.BetPlacement{details = BetModel.Details{betPlacementId}} -> betPlacementId == partyBetId)
                        $ snd <$> allBetPlacements
                assertMsgWith (msgTemplate "stake" (show partyStake)) $ partyStake == value
        -- the odds used are consistent
        Odd value -> do
            allBetPlacements <- queryFilter @BetModel.BetPlacement gambyl $ \BetModel.BetPlacement{status} -> status == statusFilter
            let [partyOdd] =
                    fmap (\BetModel.BetPlacement{details = BetModel.Details{side}} ->
                        case side of
                            BetModel.Back BetModel.SideDetails{odd} -> (odd)
                            BetModel.Lay BetModel.SideDetails{odd} -> (odd)
                    ) . filter (\BetModel.BetPlacement{details = BetModel.Details{betPlacementId}} -> betPlacementId == partyBetId)
                    $ snd <$> allBetPlacements
            assertMsgWith (msgTemplate "odd" (show partyOdd)) $ partyOdd == value
        -- check party cash used
        -- should be equals to: partyInput.depositInputs.deposit - totalMainBalance
        CashUsed value -> do
            allBetPlacements <- queryFilter @BetModel.BetPlacement gambyl $ \BetModel.BetPlacement{status} -> status == statusFilter
            let partyBetPlacements =
                    filter (\BetModel.BetPlacement{details = BetModel.Details{betPlacementId}} -> betPlacementId == partyBetId)
                    $ snd <$> allBetPlacements
            [partyExpectedCashUsed] <-
                    mapA (\BetModel.BetPlacement{bonusBetAmount, details = BetModel.Details{side}} ->
                        case side of
                            BetModel.Back BetModel.SideDetails{stake} -> do
                                let bonusAmount = Optional.fromOptional 0.0 bonusBetAmount
                                pure $ stake - bonusAmount
                            BetModel.Lay BetModel.SideDetails{odd, stake} ->
                                case BetOddsModel.calculatePayout odd stake of
                                    Left err -> assertFail $ (msgTemplate "cash used" err)
                                    Right payout -> do
                                        let bonusAmount = Optional.fromOptional 0.0 bonusBetAmount
                                            result = if bonusAmount >= payout then 0.0 else payout - bonusAmount
                                        pure $ result
                    ) $ partyBetPlacements
            assertMsgWith (msgTemplate "cash used" (show partyExpectedCashUsed)) $
                partyExpectedCashUsed == Optional.fromOptional 0.0 value
        -- the bonuses used are consistent
        BonusUsed value -> do
            allBetPlacements <- queryFilter @BetModel.BetPlacement gambyl $ \BetModel.BetPlacement{status} -> status == statusFilter
            let [partyBonus] =
                    fmap (\ BetModel.BetPlacement{bonusBetAmount} -> bonusBetAmount)
                    . filter (\BetModel.BetPlacement{details = BetModel.Details{betPlacementId}} -> betPlacementId == partyBetId)
                    $ snd <$>  allBetPlacements
            assertMsgWith (msgTemplate "bonus used" (show partyBonus)) $ partyBonus == value
        -- check party account balances
        expectedAccount@Account{} -> do
                [GamblingAccount.Account{..}] <- fmap snd <$> query @GamblingAccount.Account party
                assertMsgWith (msgTemplate "totalMainBalance" (show totalMainBalance)) $ totalMainBalance == expectedAccount.totalMainBalance
                assertMsgWith (msgTemplate "totalBetBalance" (show totalBetBalance)) $ totalBetBalance == expectedAccount.totalBetBalance
                assertMsgWith (msgTemplate "totalBonusBalance" (show totalBonusBalance)) $ totalBonusBalance == expectedAccount.totalBonusBalance
                assertMsgWith (msgTemplate "totalBonusBetBalance" (show totalBonusBetBalance)) $ totalBonusBetBalance == expectedAccount.totalBonusBetBalance
                -- Note: RunningParty key constraint prevents from having more than one Account expectation per stage
                submit party $ createCmd RunningParty with ..
                pure ()
        CashReturned value ->
                case stage of
                    Matched -> do
                        [GamblingAccount.Account{..}] <- fmap snd <$> query @GamblingAccount.Account party
                        Some (_, unmatchedRunningParty) <- queryContractKey @RunningParty party (party, id, Unmatched)
                        let cashReturned = totalMainBalance - unmatchedRunningParty.totalMainBalance
                        assertMsgWith (msgTemplate "cash returned" (show cashReturned)) $ cashReturned == Optional.fromOptional 0.0 value
                    _ -> assertFail "CashReturned is only supported during Matched stage"
        BonusReturned value -> do
                case stage of
                    Matched -> do
                        [GamblingAccount.Account{..}] <- fmap snd <$> query @GamblingAccount.Account party
                        Some (_, unmatchedRunningParty) <- queryContractKey @RunningParty party (party, id, Unmatched)
                        let bonusReturned = totalBonusBalance - unmatchedRunningParty.totalBonusBalance
                        assertMsgWith (msgTemplate "bonus returned" (show bonusReturned)) $ bonusReturned == Optional.fromOptional 0.0 value
                    _ -> assertFail "BonusReturned is only supported during Matched stage"

{- --------------------------------------GAMBLING GOLDEN TEST RUNNERS-------------------------------------- -}

test_runner: HasCallStack => TestReport -> Script ()
test_runner TestReport{..} = script $ do
    withCeremony partyReports $ \((party1, party1Report), (party2, party2Report)) -> do
        withEventOrigination $ \withEventId@(Parties{..}, _) outcomeOdds@(_::_::_) -> script $ do

            ((party1Side, betId1), (party2Side, betId2)) <-
                test_runner_bet_placement debugPrefix withEventId
                    (party1, party1Report)
                    (party2, party2Report)
                    outcomeOdds

            (winnerMatchedBetId, looserMatchedBetId, party1BetId, party2BetId) <-
                test_runner_bet_matching debugPrefix withEventId
                    winner
                    (party1, party1Side, betId1, party1Report)
                    (party2, party2Side, betId2, party2Report)
                    outcomeOdds

            test_runner_bet_settlement debugPrefix withEventId
                winner
                (party1, party1Side, party1BetId, party1Report)
                (party2, party2Side, party2BetId, party2Report)
                (winnerMatchedBetId, looserMatchedBetId)
                outcomeOdds

            test_runner_bet_cancel debugPrefix withEventId
                (party1, party1Report)
                (party2, party2Report)

            pure ()

test_runner_bet_placement :
    HasCallStack =>
    Text ->
    (Parties, Text) ->
    (Party, PartyReport) ->
    (Party, PartyReport) ->
    [EventModel.OutcomeOdds] ->
    Script ((BetModel.Side, Text), (BetModel.Side, Text))
test_runner_bet_placement debugPrefix withEventId@(Parties{..}, _) (party1, party1Report) (party2, party2Report) outcomeOdds = script $ do
    let assertMsgWith = assertMsgWithPrefix_alt debugPrefix
    let PartyReport{partyInput = party1Input, partyOutput = party1Output} = party1Report
        PartyReport{partyInput = party2Input, partyOutput = party2Output} = party2Report
    let (outcomeOdds1::_::_) = outcomeOdds

    -- | Place the bets
    let betId1 = party1Input.id <> "-1"
    let party1Side = case party1Input.side of
            Back -> back party1Input.odd party1Input.stake outcomeOdds1.outcome
            Lay -> lay party1Input.odd party1Input.stake outcomeOdds1.outcome
    testBetPlacement (withEventId, party1, party1Side, betId1, None)
    let betId2 = party2Input.id <> "-1"
    let party2Side = case party2Input.side of
            Back -> back party2Input.odd party2Input.stake outcomeOdds1.outcome
            Lay -> lay party2Input.odd party2Input.stake outcomeOdds1.outcome
    testBetPlacement (withEventId, party2, party2Side, betId2, None)

    -- there should be two bet-placements with status unmatched and
    allUnmatchedBetPlacements <- queryFilter @BetModel.BetPlacement gambyl $ \BetModel.BetPlacement{status} ->
        status == BetModel.Unmatched
    let numberOfUnmatches = length allUnmatchedBetPlacements
    assertMsgWith ("There should be only 2 bet-placements unmatched, but found: " <> show numberOfUnmatches) $
        numberOfUnmatches == 2

    -- the ids are the same as we input
    let allIds =
            (\BetModel.BetPlacement{details = BetModel.Details{betPlacementId}} -> betPlacementId)
            . snd <$> allUnmatchedBetPlacements
    assertMsgWith ("Bet ids is: " <> show allIds) $ Set.fromList allIds == Set.fromList [betId1, betId2]

    ((Unmatched,) `fmapSet` party1Output.unmatchedOutputs) & mapA_ (verify assertMsgWith gambyl party1 party1Input 1 betId1)
    ((Unmatched,) `fmapSet` party2Output.unmatchedOutputs) & mapA_ (verify assertMsgWith gambyl party2 party2Input 2 betId2)

    pure ((party1Side, betId1), (party2Side, betId2))

test_runner_bet_matching :
    HasCallStack =>
    Text ->
    (Parties, Text) ->
    Text ->
    (Party, BetModel.Side, Text, PartyReport) ->
    (Party, BetModel.Side, Text, PartyReport) ->
    [EventModel.OutcomeOdds] ->
    Script (Text, Text, Text, Text)
test_runner_bet_matching debugPrefix (p@Parties{..},_) _winner (party1, party1Side, betId1, party1Report) (party2, _, betId2, party2Report) _outcomeOdds = script $ do
    let assertMsgWith = assertMsgWithPrefix_alt debugPrefix
    let PartyReport{partyInput = party1Input, partyOutput = party1Output} = party1Report
        PartyReport{partyInput = party2Input, partyOutput = party2Output} = party2Report

    -- | Match the bets
    testMatchBets p

    -- there should be two bet-placements with status matched and
    allMatchedBetPlacements <- queryFilter @BetModel.BetPlacement gambyl $ \BetModel.BetPlacement{status} ->
        status == BetModel.Matched
    let numberOfMatches = length allMatchedBetPlacements
    assertMsgWith ("There should be only 2 bet-placements matched, but found: " <> show numberOfMatches) $ numberOfMatches == 2

    -- | Check for partial matches
    -- REVIEW: why this does not work?
    -- let (winerBetId, looserBetId) = if party1Input.id == winner then (betId1, betId2) else (betId2, betId1)
    let (winerBetId, looserBetId) = case party1Side of
            BetModel.Back{} -> (betId1, betId2)
            BetModel.Lay{} -> (betId2, betId1)
    (winnerMatchedBetId, winnerUnmatchedBetId) <- lookupForPartialMatches gambyl winerBetId
    (looserMatchedBetId, looserUnmatchedBetId) <- lookupForPartialMatches gambyl looserBetId

    -- check matching bets
    let (party1MatchedBetId, party2MatchedBetId) =
            if Text.isPrefixOf party1Input.id winnerMatchedBetId then (winnerMatchedBetId, looserMatchedBetId) else (looserMatchedBetId, winnerMatchedBetId)

    ((Matched,) `fmapSet` party1Output.matchedOutputs) & mapA_ (verify assertMsgWith gambyl party1 party1Input 1 party1MatchedBetId)
    ((Matched,) `fmapSet` party2Output.matchedOutputs) & mapA_ (verify assertMsgWith gambyl party2 party2Input 2 party2MatchedBetId)

    -- check unmatching bets
    let (mParty1UnmatchedBetId, mParty2UnmatchedBetId) =
            if Text.isPrefixOf party1Input.id winnerMatchedBetId then (winnerUnmatchedBetId, looserUnmatchedBetId) else (looserUnmatchedBetId, winnerUnmatchedBetId)

    case mParty1UnmatchedBetId of
        Some party1UnmatchedBetId -> do
            assertMsgWith ("There are unmatching bets for party: " <> show party1 <> ", but missing expectations") $
                not (Set.null party1Output.unmatchingOutputs)
            ((Unmatching,) `fmapSet` party1Output.unmatchingOutputs) &
                mapA_ (verify assertMsgWith gambyl party1 party1Input 1 party1UnmatchedBetId)
        None -> assertMsgWith ("There are no unmatching bets for party: " <> show party1) $
            Set.null party1Output.unmatchingOutputs

    case mParty2UnmatchedBetId of
        Some party2UnmatchedBetId -> do
            assertMsgWith ("There are unmatching bets for party: " <> show party2  <> ", but missing expectations") $
                not (Set.null party2Output.unmatchingOutputs)
            ((Unmatching,) `fmapSet` party2Output.unmatchingOutputs) &
                mapA_ (verify assertMsgWith gambyl party2 party2Input 2 party2UnmatchedBetId)
        None -> assertMsgWith ("There are no unmatching bets for party: " <> show party2) $
            Set.null party2Output.unmatchingOutputs

    pure (winnerMatchedBetId, looserMatchedBetId, party1MatchedBetId, party2MatchedBetId)

test_runner_bet_settlement :
    HasCallStack =>
    Text ->
    (Parties, Text) ->
    Text ->
    (Party, BetModel.Side, Text, PartyReport) ->
    (Party, BetModel.Side, Text, PartyReport) ->
    (Text, Text) ->
    [EventModel.OutcomeOdds] ->
    Script ()
test_runner_bet_settlement debugPrefix withEventId@(p@Parties{..},_) winner (party1, party1Side, party1BetId, party1Report) (party2, _, party2BetId, party2Report) (winnerMatchedBetId, looserMatchedBetId) outcomeOdds = script $ do
    let assertMsgWith = assertMsgWithPrefix_alt debugPrefix
    let PartyReport{partyInput = party1Input, partyOutput = party1Output} = party1Report
        PartyReport{partyInput = party2Input, partyOutput = party2Output} = party2Report
    let (outcomeOdds1::outcomeOdds2::_) = outcomeOdds

    -- Note: we need to match on the side so to desambiguate betId1 and betId2
    -- | Decide the winner party and the outcome
    let (winnerParty, winnerOutcome) = case party1Side of
            BetModel.Back{} ->
                    if party1Input.id == winner then (party1, outcomeOdds1.outcome) else (party2, outcomeOdds2.outcome)
            BetModel.Lay{} ->
                    if party1Input.id == winner then (party1, outcomeOdds2.outcome) else (party2, outcomeOdds1.outcome)
    testEventUpdateFromIntegration (withEventId, None, IntegrationEvents.Finished, [winnerOutcome], "yes")

    -- | Settle the bets
    settleBetsWithBonus (p, winnerParty, winnerMatchedBetId, looserMatchedBetId)

    ((Settlement,) `fmapSet` party1Output.settlementOutputs) & mapA_ (verify assertMsgWith gambyl party1 party1Input 1 party1BetId)
    ((Settlement,) `fmapSet` party2Output.settlementOutputs) & mapA_ (verify assertMsgWith gambyl party2 party2Input 2 party2BetId)

    pure ()

test_runner_bet_cancel : Text -> (Parties, b) -> (Party, PartyReport) -> (Party, PartyReport) -> Script ()
test_runner_bet_cancel debugPrefix (p@Parties{..},_) (party1, party1Report) (party2, party2Report) = script $ do
    let
      assertMsgWith = assertMsgWithPrefix_alt debugPrefix
      PartyReport{partyInput = party1Input, partyOutput = party1Output} = party1Report
      PartyReport{partyInput = party2Input, partyOutput = party2Output} = party2Report

    allUnmatchedBetPlacements <- queryFilter @BetModel.BetPlacement gambyl $ \BetModel.BetPlacement{status} ->
        status == BetModel.Unmatched

    let party1BetIds =
            fmap (\BetModel.BetPlacement{details = BetModel.Details{betPlacementId}} -> betPlacementId)
            . filter (\BetModel.BetPlacement{customer} -> customer == party1)
            $ snd <$> allUnmatchedBetPlacements

    let party2BetIds =
            fmap (\BetModel.BetPlacement{details = BetModel.Details{betPlacementId}} -> betPlacementId)
            . filter (\BetModel.BetPlacement{customer} -> customer == party2)
            $ snd <$> allUnmatchedBetPlacements

    -- | Cancel all unmatched bets
    mapA_ (\party1BetId -> testManualCancelBetAfterEventStatusChange (p, party1, party1BetId, IntegrationEvents.Finished)) party1BetIds
    mapA_ (\party2BetId -> testManualCancelBetAfterEventStatusChange (p, party2, party2BetId, IntegrationEvents.Finished)) party2BetIds

    let party1ToVerify = party1BetIds `zip` Set.toList ((Cancel,) `fmapSet` party1Output.cancelationOutputs)
    let party2ToVerify = party2BetIds `zip` Set.toList ((Cancel,) `fmapSet` party2Output.cancelationOutputs)

    if null party1ToVerify
    then assertMsgWith ("Party-1: " <> party1Input.id <> " cancelation outputs are inconsistent with its settlement outputs.") $
        party1Output.cancelationOutputs == party1Output.settlementOutputs
    else party1ToVerify & mapA_ (uncurry $ verify assertMsgWith gambyl party1 party1Input 1)

    if null party2ToVerify
    then assertMsgWith ("Party-2: " <> party2Input.id <> " cancelation outputs are inconsistent with its settlement outputs.") $
        party2Output.cancelationOutputs == party2Output.settlementOutputs
    else party2ToVerify & mapA_ (uncurry $ verify assertMsgWith gambyl party2 party2Input 2)

    pure ()