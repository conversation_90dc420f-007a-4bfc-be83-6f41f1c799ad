module Tests.Utils where

import Daml.Script

import DA.Map qualified as Map
import DA.Optional qualified as Optional
import DA.Set qualified as Set
import DA.Stack     (HasCallStack)
import DA.Text qualified as Text

import EnetPulseIntegration.Events  (OutcomeType(..), OutcomeSubType(..), Participant(..))
import EnetPulseIntegration.Events qualified as IntegrationEvents hiding (OutcomeType(..), OutcomeSubType(..), Participant(..))

import Gambyl.Gambling.Account.Model qualified as GamblingAccount
import Gambyl.Gambling.Bet.Model qualified as BetModel
import Gambyl.Gambling.Bet.Odds.Model qualified as Odds
import Gambyl.Gambling.Model qualified as GamblingModel
import Gambyl.Gambling.Role qualified as GamblingRole
import Gambyl.Gambling.Service qualified as GamblingService
import Gambyl.Gambling.Event.Model qualified as EventModel

import Scripts.Common

data OutcomeListWithOddType = OutcomeListWithOddType with
    participant : Optional Participant
    participantOrder: Int
    order: Int
    type_: OutcomeType
    subtype:OutcomeSubType
    odd: Numeric 2
  deriving (Eq, Show)

data OutcomeList = OutcomeList with
    participant : Optional Participant
    participantOrder: Int
    order: Int
    type_: OutcomeType
    subtype:OutcomeSubType
    odd: Decimal
  deriving (Eq, Show)
{- ---------------------------------------HELPER FUNCTIONS---------------------------------------- -}

queryContractWithKey : forall t k. (HasCallStack, TemplateKey t k) => Party -> k -> Text -> Script (ContractId t, t)
queryContractWithKey party key_ msg  = do
    queryContractKey @t party key_ >>= (return . Optional.fromSomeNote (msgWithFunc msg))


queryServiceWithKey party key_ = queryContractWithKey @GamblingService.Service party key_ "Service doesn't exist"


queryAccountWithKey party key_ = queryContractWithKey @GamblingAccount.Account party key_ ("Account with key <" <> show key_ <> "> doesn't exist")


queryRoleWithKey party key_ = queryContractWithKey @GamblingRole.Role party key_ "Role doesn't exist"


getErrorMessage : Party -> Either GamblingModel.ActionFailureCid a -> Script Text
getErrorMessage party (Left failureCid) = (.reason) . Optional.fromSomeNote "No error" <$> queryContractId party failureCid
getErrorMessage _ _ = return "No error"


buildSide : (BetModel.SideDetails -> BetModel.Side) -> Odds.OddType -> Numeric 2 -> IntegrationEvents.Outcome -> BetModel.Side
buildSide side odd stake outcome = side $ BetModel.SideDetails with odd, stake, outcome


buildBetPlacementDetailsList : [(BetModel.Side, (Party, Party, Text), Map.Map Text Text, Time)] -> Int -> Int -> [BetModel.Details]
buildBetPlacementDetailsList [] _ _ = []
buildBetPlacementDetailsList ((side, eventKey, eventTitle, eventStartDate)::events) betPlacementId inc =
    BetModel.Details with
                    betPlacementId = show betPlacementId <> "-" <> show inc
                    side
                    eventTitle
                    eventKey
                    eventStartDate
    :: buildBetPlacementDetailsList events betPlacementId (inc + 1)

buildOutcomesListWithOddType : [OutcomeListWithOddType] -> [(EventModel.InputOutcomeOdd)]
buildOutcomesListWithOddType [] = []
buildOutcomesListWithOddType (OutcomeListWithOddType{..}::xs) =
    (EventModel.InputOutcomeOdd with outcome = (IntegrationEvents.Outcome with participantId, participantOrder , order, type_, subtype), odd = Odds.Decimal odd)
    :: buildOutcomesListWithOddType xs
    where participantId = (\ p -> Some p.id) =<< participant


buildOutcomesList : [OutcomeList] -> [(IntegrationEvents.OutcomeOdds)]
buildOutcomesList [] = []
buildOutcomesList (OutcomeList{..}::xs) =
    (IntegrationEvents.OutcomeOdds with outcome = (IntegrationEvents.Outcome with participantId, participantOrder, order, type_, subtype), odd)
    :: buildOutcomesList xs
    where participantId = (\ p -> Some p.id) =<< participant


fromEventOutcomes : (IntegrationEvents.Outcome -> Odds.OddType -> a) -> [EventModel.OutcomeOdds] -> [a]
fromEventOutcomes mapFunction = map (\ eventOutcome -> do
            let
                [decimalOdd] = filter (\ case
                        Odds.Decimal _ -> True
                        _ -> False
                    ) $ Set.toList eventOutcome.odds

            mapFunction eventOutcome.outcome decimalOdd)

createIndexList : [a] -> [Int]
createIndexList = createIndexListStartingOn 0 []


createIndexListStartingOn : Int -> [Int] -> [a] -> [Int]
createIndexListStartingOn _ indexList [] = indexList
createIndexListStartingOn start indexList (_::xs) =
    createIndexListStartingOn (start + 1) (indexList <> [start]) xs


getTransactionFailureReason : Party -> Script GamblingModel.ActionFailureCid -> Script Text
getTransactionFailureReason party failingTransaction =
    (.reason) . Optional.fromSomeNote "ActionFailure contract not found" <$>
        (failingTransaction >>= queryContractId party)

getParticipant : Text -> Int -> Participant
getParticipant name order = Participant with
                name
                id = name <> "-id"
                order
                co_op = None

-- | Takes a list of Participant lists, where child list represents a team
--    and the parent list represents competing participants
--    ex.:
--        getEventTitle [["A"], ["C"]] => "A vs C"
--        getEventTitle [["A", "B"], ["C", "D"]] => "A & B vs C & D"
getEventTitle : [[Participant]] -> Text
getEventTitle participantsList = Text.intercalate " vs " teamsList
  where
    participantsNameList = map (map (.name)) participantsList
    teamsList = foldl (\ acc elem -> acc <> [Text.intercalate " & " elem]) [] participantsNameList

queryCidByContractKey : forall t k p. (TemplateKey t k, IsParties p) => p -> k -> Script (Optional (ContractId t))
queryCidByContractKey p k = fmap fst <$> queryContractKey p k

queryContractByContractKey : forall t k p. (TemplateKey t k, IsParties p) => p -> k -> Script (Optional t)
queryContractByContractKey p k = fmap snd <$> queryContractKey p k

queryCidList : forall t p. (HasAgreement t, HasTemplateTypeRep t, HasToAnyTemplate t, HasFromAnyTemplate t, IsParties p) => p -> Script [ContractId t]
queryCidList p = fmap fst <$> query p

queryContractList : forall t p. (HasAgreement t, HasTemplateTypeRep t, HasToAnyTemplate t, HasFromAnyTemplate t, IsParties p) => p -> Script [t]
queryContractList p = fmap snd <$> query p


