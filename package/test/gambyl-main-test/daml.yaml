# for config file options, refer to
# https://docs.daml.com/tools/assistant.html#project-config-file-daml-yaml

sdk-version: 2.9.4
name: gambyl-main-test
source: daml
init-script:
parties:
  - Operator
version: ${GAMBYL_MAIN_TEST_VERSION}
dependencies:
  - daml-prim
  - daml-stdlib
  - daml-script
data-dependencies:
  - ../../../build/implementation/gambyl-enetpulse-impl-${GAMBYL_ENETPULSE_IMPL_CURRENT_VERSION}.dar
  - ../../../build/test/gambyl-enetpulse-test-${GAMBYL_ENETPULSE_TEST_VERSION}.dar
  - ../../../build/implementation/gambyl-exberry-impl-${GAMBYL_EXBERRY_IMPL_CURRENT_VERSION}.dar
  - ../../../build/test/gambyl-exberry-test-${GAMBYL_EXBERRY_TEST_VERSION}.dar
  - ../../../build/implementation/gambyl-jumio-impl-${GAMBYL_JUMIO_IMPL_CURRENT_VERSION}.dar
  - ../../../build/test/gambyl-jumio-test-${GAMBYL_JUMIO_TEST_VERSION}.dar
  - ../../../build/implementation/gambyl-main-impl-${GAMBYL_MAIN_IMPL_CURRENT_VERSION}.dar
  - ../../../build/implementation/gambyl-moneymatrix-impl-${GAMBYL_MONEYMATRIX_IMPL_CURRENT_VERSION}.dar
  - ../../../build/test/gambyl-moneymatrix-test-${GAMBYL_MONEYMATRIX_TEST_VERSION}.dar
  - ../../../build/test/gambyl-quickbooks-test-${GAMBYL_QUICKBOOKS_TEST_VERSION}.dar
  - ../../../build/implementation/gambyl-sendgrid-impl-${GAMBYL_SENDGRID_IMPL_CURRENT_VERSION}.dar
  - ../../../ext-pkg/da-marketplace/${DA_MARKETPLACE_CURRENT_VERSION}/da-marketplace-${DA_MARKETPLACE_CURRENT_VERSION}.dar
module-prefixes:
  gambyl-enetpulse-impl-${GAMBYL_ENETPULSE_IMPL_CURRENT_VERSION}: EnetPulse
  gambyl-enetpulse-test-${GAMBYL_ENETPULSE_TEST_VERSION}: Tests
  gambyl-exberry-impl-${GAMBYL_EXBERRY_IMPL_CURRENT_VERSION}: Exberry
  gambyl-exberry-test-${GAMBYL_EXBERRY_TEST_VERSION}: Tests
  gambyl-jumio-test-${GAMBYL_JUMIO_TEST_VERSION}: Tests
  gambyl-moneymatrix-test-${GAMBYL_MONEYMATRIX_TEST_VERSION}: Tests
  gambyl-quickbooks-test-${GAMBYL_QUICKBOOKS_TEST_VERSION}: Tests
sandbox-options:
  - --wall-clock-time
build-options:
  - --ghc-option=-Wall
  - --ghc-option=-Wno-name-shadowing
  - --ghc-option=-Wno-unused-do-bind
  - --ghc-option=-fno-warn-missing-signatures
  - --ghc-option=-fno-warn-orphans
  - --ghc-option=-Werror
  - --disable-warn-large-tuples=yes
  - --output=../../../build/test/gambyl-main-test-${GAMBYL_MAIN_TEST_VERSION}.dar

