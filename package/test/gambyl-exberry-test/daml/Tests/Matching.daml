module Tests.Matching where

import Daml.Script

import Exberry.Integration

import Tests.Utils

testCreateInstrumentRequest : ([Party], Text, Text, Text, Text, Int, Int, Decimal, Decimal, Text) -> Script ([Party], ContractId Instrument)
testCreateInstrumentRequest
    (~stakeholders@[gambyl,exberry, _], symbol, quoteCurrency, instrumentDescription, calendarId
    , pricePrecision, quantityPrecision, minQuantity, maxQuantity, status
  ) = script do

  let instrumentRequest = CreateInstrumentRequest with
        integrationParty = exberry, symbol
        quoteCurrency = quoteCurrency
        instrumentDescription = instrumentDescription
        calendarId = calendarId
        pricePrecision = pricePrecision
        quantityPrecision = quantityPrecision
        minQuantity = minQuantity
        maxQuantity = maxQuantity
        status = status

  debug "Testing creating instrument..."
  insReq <- submit gambyl $ createCmd instrumentRequest

  ins <- submit exberry do
    exerciseCmd insReq $ CreateInstrumentRequest_Success with instrumentId = calendarId

  return (stakeholders, ins)

testBuySellOrder: ([Party], Text, Text, Decimal, Decimal, Text, Text,Int,Text)  -> <PERSON>ript ([Party], OrderData)
testBuySellOrder (~stakeholders@[exberry], orderType, symbol, quantity, price, side ,timeInForce, mpOrderId, userId) = script do

    let buyOrSellOrder = OrderData with
                    orderType
                    instrument = symbol
                    quantity
                    price
                    side
                    timeInForce
                    mpOrderId
                    userId

    debug "Testing creating Order Data ..."

    debug "Testing order request..."
    submit exberry $ createCmd NewOrderRequest with order = buyOrSellOrder, integrationParty = exberry,..

    return (stakeholders, buyOrSellOrder)


testExecuteReport: ([Party], OrderData,OrderData,Int,Text,Int,Int,Decimal,Decimal,Int)  -> Script ([Party], ContractId ExecutionReport)
testExecuteReport (~stakeholders@[exberry , _, _], buyOrder, sellOrder,eventId,eventTimestamp,sid, matchId,executedQuantity,executedPrice,trackingNumber) = script do

    let execReport = ExecutionReport with
          integrationParty = exberry
          sid
          eventId
          eventTimestamp
          instrument = buyOrder.instrument
          trackingNumber
          makerMpId = buyOrder.mpOrderId
          makerMpOrderId = buyOrder.mpOrderId - 1
          makerOrderId = buyOrder.mpOrderId - 1
          takerMpId = sellOrder.mpOrderId
          takerMpOrderId = sellOrder.mpOrderId + 1
          takerOrderId = sellOrder.mpOrderId + 1
          matchId
          executedQuantity
          executedPrice

    debug "Testing Execution Report ..."

    exReport <- submit exberry $ createCmd execReport

    return (stakeholders, exReport)

-- | Test must be executed against running ledger
runIntegrationTest : LedgerParties -> Script ()
runIntegrationTest LedgerParties{exchange = integrationParty} = do

  testNumber <- (/ 10000) <$> getTimestamp

  let symbol = "DA" <> show testNumber

  let instrumentRequest = CreateInstrumentRequest
        with
          integrationParty, symbol
          quoteCurrency = "DAQUOTE"
          instrumentDescription = "DA TEST INSTRUMENT"
          calendarId = "1261007448"
          pricePrecision = 2
          quantityPrecision = 2
          minQuantity = 1.0
          maxQuantity = 10000.0
          status = "Active"

  debug "Testing creating instrument..."
  submit integrationParty $ createCmd instrumentRequest

  (irCid,_) <- waitQuery 30.0
    $ filterQueryFirst @Instrument (\(_,ins) -> ins.symbol == symbol) integrationParty
  submit integrationParty $ archiveCmd irCid

  debug "Test failed instrument creation..."
  submit integrationParty $ createCmd instrumentRequest

  (firCid,_) <- waitQuery 30.0
    $ filterQueryFirst @FailedInstrumentRequest (\(_,ins) -> ins.symbol == symbol) integrationParty
  submit integrationParty $ archiveCmd firCid

  let buyId = testNumber
  let sellId = testNumber + 1

  let buyOrder = OrderData with
        orderType = "Limit"
        instrument = symbol
        quantity = 100.0
        price = 50.0
        side = "Buy"
        timeInForce = "GTC"
        mpOrderId = buyId
        userId = "Alice"

  let sellOrder = buyOrder with side = "Sell", mpOrderId = sellId, userId = "Bob"

  debug "Testing order request..."
  submit integrationParty $ createCmd NewOrderRequest with order = buyOrder, ..

  (nosCid,_) <- waitQuery 30.0 $ queryFirst @NewOrderSuccess integrationParty
  submit integrationParty $ archiveCmd nosCid

  debug "Testing order request failure..."
  submit integrationParty $ createCmd NewOrderRequest with order = buyOrder, ..

  (nofCid,_) <- waitQuery 30.0 $ queryFirst @NewOrderFailure integrationParty
  submit integrationParty $ archiveCmd nofCid

  debug "Testing order execution..."

  submit integrationParty $ createCmd NewOrderRequest with order = sellOrder, ..

  (nosCid,_) <- waitQuery 30.0 $ queryFirst @NewOrderSuccess integrationParty
  submit integrationParty $ archiveCmd nosCid

  (_, _) <- waitQuery 30.0
    $ filterQueryFirst @ExecutionReport
        (\(_,exc) -> exc.makerMpOrderId == buyOrder.mpOrderId
                  && exc.takerMpOrderId == sellOrder.mpOrderId) integrationParty

  debug "Testing order cancel..."

  -- Create order to cancel
  let cancelOrder = sellOrder with mpOrderId = sellId + 1
  submit integrationParty $ createCmd NewOrderRequest with order = cancelOrder, ..

  (nosCid,_) <- waitQuery 30.0 $ queryFirst @NewOrderSuccess integrationParty
  submit integrationParty $ archiveCmd nosCid

  -- Cancel order
  let cancelOrderRequest = CancelOrderRequest with
          integrationParty
          instrument = symbol
          orderId  = cancelOrder.mpOrderId
          userId     = cancelOrder.userId

  submit integrationParty $ createCmd cancelOrderRequest
  (cosCid,_) <- waitQuery 30.0 $ queryFirst @CancelOrderSuccess integrationParty
  submit integrationParty $ archiveCmd cosCid

  debug "Testing cancel order failure..."
  submit integrationParty $ createCmd cancelOrderRequest

  (cofCid,_) <- waitQuery 30.0 $ queryFirst @CancelOrderFailure integrationParty
  submit integrationParty $ archiveCmd cofCid

  debug "Testing mass cancel..."
  submit integrationParty $ createCmd MassCancelRequest with sid = show testNumber, instrument = symbol, ..

  (mcsCid,_) <- waitQuery 30.0 $ queryFirst @MassCancelSuccess integrationParty
  submit integrationParty $ archiveCmd mcsCid

  debug "Testing mass cancel failure..."
  submit integrationParty $ createCmd MassCancelRequest with
      sid = show (testNumber + 1), instrument = symbol <> "-no-exist", ..

  (mcsCid,_) <- waitQuery 30.0 $ queryFirst @MassCancelFailure integrationParty
  submit integrationParty $ archiveCmd mcsCid

  debug "Done!"

  return ()


allocateParties : Script [Party]
allocateParties = script do
    allocate ["Exberry", "Alice"]
    where
        allocate = mapA (\ p -> allocatePartyWithHint p (PartyIdHint p))