sdk-version: 2.9.4
name: gambyl-moneymatrix-test
source: daml
init-script:
version: ${GAMBYL_MONEYMATRIX_TEST_VERSION}
dependencies:
  - daml-prim
  - daml-stdlib
  - daml-script
data-dependencies:
  - ../../../build/implementation/gambyl-moneymatrix-impl-${GAMBYL_MONEYMATRIX_IMPL_CURRENT_VERSION}.dar
sandbox-options:
  - --wall-clock-time
build-options:
  - --ghc-option=-Wall
  - --ghc-option=-Wno-name-shadowing
  - --ghc-option=-Wno-unused-do-bind
  - --ghc-option=-fno-warn-missing-signatures
  - --ghc-option=-Werror
  - --output=../../../build/test/gambyl-moneymatrix-test-${GAMBYL_MONEYMATRIX_TEST_VERSION}.dar
