module Tests.MoneyMatrix where

import Daml.Script

import DA.Optional qualified as Optional
import DA.Date qualified as Date

import MoneyMatrixIntegration.Deposit qualified as Deposit
import MoneyMatrixIntegration.Withdraw qualified as Withdraw


---------------------------------- WITHDRAW -----------------------------------------

-- Initial Withdraw Request
testInitialWithdrawRequest : ([Party], Numeric 2) -> <PERSON><PERSON><PERSON> ([Party], ContractId Withdraw.InitialWithdrawRequest)
testInitialWithdrawRequest (~stakeholders@[moneyMatrix, client], requestedAmount) = script do

    now <- getTime
    initialWithdrawRequestCid <- submit client do
        createCmd Withdraw.InitialWithdrawRequest
            with
                integrationParty = moneyMatrix
                client
                requestedAmount
                currency = "USD"
                observers = []
                transactionId = "TestWithdrawtransactionId"
                timestamp = now
                userInformation = Withdraw.UserInformation with
                    firstName    = "John"
                    lastName     = "Doe"
                    emailAddress = "<EMAIL>"
                    phoneNumber  = "0123456789"
                    birthday     = Date.fromGregorian (1980, Date.Jan, 1)
                    city         = "City"
                    countryCode  = "Country"
                    postalCode   = "Postal Code"
                    subDivision  = "Sub Division"
                    addressLine1 = "Address Line 1"
                language     = "PT"

    return (stakeholders, initialWithdrawRequestCid)

run_testInitialWithdrawRequest = script do
    allocateParties
    >>= testInitialWithdrawRequest . (, 40.00)

-- Withdraw Request

testWithdrawRequest : ([Party], ContractId Withdraw.InitialWithdrawRequest) -> Script ([Party], ContractId Withdraw.WithdrawRequest)
testWithdrawRequest (~stakeholders@[moneyMatrix, _], initialWithdrawRequestCid)= script do

    let  transactionCode = "dhi234324ohdisa23n4jb23vj2b2"

    withdrawRequestCid <- submit moneyMatrix do
        exerciseCmd initialWithdrawRequestCid Withdraw.ProcessWithdrawRequest
            with
                cashierURL = "cashURL/moneyMatrix/test"
                transactionCode
                countryCode = "MT"

    return (stakeholders, withdrawRequestCid)

run_testWithdrawRequest = script do
    allocateParties
    >>= testInitialWithdrawRequest . (, 40.0)
    >>= testWithdrawRequest


-- Finished Withdrawal Transaction

testFinalizeWithdrawTransaction : ([Party], ContractId Withdraw.WithdrawRequest) -> Script ()
testFinalizeWithdrawTransaction (~[moneyMatrix, _], withdrawRequestCid) = script do

    Withdraw.WithdrawRequest{requestedAmount} <-
        Optional.fromSomeNote "Withdraw Request Contract not found" <$> queryContractId moneyMatrix withdrawRequestCid

    submit moneyMatrix do
        exerciseCmd withdrawRequestCid $
            Withdraw.FinalizeTransaction with confirmedAmount = requestedAmount

    return ()

run_testFinishedWithdrawTransaction = script do
    allocateParties
    >>= testInitialWithdrawRequest . (, 40.0)
    >>= testWithdrawRequest
    >>= testFinalizeWithdrawTransaction


---------------------------------- Deposit -----------------------------------------

testInitialDepositRequest : ([Party], Numeric 2)  -> Script ([Party], ContractId Deposit.InitialDepositRequest)
testInitialDepositRequest (~stakeholders@[moneyMatrix, client], requestedAmount) = script do

    now <- getTime
    initialDepositRequestCid <- submit client do
        createCmd Deposit.InitialDepositRequest
            with
                integrationParty = moneyMatrix
                client
                requestedAmount
                currency = "USD"
                observers = []
                transactionId = "TesttransactionId"
                timestamp = now
                userInformation = Deposit.UserInformation with
                    firstName    = "John"
                    lastName     = "Doe"
                    emailAddress = "<EMAIL>"
                    phoneNumber  = "0123456789"
                    birthday     = Date.fromGregorian (1980, Date.Jan, 1)
                    city         = "City"
                    countryCode  = "Country"
                    postalCode   = "Postal Code"
                    subDivision  = "Sub Division"
                    addressLine1 = "Address Line 1"
                language     = "PT"

    return (stakeholders, initialDepositRequestCid)

run_testInitialDepositRequest = script do
    allocateParties >>=
        testInitialDepositRequest . (, 20.0)


testDepositRequest : (([Party], ContractId Deposit.InitialDepositRequest), Text) -> Script ([Party], ContractId Deposit.DepositRequest)
testDepositRequest ((~stakeholders@[moneyMatrix, _], initialDepositRequestCid), transactionCode)= script do

    depositRequestCid <- submit moneyMatrix do
      exerciseCmd initialDepositRequestCid Deposit.ProcessDepositRequest
          with
              cashierURL = "cashURL/moneyMatrix/test"
              transactionCode
              countryCode = "MT"

    return (stakeholders, depositRequestCid)

run_testDepositRequest = script do
    allocateParties
    >>= testInitialDepositRequest . (, 20.0)
    >>= testDepositRequest .(,"123")


testFinalizeTransaction : ([Party], ContractId Deposit.DepositRequest) -> Script ()
testFinalizeTransaction (~[moneyMatrix, _], depositRequestCid) = script do

    Deposit.DepositRequest{requestedAmount} <-
        Optional.fromSomeNote "Deposit Request Contract not found" <$> queryContractId moneyMatrix depositRequestCid

    submit moneyMatrix do
        exerciseCmd depositRequestCid $ Deposit.FinalizeTransaction with confirmedAmount = requestedAmount

    return ()


run_testFinalizeTransaction = script do
    allocateParties
    >>= testInitialDepositRequest . (, 20.0)
    >>= testDepositRequest .(,"123")
    >>= testFinalizeTransaction



testFailTransaction : ([Party], ContractId Deposit.DepositRequest) -> Script ()
testFailTransaction (~[moneyMatrix, _], depositRequestCid) = script do

    Optional.fromSomeNote "Deposit Request Contract not found" <$> queryContractId moneyMatrix depositRequestCid

    submit moneyMatrix do
        exerciseCmd depositRequestCid $ Deposit.FailTransaction with reason = "Test"

    return ()


run_testFailTransaction = script do
    allocateParties
    >>= testInitialDepositRequest . (, 20.0)
    >>= testDepositRequest .(,"123")
    >>= testFailTransaction


---------------------------------- All -----------------------------------------

testAll : Script ()
testAll = script do

        stakeholders <- allocateParties

        -- Deposit
        testInitialDepositRequest (stakeholders, 20.0)
          >>= testDepositRequest .(,"123")
          >>= testFinalizeTransaction

        -- -- Withdraw
        testInitialWithdrawRequest (stakeholders, 20.0)
          >>= testWithdrawRequest
          >>= testFinalizeWithdrawTransaction


        return ()

{- ---------------------------------------HELPER FUNCTIONS---------------------------------------- -}

allocateParties : Script [Party]
allocateParties = script do
    allocate ["MoneyMatrix", "Alice"]
    where
        allocate = mapA (\ p -> allocatePartyWithHint p (PartyIdHint p))

queryKey : TemplateKey t k => Party -> k -> Script (ContractId t)
queryKey party key_ = queryContractKey party key_ >>= (return . (._1) . Optional.fromSomeNote ("Contract doesn't exist"))